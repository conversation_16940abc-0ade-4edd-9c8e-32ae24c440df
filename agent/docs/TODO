TODO:

- 解决 nomad 无法获取状态
- 解决 启动守护进程出现锁失败
- 日志链路不完整

我让 docker 和 nomad 在 WSL2 Ubuntu24定制镜像上使用 cgroupv2，这个定制系统没有 systemd 为此我做了以下操作：



1. mount -t cgroup2 none /sys/fs/cgroup

2. 修改 docker daemon

```json

{

    "userland-proxy": false,

    "registry-mirrors": [

        "https://ocyev7hi.mirror.aliyuncs.com",

        "https://docker.m.daocloud.io",

        "https://huecker.io",

        "https://dockerhub.timeweb.cloud",

        "https://dockerhub.icu",

        "https://docker.chenby.cn",

        "https://docker.1panel.live",

        "https://docker.awsl9527.cn",

        "https://docker.anyhub.us.kg",

        "https://dhub.kubesre.xyz",

        "https://docker.m.daocloud.io",

        "https://4nf8ppbu.mirror.aliyuncs.com",

        "https://mirror.ccs.tencentyun.com"

    ],

    "insecure-registries": [

        "http://100.64.0.3:8443"

    ],

    "runtimes": {

        "nvidia": {

            "args": [],

            "path": "nvidia-container-runtime"

        }

    },

    "exec-opts": [

        "native.cgroupdriver=cgroupfs"

    ],

    "storage-driver": "overlay2",

    "features": {

        "buildkit": true

    },

    "cgroup-parent": "/docker",

    "default-cgroupns-mode": "private",

    "log-driver": "json-file",

    "log-opts": {

        "max-size": "10m",

        "max-file": "3"

    }

}

```

3. 修改 nomad hcl

```

data_dir  = "/opt/nomad/data"



bind_addr = "{HOST}"



advertise {

  http = "{HOST}:4646"

}



client {

  enabled = true

  cgroup_parent = "nomad.slice"

  options {

    "docker.volumes.enabled" = true

  }

  servers = ["{SERVER}:4647"]

}

plugin "docker" {

  config {

    # 确保 Docker 使用正确的 cgroup 驱动

    allow_privileged = true

    volumes {

      enabled = true

    }

    # 如果使用 cgroupfs 驱动

    # cgroup_fs_root = "/sys/fs/cgroup"

  }

}

plugin "nomad-device-nvidia" {

  config {

    enabled            = true

    fingerprint_period = "1m"

  }

}

```



现在nomad爆如下错误，应该怎么解决？

2025-07-24T07:44:49.025+0800 [ERROR] agent: error starting agent: error="client setup failed: failed to initialize process manager: failed to detect memset: open /sys/fs/cgroup/cpuset/cpuset.mems: no such file or directory" daemon="nomad" stream="stdout" module="nomad::24T07" timestamp="2025-07-24T07:44:49



这个错误怎么解决？


⏺ 这个错误是因为 Nomad 正在寻找 cgroup v1 的路径结构，但您的系统使用的是 cgroup v2。Nomad 试图访问 /sys/fs/cgroup/cpuset/cpuset.mems，这是 cgroup v1 的路径格式。

  问题分析

  在 cgroup v2 中，层次结构是统一的，不再有独立的控制器子目录（如 cpuset、memory 等）。而 Nomad 的某些版本可能还没有完全支持纯 cgroup v2 环境。

  解决方案

  方案一：启用 cgroup v1/v2 混合模式（推荐）

  这是最兼容的解决方案，允许 Nomad 使用它期望的 cgroup v1 接口：

  # 1. 卸载当前的 cgroup v2
  umount /sys/fs/cgroup

  # 2. 挂载 tmpfs
  mount -t tmpfs cgroup_root /sys/fs/cgroup

  # 3. 创建并挂载 cgroup v2（统一层次）
  mkdir -p /sys/fs/cgroup/unified
  mount -t cgroup2 none /sys/fs/cgroup/unified

  # 4. 创建并挂载 cgroup v1 控制器
  for controller in cpu cpuacct cpuset memory devices freezer net_cls blkio perf_event net_prio hugetlb pids rdma misc; do
      mkdir -p /sys/fs/cgroup/$controller
      mount -t cgroup -o $controller none /sys/fs/cgroup/$controller 2>/dev/null || true
  done

  # 5. 创建 systemd cgroup（如果需要）
  mkdir -p /sys/fs/cgroup/systemd
  mount -t cgroup -o none,name=systemd none /sys/fs/cgroup/systemd

  方案二：配置 Nomad 使用 cgroup v2（如果 Nomad 版本支持）

  修改 Nomad 配置以明确指定 cgroup 版本：

  client {
    enabled = true

    # 指定 cgroup 版本
    cgroup_parent = "/nomad.slice"

    # 添加 cgroup v2 相关配置
    host_volume "cgroup" {
      path = "/sys/fs/cgroup"
      read_only = true
    }

    options {
      "docker.volumes.enabled" = true
      # 明确指定使用 cgroup v2
      "fingerprint.cgroups.version" = "v2"
    }
  }

  # 可能需要添加的环境变量
  plugin "docker" {
    config {
      allow_privileged = true
      volumes {
        enabled = true
      }
      # 指定 cgroup 路径
      cgroup_parent = "/docker"
    }
  }

  方案三：创建兼容性符号链接（临时方案）

  如果上述方案不可行，可以创建符号链接来模拟 v1 结构：

  # 创建必要的目录和文件
  mkdir -p /sys/fs/cgroup/cpuset
  echo "0" > /sys/fs/cgroup/cpuset/cpuset.mems
  echo "0" > /sys/fs/cgroup/cpuset/cpuset.cpus

  建议的完整解决步骤
  ⎿  User rejected Claude's plan:
     ╭──────────────────────────────────────────────────────────────────────╮
     │ 1. 首先尝试混合模式（最可靠）：                                      │
     │   - 卸载当前 cgroup v2 挂载                                          │
     │   - 设置 cgroup v1/v2 混合模式                                       │
     │   - 这将为 Nomad 提供它期望的 v1 接口，同时保留 v2 功能              │
     │ 2. 更新 Docker 配置：                                                │
     │   - 将 Docker daemon.json 中的 native.cgroupdriver=cgroupfs 保持不变 │
     │   - 确保 cgroup-parent 设置正确                                      │
     │ 3. 调整 Nomad 配置：                                                 │
     │   - 如果使用混合模式，保持现有配置                                   │
     │   - 如果 Nomad 版本支持 v2，添加相应的 v2 配置选项                   │
     │ 4. 创建启动脚本：                                                    │
     │   - 创建一个脚本在系统启动时自动设置 cgroup 挂载                     │
     │   - 确保在启动 Docker 和 Nomad 之前运行                              │
     │ 5. 测试验证：                                                        │
     │   - 重启 Docker 服务                                                 │
     │   - 重启 Nomad 服务                                                  │
     │   - 检查日志确认没有 cgroup 相关错误