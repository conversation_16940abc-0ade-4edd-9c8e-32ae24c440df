## 目录结构

```bash
drwxr-xr-x 18 <USER> <GROUP> 360 Jul 24 17:54 .
drwxr-xr-x 10 <USER> <GROUP>   0 Jul 24 15:23 ..
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:40 blkio
dr-xr-xr-x 12 <USER> <GROUP>   0 Jul 24 15:23 cpu
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 cpuacct
dr-xr-xr-x  5 <USER> <GROUP>   0 Jul 24 16:01 cpuset
dr-xr-xr-x 11 <USER> <GROUP>   0 Jul 24 15:23 devices
dr-xr-xr-x  5 <USER> <GROUP>   0 Jul 24 16:01 freezer
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 hugetlb
dr-xr-xr-x 12 <USER> <GROUP>   0 Jul 24 15:23 memory
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 misc
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 net_cls
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 net_prio
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 perf_event
dr-xr-xr-x 11 <USER> <GROUP>   0 Jul 24 15:23 pids
dr-xr-xr-x  4 <USER> <GROUP>   0 Jul 24 16:01 rdma
dr-xr-xr-x 11 <USER> <GROUP>   0 Jul 24 18:17 systemd
dr-xr-xr-x 18 <USER> <GROUP>   0 Jul 24 14:57 unified
```

## 排查方式

确认目标进程是什么

```bash
ps -p 743 -o pid,ppid,comm,args
cat /proc/743/cgroup
nsenter -t 743 -m -- mount | grep cgroup
```

- `/proc/743/cgroup` 如果只有一行 0::/ → 这是纯 v2 视角；
- 如果空/报错 → 该进程所在的 mount namespace 里根本没挂载 cgroup；
- nsenter 那个命令能看到它自己的 mount namespace 里到底有没有 `/sys/fs/cgroup/*`。

## 挂载

A. Bind mount

```bash
mount --rbind /sys/fs/cgroup /sys/fs/cgroup
mount --make-rshared /sys/fs/cgroup
```

B: mount propagation 不是 rshared

```bash
mount --make-rshared /
mount --make-rshared /sys/fs/cgroup
```

C: 将 CGROUP 做成共享传播

```bash
#!/bin/sh
mountpoint -q /sys/fs/cgroup || mount -t tmpfs -o mode=755 tmpfs /sys/fs/cgroup

for s in cpuset cpu cpuacct blkio memory devices freezer net_cls perf_event net_prio hugetlb pids rdma misc; do
  mkdir -p /sys/fs/cgroup/$s
  mountpoint -q /sys/fs/cgroup/$s || mount -t cgroup -o $s $s /sys/fs/cgroup/$s
done

mkdir -p /sys/fs/cgroup/unified
mountpoint -q /sys/fs/cgroup/unified || mount -t cgroup2 -o nsdelegate cgroup2 /sys/fs/cgroup/unified

# 关键点：让后续 namespace 也看得到
mount --make-rshared /sys/fs/cgroup
```

D: 给 System 一个位置

```bash
mkdir -p /sys/fs/cgroup/systemd
mountpoint -q /sys/fs/cgroup/systemd || \
  mount -t cgroup -o name=systemd cgroup /sys/fs/cgroup/systemd

# 别忘了传播
mount --make-rshared /sys/fs/cgroup
```

## 当前 Docker daemon 配置 【已排除： 非 Docker 配置原因】

```json
{
  "userland-proxy": false,
  "registry-mirrors": [
    "https://ocyev7hi.mirror.aliyuncs.com",
    "https://docker.m.daocloud.io",
    "https://huecker.io",
    "https://dockerhub.timeweb.cloud",
    "https://dockerhub.icu",
    "https://docker.chenby.cn",
    "https://docker.1panel.live",
    "https://docker.awsl9527.cn",
    "https://docker.anyhub.us.kg",
    "https://dhub.kubesre.xyz",
    "https://docker.m.daocloud.io",
    "https://4nf8ppbu.mirror.aliyuncs.com",
    "https://mirror.ccs.tencentyun.com"
  ],
  "insecure-registries": ["http://**********:8443"],
  "default-runtime": "runc-custom",
  "runtimes": {
    "nvidia": {
      "args": [],
      "path": "nvidia-container-runtime"
    },
    "runc-custom": {
      "path": "/opt/docker/runc",
      "runtimeArgs": ["--systemd-cgroup=false"]
    }
  },
  "exec-opts": ["native.cgroupdriver=cgroupfs"],
  "storage-driver": "overlay2",
  "features": {
    "buildkit": true
  },
  "default-cgroupns-mode": "host",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
```
