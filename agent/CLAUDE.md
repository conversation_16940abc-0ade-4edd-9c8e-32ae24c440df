# EchoWave Agent 模块

此文件为 Claude Code 提供在处理 EchoWave Agent 模块代码时的指导。

## 模块概述

EchoWave Agent 是一个 **Linux 专用** 的守护进程管理模块，作为 EchoWave 客户端四模块架构的关键组件，在 WSL/Linux 环境中运行。它负责管理系统级守护进程（Tailscale、Docker、Nomad）并通过标准输入/输出与核心模块通信。

### 架构定位

Agent 模块在整体架构中的职责：

- **进程管理**: 管理 Tailscale、Docker、Nomad 守护进程的生命周期
- **数据收集**: 收集任务引擎运行数据和节点状态
- **通信桥梁**: 通过 stdio + MessageFrame 协议与 Core 模块通信
- **平台限制**: 仅在 Linux/WSL 环境运行（编译时强制检查）

### 关键设计决策

1. **Linux 专用**: 使用 `#[cfg(target_os = "linux")]` 强制平台检查
2. **单实例控制**: 通过 PID 文件防止重复运行
3. **stdio 通信**: 使用标准输入输出作为进程间通信通道
4. **二进制协议**: 基于 MessageFrame 的结构化消息传递

## 项目结构

```
agent/
├── src/
│   ├── main.rs                 # 入口点，处理命令行参数和启动流程
│   ├── server.rs               # 主服务循环和任务管理
│   ├── handler.rs              # 事件处理器，协调各模块
│   ├── pidfile.rs              # PID 文件管理
│   ├── mount.rs                # cgroup 挂载功能
│   ├── communication/          # 通信子系统
│   │   ├── stdio.rs            # stdio 通道实现
│   │   ├── handler.rs          # 消息分发器
│   │   └── request_response.rs # 请求响应管理
│   ├── daemon/                 # 守护进程管理
│   │   ├── launcher.rs         # 进程启动器
│   │   ├── lifecycle.rs        # 生命周期管理
│   │   ├── health.rs           # 健康检查
│   │   └── logging.rs          # 日志收集
│   ├── docker/                 # Docker 特定逻辑
│   ├── nomad/                  # Nomad 特定逻辑
│   ├── tailscale/              # Tailscale 特定逻辑
│   └── logging/                # 日志转发系统
├── debian/etc/                 # 配置文件模板
├── Makefile                    # 构建脚本
└── tests/                      # 集成测试
```

## 开发指南

### 编译要求

- **平台**: Linux 或 WSL（Windows 开发使用 `--features dev-env`）
- **Rust**: 1.88+
- **依赖**: tokio, tracing, protocol crate

### 开发命令

```bash
# Linux/WSL 构建
cargo build --release

# Windows 开发（仅用于 IDE 支持）
cargo check --features dev-env

# 运行测试
cargo test

# 使用 Makefile（WSL）
make build    # 构建并复制到 Windows 路径
make clean    # 清理构建产物
```

### 调试

```bash
# 设置日志级别
RUST_LOG=debug cargo run
RUST_LOG=trace cargo run

# 检查运行状态
ps aux | grep agent
cat /tmp/agent.pid
```

## 通信协议

### AgentEvent 定义

```rust
pub enum AgentEvent {
    Shutdown { trace_id: Uuid, graceful: bool },
    Start { trace_id: Uuid, daemon: String },
    Stop { trace_id: Uuid, daemon: String },
    GetNodeStatus { trace_id: Uuid },
    GetNetworkLatency { trace_id: Uuid },
}
```

### 通信流程

1. Core 通过 stdio 发送 MessageFrame 消息
2. Agent 解析消息并分发到对应处理器
3. 处理器执行操作并返回响应
4. 响应通过 MessageFrame 返回给 Core

### 错误处理

- 使用标准化错误码（AgentErrorCode）
- 错误通过 MessageFrame 的错误响应传递
- 所有错误包含 trace_id 用于调试

## 守护进程管理

### 启动顺序

1. **Tailscale**: 网络层，必须最先启动
2. **Docker**: 容器运行时，依赖网络
3. **Nomad**: 任务调度器，依赖 Docker

### 健康检查

每个守护进程实现两层健康检查：

1. **Socket 检查**: 验证进程是否运行
2. **功能检查**: 验证服务是否正常工作

### 日志管理

- 守护进程日志通过 pipe 收集
- 结构化日志解析（特别是 Docker）
- 日志转发到 Core 模块
- 支持日志级别过滤

## 重要注意事项

### 安全性

1. **硬编码凭据**: 当前版本包含硬编码的密码和 authkey，生产环境需要外部化
2. **权限管理**: 需要 Docker 和系统管理权限
3. **网络安全**: Tailscale 提供加密网络通道

### 性能考虑

1. **异步 I/O**: 所有 I/O 操作使用 tokio 异步
2. **缓冲管理**: stdio 通信使用适当的缓冲策略
3. **资源清理**: 确保守护进程正确关闭

### 错误恢复

1. **进程重启**: 守护进程异常退出时自动重启
2. **超时控制**: 所有操作设置合理超时
3. **优雅关闭**: 支持 SIGTERM 信号处理

## 测试策略

### 单元测试

- 测试各个模块的独立功能
- Mock 外部依赖（如守护进程）

### 集成测试

- 测试完整的启动和关闭流程
- 验证通信协议正确性
- 检查错误处理路径

### 手动测试

1. 启动 Agent 并验证 PID 文件
2. 通过 Core 发送各种命令
3. 检查守护进程状态
4. 测试异常情况恢复

## 未来改进方向

1. **配置外部化**: 将硬编码配置移到配置文件
2. **监控增强**: 添加更多性能指标
3. **安全加固**: 改进凭据管理机制
4. **功能扩展**: 支持更多守护进程类型

## 与其他模块的关系

- **Core 模块**: Agent 的直接调用方，通过 AgentAdapter 管理
- **Protocol 模块**: 提供通信协议实现
- **Desktop 模块**: 间接关系，通过 Core 展示 Agent 状态

## 常见问题

1. **编译失败**: 确保在 Linux 环境或使用 `--features dev-env`
2. **权限错误**: 需要运行用户有 Docker 组权限
3. **通信中断**: 检查 stdio 通道是否正确建立
4. **守护进程启动失败**: 查看详细日志和系统依赖