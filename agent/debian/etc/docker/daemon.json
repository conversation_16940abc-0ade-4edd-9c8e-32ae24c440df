{"userland-proxy": false, "registry-mirrors": ["https://ocyev7hi.mirror.aliyuncs.com", "https://docker.m.daocloud.io", "https://huecker.io", "https://dockerhub.timeweb.cloud", "https://dockerhub.icu", "https://docker.chenby.cn", "https://docker.1panel.live", "https://docker.awsl9527.cn", "https://docker.anyhub.us.kg", "https://dhub.kubesre.xyz", "https://docker.m.daocloud.io", "https://4nf8ppbu.mirror.aliyuncs.com", "https://mirror.ccs.tencentyun.com"], "insecure-registries": ["http://100.64.0.3:8443"], "runtimes": {"nvidia": {"args": [], "path": "nvidia-container-runtime"}}, "exec-opts": ["native.cgroupdriver=cgroupfs"], "storage-driver": "overlay2", "features": {"buildkit": true}, "default-cgroupns-mode": "private", "log-driver": "json-file", "log-opts": {"max-size": "10m", "max-file": "3"}}