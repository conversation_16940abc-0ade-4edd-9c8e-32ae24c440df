use std::time::Instant;
use tokio::time::{sleep, Duration};

pub async fn terminate_gracefully(pid: i32, grace_period: Duration) -> anyhow::Result<()> {
    // 发送 SIGTERM ---
    let result = unsafe { libc::kill(pid as libc::pid_t, libc::SIGTERM) };

    if result == -1 {
        // 获取底层的错误码 (errno)
        let err = std::io::Error::last_os_error();
        // 如果错误是 ESRCH (Error: No such process)，说明进程已经不在了
        if err.raw_os_error() == Some(libc::ESRCH) {
            return Ok(());
        }
        anyhow::bail!("发送 SIGTERM 失败: {}", err);
    }

    // --- 等待宽限期 ---
    let start = Instant::now();
    while start.elapsed() < grace_period {
        // 使用 kill(pid, 0) 检查进程是否存在
        let poll_result = unsafe { libc::kill(pid as libc::pid_t, 0) };

        if poll_result == -1 {
            let poll_err = std::io::Error::last_os_error();
            if poll_err.raw_os_error() == Some(libc::ESRCH) {
                return Ok(());
            }
            // 如果是其他错误（如权限不足），则直接报错
            anyhow::bail!("轮询检查进程状态时出错: {}", poll_err);
        }
        // 如果 kill(pid, 0) 成功，说明进程仍在，继续等待
        sleep(Duration::from_millis(100)).await;
    }

    // 发送 SIGKILL ---
    let kill_result = unsafe { libc::kill(pid as libc::pid_t, libc::SIGKILL) };

    if kill_result == -1 {
        let kill_err = std::io::Error::last_os_error();
        // 在我们准备发送 SIGKILL 时，它可能自己退出了
        if kill_err.raw_os_error() == Some(libc::ESRCH) {
            return Ok(());
        }
        anyhow::bail!("发送 SIGKILL 失败: {}", kill_err);
    }
    Ok(())
}
