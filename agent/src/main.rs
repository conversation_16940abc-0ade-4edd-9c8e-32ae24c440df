#[cfg(any(target_os = "linux", feature = "dev-env"))]
use tokio_util::sync::CancellationToken;

#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod communication;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod daemon;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod docker;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod handler;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod kill;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod logging;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod mount;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod nomad;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod pidfile;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod server;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod tailscale;
#[cfg(any(target_os = "linux", feature = "dev-env"))]
mod transfer;

#[tokio::main]
async fn main() {
    #[cfg(any(target_os = "linux", feature = "dev-env"))]
    {
        // 在父进程死亡时，期望自身进行退出而不是被 /init 收养

        use crate::mount::mount;
        unsafe {
            libc::prctl(libc::PR_SET_PDEATHSIG, libc::SIGKILL);
        }

        let args = std::env::args().collect::<Vec<_>>();
        let is_join = args.iter().any(|it| it == "--join");
        let is_version = args.iter().any(|it| it == "--version");
        let is_distribution_info = args.iter().any(|it| it == "--distribution-info");
        if is_version {
            print_version();
            return;
        }
        if is_distribution_info {
            print_distribution_info();
            return;
        }
        if let Some(pid) = pidfile::Pidfile::instance_pid() {
            if is_join {
                println!("Instance already exists, terminating...");
                if let Err(err) =
                    kill::terminate_gracefully(pid, std::time::Duration::from_secs(5)).await
                {
                    eprintln!("Failed to terminate instance: {}", err);
                    std::process::exit(1);
                };
                println!("Instance terminated, starting new instance...");
            } else {
                eprintln!("Failed to start agent, instance already exists");
                std::process::exit(1);
            }
        }

        // 根据 join 参数选择日志输出方式
        if is_join {
            // join 模式：使用带缓冲的 stdio 日志
            logging::registry_with_stdio_buffering();
        } else {
            // 普通模式：使用标准输出日志
            logging::registry();
            tracing::info!("EchoWave TaskEngine Agent {}", env!("CARGO_PKG_VERSION"));
        }

        let _pidfile = pidfile::Pidfile::new();
        if let Err(e) = mount() {
            tracing::error!("Failed to mount cgroup systemd: {}", e);
            std::process::exit(1);
        }
        let shutdown_signal = CancellationToken::new();

        // 设置panic hook进行错误处理
        std::panic::set_hook(Box::new(|panic_info| {
            tracing::error!("Agent发生panic: {}", panic_info);
            std::process::exit(1);
        }));

        let result = run_agent(shutdown_signal.clone()).await;

        // 确保资源清理
        tracing::info!("开始清理资源");
        cleanup_resources().await;

        match result {
            Ok(()) => {
                tracing::info!("Agent正常退出");
                std::process::exit(0);
            }
            Err(e) => {
                tracing::error!("Agent异常退出: {}", e);
                std::process::exit(1);
            }
        }
    }

    /// 运行Agent主逻辑
    #[cfg(any(target_os = "linux", feature = "dev-env"))]
    async fn run_agent(shutdown_signal: CancellationToken) -> anyhow::Result<()> {
        tracing::info!("开始启动服务器");
        let server = server::Server::launch(shutdown_signal.clone());

        tracing::info!("开始启动守护进程");
        if let Err(err) = daemon::launch_daemons(shutdown_signal.clone()).await {
            tracing::error!("启动守护进程失败，原因: {:?}", err);
            tracing::error!("开始关闭已启动的进程并退出程序");
            shutdown_signal.cancel();
            return Err(anyhow::anyhow!("守护进程启动失败: {}", err));
        };

        tracing::info!("等待服务器结束");
        server.run_until_done().await;

        tracing::info!("等待所有守护进程退出");
        daemon::wait_shutdown().await;

        Ok(())
    }

    /// 清理资源
    #[cfg(any(target_os = "linux", feature = "dev-env"))]
    async fn cleanup_resources() {
        tracing::info!("执行资源清理");

        // 清理临时文件
        if let Err(e) = cleanup_temp_files().await {
            tracing::warn!("清理临时文件失败: {}", e);
        }

        // 确保所有守护进程都已停止
        if let Err(e) = ensure_daemons_stopped().await {
            tracing::warn!("确保守护进程停止失败: {}", e);
        }

        tracing::info!("资源清理完成");
    }

    /// 清理临时文件
    #[cfg(any(target_os = "linux", feature = "dev-env"))]
    async fn cleanup_temp_files() -> anyhow::Result<()> {
        // 这里可以添加清理临时文件的逻辑
        // 例如清理日志文件、配置文件等
        Ok(())
    }

    /// 确保所有守护进程都已停止
    #[cfg(any(target_os = "linux", feature = "dev-env"))]
    async fn ensure_daemons_stopped() -> anyhow::Result<()> {
        // 这里可以添加确保守护进程停止的逻辑
        // 例如发送SIGTERM信号等
        Ok(())
    }

    #[cfg(not(target_os = "linux"))]
    {
        println!("EchoWave TaskEngine Agent is only supported on Linux");
        println!("Current platform: {}", std::env::consts::OS);
    }
}

fn print_distribution_info() {
    match std::fs::read_to_string("/etc/distribution_info") {
        Ok(content) => {
            println!("{}", content);
        }
        Err(e) => {
            eprintln!("Failed to read distribution info: {}", e);
        }
    }
}

fn print_version() {
    println!("{}", env!("CARGO_PKG_VERSION"));
}
