use super::request_response::{RequestError, RequestResponseManager};
use protocol::events::{AgentEvent, AgentResponse};
use protocol::frame::Message<PERSON>rame;
use std::sync::Arc;
use thiserror::Error;
use uuid::Uuid;

/// 简化的事件处理器，使用统一的 AgentHandler
pub struct AgentEventHandler {
    handler: crate::handler::AgentHandler,
}

impl AgentEventHandler {
    pub fn new(message_sender: Arc<super::stdio::MessageSender>) -> Self {
        let mut handler = crate::handler::AgentHandler::new();
        handler.set_message_sender(message_sender);
        Self { handler }
    }

    /// 获取 AgentHandler 的引用，用于 chunk 处理
    pub fn get_handler(&self) -> &crate::handler::AgentHandler {
        &self.handler
    }

    pub async fn handle(&self, event: AgentEvent) -> Result<AgentResponse, HandlerError> {
        // 委托给统一的处理器
        self.handler.handle(event).await.map_err(|e| match e {
            crate::handler::HandlerError::UnsupportedEvent(name) => {
                HandlerError::UnsupportedEvent(name)
            }
            crate::handler::HandlerError::Internal(msg) => HandlerError::Internal(msg),
        })
    }
}

/// 消息分发器
pub struct EventDispatcher {
    handler: Arc<AgentEventHandler>,
    request_manager: Arc<RequestResponseManager>,
}

impl EventDispatcher {
    pub fn new(request_manager: Arc<RequestResponseManager>) -> Self {
        let message_sender = request_manager.get_message_sender();
        Self {
            handler: Arc::new(AgentEventHandler::new(message_sender)),
            request_manager,
        }
    }

    /// 获取 AgentHandler 的引用，用于 chunk 处理
    pub fn get_agent_handler(&self) -> &crate::handler::AgentHandler {
        self.handler.get_handler()
    }

    /// 分发事件到对应处理器
    pub async fn dispatch(&self, frame: MessageFrame) -> Result<(), DispatchError> {
        if frame.is_response {
            // 处理响应
            tracing::debug!(operation_id = %frame.operation_id, "接收到响应，正在处理");
            let response: AgentResponse = serde_json::from_slice(&frame.bytes).map_err(|e| {
                tracing::error!(operation_id = %frame.operation_id, "响应反序列化失败: {}", e);
                DispatchError::Deserialization(e.to_string())
            })?;
            self.request_manager
                .handle_response(frame.operation_id, response)
                .await;
            return Ok(());
        }

        // 解析事件
        let event: AgentEvent = serde_json::from_slice(&frame.bytes).map_err(|e| {
            tracing::error!(operation_id = %frame.operation_id, "事件反序列化失败: {}", e);
            DispatchError::Deserialization(e.to_string())
        })?;

        // 提取trace_id用于错误响应
        let trace_id = self.extract_trace_id(&event);
        let event_name = event.event_name();

        let span = tracing::info_span!("event_dispatch", trace_id = %trace_id, operation_id = %frame.operation_id, event = %event_name);
        let _guard = span.enter();

        tracing::info!("接收到事件 {}，开始处理", event_name);

        // 处理事件
        match self.handler.handle(event).await {
            Ok(response) => {
                tracing::debug!("事件处理成功，发送响应");
                // 发送响应
                if let Err(err) = self
                    .request_manager
                    .send_response(frame.operation_id, response)
                    .await
                {
                    tracing::error!("发送成功响应失败: {}", err);
                    return Err(DispatchError::ResponseSendFailed(err.to_string()));
                }
            }
            Err(err) => {
                tracing::warn!("事件处理失败: {}", err);
                // 发送错误响应
                let error_response = AgentResponse::Error {
                    trace_id,
                    code: err.code(),
                    message: err.to_string(),
                };
                if let Err(send_err) = self
                    .request_manager
                    .send_response(frame.operation_id, error_response)
                    .await
                {
                    tracing::error!("发送错误响应失败: {}", send_err);
                    return Err(DispatchError::ResponseSendFailed(send_err.to_string()));
                }
            }
        }

        Ok(())
    }

    fn extract_trace_id(&self, event: &AgentEvent) -> Uuid {
        match event {
            AgentEvent::Shutdown { trace_id } => *trace_id,
            AgentEvent::Stop { trace_id } => *trace_id,
            AgentEvent::Start { trace_id, .. } => *trace_id,
            AgentEvent::WaitForTailscaleReady { trace_id, .. } => *trace_id,
            AgentEvent::GetNodeStatus { trace_id } => *trace_id,
            AgentEvent::GetNetworkLatency { trace_id } => *trace_id,
            AgentEvent::GetHealthStatus { trace_id } => *trace_id,
            AgentEvent::LogForward(_) => Uuid::from_u64_pair(0, 0),
            AgentEvent::LogBatchForward(_) => Uuid::from_u64_pair(0, 0),
            AgentEvent::StartSelfUpdate { trace_id, .. } => *trace_id,
            AgentEvent::SelfUpdateReady { trace_id, .. } => *trace_id,
            AgentEvent::Transfer(transfer_event) => transfer_event.trace_id(),
        }
    }
}

#[derive(Debug, Error)]
pub enum HandlerError {
    #[error("不支持的事件: {0}")]
    UnsupportedEvent(&'static str),
    #[error("内部错误: {0}")]
    Internal(String),
}

impl HandlerError {
    pub fn code(&self) -> u32 {
        match self {
            HandlerError::UnsupportedEvent(_) => 1001,
            HandlerError::Internal(_) => 1000,
        }
    }
}

#[derive(Debug, Error)]
pub enum DispatchError {
    #[error("反序列化失败: {0}")]
    Deserialization(String),
    #[error("响应发送失败: {0}")]
    ResponseSendFailed(String),
    #[error("请求处理失败: {0}")]
    RequestFailed(#[from] RequestError),
}
