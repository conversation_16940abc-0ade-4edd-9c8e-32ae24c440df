use super::stdio::{CommunicationError, MessageSender};
use protocol::events::AgentResponse;
use std::collections::HashMap;
use std::sync::atomic::{AtomicU32, Ordering};
use std::sync::Arc;
use tokio::sync::{oneshot, Mutex};
use tokio::time::{timeout, Duration};

/// 请求/响应管理器
pub struct RequestResponseManager {
    next_operation_id: AtomicU32,
    pending_requests: Arc<Mutex<HashMap<u32, oneshot::Sender<AgentResponse>>>>,
    message_sender: Arc<MessageSender>,
}

impl RequestResponseManager {
    pub fn new(message_sender: Arc<MessageSender>) -> Self {
        Self {
            next_operation_id: AtomicU32::new(1),
            pending_requests: Arc::new(Mutex::new(HashMap::new())),
            message_sender,
        }
    }

    /// 发送请求并等待响应 (类似 HTTP 请求)
    pub async fn send_request<T: serde::Serialize>(
        &self,
        event: T,
        timeout_duration: Duration,
    ) -> Result<AgentResponse, RequestError> {
        let operation_id = self.next_operation_id.fetch_add(1, Ordering::Relaxed);
        let (tx, rx) = oneshot::channel();

        // 注册等待响应
        {
            let mut pending = self.pending_requests.lock().await;
            pending.insert(operation_id, tx);
        }

        // 发送请求
        self.message_sender
            .send_event(operation_id, false, &event)
            .await
            .map_err(RequestError::Communication)?;

        // 等待响应 (带超时)
        let response = timeout(timeout_duration, rx)
            .await
            .map_err(|_| RequestError::Timeout)?
            .map_err(|_| RequestError::Cancelled)?;

        Ok(response)
    }

    /// 发送响应
    pub async fn send_response(
        &self,
        operation_id: u32,
        response: AgentResponse,
    ) -> Result<(), RequestError> {
        self.message_sender
            .send_response(operation_id, &response)
            .await
            .map_err(RequestError::Communication)?;
        Ok(())
    }

    /// 处理接收到的响应
    pub async fn handle_response(&self, operation_id: u32, response: AgentResponse) {
        let mut pending = self.pending_requests.lock().await;
        if let Some(tx) = pending.remove(&operation_id) {
            let _ = tx.send(response); // 忽略接收方已关闭的错误
        } else {
            tracing::warn!("收到未匹配的响应: operation_id={}", operation_id);
        }
    }

    /// 发送事件但不等待响应 (fire-and-forget)
    pub async fn send_event_no_response<T: serde::Serialize>(
        &self,
        event: T,
    ) -> Result<(), RequestError> {
        let operation_id = self.next_operation_id.fetch_add(1, Ordering::Relaxed);
        self.message_sender
            .send_event(operation_id, false, &event)
            .await
            .map_err(RequestError::Communication)?;
        Ok(())
    }

    /// 清理超时的待处理请求
    pub async fn cleanup_expired_requests(&self) {
        let mut pending = self.pending_requests.lock().await;
        let expired_count = pending.len();

        // 简单清理：移除所有待处理请求（在实际应用中应该基于时间戳清理）
        pending.clear();

        if expired_count > 0 {
            tracing::warn!("清理了 {} 个过期的待处理请求", expired_count);
        }
    }

    /// 获取当前待处理请求数量
    pub async fn get_pending_count(&self) -> usize {
        let pending = self.pending_requests.lock().await;
        pending.len()
    }

    /// 获取消息发送器的引用
    pub fn get_message_sender(&self) -> Arc<MessageSender> {
        self.message_sender.clone()
    }
}

#[derive(Debug, thiserror::Error)]
pub enum RequestError {
    #[error("请求超时")]
    Timeout,
    #[error("请求被取消")]
    Cancelled,
    #[error("通信错误: {0}")]
    Communication(#[from] CommunicationError),
}

/// 请求上下文，用于处理器中获取请求信息
#[derive(Debug, Clone)]
pub struct RequestContext {
    pub operation_id: u32,
    pub timestamp: u64,
}

impl RequestContext {
    pub fn new(operation_id: u32) -> Self {
        Self {
            operation_id,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::communication::stdio::MessageSender;
    use protocol::events::AgentEvent;
    use uuid::Uuid;

    #[tokio::test]
    async fn test_request_response_flow() {
        let (message_sender, mut _receiver) = MessageSender::new();
        let message_sender = Arc::new(message_sender);
        let request_manager = RequestResponseManager::new(message_sender);

        // 模拟响应处理
        let request_manager_clone = Arc::new(request_manager);
        let manager_for_response = request_manager_clone.clone();

        // 启动一个任务来模拟接收响应
        tokio::spawn(async move {
            tokio::time::sleep(Duration::from_millis(100)).await;
            let trace_id = Uuid::new_v4();
            let response = AgentResponse::Success { trace_id };
            manager_for_response.handle_response(1, response).await;
        });

        // 发送请求
        let trace_id = Uuid::new_v4();
        let shutdown_event = AgentEvent::Shutdown {
            trace_id: Uuid::new_v4(),
        };
        let result = request_manager_clone
            .send_request(shutdown_event, Duration::from_secs(1))
            .await;

        assert!(result.is_ok());
        match result.unwrap() {
            AgentResponse::Success { .. } => (),
            _ => panic!("期望收到成功响应"),
        }
    }

    #[tokio::test]
    async fn test_request_timeout() {
        let (message_sender, mut _receiver) = MessageSender::new();
        let message_sender = Arc::new(message_sender);
        let request_manager = RequestResponseManager::new(message_sender);

        // 发送请求但不响应，应该超时
        let trace_id = Uuid::new_v4();
        let shutdown_event = AgentEvent::Shutdown {
            trace_id: Uuid::new_v4(),
        };
        let result = request_manager
            .send_request(shutdown_event, Duration::from_millis(100))
            .await;

        assert!(result.is_err());
        match result.unwrap_err() {
            RequestError::Timeout => (),
            _ => panic!("期望超时错误"),
        }
    }

    #[tokio::test]
    async fn test_pending_count() {
        let (message_sender, mut _receiver) = MessageSender::new();
        let message_sender = Arc::new(message_sender);
        let request_manager = Arc::new(RequestResponseManager::new(message_sender));

        assert_eq!(request_manager.get_pending_count().await, 0);

        // 发送请求但不响应
        let trace_id = Uuid::new_v4();
        let shutdown_event = AgentEvent::Shutdown {
            trace_id: Uuid::new_v4(),
        };
        let request_manager_clone = request_manager.clone();

        // 启动异步任务来发送请求
        let _request_task = tokio::spawn(async move {
            let _result = request_manager_clone
                .send_request(shutdown_event, Duration::from_secs(10))
                .await;
        });

        // 给一点时间让请求被注册
        tokio::time::sleep(Duration::from_millis(50)).await;
        assert_eq!(request_manager.get_pending_count().await, 1);

        // 清理过期请求
        request_manager.cleanup_expired_requests().await;
        assert_eq!(request_manager.get_pending_count().await, 0);
    }
}
