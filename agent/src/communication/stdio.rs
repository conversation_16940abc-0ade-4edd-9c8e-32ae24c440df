use super::handler::EventDispatcher;
use super::request_response::RequestResponseManager;
use protocol::events::AgentResponse;
use protocol::frame::{read_message_frame, send_message_frame, EventCode, MessageFrame};
use std::sync::Arc;
use tokio::io::{self, stdin, stdout, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Stdout};
use tokio::sync::mpsc;
use tokio_util::sync::CancellationToken;

/// MessageFrame 发送器封装
pub struct MessageSender {
    sender: mpsc::UnboundedSender<MessageFrame>,
}

/// MessageFrame 接收器封装  
pub struct MessageReceiver {
    receiver: mpsc::UnboundedReceiver<MessageFrame>,
}

impl MessageSender {
    pub fn new() -> (Self, MessageReceiver) {
        let (tx, rx) = mpsc::unbounded_channel();
        (Self { sender: tx }, MessageReceiver { receiver: rx })
    }

    /// 发送事件消息
    pub async fn send_event<T: serde::Serialize>(
        &self,
        operation_id: u32,
        is_response: bool,
        event: &T,
    ) -> Result<(), CommunicationError> {
        let payload = serde_json::to_vec(event)
            .map_err(|e| CommunicationError::Serialization(e.to_string()))?;

        let frame = MessageFrame {
            event_code: EventCode::Data,
            operation_id,
            is_response,
            bytes: payload,
        };

        self.sender
            .send(frame)
            .map_err(|_| CommunicationError::ChannelClosed)?;

        Ok(())
    }

    /// 发送响应消息
    pub async fn send_response(
        &self,
        operation_id: u32,
        response: &AgentResponse,
    ) -> Result<(), CommunicationError> {
        let payload = serde_json::to_vec(response)
            .map_err(|e| CommunicationError::Serialization(e.to_string()))?;

        let frame = MessageFrame {
            event_code: EventCode::Data,
            operation_id,
            is_response: true,
            bytes: payload,
        };

        self.sender
            .send(frame)
            .map_err(|_| CommunicationError::ChannelClosed)?;

        Ok(())
    }

    /// 发送控制信号
    pub async fn send_signal(&self, event_code: EventCode) -> Result<(), CommunicationError> {
        let frame = MessageFrame {
            event_code,
            operation_id: 0,
            is_response: false,
            bytes: Vec::new(),
        };

        self.sender
            .send(frame)
            .map_err(|_| CommunicationError::ChannelClosed)?;

        Ok(())
    }
}

impl MessageReceiver {
    /// 接收下一个消息帧
    pub async fn recv(&mut self) -> Option<MessageFrame> {
        self.receiver.recv().await
    }
}

/// Stdio 通信处理器
pub struct StdioHandler {
    stdin: BufReader<Stdin>,
    stdout: Stdout,
    message_sender: Arc<MessageSender>,
    message_receiver: MessageReceiver,
    shutdown_signal: CancellationToken,
    dispatcher: Arc<EventDispatcher>,
    // 添加分发任务计数器，限制并发数量，防止无限制任务堆积
    dispatch_semaphore: Arc<tokio::sync::Semaphore>,
}

impl StdioHandler {
    pub fn new(shutdown_signal: CancellationToken) -> Self {
        let (message_sender, message_receiver) = MessageSender::new();
        let message_sender = Arc::new(message_sender);

        // 创建 Request/Response 管理器
        let request_manager = Arc::new(RequestResponseManager::new(message_sender.clone()));

        // 创建事件分发器
        let dispatcher = Arc::new(EventDispatcher::new(request_manager));

        // 创建信号量限制并发分发任务数量（最多16个并发任务）
        let dispatch_semaphore = Arc::new(tokio::sync::Semaphore::new(16));

        Self {
            stdin: BufReader::new(stdin()),
            stdout: stdout(),
            message_sender,
            message_receiver,
            shutdown_signal,
            dispatcher,
            dispatch_semaphore,
        }
    }

    /// 获取消息发送器的引用（用于其他组件发送消息）
    pub fn get_sender(&self) -> Arc<MessageSender> {
        self.message_sender.clone()
    }

    /// 运行 stdio 通信循环
    pub async fn run(mut self) -> Result<(), CommunicationError> {
        tracing::info!("启动 stdio 通信处理器");

        // 先克隆所有需要的共享状态
        let shutdown_signal_for_sender = self.shutdown_signal.clone();
        let shutdown_signal_for_receiver = self.shutdown_signal.clone();
        let dispatch_semaphore = self.dispatch_semaphore.clone();
        let dispatcher = self.dispatcher.clone();

        let sender = {
            tokio::spawn(async move {
                loop {
                    tokio::select! {
                        // 处理来自 stdin 的消息
                        frame_result = read_message_frame(&mut self.stdin) => {
                            match frame_result {
                                Ok(Some(frame)) => {
                                    tracing::debug!("接收到消息帧: operation_id={}, is_response={}, event_code={:?}",
                                        frame.operation_id, frame.is_response, frame.event_code);

                                    // 根据 EventCode 决定如何处理
                                    match frame.event_code {
                                        EventCode::Data => {
                                            // 原有的 JSON 事件处理逻辑
                                            let semaphore = dispatch_semaphore.clone();
                                            let cancellation_token = shutdown_signal_for_sender.clone();
                                            let dispatcher = dispatcher.clone();
                                            tokio::spawn(async move {
                                                let permit = tokio::select! {
                                                    permit = semaphore.acquire() => match permit {
                                                        Ok(p) => p,
                                                        Err(_) => return,
                                                    },
                                                    _ = cancellation_token.cancelled() => return,
                                                };

                                                tokio::select! {
                                                    result = dispatcher.dispatch(frame) => {
                                                        if let Err(e) = result {
                                                            tracing::error!("分发事件失败: {}", e);
                                                        }
                                                    },
                                                    _ = cancellation_token.cancelled() => {}
                                                }
                                                drop(permit);
                                            });
                                        }
                                        EventCode::Binary => {
                                            // 新增的二进制块处理逻辑
                                            let semaphore = dispatch_semaphore.clone();
                                            let cancellation_token = shutdown_signal_for_sender.clone();
                                            let dispatcher = dispatcher.clone();
                                            tokio::spawn(async move {
                                                let permit = tokio::select! {
                                                    permit = semaphore.acquire() => match permit {
                                                        Ok(p) => p,
                                                        Err(_) => return,
                                                    },
                                                     _ = cancellation_token.cancelled() => return,
                                                };

                                                // 处理二进制数据块
                                                match protocol::events::transfer::ChunkData::from_bytes(&frame.bytes) {
                                                    Ok(chunk_data) => {
                                                        tracing::debug!(
                                                            transfer_id = chunk_data.transfer_id,
                                                            chunk_id = chunk_data.chunk_id,
                                                            data_size = chunk_data.data.len(),
                                                            "接收到二进制数据块"
                                                        );
                                                        
                                                        // 提取用于错误日志的信息
                                                        let transfer_id = chunk_data.transfer_id;
                                                        let chunk_id = chunk_data.chunk_id;
                                                        
                                                        // 调用 AgentHandler 处理二进制数据块
                                                        let handler = dispatcher.get_agent_handler();
                                                        if let Err(e) = handler.handle_chunk(chunk_data).await {
                                                            tracing::error!(
                                                                transfer_id = transfer_id,
                                                                chunk_id = chunk_id,
                                                                error = %e,
                                                                "处理二进制数据块失败"
                                                            );
                                                        }
                                                    }
                                                    Err(e) => {
                                                        tracing::error!(
                                                            error = %e,
                                                            "解析二进制数据块失败"
                                                        );
                                                    }
                                                }

                                                drop(permit);
                                            });
                                        }
                                        _ => {
                                            tracing::warn!("接收到未处理的 EventCode: {:?}", frame.event_code);
                                        }
                                    }
                                }
                                Ok(None) => {
                                    tracing::info!("stdin 已关闭，退出通信循环");
                                    anyhow::bail!("stdin 已关闭，退出通信循环");
                                }
                                Err(e) => {
                                    if e.kind() == io::ErrorKind::UnexpectedEof {
                                        tracing::info!("接收到错误的 EOF，退出通信循环");
                                        anyhow::bail!("接收到错误的 EOF，退出通信循环");
                                    } else {
                                        tracing::error!("读取 stdin 失败: {}", e);
                                        anyhow::bail!("读取 stdin 失败: {}", e);
                                    }
                                }
                            }
                        }
                        _ = shutdown_signal_for_sender.cancelled() => {
                            tracing::info!("接收到关闭信号，退出 stdio 通信循环");
                            break;
                        }
                    }
                }
                Ok(()) as anyhow::Result<()>
            })
        };

        let receiver = {
            tokio::spawn(async move {
                loop {
                    tokio::select! {
                        // 处理需要发送到 stdout 的消息
                        frame = self.message_receiver.recv() => {
                            match frame {
                                Some(frame) => {
                                    // tracing::debug!("发送消息帧: operation_id={}, is_response={}, event_code={:?}",
                                        // frame.operation_id, frame.is_response, frame.event_code);

                                    if let Err(e) = send_message_frame(
                                        &mut self.stdout,
                                        frame.event_code,
                                        frame.operation_id,
                                        frame.is_response,
                                        &frame.bytes,
                                    ).await {
                                        tracing::error!("发送消息帧失败: {}", e);
                                        anyhow::bail!("发送消息帧失败: {}", e);
                                    }
                                }
                                None => {
                                    tracing::info!("消息发送通道已关闭");
                                    anyhow::bail!("消息发送通道已关闭");
                                }
                            }
                        }
                        _ = shutdown_signal_for_receiver.cancelled() => {
                            tracing::info!("接收到关闭信号，退出 stdio 通信循环");
                            break;
                        }
                    }
                }
                Ok(()) as anyhow::Result<()>
            })
        };
        tokio::select! {
            a = sender => {
                match a {
                    Ok(Ok(_)) => {}
                    Ok(Err(e)) => {
                        tracing::error!("stdio 通信发送处理器异常: {:?}", e);
                    }
                    Err(e) => {
                        tracing::error!("stdio 通信发送处理器 Join 异常: {:?}", e);
                    }
                }
            }
            b = receiver => {
                match b {
                    Ok(Ok(_)) => {}
                    Ok(Err(e)) => {
                        tracing::error!("stdio 通信接收处理器异常: {:?}", e);
                    }
                    Err(e) => {
                        tracing::error!("stdio 通信接收处理器 Join 异常: {:?}", e);
                    }
                }
            }
        }

        self.shutdown_signal.cancel();

        tracing::info!("stdio 通信处理器已完全停止");
        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum CommunicationError {
    #[error("序列化错误: {0}")]
    Serialization(String),
    #[error("IO错误: {0}")]
    IoError(String),
    #[error("通信通道已关闭")]
    ChannelClosed,
    #[error("消息格式错误: {0}")]
    InvalidMessage(String),
}

#[cfg(test)]
mod tests {
    use super::*;
    use protocol::events::AgentEvent;

    #[tokio::test]
    async fn test_message_sender_receiver() {
        let (sender, mut receiver) = MessageSender::new();

        let trace_id = uuid::Uuid::new_v4();
        let event = AgentEvent::Shutdown { trace_id };
        sender.send_event(100, false, &event).await.unwrap();

        let frame = receiver.recv().await.unwrap();
        assert_eq!(frame.operation_id, 100);
        assert_eq!(frame.is_response, false);
        assert_eq!(frame.event_code, EventCode::Data);

        let received_event: AgentEvent = serde_json::from_slice(&frame.bytes).unwrap();
        match received_event {
            AgentEvent::Shutdown {
                trace_id: received_trace_id,
            } => {
                assert_eq!(received_trace_id, trace_id);
            }
            _ => panic!("期望收到关闭事件"),
        }
    }

    #[test]
    fn test_shutdown_event() {
        let shutdown = AgentEvent::Shutdown {
            trace_id: uuid::Uuid::new_v4(),
        };
        let frame = MessageFrame {
            event_code: EventCode::Data,
            operation_id: 123,
            is_response: false,
            bytes: serde_json::to_vec(&shutdown).unwrap(),
        };

        let deserialized: AgentEvent = serde_json::from_slice(&frame.bytes).unwrap();

        if let AgentEvent::Shutdown { trace_id } = deserialized {
            assert!(trace_id.to_string().len() > 0);
        } else {
            panic!("应该是 Shutdown 事件");
        }
    }
}
