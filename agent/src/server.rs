use crate::communication::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio::{signal, task::JoinSet};
use tokio_util::sync::CancellationToken;

pub struct Server {
    tasks: JoinSet<anyhow::Result<()>>,
}

impl Server {
    pub fn launch(shutdown_signal: CancellationToken) -> Self {
        let mut tasks = JoinSet::new();

        // 监听来自父进程的消息 (stdio 通信)
        'stdio: {
            if shutdown_signal.is_cancelled() {
                break 'stdio;
            }
            let shutdown_signal_clone = shutdown_signal.clone();
            tasks.spawn(async move {
                let stdio_handler = StdioHandler::new(shutdown_signal_clone.clone());
                let _guard = crate::logging::set_stdio_sender(stdio_handler.get_sender()).await;
                let res = match stdio_handler.run().await {
                    Ok(()) => {
                        tracing::info!("stdio 通信处理器正常退出");
                        Ok(())
                    }
                    Err(e) => {
                        tracing::error!("stdio 通信处理器异常: {}", e);
                        Err(anyhow::anyhow!("stdio 通信异常: {}", e))
                    }
                };
                if !shutdown_signal_clone.is_cancelled() {
                    tracing::info!("发送取消信号");
                    shutdown_signal_clone.cancel();
                }
                res
            });
        }
        // ctrl + c
        'ctrlc: {
            if shutdown_signal.is_cancelled() {
                break 'ctrlc;
            }
            let shutdown_signal = shutdown_signal.clone();
            tasks.spawn(async move {
                tokio::select! {
                    _ = signal::ctrl_c() => {
                        shutdown_signal.cancel();
                    },
                    _ = shutdown_signal.cancelled() => {

                    }
                }
                Ok(())
            });
        }
        // unix signal
        'signal: {
            if shutdown_signal.is_cancelled() {
                break 'signal;
            }
            let shutdown_signal = shutdown_signal.clone();
            tasks.spawn(async move {
                let mut usr1 = signal::unix::signal(signal::unix::SignalKind::user_defined1())?;
                let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())?;
                loop {
                    tokio::select! {
                        _ = sigterm.recv() => {
                            tracing::debug!("Received SIGTERM signal, start terminating");
                            shutdown_signal.cancel();
                            break
                        }
                        _ = usr1.recv() => {
                            tracing::debug!("Received USR1 signal, start reopening log files");
                        },
                        _ = shutdown_signal.cancelled() => {
                            break
                        }
                    }
                }
                Ok(())
            });
        }

        Self { tasks }
    }

    pub async fn run_until_done(mut self) {
        while let Some(res) = self.tasks.join_next().await {
            match res {
                Ok(Ok(())) => (),
                Ok(Err(err)) => {
                    tracing::error!("任务进程发生错误，原因: {err:?}")
                }
                Err(err) => {
                    tracing::error!("任务未能正常执行, 原因: {err:?}")
                }
            }
        }
    }
}
