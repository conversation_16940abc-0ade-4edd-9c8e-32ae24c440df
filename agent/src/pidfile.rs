use anyhow::Context;
use std::io::Write;
use std::path::Path;
use std::{fs, process};

static PID_FILE: &str = "/var/run/agent.pid";

pub struct Pidfile {}

impl Pidfile {
    pub fn new() -> anyhow::Result<Self> {
        {
            let path = Path::new(PID_FILE);
            let mut file = fs::OpenOptions::new()
                .create(true)
                .truncate(true)
                .write(true)
                .open(path)
                .with_context(|| format!("Failed to create pidfile '{path:?}'"))?;
            file.write_all(format!("{}", process::id()).as_bytes())?;
        }
        Ok(Self {})
    }
    pub fn instance_pid() -> Option<i32> {
        if !Path::new(PID_FILE).exists() {
            return None;
        }
        let pid = if let Ok(pid) = fs::read_to_string(PID_FILE) {
            pid
        } else {
            return None;
        };
        if !Path::new(&format!("/proc/{pid}")).exists() {
            return None;
        }
        Some(pid.parse::<i32>().ok()?)
    }
}
impl Drop for Pidfile {
    fn drop(&mut self) {
        let path = Path::new(PID_FILE);
        if let Err(err) = fs::remove_file(path) {
            eprintln!("Failed to remove pidfile '{path:?}', reason: {err}")
        }
    }
}
