//! 尝试挂载 cgroup systemd 目录，让 docker 不出现找不到 cgroup 的错误
//!
//! 错误细节示例
//! ```
//! ERROR ... msg="loading cgroup for 743" error="cgroups: cannot find cgroup mount destination"
//! ```
use std::ffi::CString;
use std::fs;
use std::io;
use std::os::unix::ffi::OsStrExt;
use std::path::Path;

use tracing::error;
use tracing::info;

pub fn mount() -> io::Result<()> {
    let mount_path = "/sys/fs/cgroup/systemd";

    // 对应 shell: mkdir -p /sys/fs/cgroup/systemd
    ensure_dir_exists(mount_path)?;

    // 对应 shell: mountpoint -q /sys/fs/cgroup/systemd || mount ...
    let is_mounted = is_mount_point(mount_path)?;

    if !is_mounted {
        info!(
            "Path '{}' is not a mount point. Attempting to mount...",
            mount_path
        );
        mount_cgroup_systemd(mount_path).map_err(|e| {
            error!("Failed to mount cgroup systemd: {}", e);
            e
        })?;
        info!("Mount successful.");
    } else {
        info!(
            "Path '{}' is already a mount point. Nothing to do.",
            mount_path
        );
    }

    Ok(())
}

/// 确保目录存在，等同于 `mkdir -p`。
/// 使用标准库的 `create_dir_all` 是最安全和最符合 Rust 习惯的方式。
fn ensure_dir_exists(path: &str) -> io::Result<()> {
    info!("Ensuring directory '{}' exists...", path);
    fs::create_dir_all(path)
}

/// 检查一个路径是否是挂载点，等同于 `mountpoint` 命令。
/// 原理：如果一个目录的设备ID (st_dev) 与其父目录的设备ID不同，
/// 那么它就是一个挂载点。
fn is_mount_point(path_str: &str) -> io::Result<bool> {
    let path = Path::new(path_str);

    // 获取当前路径的元数据
    let child_stat = get_stat(path)?;

    // 获取父路径 ".." 的元数据
    let mut parent_path_buf = path.as_os_str().as_bytes().to_vec();
    parent_path_buf.extend_from_slice(b"/..");
    let parent_path = Path::new(std::ffi::OsStr::from_bytes(&parent_path_buf));
    let parent_stat = get_stat(parent_path)?;

    // 如果设备ID不同，则它是一个挂载点
    Ok(child_stat.st_dev != parent_stat.st_dev)
}

/// 辅助函数：使用 libc::stat 获取文件元数据
fn get_stat(path: &Path) -> io::Result<libc::stat> {
    let c_path = CString::new(path.as_os_str().as_bytes())?;
    let mut stat: libc::stat = unsafe { std::mem::zeroed() };

    // unsafe 代码块是必要的，因为我们正在调用C语言的函数
    let result = unsafe { libc::stat(c_path.as_ptr(), &mut stat) };

    if result == -1 {
        // 如果 syscall 失败，返回一个标准的 Rust I/O 错误
        return Err(io::Error::last_os_error());
    }
    Ok(stat)
}

/// 执行挂载操作，等同于 `mount -t cgroup -o name=systemd cgroup /sys/fs/cgroup/systemd`
fn mount_cgroup_systemd(target_path: &str) -> io::Result<()> {
    // 准备 mount 系统调用需要的所有参数，并将它们转换为 C 字符串
    let source = CString::new("cgroup")?;
    let target = CString::new(target_path)?;
    let fstype = CString::new("cgroup")?;
    let data = CString::new("name=systemd")?;

    // 设置挂载标志位。这些标志位等同于命令行中常见的 rw,nosuid,nodev,noexec
    // 使用 libc::MS_NOSUID | libc::MS_NODEV | libc::MS_NOEXEC
    let mountflags = libc::MS_NOSUID | libc::MS_NODEV | libc::MS_NOEXEC;

    // 调用 libc::mount。这是直接的系统调用，必须在 unsafe 块中。
    let result = unsafe {
        libc::mount(
            source.as_ptr(),
            target.as_ptr(),
            fstype.as_ptr(),
            mountflags.into(),
            data.as_ptr() as *const libc::c_void,
        )
    };

    if result == -1 {
        return Err(io::Error::last_os_error());
    }

    Ok(())
}
