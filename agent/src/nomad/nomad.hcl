data_dir  = "/opt/nomad/data"

bind_addr = "{HOST}"

advertise {
  http = "{HOST}:4646"
}

client {
  enabled = true
  cgroup_parent = "nomad.slice"
  options {
    "docker.volumes.enabled" = true
    "fingerprint.cgroups.version" = "v2"
  }
  # 总共 16 PB
  disk_total_mb = 17179869184
  # 可用 64 GB
  disk_free_mb = 65536
  servers = ["{SERVER}:4647"]
}
plugin "docker" {
  config {
    # 确保 Docker 使用正确的 cgroup 驱动
    allow_privileged = true
    volumes {
      enabled = true
    }
    # 如果使用 cgroupfs 驱动
    # cgroup_fs_root = "/sys/fs/cgroup"
  }
}
plugin "nomad-device-nvidia" {
  config {
    enabled            = true
    fingerprint_period = "1m"
  }
}