use std::io;
use std::path::Path;
use std::time::Duration;
use tokio::net::UnixDatagram;
use tokio::process::Command;
use tokio::time::{sleep, timeout};

use super::launcher::SocketAddress;

/// Readiness check types for different daemons
#[derive(Debug, Clone)]
pub enum ReadinessCheck {
    /// Tailscale specific health check
    TailscaleHealth,
    /// Docker specific health check
    DockerPing,
    /// Nomad specific health check
    NomadLeader(String), // IP address
}

/// Perform specific readiness check
pub async fn perform_readiness_check(
    check: &ReadinessCheck,
    socket: &SocketAddress,
    sd_notify: Option<UnixDatagram>,
    daemon_name: &str,
) -> anyhow::Result<()> {
    const TOTAL_TIMEOUT: Duration = Duration::from_secs(180);

    // Use systemd notify if enabled, otherwise use socket check
    if let Some(socket) = sd_notify {
        tracing::debug!("使用 systemd notify 协议等待 {} 就绪", daemon_name);
        match timeout(TOTAL_TIMEOUT, check_systemd_notify(&socket, daemon_name)).await {
            Ok(Ok(())) => {}
            Ok(Err(err)) => {
                tracing::error!("{} 的 systemd notify 检查失败: {:?}", daemon_name, err);
                return Err(anyhow::anyhow!(
                    "{} 的 systemd notify 检查失败: {:?}",
                    daemon_name,
                    err
                ));
            }
            Err(err) => {
                tracing::error!("{} 的等待检查超时: {:?}", daemon_name, err);
                return Err(anyhow::anyhow!("{} 的等待检查超时", daemon_name));
            }
        }
    } else {
        tracing::debug!("开始连接 {} 套接字", daemon_name);
        match timeout(TOTAL_TIMEOUT, wait_for_socket_ready(socket, daemon_name)).await {
            Ok(Ok(())) => {}
            Ok(Err(err)) => {
                tracing::error!("无法连接 {} 套接字: {:?}", daemon_name, err);
                return Err(anyhow::anyhow!(
                    "无法连接 {} 套接字: {:?}",
                    daemon_name,
                    err
                ));
            }
            Err(err) => {
                tracing::error!("连接 {} 套接字超时: {:?}", daemon_name, err);
                return Err(anyhow::anyhow!("连接 {} 套接字超时", daemon_name));
            }
        }
    }

    tracing::debug!("开始进行 {} 健康检查", daemon_name);
    let result = timeout(TOTAL_TIMEOUT, async {
        match check {
            ReadinessCheck::TailscaleHealth => check_tailscale_health(daemon_name).await,
            ReadinessCheck::DockerPing => check_docker_health(daemon_name).await,
            ReadinessCheck::NomadLeader(ip) => check_nomad_health(ip, daemon_name).await,
        }
    })
    .await;

    match result {
        Ok(Ok(())) => Ok(()),
        Ok(Err(err)) => {
            tracing::error!(name = daemon_name, "健康检查失败: {:?}", err);
            Err(anyhow::anyhow!("{} 健康检查失败", daemon_name))
        }
        Err(_) => {
            tracing::error!(
                name = daemon_name,
                timeout_secs = TOTAL_TIMEOUT.as_secs(),
                "健康检查超时"
            );
            Err(anyhow::anyhow!("{} 健康检查超时", daemon_name))
        }
    }
}

/// Tailscale health check using tailscale status command
async fn check_tailscale_health(daemon_name: &str) -> anyhow::Result<()> {
    const POLL_INTERVAL: Duration = Duration::from_millis(1000);

    loop {
        tracing::debug!("[{}] 执行 Tailscale 健康检查...", daemon_name);

        match Command::new("/opt/tailscale/tailscale")
            .args(["status", "--json"])
            .output()
            .await
        {
            Ok(output) if output.status.success() => {
                let status_str = String::from_utf8(output.stdout).map_err(|err| {
                    tracing::error!("无法解析 Tailscale 输出为 UTF8, 原因: {err:?}");
                    anyhow::anyhow!("无法解析 Tailscale 输出为 UTF8")
                })?;
                let status = serde_json::from_str::<crate::tailscale::StatusObj>(&status_str)
                    .map_err(|err| {
                        tracing::error!("无法解析 Tailscale Status, 原因: {err:?}");
                        anyhow::anyhow!("无法解析 Tailscale Status")
                    })?;

                if status.backend_state == "Running" {
                    tracing::info!("[{}] Tailscale 运行状态正常", daemon_name);
                    return Ok(());
                }
                if status.backend_state == "NoState" {
                    tracing::debug!("Tailscale 处于完全不活动或未初始化, 进行主动激活");
                    if let Err(err) = crate::tailscale::up_without_auth().await {
                        tracing::error!("尝试激活 Tailscale 失败, 原因: {err:?}")
                    };
                } else {
                    tracing::debug!(
                        backend_state = status.backend_state,
                        health = ?status.health,
                        "[{}] Tailscale 尚未完全启动，继续等待",
                        daemon_name
                    );
                }
            }
            Ok(output) => {
                let stderr = String::from_utf8_lossy(&output.stderr);
                tracing::debug!("[{}] Tailscale status 检查失败: {}", daemon_name, stderr);
            }
            Err(err) => {
                tracing::debug!("[{}] 无法执行 tailscale status: {:?}", daemon_name, err);
            }
        }

        sleep(POLL_INTERVAL).await;
    }
}

/// Docker health check using docker version and info
async fn check_docker_health(daemon_name: &str) -> anyhow::Result<()> {
    const POLL_INTERVAL: Duration = Duration::from_millis(2000);

    loop {
        tracing::debug!("[{}] 执行 Docker 健康检查...", daemon_name);

        match Command::new("/opt/docker/docker")
            .args(["version", "--format", "{{.Server.Version}}"])
            .output()
            .await
        {
            Ok(output) if output.status.success() => {
                let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
                if !version.is_empty() {
                    tracing::info!("[{}] Docker 服务器版本: {}", daemon_name, version);

                    // Further check Docker info
                    match Command::new("docker")
                        .args(["info", "--format", "{{.ServerVersion}}"])
                        .output()
                        .await
                    {
                        Ok(info_output) if info_output.status.success() => {
                            tracing::info!("[{}] Docker 守护进程完全就绪", daemon_name);
                            return Ok(());
                        }
                        _ => {
                            tracing::debug!("[{}] Docker 信息检查失败，继续等待", daemon_name);
                        }
                    }
                }
            }
            Ok(output) => {
                let stderr = String::from_utf8_lossy(&output.stderr);
                tracing::debug!("[{}] Docker version 检查失败: {}", daemon_name, stderr);
            }
            Err(err) => {
                tracing::debug!("[{}] 无法执行 docker version: {:?}", daemon_name, err);
            }
        }

        sleep(POLL_INTERVAL).await;
    }
}

/// Nomad health check: leader status and node registration
async fn check_nomad_health(ip: &str, daemon_name: &str) -> anyhow::Result<()> {
    const POLL_INTERVAL: Duration = Duration::from_millis(3000);

    loop {
        tracing::debug!("[{}] 执行 Nomad 健康检查...", daemon_name);

        // Check Nomad agent info
        match Command::new("nomad")
            .args(["agent-info", "-address", &format!("http://{}:4646", ip)])
            .output()
            .await
        {
            Ok(output) if output.status.success() => {
                let info = String::from_utf8_lossy(&output.stdout);

                // Check if contains key information
                if info.contains("client") || info.contains("server") {
                    tracing::debug!("[{}] Nomad agent 信息获取成功", daemon_name);

                    // Further check node status
                    match Command::new("nomad")
                        .args(["node", "status", "-address", &format!("http://{}:4646", ip)])
                        .output()
                        .await
                    {
                        Ok(node_output) if node_output.status.success() => {
                            tracing::info!("[{}] Nomad 节点状态正常", daemon_name);
                            return Ok(());
                        }
                        Ok(node_output) => {
                            let stderr = String::from_utf8_lossy(&node_output.stderr);
                            tracing::debug!("[{}] Nomad 节点状态检查: {}", daemon_name, stderr);
                        }
                        Err(err) => {
                            tracing::debug!("[{}] Nomad 节点状态检查失败: {:?}", daemon_name, err);
                        }
                    }
                }
            }
            Ok(output) => {
                let stderr = String::from_utf8_lossy(&output.stderr);
                tracing::debug!("[{}] Nomad agent-info 检查失败: {}", daemon_name, stderr);
            }
            Err(err) => {
                tracing::debug!("[{}] 无法执行 nomad agent-info: {:?}", daemon_name, err);
            }
        }

        sleep(POLL_INTERVAL).await;
    }
}

/// Wait for systemd notify (sd_notify) signal
async fn check_systemd_notify(socket: &UnixDatagram, daemon_name: &str) -> anyhow::Result<()> {
    let mut buf = vec![0; 1024];
    tracing::debug!("[{}] 等待 systemd notify (sd_notify) 信号...", daemon_name);
    let len = socket.recv(&mut buf).await?;
    let message = String::from_utf8_lossy(&buf[..len]);
    tracing::info!("[{}] 收到 notify 消息: {}", daemon_name, message);

    if message.contains("READY=1") {
        tracing::info!("[{}] 检测到 READY=1，服务已就绪", daemon_name);
        Ok(())
    } else {
        Err(anyhow::anyhow!("未收到 READY=1 通知"))
    }
}

/// Wait for socket readiness unified interface
async fn wait_for_socket_ready(socket: &SocketAddress, daemon_name: &str) -> anyhow::Result<()> {
    match socket {
        SocketAddress::Unix(path) => wait_for_unix_socket(path, daemon_name).await,
        SocketAddress::Tcp(addr) => wait_for_tcp_socket(addr, daemon_name).await,
    }
    .map_err(|err| anyhow::anyhow!("等待 {} 套接字就绪失败: {}", daemon_name, err))
}

/// Wait for Unix socket readiness
async fn wait_for_unix_socket(socket_path: &Path, daemon_name: &str) -> io::Result<()> {
    const POLL_INTERVAL: Duration = Duration::from_millis(200);

    loop {
        if socket_path.exists() {
            tracing::debug!("[{}] Unix 套接字文件存在，尝试连接...", daemon_name);

            match tokio::net::UnixStream::connect(socket_path).await {
                Ok(_) => {
                    tracing::info!("[{}] Unix 套接字连接成功", daemon_name);
                    return Ok(());
                }
                Err(e) => {
                    tracing::trace!("[{}] Unix 套接字连接失败: {}, 将稍后重试", daemon_name, e);
                }
            }
        } else {
            tracing::trace!("[{}] Unix 套接字文件不存在，等待中...", daemon_name);
        }

        sleep(POLL_INTERVAL).await;
    }
}

/// Wait for TCP socket readiness
async fn wait_for_tcp_socket(addr: &str, daemon_name: &str) -> io::Result<()> {
    const POLL_INTERVAL: Duration = Duration::from_millis(2000);

    let socket_addr = addr.parse::<std::net::SocketAddr>().map_err(|err| {
        tracing::error!(addr = addr, "无法解析地址: {:?}", err);
        io::Error::new(io::ErrorKind::InvalidInput, format!("无效地址: {}", addr))
    })?;

    loop {
        match tokio::net::TcpStream::connect(socket_addr).await {
            Ok(_) => {
                tracing::info!("[{}] TCP 套接字连接成功", daemon_name);
                return Ok(());
            }
            Err(e) => {
                tracing::trace!("[{}] TCP 套接字连接失败: {}, 将稍后重试", daemon_name, e);
            }
        }
        sleep(POLL_INTERVAL).await;
    }
}
