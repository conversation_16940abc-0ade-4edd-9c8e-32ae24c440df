use protocol::events::LogLevel;
use std::path::PathBuf;
use std::process::Stdio;
use std::sync::Arc;
use tempfile::{tempdir, TempDir};
use tokio::net::UnixDatagram;
use tokio::process::{Child, ChildStderr, ChildStdout, Command};
use tokio::sync::Notify;
use tokio_util::sync::CancellationToken;

use super::health::ReadinessCheck;
use super::logging::{DaemonLogForwarder, DaemonType};

/// Socket address types for daemon communication
#[derive(Debug, Clone)]
pub enum SocketAddress {
    Unix(PathBuf),
    Tcp(String),
}

/// Daemon launcher configuration
#[derive(Debug, Clone)]
pub struct DaemonLauncher {
    pub name: &'static str,
    pub program: PathBuf,
    pub args: Vec<String>,
    pub readiness_check: ReadinessCheck,
    pub enable_systemd_notify: bool,
    pub socket: SocketAddress,
}

/// SystemD notify resources that need cleanup
struct SdNotifyResources {
    socket: UnixDatagram,
    _tempdir_guard: TempDir,
}

/// Running daemon instance
pub struct Daemon {
    pub launcher: DaemonLauncher,
    pub process: Child,
    pub stdout: ChildStdout,
    pub stderr: ChildStderr,
    ready_notify: Arc<Notify>,
    sd_notify_resources: Option<SdNotifyResources>,
}

impl DaemonLauncher {
    /// Launch the daemon process
    pub async fn launch(self) -> anyhow::Result<Daemon> {
        let mut sd_notify_resources: Option<SdNotifyResources> = None;
        let mut command = Command::new(&self.program);

        // Setup systemd notify socket if enabled
        if self.enable_systemd_notify {
            let dir = tempdir()?;
            let socket_path = dir.path().join("notify.sock");
            let socket = UnixDatagram::bind(&socket_path)?;

            command.env("NOTIFY_SOCKET", &socket_path);
            sd_notify_resources = Some(SdNotifyResources {
                socket,
                _tempdir_guard: dir,
            });
        }

        // Spawn the process
        let mut child = command
            .args(&self.args)
            .stdin(Stdio::null())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .map_err(|err| {
                tracing::error!(
                    name = self.name,
                    program = ?self.program,
                    args = ?self.args,
                    "Failed to launch daemon: {}", err
                );
                anyhow::anyhow!("Failed to launch {}: {}", self.name, err)
            })?;

        let stdout = child
            .stdout
            .take()
            .ok_or_else(|| anyhow::anyhow!("无法获取 {} 的 stdout 管道", self.name))?;

        let stderr = child
            .stderr
            .take()
            .ok_or_else(|| anyhow::anyhow!("无法获取 {} 的 stderr 管道", self.name))?;

        tracing::debug!("{} 进程已启动, 等待其就绪", self.name);

        Ok(Daemon {
            launcher: self,
            process: child,
            stdout,
            stderr,
            ready_notify: Arc::new(Notify::new()),
            sd_notify_resources,
        })
    }
}

impl Daemon {
    /// Run daemon until done, returns ready notifier and process handle
    pub fn run_until_done(
        mut self,
        group_signal: &CancellationToken,
    ) -> (Arc<Notify>, tokio::task::JoinHandle<anyhow::Result<()>>) {
        let name = self.launcher.name;
        let socket = self.launcher.socket.clone();
        let ready_notify = Arc::clone(&self.ready_notify);
        let readiness_check = self.launcher.readiness_check.clone();
        // 同一个守护进程组的取消信号，当当前守护进程被终止时，会自动取消同一组的其他守护进程的取消信号
        let group_signal = group_signal.clone();

        let ready_notify_clone = Arc::clone(&ready_notify);
        let sd_notify_socket = self.sd_notify_resources.take().map(|r| r.socket);

        // 健康检查任务，当失败时意味着这个守护进程已经无法正常工作，需要被终止
        let readiness_handle = {
            let signal = group_signal.clone();
            tokio::spawn(async move {
                tracing::info!("运行 {} 健康检查", name);
                tokio::select! {
                    result = super::health::perform_readiness_check(&readiness_check, &socket, sd_notify_socket, name) => {
                        match result {
                            Ok(()) => {
                                tracing::info!("{} 健康检查通过，服务已就绪", name);
                                ready_notify.notify_waiters();
                            }
                            Err(err) => {
                                tracing::error!("{} 健康检查失败: {:?}", name, err);
                                ready_notify.notify_waiters();
                                signal.cancel();
                            }
                        }
                    },
                    _ = signal.cancelled() => {
                        tracing::info!("收到终止信号，终止 {} 健康检查", name);
                        ready_notify.notify_waiters();
                    }
                }
            })
        };

        // Start main daemon management task with unified logging
        let signal = group_signal.clone();
        let handle = tokio::spawn(async move {
            let daemon_type = DaemonType::from_name(name);

            // Create log parsers based on daemon type
            let stdout_parser = Self::create_log_parser(daemon_type);
            let stderr_parser = Self::create_log_parser(daemon_type);

            let stdout_task = DaemonLogForwarder::spawn_handler(
                self.stdout,
                daemon_type,
                "stdout",
                stdout_parser,
            )
            .await;

            let stderr_task = DaemonLogForwarder::spawn_handler(
                self.stderr,
                daemon_type,
                "stderr",
                stderr_parser,
            )
            .await;

            let finish = async { tokio::join!(stdout_task, stderr_task) };

            tokio::select! {
                _ = signal.cancelled() => {
                    tracing::info!("收到终止信号，开始终止 {} 守护进程", name);
                    super::lifecycle::shutdown_gracefully(name, self.process).await;
                }
                result = finish => {
                    if !signal.is_cancelled() {
                        signal.cancel();
                    }
                    match result {
                        (Ok(Ok(_)), Ok(Ok(_))) => tracing::info!("{} 输出处理器正常结束", name),
                        (Err(err), _) | (_, Err(err)) => {
                            tracing::error!("{} 输出处理器异常: {:?}", name, err)
                        }
                        (Ok(Err(err)), _) | (_, Ok(Err(err))) => {
                            tracing::error!("{} 输出处理器异常: {:?}", name, err)
                        }
                    }
                }
            }

            if !readiness_handle.is_finished() {
                readiness_handle.abort();
            }
            Ok(()) as anyhow::Result<()>
        });

        (ready_notify_clone, handle)
    }

    /// Wait for daemon to be ready
    pub async fn wait_ready_with_notify(ready_notify: &Arc<Notify>) {
        ready_notify.notified().await;
    }

    /// Create appropriate log parser for daemon type
    fn create_log_parser(
        daemon_type: DaemonType,
    ) -> impl Fn(&str) -> super::logging::ParsedLog + Send + 'static {
        move |line: &str| match daemon_type {
            DaemonType::Tailscaled => {
                let info = crate::tailscale::TailscaleLogParser::parse_log(line);
                super::logging::ParsedLog {
                    level: info.level,
                    timestamp: info.timestamp,
                    module: info.component,
                    raw_message: info.raw_message,
                }
            }
            DaemonType::Dockerd => {
                let info = crate::docker::DockerLogParser::parse_log(line);
                super::logging::ParsedLog {
                    level: info.level,
                    timestamp: info.timestamp,
                    module: info.module,
                    raw_message: info.raw_message,
                }
            }
            DaemonType::Nomad => {
                let info = crate::nomad::NomadLogParser::parse_log(line);
                super::logging::ParsedLog {
                    level: info.level,
                    timestamp: info.timestamp,
                    module: info.module,
                    raw_message: info.raw_message,
                }
            }
            DaemonType::Unknown => super::logging::ParsedLog {
                level: LogLevel::Info,
                timestamp: None,
                module: None,
                raw_message: line.to_string(),
            },
        }
    }
}
