use protocol::events::LogLevel;
use tokio::io::{AsyncBufReadExt, AsyncRead, BufReader};

/// 已解析的守护进程日志信息
///
/// 存储从单行原始日志中提取出的结构化数据。
#[derive(Debug, <PERSON>lone)]
pub struct ParsedLog {
    pub level: LogLevel,
    /// 可选的时间戳字符串
    pub timestamp: Option<String>,
    /// 可选的模块名，用于指明日志来源的内部组件
    pub module: Option<String>,
    /// 未经处理的原始日志消息内容
    pub raw_message: String,
}
/// Daemon type enum for compile-time target resolution
#[derive(Debug, <PERSON>lone, Copy)]
pub enum DaemonType {
    Tailscaled,
    Dockerd,
    Nomad,
    Unknown,
}

impl DaemonType {
    pub const fn target(&self) -> &'static str {
        match self {
            DaemonType::Tailscaled => "tailscaled",
            DaemonType::Dockerd => "dockerd",
            DaemonType::Nomad => "nomad",
            DaemonType::Unknown => "unknown",
        }
    }
}

impl DaemonType {
    pub fn from_name(name: &str) -> Self {
        match name {
            "tailscaled" => Self::Tailscaled,
            "dockerd" => Self::Dockerd,
            "nomad" => Self::Nomad,
            _ => Self::Unknown,
        }
    }
}

/// Generic log parser trait that different daemons can implement
pub trait LogParser {
    fn parse_log(line: &str) -> ParsedLog;
}

/// Unified daemon log forwarder using tracing's structured logging
pub struct DaemonLogForwarder;

impl DaemonLogForwarder {
    /// Spawn a task to forward daemon logs to tracing with proper structure
    pub async fn spawn_handler<R>(
        reader: R,
        daemon_type: DaemonType,
        stream_type: &'static str,
        parser: impl Fn(&str) -> ParsedLog + Send + 'static,
    ) -> tokio::task::JoinHandle<anyhow::Result<()>>
    where
        R: AsyncRead + Unpin + Send + 'static,
    {
        tokio::spawn(async move {
            let mut lines = BufReader::new(reader).lines();

            while let Some(line) = lines.next_line().await? {
                if line.trim().is_empty() {
                    continue;
                }

                let parsed_log = parser(&line);
                Self::forward_log_entry(daemon_type, stream_type, parsed_log);
            }

            tracing::debug!(
                daemon = daemon_type.target(),
                stream = stream_type,
                "log stream ended"
            );
            Ok(())
        })
    }

    /// Emit a structured log event using tracing with compile-time targets
    fn forward_log_entry(daemon_type: DaemonType, stream_type: &str, parsed: ParsedLog) {
        let module = parsed.module.as_deref().unwrap_or("");
        let level = parsed.level;

        macro_rules! dispatch_log {
            (target: $target:expr) => {
                match level {
                    LogLevel::Error => {
                        tracing::event!(target: $target, tracing::Level::ERROR, module, stream = stream_type, "{}", parsed.raw_message)
                    }
                    LogLevel::Warn => {
                        tracing::event!(target: $target, tracing::Level::WARN, module, stream = stream_type, "{}", parsed.raw_message)
                    }
                    LogLevel::Info => {
                        tracing::event!(target: $target, tracing::Level::INFO, module, stream = stream_type, "{}", parsed.raw_message)
                    }
                    LogLevel::Debug => {
                        tracing::event!(target: $target, tracing::Level::DEBUG, module, stream = stream_type, "{}", parsed.raw_message)
                    }
                    LogLevel::Trace => {
                        tracing::event!(target: $target, tracing::Level::TRACE, module, stream = stream_type, "{}", parsed.raw_message)
                    }
                }
            };
        }

        // Use daemon-specific emit functions to ensure compile-time constant targets
        match daemon_type {
            DaemonType::Tailscaled => {
                dispatch_log!(target: "tailscaled");
            }
            DaemonType::Dockerd => {
                dispatch_log!(target: "dockerd");
            }
            DaemonType::Nomad => {
                dispatch_log!(target: "nomad");
            }
            DaemonType::Unknown => {
                dispatch_log!(target: "unknown");
            }
        }
    }
}
