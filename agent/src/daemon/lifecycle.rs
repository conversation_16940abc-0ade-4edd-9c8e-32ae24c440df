use protocol::events::DockerAuth;
use std::sync::{Arc, LazyLock};
use std::time::{Duration, Instant};
use tokio::process::Child;
use tokio::sync::{<PERSON>te<PERSON>, Notify};
use tokio::time::{sleep, timeout};
use tokio_util::sync::CancellationToken;

use super::health::ReadinessCheck;
use super::launcher::{<PERSON>, DaemonLauncher, SocketAddress};

/// Daemon context for lifecycle management
struct DaemonContext {
    global_shutdown_signal: CancellationToken,
    handlers: Vec<(
        &'static str,
        tokio::task::Jo<PERSON><PERSON><PERSON><PERSON><anyhow::Result<()>>,
        CancellationToken,
    )>,
    finish_notify: Arc<Notify>,
}

impl DaemonContext {
    pub async fn shutdown(self) {
        self.global_shutdown_signal.cancel();

        // Sort handlers by shutdown order: Nomad → Docker → Tailscale
        let mut sorted_handlers = self.handlers;
        sorted_handlers.sort_by_key(|(name, _, _)| {
            match *name {
                "nomad" => 0,     // Stop first
                "docker" => 1,    // Stop second
                "tailscale" => 2, // Stop last
                _ => 99,          // Unknown daemons stop at the end
            }
        });

        for (name, handle, _) in sorted_handlers {
            if handle.is_finished() {
                continue;
            }
            tracing::info!("等待 {} 守护进程关闭", name);
            match handle.await {
                Ok(Ok(())) => {
                    tracing::info!("{} 守护进程已正常关闭", name);
                }
                Ok(Err(err)) => {
                    tracing::error!("{name} 守护进程发生错误, 原因: {err:?}")
                }
                Err(err) => {
                    tracing::error!("{name} 守护进程在终止时发生错误, 原因: {err:?}")
                }
            }
        }
        self.finish_notify.notify_waiters();
        tracing::info!("所有守护进程已按顺序关闭")
    }
}

static DAEMON_CTX: LazyLock<Arc<Mutex<Option<DaemonContext>>>> =
    LazyLock::new(|| Arc::new(Mutex::new(None)));

/// Launch daemons (initially only Tailscale)
pub async fn launch_daemons(shutdown_signal: CancellationToken) -> anyhow::Result<()> {
    let mut guard = DAEMON_CTX.try_lock().map_err(|err| {
        tracing::error!("无法锁定上下文, 原因: {err:?}");
        anyhow::anyhow!("无法锁定上下文")
    })?;
    if guard.is_some() {
        tracing::warn!("检测到重复启动守护进程");
        anyhow::bail!("无法重复启动守护进程")
    }
    let mut ctx = DaemonContext {
        global_shutdown_signal: shutdown_signal.child_token(),
        handlers: vec![],
        finish_notify: Arc::new(Notify::new()),
    };

    // Only launch Tailscale initially
    tracing::debug!("启动 Tailscale 守护进程");
    let tailscale_daemon = launch_tailscale(&ctx.global_shutdown_signal).await?;
    let (tailscale_ready, tailscale_handle) =
        tailscale_daemon.run_until_done(&ctx.global_shutdown_signal);
    tracing::debug!("等待 Tailscale 守护进程就绪");
    Daemon::wait_ready_with_notify(&tailscale_ready).await;
    tracing::debug!("Tailscale 守护进程已就绪");
    ctx.handlers.push((
        "tailscale",
        tailscale_handle,
        ctx.global_shutdown_signal.clone(),
    ));

    // Docker and Nomad will be started via AgentEvent::Start

    // Cleanup context when main signal is sent
    tokio::spawn(async move {
        shutdown_signal.cancelled().await;
        let guard = DAEMON_CTX.try_lock().map(|mut it| it.take());
        if let Ok(Some(ctx)) = guard {
            ctx.shutdown().await;
        }
    });

    *guard = Some(ctx);
    tracing::info!("Tailscale 守护进程已成功启动，Docker 和 Nomad 等待事件启动");
    Ok(())
}

/// Launch Tailscale daemon
async fn launch_tailscale(shutdown_signal: &CancellationToken) -> anyhow::Result<Daemon> {
    if shutdown_signal.is_cancelled() {
        return Err(anyhow::anyhow!("收到关闭信号，取消启动"));
    }

    let launcher = DaemonLauncher {
        name: "tailscaled",
        program: std::path::PathBuf::from("/opt/tailscale/tailscaled"),
        args: vec![
            "--state=/var/lib/tailscale/tailscaled.state".to_string(),
            format!("--socket={}", crate::tailscale::SOCK_PATH),
            format!("--port={}", crate::tailscale::PORT),
        ],
        socket: SocketAddress::Unix(std::path::PathBuf::from(crate::tailscale::SOCK_PATH)),
        readiness_check: ReadinessCheck::TailscaleHealth,
        enable_systemd_notify: false,
    };

    launcher.launch().await.map_err(|err| {
        tracing::error!("无法启动 Tailscale 守护程序: {:?}", err);
        anyhow::anyhow!("Tailscale 启动失败")
    })
}

/// Wait for daemon shutdown, returns if unable to lock context within 150ms
pub async fn wait_shutdown() {
    let finish_notify = match timeout(Duration::from_millis(150), DAEMON_CTX.lock()).await {
        Ok(guard) => guard.as_ref().map(|it| it.finish_notify.clone()),
        Err(err) => {
            tracing::warn!("无法开始等待守护进程关闭, 原因: {err:?}");
            return;
        }
    };
    let finish_notify = if let Some(finish_notify) = finish_notify {
        finish_notify
    } else {
        return;
    };
    finish_notify.notified().await;
}

/// Dynamically start Docker and Nomad
pub async fn start_docker_and_nomad(
    tailscale_ip: &str,
    docker_auth: Option<&DockerAuth>,
) -> anyhow::Result<()> {
    let mut guard = DAEMON_CTX.lock().await;

    let ctx = guard
        .as_mut()
        .ok_or_else(|| anyhow::anyhow!("守护进程上下文未初始化"))?;

    let group_signal = ctx.global_shutdown_signal.child_token();
    // Check if already started
    if ctx
        .handlers
        .iter()
        .any(|(name, _, _)| *name == "docker&nomad")
    {
        tracing::info!("Docker & Nomad 已经在运行");
        return Ok(());
    }

    // Start Docker
    let docker_daemon = launch_docker(&group_signal).await?;
    let (docker_ready, docker_handle) = docker_daemon.run_until_done(&group_signal);
    Daemon::wait_ready_with_notify(&docker_ready).await;

    // Docker login
    if let Some(auth) = docker_auth {
        crate::docker::login(auth).await?;
    } else {
        tracing::warn!("没有提供 Docker 认证信息，跳过登录");
    }

    tracing::info!("Tailscale IP: {}", tailscale_ip);

    let nomad_daemon = launch_nomad(&tailscale_ip, &group_signal).await?;
    let (nomad_ready, nomad_handle) = nomad_daemon.run_until_done(&group_signal);
    Daemon::wait_ready_with_notify(&nomad_ready).await;
    let handle = {
        let group_signal = group_signal.clone();
        tokio::spawn(async move {
            match tokio::join!(docker_handle, nomad_handle) {
                (Ok(Ok(_)), Ok(Ok(_))) => {
                    tracing::info!("守护进程组 (Docker, Nomad) 已正常退出");
                }
                (docker_res, nomad_res) => {
                    if let Err(e) = &docker_res {
                        tracing::error!("Docker 守护进程任务出错(JoinError): {:?}", e);
                    } else if let Ok(Err(e)) = &docker_res {
                        tracing::error!("Docker 守护进程出错: {:?}", e);
                    }

                    if let Err(e) = &nomad_res {
                        tracing::error!("Nomad 守护进程任务出错(JoinError): {:?}", e);
                    } else if let Ok(Err(e)) = &nomad_res {
                        tracing::error!("Nomad 守护进程出错: {:?}", e);
                    }
                    tracing::warn!(
                        "守护进程组 (Docker, Nomad) 异常退出，正在触发关闭. Docker: {:?}, Nomad: {:?}",
                        docker_res,
                        nomad_res
                    );
                }
            }
            if !group_signal.is_cancelled() {
                group_signal.cancel();
            }
            Ok(()) as anyhow::Result<()>
        })
    };
    ctx.handlers.push(("docker&nomad", handle, group_signal));

    tracing::info!("Docker 和 Nomad 已成功启动");
    Ok(())
}

/// Dynamically stop Docker and Nomad
pub async fn stop_docker_and_nomad() -> anyhow::Result<()> {
    let mut guard = DAEMON_CTX.try_lock().map_err(|err| {
        tracing::error!("无法锁定上下文, 原因: {err:?}");
        anyhow::anyhow!("无法锁定上下文")
    })?;

    let ctx = guard
        .as_mut()
        .ok_or_else(|| anyhow::anyhow!("守护进程上下文未初始化"))?;

    if let Some(index) = ctx
        .handlers
        .iter()
        .position(|(name, _, _)| *name == "docker&nomad")
    {
        let (name, handle, daemon_signal) = ctx.handlers.remove(index);
        tracing::info!("正在停止 {} 守护进程组", name);
        daemon_signal.cancel();

        match timeout(Duration::from_secs(30), handle).await {
            Ok(Ok(Ok(_))) => tracing::info!("{} 守护进程组已优雅退出", name),
            Ok(Ok(Err(err))) => tracing::warn!("{} 守护进程组退出时发生错误: {:?}", name, err),
            Ok(Err(join_err)) => tracing::warn!("{} 守护进程组任务被取消: {:?}", name, join_err),
            Err(_) => tracing::warn!("等待 {} 守护进程组退出超时", name),
        }
    } else {
        tracing::info!("Docker 和 Nomad 不在运行中");
    }

    tracing::info!("Docker 和 Nomad 已停止");
    Ok(())
}

/// Launch Docker daemon
async fn launch_docker(shutdown_signal: &CancellationToken) -> anyhow::Result<Daemon> {
    if shutdown_signal.is_cancelled() {
        return Err(anyhow::anyhow!("收到关闭信号，取消启动"));
    }

    let launcher = DaemonLauncher {
        name: "dockerd",
        program: std::path::PathBuf::from("/opt/docker/dockerd"),
        args: vec![format!("-H unix://{}", crate::docker::SOCK_PATH)],
        socket: SocketAddress::Unix(std::path::PathBuf::from(crate::docker::SOCK_PATH)),
        readiness_check: ReadinessCheck::DockerPing,
        enable_systemd_notify: false,
    };

    launcher.launch().await.map_err(|err| {
        tracing::error!("无法启动 Docker 守护程序: {:?}", err);
        anyhow::anyhow!("Docker 启动失败")
    })
}

/// Launch Nomad daemon
async fn launch_nomad(
    tailscale_ip: &str,
    shutdown_signal: &CancellationToken,
) -> anyhow::Result<Daemon> {
    if shutdown_signal.is_cancelled() {
        return Err(anyhow::anyhow!("收到关闭信号，取消启动"));
    }

    const NOMAD_SERVER: &str = "**********";

    crate::nomad::write_hcl(tailscale_ip, NOMAD_SERVER)
        .await
        .map_err(|err| {
            tracing::error!("无法更新 Nomad 配置文件: {:?}", err);
            anyhow::anyhow!("Nomad 配置文件更新失败")
        })?;

    let launcher = DaemonLauncher {
        name: "nomad",
        program: std::path::PathBuf::from("/opt/nomad/nomad"),
        args: vec![
            "agent".to_string(),
            "-config".to_string(),
            crate::nomad::HCL_PATH.to_string(),
        ],
        socket: SocketAddress::Tcp(format!("{tailscale_ip}:4646")),
        readiness_check: ReadinessCheck::NomadLeader(tailscale_ip.to_string()),
        enable_systemd_notify: false,
    };

    launcher.launch().await.map_err(|err| {
        tracing::error!("无法启动 Nomad 守护程序: {:?}", err);
        anyhow::anyhow!("Nomad 启动失败")
    })
}

/// Terminate daemons
pub async fn terminate_daemons() -> anyhow::Result<()> {
    let mut guard = DAEMON_CTX.try_lock().map_err(|err| {
        tracing::error!("无法锁定上下文, 原因: {err:?}");
        anyhow::anyhow!("无法锁定上下文")
    })?;
    if guard.is_none() {
        tracing::warn!("未检测到重复启动守护进程");
        anyhow::bail!("未检测到重复启动守护进程")
    }
    let ctx = guard.take().unwrap();
    ctx.shutdown().await;
    Ok(())
}

/// Gracefully shutdown daemon process
pub async fn shutdown_gracefully(name: &str, mut process: Child) {
    let Some(pid) = process.id() else {
        tracing::error!("无法获取 {} 守护进程的 PID", name);
        return;
    };

    // Send SIGTERM signal
    unsafe {
        libc::kill(pid as i32, libc::SIGTERM);
    }

    const GRACEFUL_TIMEOUT: Duration = Duration::from_secs(15);

    // Wait for process to gracefully exit
    match wait_for_child_exit(&mut process, GRACEFUL_TIMEOUT).await {
        Ok(true) => {
            tracing::info!("守护进程 {} 已优雅退出", name);
            return;
        }
        Ok(false) => {
            tracing::warn!(
                name = name,
                timeout_secs = GRACEFUL_TIMEOUT.as_secs(),
                "守护进程优雅退出超时，开始强制终止"
            );
        }
        Err(err) => {
            tracing::warn!(
                name = name,
                "等待守护进程退出时出错: {:?}，开始强制终止",
                err
            );
        }
    }

    // Force terminate process
    if let Err(err) = process.kill().await {
        tracing::error!("无法强制终止守护进程 {}: {:?}", name, err);
    } else {
        tracing::info!("守护进程 {} 已强制终止", name);
    }
}

/// Wait for child process to exit, returns whether it successfully exited
async fn wait_for_child_exit(
    child: &mut Child,
    timeout_duration: Duration,
) -> std::io::Result<bool> {
    let start = Instant::now();
    const CHECK_INTERVAL: Duration = Duration::from_millis(100);

    while start.elapsed() < timeout_duration {
        match child.try_wait()? {
            Some(_) => return Ok(true), // Process has exited
            None => sleep(CHECK_INTERVAL).await,
        }
    }

    Ok(false) // Timeout
}
