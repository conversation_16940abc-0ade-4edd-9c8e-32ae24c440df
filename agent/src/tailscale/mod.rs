use protocol::events::{ExposeS<PERSON><PERSON>, Log<PERSON><PERSON><PERSON>, Tai<PERSON><PERSON>Auth};
use regex::Regex;
use serde::Deserialize;
use std::sync::LazyLock;
use std::time::Duration;
use tokio::{process::Command, time::timeout};

// Tailscale 日志解析正则表达式
static TAILSCALE_LOG_LEVEL_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"(?i)\b(DEBUG|INFO|WARN|ERROR|TRACE)\b").unwrap());

static TAILSCALE_TIMESTAMP_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\d{4}/\d{2}/\d{2} \d{2}:\d{2}:\d{2}").unwrap());

static TAILSCALE_COMPONENT_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\[(\w+)\]").unwrap());

pub const PORT: u16 = 41641;
pub const SOCK_PATH: &str = "/run/tailscale/tailscaled.sock";

pub async fn ipv4(auth: &TailscaleAuth) -> anyhow::Result<String> {
    up(auth).await?;
    tracing::debug!("开始获取 Ipv4 地址");
    let total_timeout = Duration::from_secs(180);
    timeout(total_timeout, wait_for_ipv4())
        .await
        .map_err(|err| {
            tracing::error!(
                "等待获取 tailscale IP 地址 超时 ({}s) {err:?}",
                total_timeout.as_secs()
            );
            anyhow::format_err!("等待 socket 超时")
        })?
}

pub async fn wait_for_ipv4() -> anyhow::Result<String> {
    let poll_interval = Duration::from_secs(5);
    loop {
        let result = Command::new("tailscale")
            .arg("ip")
            .arg("-4")
            .output()
            .await
            .map_err(|err| {
                tracing::error!(
                    program = "tailscale",
                    args = "ip -4",
                    "无法执行 Tailscale 命令，原因: {err:?}"
                );
                err
            })?;
        if result.status.success() {
            return Ok(String::from_utf8_lossy(result.stdout.trim_ascii_end()).to_string());
        } else {
            tracing::debug!(
                "无法获取 Tailscale IPv4 地址, 原因: {}, 将稍后重试",
                String::from_utf8(result.stderr)?
            )
        }

        tokio::time::sleep(poll_interval).await;
    }
}

pub async fn up(auth: &TailscaleAuth) -> anyhow::Result<()> {
    tracing::info!("开始进行 Tailscale 登录");
    let result = Command::new("tailscale")
        .arg("up")
        .arg("--reset")
        .arg("--force-reauth")
        .arg(format!("--login-server={}", auth.login_server))
        .arg(format!("--authkey={}", auth.auth_key.expose_secret()))
        .arg("--accept-dns=false")
        .arg("--accept-routes")
        .output()
        .await
        .map_err(|err| {
            tracing::error!(
                program = "tailscale",
                args = "up",
                "无法执行 Tailscale 命令，原因: {err:?}"
            );
            err
        })?;
    if result.status.success() {
        tracing::info!("Tailscale 登录成功");
        Ok(())
    } else {
        tracing::error!(
            "无法启动 Tailscale, 原因: {}",
            String::from_utf8(result.stderr)?
        );
        anyhow::bail!("Failed launch tailscale")
    }
}
pub async fn up_without_auth() -> anyhow::Result<()> {
    tracing::info!("开始进行 Tailscale up");
    let result = Command::new("tailscale")
        .arg("up")
        .output()
        .await
        .map_err(|err| {
            tracing::error!(
                program = "tailscale",
                args = "up",
                "无法执行 Tailscale 命令，原因: {err:?}"
            );
            err
        })?;
    if result.status.success() {
        tracing::info!("Tailscale up success");
        Ok(())
    } else {
        // todo: 查阅code，
        tracing::error!(
            "无法执行 Tailscale up, 原因: {}, 错误码: {}",
            String::from_utf8(result.stderr)?,
            result.status.code().unwrap_or(-1)
        );
        anyhow::bail!("Failed launch tailscale")
    }
}

#[derive(Debug, Deserialize)]
#[serde(rename_all = "PascalCase")]
pub struct StatusObj {
    pub version: String,
    pub backend_state: String,
    #[serde(rename = "TailscaleIPs")]
    pub tailscale_ips: Option<Vec<String>>,
    pub health: Vec<String>,
}

/// Tailscale 专用日志解析器
pub struct TailscaleLogParser;

impl TailscaleLogParser {
    /// 解析 Tailscale 日志级别
    /// 典型格式: 2024/06/30 10:30:45 [INFO] derp: connected to region 1
    pub fn parse_log_level(message: &str) -> LogLevel {
        // 首先尝试从日志级别关键字中提取
        if let Some(captures) = TAILSCALE_LOG_LEVEL_REGEX.find(message) {
            return LogLevel::from_str(captures.as_str());
        }

        // 根据内容推断
        let message_lower = message.to_lowercase();
        if message_lower.contains("error")
            || message_lower.contains("failed")
            || message_lower.contains("fatal")
        {
            LogLevel::Error
        } else if message_lower.contains("warn") || message_lower.contains("warning") {
            LogLevel::Warn
        } else if message_lower.contains("debug") {
            LogLevel::Debug
        } else {
            LogLevel::Info
        }
    }

    /// 解析 Tailscale 日志时间戳
    pub fn parse_timestamp(message: &str) -> Option<String> {
        TAILSCALE_TIMESTAMP_REGEX
            .find(message)
            .map(|m| m.as_str().to_string())
    }

    /// 解析 Tailscale 组件
    pub fn parse_component(message: &str) -> Option<String> {
        TAILSCALE_COMPONENT_REGEX
            .captures(message)
            .and_then(|cap| cap.get(1))
            .map(|m| m.as_str().to_string())
    }

    /// 完整解析 Tailscale 日志
    pub fn parse_log(message: &str) -> TailscaleLogInfo {
        TailscaleLogInfo {
            level: Self::parse_log_level(message),
            timestamp: Self::parse_timestamp(message),
            component: Self::parse_component(message),
            raw_message: message.to_string(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct TailscaleLogInfo {
    pub level: LogLevel,
    pub timestamp: Option<String>,
    pub component: Option<String>,
    pub raw_message: String,
}

/// 测量 Tailscale 到服务器的延迟
/// 使用 tailscale ping 命令测量延迟
pub async fn measure_server_latency(server_ip: &str) -> anyhow::Result<u64> {
    tracing::debug!("开始测量 Tailscale 到服务器的延迟: {}", server_ip);

    let result = Command::new("tailscale")
        .arg("ping")
        .arg(server_ip)
        .output()
        .await
        .map_err(|err| {
            tracing::error!(
                program = "tailscale",
                args = "ping",
                "无法执行 Tailscale ping 命令，原因: {err:?}"
            );
            err
        })?;

    if result.status.success() {
        // 解析 tailscale ping 输出获取延迟时间
        let output = String::from_utf8_lossy(&result.stdout);
        let latency_ms = parse_ping_latency(&output)?;
        tracing::debug!(
            server_ip = server_ip,
            "Tailscale 到服务器延迟: {}ms",
            latency_ms
        );
        Ok(latency_ms)
    } else {
        let error_msg = String::from_utf8_lossy(&result.stderr);
        let exit_code = result.status.code().unwrap_or(-1);
        tracing::error!(
            server_ip = server_ip,
            exit_code = exit_code,
            "Tailscale ping 失败: {}",
            error_msg
        );
        anyhow::bail!("Tailscale ping 失败: {}", error_msg)
    }
}

/// 解析 tailscale ping 输出获取延迟时间
fn parse_ping_latency(output: &str) -> anyhow::Result<u64> {
    // tailscale ping 输出格式类似: "pong from 100.x.x.x via DERP(region-id) in 45ms"
    let time_regex = Regex::new(r"in (\d+)ms").unwrap();

    if let Some(captures) = time_regex.captures(output) {
        if let Some(time_str) = captures.get(1) {
            let latency = time_str
                .as_str()
                .parse::<u64>()
                .map_err(|e| anyhow::anyhow!("解析延迟时间失败: {}", e))?;
            return Ok(latency);
        }
    }

    anyhow::bail!("无法从 ping 输出中解析延迟时间: {}", output)
}

/// 等待 Tailscale 完全准备就绪
pub async fn wait_for_ready(auth: &TailscaleAuth) -> anyhow::Result<String> {
    tracing::info!("正在等待 Tailscale 准备就绪...");

    // 1. 等待获取 IPv4 地址
    let ip = match ipv4(auth).await {
        Ok(ip_addr) => {
            tracing::info!("成功获取 Tailscale IPv4 地址: {}", ip_addr);
            ip_addr
        }
        Err(e) => {
            tracing::error!("获取 Tailscale IPv4 地址失败: {}", e);
            return Err(e);
        }
    };

    // 2. 检查网络连通性
    // 默认使用 EchoWave 服务器 IP 地址 (示例，需要根据实际情况调整)
    let server_ip = "**********";
    tracing::info!("正在检查到服务器 {} 的网络连通性...", server_ip);

    match measure_server_latency(server_ip).await {
        Ok(latency) => {
            tracing::info!(
                "到服务器 {} 的网络连通性正常，延迟: {}ms",
                server_ip,
                latency
            );
        }
        Err(e) => {
            tracing::warn!("到服务器 {} 的网络连通性检查失败: {}", server_ip, e);
            // 这里我们选择只记录警告，因为有时候 ping 可能因为防火墙等原因失败
            // 但获取到 IP 地址通常意味着 Tailscale 核心服务已启动
        }
    }

    tracing::info!("Tailscale 已准备就绪");
    Ok(ip)
}
