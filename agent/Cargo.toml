[package]
name = "agent"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "agent"
path = "src/main.rs"

[features]
# 开发环境feature，允许在非Linux平台编译（仅用于IDE支持）
dev-env = []

[dependencies]
# 通过 path 引用本地的 protocol crate
protocol = { path = "../crates/protocol", features = ["compression"] }
serde_json = { workspace = true }
tokio = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = [
    "env-filter",
    "time",
    "local-time",
] }
hyper = { version = "1.6.0", features = ["full"] }
base64 = "0.22.1"
http = "1.3.1"
rustls-pemfile = "2.2.0"
serde = { version = "1.0.219", features = ["derive"] }
chrono = { workspace = true }
tokio-util = { version = "0.7.8", features = ["io"] }
libc = "0.2.174"
regex = "1.11.1"
uuid = { version = "1.0", features = ["v4", "serde"] }
tempfile = "3.20.0"
sha2 = "0.10.8"
