# EchoWave Agent - 任务引擎代理

**Linux 专用守护进程管理系统**

EchoWave Agent 是 EchoWave 客户端四模块架构中的关键组件，专门在 WSL/Linux 环境中运行，负责管理 Tailscale、Docker、Nomad 等守护进程，并与 Core 模块进行通信协调。

## 📋 功能概述

### 核心职责

根据 EchoWave 客户端架构设计，Agent 模块的核心职责包括：

- **进程管理**: 管理 Tailscale、Docker、Nomad 守护进程的生命周期
- **数据收集**: 收集任务引擎运行数据和节点状态
- **通信协调**: 通过 stdio + MessageFrame 协议与 Core 模块通信
- **环境限制**: 仅在 Linux/WSL 环境运行（编译时强制检查）

### 架构定位

```mermaid
graph TB
    subgraph "客户端核心模块 (core)"
        CORE[业务逻辑层]
        AGENT_ADAPTER[AgentAdapter]
    end
    
    subgraph "任务引擎代理模块 (agent)"
        AGENT_MAIN[Agent主进程<br/>Linux Only]
        PROC_MGR[进程管理器<br/>Tailscale/Docker/Nomad]
        DATA_COL[数据收集器<br/>状态监控]
    end
    
    subgraph "通信协议 (protocol)"
        PROTOCOL[MessageFrame 协议<br/>stdio 通信]
    end
    
    CORE --> AGENT_ADAPTER
    AGENT_ADAPTER -.stdio + protocol.-> AGENT_MAIN
    AGENT_MAIN --> PROC_MGR
    AGENT_MAIN --> DATA_COL
    
    style AGENT_MAIN fill:#e1f5fe
    style PROTOCOL fill:#f3e5f5
```

## ✅ 已实现功能

### 1. 基础架构 (完整实现)

- **平台检查**: 编译时强制 Linux 平台检查 (`#[cfg(target_os = "linux")]`)
- **单实例控制**: PID 文件管理，防止重复运行
- **优雅关闭**: 信号处理（SIGTERM、USR1）和资源清理
- **命令行支持**: `--version`, `--join` 参数处理

### 2. 通信系统 (完整实现)

- **MessageFrame 协议**: 基于 stdio 的二进制消息帧通信
- **事件分发**: 统一的 AgentEvent 处理机制
- **请求/响应**: 操作ID关联的请求响应管理
- **错误处理**: 标准化错误码和错误传播

**支持的事件类型:**
```rust
pub enum AgentEvent {
    Shutdown { trace_id: Uuid, graceful: bool },
    Start { trace_id: Uuid, daemon: String },
    Stop { trace_id: Uuid, daemon: String },
    GetNodeStatus { trace_id: Uuid },
    GetNetworkLatency { trace_id: Uuid },
}
```

### 3. 守护进程管理 (完整实现)

#### 3.1 Tailscale 管理
- **自动登录**: 支持 authkey 自动登录
- **网络配置**: IPv4 地址获取和网络连接
- **延迟测量**: 网络延迟监控功能
- **健康检查**: Socket 连接和功能性检查

#### 3.2 Docker 管理
- **守护进程启动**: Docker daemon 启动和配置
- **Harbor 登录**: 镜像仓库认证 (`harbor.echowave.cn:8443`)
- **Hosts 文件**: 动态 hosts 文件管理
- **日志解析**: Docker 日志结构化解析

#### 3.3 Nomad 管理
- **客户端配置**: HCL 配置文件动态生成
- **GPU 支持**: NVIDIA GPU 插件配置
- **服务器连接**: 连接到 Nomad 服务器 (`**********:4647`)
- **节点状态**: 节点信息和任务状态监控

### 4. 健康检查系统 (完整实现)

- **多层次检查**: Socket 连接 + 功能性验证
- **SystemD 协议**: SD_NOTIFY 支持
- **超时控制**: 可配置的检查超时时间
- **重试机制**: 失败时的自动重试

### 5. 日志系统 (完整实现)

- **结构化日志**: 基于 tracing 的结构化日志记录
- **日志转发**: 守护进程日志收集和转发
- **Trace 支持**: 全链路追踪 trace_id 支持
- **缓冲控制**: 可配置的日志缓冲策略

### 6. 启动流程 (完整实现)

根据架构设计，Agent 实现了完整的启动流程：

1. **环境检查**: 验证 Linux 环境
2. **PID 管理**: 单实例控制
3. **通信建立**: stdio 通道初始化
4. **守护进程启动**: 按依赖关系启动服务
   - Tailscale → Docker → Nomad
5. **监控循环**: 持续监控和数据收集

## ⚠️ 配置和安全问题

### 1. 硬编码配置

当前实现存在硬编码配置问题，影响部署灵活性：

- **Docker Harbor 密码**: 硬编码在源代码中
- **Tailscale authkey**: 硬编码登录凭据
- **服务器地址**: 硬编码 Nomad 服务器地址

### 2. 安全性改进空间

- **凭据管理**: 缺乏安全的凭据存储机制
- **权限控制**: 可进一步细化权限管理
- **网络安全**: 网络连接的安全性验证

## 🔧 潜在改进点

### 1. 配置外部化

```rust
// 建议的配置结构
#[derive(Debug, Deserialize)]
pub struct AgentConfig {
    pub tailscale: TailscaleConfig,
    pub docker: DockerConfig,
    pub nomad: NomadConfig,
    pub logging: LoggingConfig,
}

#[derive(Debug, Deserialize)]
pub struct TailscaleConfig {
    pub authkey: String,
    pub derp_server: String,
    pub port: u16,
}
```

### 2. 监控和度量

- **性能指标**: 守护进程资源使用监控
- **健康状态**: 更详细的健康检查状态
- **错误度量**: 错误率和恢复时间统计

### 3. 功能增强

- **配置热重载**: 运行时配置更新
- **日志轮转**: 日志文件大小和保留策略
- **网络诊断**: 更完善的网络连接诊断

## 📁 项目结构

```
agent/
├── Cargo.toml                  # 项目配置和依赖
├── Makefile                    # 构建脚本
├── README.md                   # 项目文档
├── src/
│   ├── main.rs                 # 入口点和主要逻辑
│   ├── server.rs               # 服务器任务管理
│   ├── handler.rs              # 事件处理器
│   ├── pidfile.rs              # PID 文件管理
│   ├── communication/          # 通信模块
│   │   ├── mod.rs              # 模块定义
│   │   ├── stdio.rs            # stdio 通信实现
│   │   ├── handler.rs          # 事件分发器
│   │   └── request_response.rs # 请求响应管理
│   ├── daemon/                 # 守护进程管理
│   │   ├── mod.rs              # 模块定义
│   │   ├── launcher.rs         # 守护进程启动器
│   │   ├── lifecycle.rs        # 生命周期管理
│   │   ├── health.rs           # 健康检查
│   │   └── logging.rs          # 日志管理
│   ├── docker/                 # Docker 集成
│   │   ├── mod.rs              # Docker 管理
│   │   └── hosts               # Hosts 文件模板
│   ├── nomad/                  # Nomad 集成
│   │   ├── mod.rs              # Nomad 管理
│   │   └── nomad.hcl           # Nomad 配置模板
│   ├── tailscale/              # Tailscale 集成
│   │   └── mod.rs              # Tailscale 管理
│   └── logging/                # 日志系统
│       └── mod.rs              # 日志转发
└── tests/
    └── integration_test.rs     # 集成测试
```

## 🚀 快速开始

### 编译和运行

```bash
# 构建（仅在 Linux 环境）
cd agent
cargo build --release

# 运行
./target/release/agent

# 带参数运行
./target/release/agent --version
./target/release/agent --join
```

### 使用 Makefile（WSL 环境）

```bash
# 构建并复制到 Windows 路径
make build

# 清理
make clean
```

### 环境要求

- **操作系统**: Linux 或 WSL
- **Rust**: 1.70+
- **依赖服务**: Tailscale, Docker, Nomad

## 🧪 测试

```bash
# 运行单元测试
cargo test

# 运行集成测试
cargo test --test integration_test

# 测试覆盖率
cargo tarpaulin
```

## 📊 性能指标

| 指标 | 目标值 | 当前状态   |
|------|--------|--------|
| 启动时间 | < 5s | ⚠️ 未曝光 |
| 内存占用 | < 10MB | ⚠️ 未曝光  |
| 通信延迟 | < 10ms | ⚠️ 未曝光  |
| 守护进程恢复时间 | < 30s | ⚠️ 未曝光  |

## 🔍 调试和诊断

### 日志级别

```bash
# 设置日志级别
RUST_LOG=debug ./target/release/agent
RUST_LOG=trace ./target/release/agent
```

### 常见问题

1. **权限问题**: 确保具有 Docker 和 Nomad 的执行权限
2. **网络问题**: 检查 Tailscale 连接状态
3. **依赖问题**: 确保所有依赖的守护进程已安装

### 状态查询

```bash
# 检查进程状态
ps aux | grep agent

# 检查 PID 文件
cat /tmp/agent.pid

# 检查守护进程状态
systemctl status docker
systemctl status nomad
```

## 📋 与文档对比

### 架构符合性

| 设计要求 | 实现状态 |
|----------|----------|
| Linux 专用 | ✅ 编译时强制检查 |
| stdio 通信 | ✅ MessageFrame 协议 |
| 进程管理 | ✅ 完整实现 |
| 数据收集 | ✅ 节点状态监控 |
| 优雅关闭 | ✅ 信号处理 |
| trace_id 支持 | ✅ 全链路追踪 |

### 功能完整性

根据架构文档，Agent 模块的所有核心功能都已实现：

- ✅ 守护进程生命周期管理
- ✅ 与 Core 模块的通信协调
- ✅ 任务引擎数据收集
- ✅ 网络状态监控
- ✅ 错误处理和恢复

## 📜 许可证

本项目是 EchoWave 客户端的一部分，遵循项目整体许可证。

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

---

**注意**: 此模块仅在 Linux/WSL 环境中运行，在其他平台上编译将失败。这是设计特性，不是 bug。