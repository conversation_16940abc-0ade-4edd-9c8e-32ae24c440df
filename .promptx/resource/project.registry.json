{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-04T10:02:01.549Z", "updatedAt": "2025-07-04T10:02:01.551Z", "resourceCount": 10}, "resources": [{"id": "rust-developer", "source": "project", "protocol": "role", "name": "Rust Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/rust-developer/rust-developer.role.md", "metadata": {"createdAt": "2025-07-04T10:02:01.550Z", "updatedAt": "2025-07-04T10:02:01.550Z", "scannedAt": "2025-07-04T10:02:01.550Z"}}, {"id": "memory-safety", "source": "project", "protocol": "thought", "name": "Memory Safety 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/rust-developer/thought/memory-safety.thought.md", "metadata": {"createdAt": "2025-07-04T10:02:01.550Z", "updatedAt": "2025-07-04T10:02:01.550Z", "scannedAt": "2025-07-04T10:02:01.550Z"}}, {"id": "performance-optimization", "source": "project", "protocol": "thought", "name": "Performance Optimization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/rust-developer/thought/performance-optimization.thought.md", "metadata": {"createdAt": "2025-07-04T10:02:01.550Z", "updatedAt": "2025-07-04T10:02:01.550Z", "scannedAt": "2025-07-04T10:02:01.550Z"}}, {"id": "rust-systems-thinking", "source": "project", "protocol": "thought", "name": "Rust Systems Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/rust-developer/thought/rust-systems-thinking.thought.md", "metadata": {"createdAt": "2025-07-04T10:02:01.550Z", "updatedAt": "2025-07-04T10:02:01.550Z", "scannedAt": "2025-07-04T10:02:01.550Z"}}, {"id": "code-quality-standards", "source": "project", "protocol": "execution", "name": "Code Quality Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/rust-developer/execution/code-quality-standards.execution.md", "metadata": {"createdAt": "2025-07-04T10:02:01.550Z", "updatedAt": "2025-07-04T10:02:01.551Z", "scannedAt": "2025-07-04T10:02:01.550Z"}}, {"id": "rust-development-workflow", "source": "project", "protocol": "execution", "name": "Rust Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/rust-developer/execution/rust-development-workflow.execution.md", "metadata": {"createdAt": "2025-07-04T10:02:01.551Z", "updatedAt": "2025-07-04T10:02:01.551Z", "scannedAt": "2025-07-04T10:02:01.551Z"}}, {"id": "system-integration", "source": "project", "protocol": "execution", "name": "System Integration 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/rust-developer/execution/system-integration.execution.md", "metadata": {"createdAt": "2025-07-04T10:02:01.551Z", "updatedAt": "2025-07-04T10:02:01.551Z", "scannedAt": "2025-07-04T10:02:01.551Z"}}, {"id": "echowave-architecture", "source": "project", "protocol": "knowledge", "name": "Echowave Architecture 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/rust-developer/knowledge/echowave-architecture.knowledge.md", "metadata": {"createdAt": "2025-07-04T10:02:01.551Z", "updatedAt": "2025-07-04T10:02:01.551Z", "scannedAt": "2025-07-04T10:02:01.551Z"}}, {"id": "performance-engineering", "source": "project", "protocol": "knowledge", "name": "Performance Engineering 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/rust-developer/knowledge/performance-engineering.knowledge.md", "metadata": {"createdAt": "2025-07-04T10:02:01.551Z", "updatedAt": "2025-07-04T10:02:01.551Z", "scannedAt": "2025-07-04T10:02:01.551Z"}}, {"id": "rust-ecosystem", "source": "project", "protocol": "knowledge", "name": "Rust Ecosystem 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/rust-developer/knowledge/rust-ecosystem.knowledge.md", "metadata": {"createdAt": "2025-07-04T10:02:01.551Z", "updatedAt": "2025-07-04T10:02:01.551Z", "scannedAt": "2025-07-04T10:02:01.551Z"}}], "stats": {"totalResources": 10, "byProtocol": {"role": 1, "thought": 3, "execution": 3, "knowledge": 3}, "bySource": {"project": 10}}}