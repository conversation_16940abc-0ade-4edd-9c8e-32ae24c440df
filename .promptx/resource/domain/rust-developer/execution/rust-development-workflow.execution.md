<execution>
  <constraint>
    ## 开发环境约束
    - **Rust版本**：使用项目指定的Rust版本，避免版本兼容问题
    - **依赖管理**：使用工作区级别的依赖管理，避免版本冲突
    - **平台兼容**：确保代码在Windows和WSL环境下都能正常运行
    - **内存限制**：代码必须在有限内存环境下高效运行
    - **性能要求**：API响应时间必须<10ms，空闲CPU使用<5%
  </constraint>

  <rule>
    ## 强制性开发规则
    - **零panic原则**：生产代码绝对不能panic，必须使用Result进行错误处理
    - **借用检查通过**：所有代码必须通过借用检查器验证
    - **Clippy零警告**：所有代码必须通过Clippy检查无警告
    - **测试覆盖率**：核心功能必须有单元测试覆盖
    - **文档完整性**：公共API必须有完整的文档注释
    - **unsafe最小化**：unsafe代码必须有详细的安全性注释
  </rule>

  <guideline>
    ## 开发指导原则
    - **类型驱动开发**：先设计类型系统，再实现逻辑
    - **错误处理优先**：优先考虑错误处理策略和错误类型设计
    - **性能意识**：在开发过程中持续关注性能影响
    - **可测试性**：编写易于测试的代码结构
    - **文档同步**：代码变更时同步更新文档
  </guideline>

  <process>
    ## Rust开发工作流程
    
    ### Phase 1: 需求分析和设计 (20%)
    
    ```mermaid
    flowchart TD
        A[需求分析] --> B[性能要求分析]
        B --> C[安全要求分析]
        C --> D[架构设计]
        D --> E[API设计]
        E --> F[类型系统设计]
        F --> G[错误处理设计]
        G --> H[测试策略设计]
    ```
    
    **关键输出**：
    - 性能指标定义
    - 安全边界定义
    - 模块架构图
    - API接口规范
    - 核心数据类型定义
    
    ### Phase 2: 核心实现 (50%)
    
    ```mermaid
    flowchart TD
        A[创建项目结构] --> B[实现核心类型]
        B --> C[实现核心逻辑]
        C --> D[错误处理实现]
        D --> E[异步代码实现]
        E --> F[性能优化]
        F --> G[安全检查]
        G --> H[集成测试]
    ```
    
    **实现检查清单**：
    - [ ] 类型定义完整且正确
    - [ ] 所有权关系清晰
    - [ ] 错误处理覆盖所有情况
    - [ ] 异步代码无死锁风险
    - [ ] 性能满足要求
    - [ ] 内存安全保证
    
    ### Phase 3: 测试和文档 (20%)
    
    ```mermaid
    flowchart TD
        A[单元测试] --> B[集成测试]
        B --> C[性能测试]
        C --> D[安全测试]
        D --> E[文档编写]
        E --> F[代码审查]
        F --> G[发布准备]
    ```
    
    **测试策略**：
    - **单元测试**：每个函数的核心逻辑测试
    - **集成测试**：模块间交互测试
    - **性能测试**：基准测试和压力测试
    - **安全测试**：边界条件和异常情况
    - **内存测试**：使用valgrind检测内存问题
    
    ### Phase 4: 部署和监控 (10%)
    
    ```mermaid
    flowchart TD
        A[构建优化] --> B[部署测试]
        B --> C[性能监控]
        C --> D[错误监控]
        D --> E[日志收集]
        E --> F[性能分析]
        F --> G[持续优化]
    ```
    
    ## 日常开发工作流
    
    ### 每日开发循环
    ```mermaid
    graph LR
        A[拉取代码] --> B[运行测试]
        B --> C[实现功能]
        C --> D[编写测试]
        D --> E[性能检查]
        E --> F[代码审查]
        F --> G[提交代码]
        G --> A
    ```
    
    ### 核心开发命令
    ```bash
    # 开发环境检查
    cargo check                    # 快速语法检查
    cargo clippy                   # 代码质量检查
    cargo fmt                      # 代码格式化
    
    # 测试相关
    cargo test                     # 运行所有测试
    cargo test --release           # 发布模式测试
    cargo bench                    # 性能基准测试
    
    # 构建相关
    cargo build                    # 调试构建
    cargo build --release          # 发布构建
    cargo run -p <crate>          # 运行特定crate
    
    # 分析工具
    cargo flamegraph              # 性能分析
    cargo audit                   # 安全审计
    cargo outdated                # 依赖更新检查
    ```
    
    ### 代码质量检查流程
    ```mermaid
    flowchart TD
        A[代码提交] --> B{Clippy检查}
        B -->|通过| C{格式化检查}
        B -->|失败| D[修复Clippy警告]
        C -->|通过| E{测试检查}
        C -->|失败| F[运行cargo fmt]
        E -->|通过| G{性能检查}
        E -->|失败| H[修复测试]
        G -->|通过| I[提交到仓库]
        G -->|失败| J[性能优化]
        
        D --> B
        F --> C
        H --> E
        J --> G
    ```
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 代码质量标准
    - ✅ **编译通过**：所有代码必须无警告编译通过
    - ✅ **Clippy通过**：无任何Clippy警告
    - ✅ **测试覆盖**：核心功能测试覆盖率>80%
    - ✅ **文档完整**：公共API文档覆盖率100%
    - ✅ **格式规范**：代码格式符合rustfmt标准
    
    ### 性能标准
    - ✅ **响应时间**：API响应时间<10ms
    - ✅ **CPU使用率**：空闲时CPU使用<5%
    - ✅ **内存使用**：内存使用合理，无内存泄漏
    - ✅ **并发性能**：多线程性能线性扩展
    
    ### 安全标准
    - ✅ **内存安全**：无段错误、无内存泄漏
    - ✅ **线程安全**：无数据竞争、无死锁
    - ✅ **错误处理**：所有错误情况都得到妥善处理
    - ✅ **输入验证**：所有外部输入都经过验证
    
    ### 可维护性标准
    - ✅ **代码结构**：模块划分清晰，职责明确
    - ✅ **接口设计**：API设计简洁易用
    - ✅ **错误信息**：错误信息清晰有用
    - ✅ **日志记录**：关键操作有适当的日志记录
  </criteria>
</execution>