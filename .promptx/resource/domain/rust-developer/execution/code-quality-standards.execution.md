<execution>
  <constraint>
    ## 代码质量技术约束
    - **编译器版本**：使用工作区指定的Rust版本，确保一致性
    - **依赖限制**：严格控制依赖数量和版本，避免供应链风险
    - **平台兼容**：代码必须在Windows和Linux环境下都能正常运行
    - **构建时间**：增量编译时间近可能的快
    - **二进制大小**：发布二进制大小必须合理控制
  </constraint>

  <rule>
    ## 代码质量强制规则
    - **零警告原则**：所有代码必须零编译器警告
    - **Clippy强制**：所有代码必须通过Clippy检查
    - **格式统一**：所有代码必须使用rustfmt格式化
    - **测试必须**：核心功能必须有对应的测试
    - **文档必须**：公共API必须有完整的rustdoc文档
    - **错误类型**：使用thiserror定义错误类型
    - **日志规范**：使用tracing crate记录结构化日志
  </rule>

  <guideline>
    ## 代码质量指导原则
    - **可读性优先**：代码首先要易于阅读和理解
    - **简洁性原则**：避免过度工程化，保持代码简洁
    - **依赖来源明确**：必需明确每一个导入和导出项，禁止使用 `*` 来批量导入或导出
    - **一致性原则**：在整个项目中保持编码风格一致
    - **性能意识**：在不影响可读性的前提下优化性能
    - **安全优先**：安全性优于性能，性能优于便利性
  </guideline>

  <process>
    ## 代码质量保证流程
    
    ### 编码规范
    
    #### 1. 命名规范
    ```rust
    // 类型使用PascalCase
    struct UserSession { }
    enum ConnectionState { }
    
    // 函数和变量使用snake_case
    fn process_request() { }
    let user_id = 123;
    
    // 常量使用SCREAMING_SNAKE_CASE
    const MAX_CONNECTIONS: usize = 1000;
    
    // 生命周期使用简短的小写字母
    fn process<'a>(data: &'a str) -> &'a str { }
    ```
    
    #### 2. 错误处理规范
    ```rust
    // 使用thiserror定义错误类型
    use thiserror::Error;
    
    #[derive(Error, Debug)]
    pub enum ProcessError {
        #[error("IO error: {0}")]
        Io(#[from] std::io::Error),
        #[error("Invalid configuration: {msg}")]
        Config { msg: String },
        #[error("Network timeout")]
        Timeout,
    }
    
    // 使用anyhow处理错误传播
    use anyhow::{Context, Result};
    
    fn process_file(path: &str) -> Result<String> {
        std::fs::read_to_string(path)
            .with_context(|| format!("Failed to read file: {}", path))
    }
    ```
    
    #### 3. 异步代码规范
    ```rust
    // 优先使用async/await
    async fn fetch_data(url: &str) -> Result<String> {
        let response = reqwest::get(url)
            .await
            .context("Failed to fetch data")?;
        
        let content = response.text()
            .await
            .context("Failed to read response")?;
        
        Ok(content)
    }
    
    // 合理使用tokio::spawn
    let handle = tokio::spawn(async move {
        // 异步任务逻辑
    });
    ```
    
    #### 4. 日志记录规范
    ```rust
    // 使用结构化日志
    tracing::info!(
        user_id = %user.id,
        action = "login",
        "User logged in successfully"
    );
    
    // 错误日志包含足够的上下文
    tracing::error!(
        error = %err,
        path = %file_path,
        "Failed to process file"
    );
    ```
    
    ### 代码审查检查清单
    
    #### 基础检查
    - [ ] 代码编译无警告
    - [ ] 通过Clippy检查
    - [ ] 代码已格式化
    - [ ] 测试覆盖核心功能
    - [ ] 文档完整且准确
    
    #### 设计检查
    - [ ] 模块职责单一明确
    - [ ] 接口设计合理
    - [ ] 错误处理完整
    - [ ] 资源管理正确
    - [ ] 并发安全保证
    
    #### 性能检查
    - [ ] 无不必要的内存分配
    - [ ] 算法复杂度合理
    - [ ] 无阻塞操作
    - [ ] 并发性能良好
    - [ ] 内存使用合理
    
    ### 自动化质量检查
    
    ```mermaid
    flowchart TD
        A[代码提交] --> B[cargo check]
        B --> C[cargo clippy]
        C --> D[cargo fmt --check]
        D --> E[cargo test]
        E --> F[cargo doc]
        F --> G[性能基准测试]
        G --> H[安全审计]
        H --> I[代码覆盖率]
        I --> J[质量报告]
        
        B -->|失败| K[修复编译错误]
        C -->|失败| L[修复Clippy警告]
        D -->|失败| M[格式化代码]
        E -->|失败| N[修复测试]
        F -->|失败| O[修复文档]
        
        K --> B
        L --> C
        M --> D
        N --> E
        O --> F
    ```
    
    ### 质量度量
    
    #### 代码质量指标
    ```rust
    // 示例：复杂度控制
    // 函数复杂度应该 < 10
    fn process_request(request: Request) -> Result<Response> {
        // 实现应该简洁明了
        // 复杂逻辑应该拆分为多个函数
    }
    
    // 模块行数应该 < 500行
    // 函数行数应该 < 50行
    // 单个文件行数应该 < 1000行
    ```
    
    #### 性能质量指标
    ```rust
    // 关键路径性能要求
    #[bench]
    fn bench_critical_path(b: &mut Bencher) {
        b.iter(|| {
            // 关键路径应该 < 1ms
            critical_function(black_box(test_data))
        });
    }
    ```
    
    ### 持续改进流程
    
    ```mermaid
    graph LR
        A[质量度量] --> B[问题识别]
        B --> C[改进计划]
        C --> D[实施改进]
        D --> E[效果验证]
        E --> A
        
        B --> F[技术债务管理]
        F --> C
        
        E --> G[最佳实践更新]
        G --> H[团队培训]
        H --> A
    ```
  </process>

  <criteria>
    ## 代码质量评价标准
    
    ### 编译和检查标准
    - ✅ **编译通过**：零警告编译成功
    - ✅ **Clippy通过**：无任何Clippy警告
    - ✅ **格式规范**：代码格式符合rustfmt标准
    - ✅ **文档完整**：公共API文档覆盖率100%
    
    ### 代码结构标准
    - ✅ **模块化**：模块职责单一，接口清晰
    - ✅ **函数大小**：函数行数<50行，复杂度<10
    - ✅ **文件大小**：单个文件<1000行
    - ✅ **依赖管理**：依赖数量合理，版本锁定
    
    ### 错误处理标准
    - ✅ **错误类型**：使用thiserror定义错误类型
    - ✅ **错误传播**：使用anyhow处理错误链
    - ✅ **错误信息**：错误信息清晰有用
    - ✅ **恢复策略**：关键错误有恢复机制
    
    ### 性能标准
    - ✅ **内存效率**：无内存泄漏，分配合理
    - ✅ **CPU效率**：关键路径性能优化
    - ✅ **并发性能**：多线程性能线性扩展
    - ✅ **I/O性能**：异步I/O高效利用
    
    ### 安全标准
    - ✅ **内存安全**：通过借用检查器验证
    - ✅ **线程安全**：无数据竞争和死锁
    - ✅ **输入验证**：所有外部输入经过验证
    - ✅ **依赖安全**：定期进行安全审计
    
    ### 可维护性标准
    - ✅ **代码可读性**：代码清晰易懂
    - ✅ **测试覆盖**：核心功能测试覆盖率>80%
    - ✅ **文档同步**：文档与代码保持同步
    - ✅ **版本控制**：Git历史清晰，提交信息明确
  </criteria>
</execution>