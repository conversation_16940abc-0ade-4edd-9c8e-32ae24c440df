<execution>
  <constraint>
    ## 系统集成技术约束
    - **平台兼容性**：必须同时支持Windows和WSL环境
    - **FFI安全性**：与C代码交互必须保证内存安全
    - **性能要求**：系统调用延迟必须<1ms
    - **资源限制**：文件句柄、网络连接等系统资源有限
    - **权限限制**：在受限权限环境下正常工作
  </constraint>

  <rule>
    ## 系统集成强制规则
    - **错误处理覆盖**：所有系统调用必须处理错误情况
    - **资源清理**：所有系统资源必须正确释放
    - **超时控制**：所有阻塞操作必须有超时控制
    - **权限检查**：操作前必须检查必要的权限
    - **平台特定代码隔离**：平台特定代码必须用cfg条件编译隔离
  </rule>

  <guideline>
    ## 系统集成指导原则
    - **最小权限原则**：只请求必要的系统权限
    - **优雅降级**：系统功能不可用时提供替代方案
    - **资源池化**：复用系统资源，避免频繁创建销毁
    - **异步优先**：优先使用异步I/O避免阻塞
    - **错误恢复**：系统错误后能够自动恢复
  </guideline>

  <process>
    ## 系统集成实施流程
    
    ### Phase 1: 环境适配 (25%)
    
    ```mermaid
    flowchart TD
        A[平台检测] --> B[权限检查]
        B --> C[依赖检查]
        C --> D[配置加载]
        D --> E[系统资源初始化]
        E --> F[兼容性测试]
        F --> G[环境验证完成]
    ```
    
    #### 平台适配代码模式
    ```rust
    // 平台特定功能隔离
    #[cfg(target_os = "windows")]
    mod windows_specific {
        pub fn get_system_info() -> SystemInfo {
            // Windows特定实现
        }
    }
    
    #[cfg(target_os = "linux")]
    mod linux_specific {
        pub fn get_system_info() -> SystemInfo {
            // Linux/WSL特定实现
        }
    }
    
    // 统一接口
    pub fn get_system_info() -> SystemInfo {
        #[cfg(target_os = "windows")]
        return windows_specific::get_system_info();
        
        #[cfg(target_os = "linux")]
        return linux_specific::get_system_info();
    }
    ```
    
    ### Phase 2: WSL集成 (30%)
    
    ```mermaid
    flowchart TD
        A[WSL环境检测] --> B[WSL版本检查]
        B --> C[网络配置检查]
        C --> D[文件系统映射]
        D --> E[进程间通信设置]
        E --> F[资源共享配置]
        F --> G[WSL集成完成]
    ```
    
    #### WSL交互模式
    ```rust
    use std::process::Command;
    use tokio::process::Command as AsyncCommand;
    
    pub struct WSLManager {
        distro_name: String,
        wsl_executable: PathBuf,
    }
    
    impl WSLManager {
        pub async fn execute_command(&self, cmd: &str) -> Result<String> {
            let output = AsyncCommand::new(&self.wsl_executable)
                .args(&["-d", &self.distro_name, "sh", "-c", cmd])
                .output()
                .await
                .context("Failed to execute WSL command")?;
                
            if !output.status.success() {
                return Err(anyhow::anyhow!(
                    "WSL command failed: {}", 
                    String::from_utf8_lossy(&output.stderr)
                ));
            }
            
            Ok(String::from_utf8_lossy(&output.stdout).to_string())
        }
        
        pub async fn file_exists(&self, path: &str) -> Result<bool> {
            let cmd = format!("test -f '{}'", path.replace("'", "'\"'\"'"));
            let result = self.execute_command(&cmd).await;
            Ok(result.is_ok())
        }
    }
    ```
    
    ### Phase 3: 进程管理 (25%)
    
    ```mermaid
    flowchart TD
        A[进程启动] --> B[PID管理]
        B --> C[进程监控]
        C --> D[资源监控]
        D --> E[健康检查]
        E --> F[进程重启]
        F --> G[优雅关闭]
    ```
    
    #### 进程管理实现
    ```rust
    use tokio::process::{Child, Command};
    use sysinfo::{System, SystemExt, ProcessExt};
    
    pub struct ProcessManager {
        processes: HashMap<String, ProcessInfo>,
        system: System,
    }
    
    struct ProcessInfo {
        child: Option<Child>,
        pid: Option<u32>,
        start_time: SystemTime,
        restart_count: u32,
    }
    
    impl ProcessManager {
        pub async fn start_process(&mut self, name: &str, cmd: Command) -> Result<()> {
            let mut child = cmd.spawn()
                .context("Failed to start process")?;
                
            let pid = child.id();
            
            let process_info = ProcessInfo {
                child: Some(child),
                pid,
                start_time: SystemTime::now(),
                restart_count: 0,
            };
            
            self.processes.insert(name.to_string(), process_info);
            
            info!(process = %name, pid = ?pid, "Process started");
            Ok(())
        }
        
        pub async fn monitor_processes(&mut self) -> Result<()> {
            self.system.refresh_processes();
            
            for (name, info) in &mut self.processes {
                if let Some(pid) = info.pid {
                    if let Some(process) = self.system.process(pid.into()) {
                        let cpu_usage = process.cpu_usage();
                        let memory = process.memory();
                        
                        debug!(
                            process = %name,
                            cpu = %cpu_usage,
                            memory = %memory,
                            "Process status"
                        );
                        
                        // 检查进程健康状态
                        if cpu_usage > 90.0 {
                            warn!(process = %name, "High CPU usage detected");
                        }
                    } else {
                        warn!(process = %name, "Process not found, may have crashed");
                        // 触发重启逻辑
                    }
                }
            }
            
            Ok(())
        }
    }
    ```
    
    ### Phase 4: 网络集成 (20%)
    
    ```mermaid
    flowchart TD
        A[网络配置] --> B[连接池管理]
        B --> C[负载均衡]
        C --> D[故障转移]
        D --> E[性能监控]
        E --> F[连接优化]
    ```
    
    #### 网络管理实现
    ```rust
    use reqwest::{Client, ClientBuilder};
    use std::time::Duration;
    
    pub struct NetworkManager {
        client: Client,
        connection_pool_size: usize,
        timeout: Duration,
    }
    
    impl NetworkManager {
        pub fn new() -> Result<Self> {
            let client = ClientBuilder::new()
                .pool_max_idle_per_host(10)
                .pool_idle_timeout(Duration::from_secs(30))
                .timeout(Duration::from_secs(10))
                .tcp_keepalive(Duration::from_secs(60))
                .build()
                .context("Failed to create HTTP client")?;
                
            Ok(Self {
                client,
                connection_pool_size: 10,
                timeout: Duration::from_secs(10),
            })
        }
        
        pub async fn make_request(&self, url: &str) -> Result<String> {
            let response = self.client
                .get(url)
                .timeout(self.timeout)
                .send()
                .await
                .context("HTTP request failed")?;
                
            if !response.status().is_success() {
                return Err(anyhow::anyhow!(
                    "HTTP error: {}", 
                    response.status()
                ));
            }
            
            let text = response.text()
                .await
                .context("Failed to read response body")?;
                
            Ok(text)
        }
    }
    ```
    
    ## 系统资源管理
    
    ### 文件系统操作
    ```rust
    use tokio::fs;
    use std::path::PathBuf;
    
    pub struct FileManager {
        base_path: PathBuf,
        temp_dir: PathBuf,
    }
    
    impl FileManager {
        pub async fn safe_write(&self, relative_path: &str, content: &[u8]) -> Result<()> {
            let full_path = self.base_path.join(relative_path);
            
            // 确保路径在允许的目录内
            if !full_path.starts_with(&self.base_path) {
                return Err(anyhow::anyhow!("Path traversal detected"));
            }
            
            // 创建父目录
            if let Some(parent) = full_path.parent() {
                fs::create_dir_all(parent).await
                    .context("Failed to create parent directory")?;
            }
            
            // 原子写入
            let temp_path = self.temp_dir.join(format!(
                "temp_{}", 
                uuid::Uuid::new_v4()
            ));
            
            fs::write(&temp_path, content).await
                .context("Failed to write temporary file")?;
                
            fs::rename(&temp_path, &full_path).await
                .context("Failed to move file to final location")?;
                
            Ok(())
        }
    }
    ```
    
    ### 系统监控
    ```rust
    use sysinfo::{System, SystemExt, CpuExt, DiskExt};
    
    pub struct SystemMonitor {
        system: System,
        alert_thresholds: AlertThresholds,
    }
    
    struct AlertThresholds {
        cpu_usage: f32,
        memory_usage: f32,
        disk_usage: f32,
    }
    
    impl SystemMonitor {
        pub fn collect_metrics(&mut self) -> SystemMetrics {
            self.system.refresh_all();
            
            let cpu_usage = self.system.global_cpu_info().cpu_usage();
            let memory_usage = (self.system.used_memory() as f32 / 
                              self.system.total_memory() as f32) * 100.0;
            
            let disk_usage = self.system.disks()
                .iter()
                .map(|disk| {
                    let total = disk.total_space();
                    let available = disk.available_space();
                    ((total - available) as f32 / total as f32) * 100.0
                })
                .max_by(|a, b| a.partial_cmp(b).unwrap_or(std::cmp::Ordering::Equal))
                .unwrap_or(0.0);
            
            SystemMetrics {
                cpu_usage,
                memory_usage,
                disk_usage,
                timestamp: SystemTime::now(),
            }
        }
    }
    ```
  </process>

  <criteria>
    ## 系统集成质量标准
    
    ### 平台兼容性标准
    - ✅ **Windows兼容**：在Windows 10/11上正常运行
    - ✅ **WSL兼容**：在WSL2环境下正常运行
    - ✅ **跨平台构建**：单一代码库支持多平台
    - ✅ **版本兼容**：支持不同版本的系统组件
    
    ### 性能标准
    - ✅ **系统调用延迟**：<1ms响应时间
    - ✅ **资源使用**：内存使用合理，CPU占用<5%
    - ✅ **并发处理**：支持多个并发系统操作
    - ✅ **网络性能**：HTTP请求<100ms响应时间
    
    ### 可靠性标准
    - ✅ **错误处理**：所有系统调用都有错误处理
    - ✅ **资源清理**：系统资源正确释放
    - ✅ **故障恢复**：系统故障后能自动恢复
    - ✅ **监控告警**：异常情况及时发现和处理
    
    ### 安全标准
    - ✅ **权限控制**：最小权限原则
    - ✅ **输入验证**：所有外部输入验证
    - ✅ **路径安全**：防止路径遍历攻击
    - ✅ **进程隔离**：进程间安全隔离
    
    ### 维护性标准
    - ✅ **日志记录**：关键操作有详细日志
    - ✅ **配置管理**：系统配置外部化
    - ✅ **监控指标**：系统状态可观测
    - ✅ **文档完整**：集成过程有详细文档
  </criteria>
</execution>