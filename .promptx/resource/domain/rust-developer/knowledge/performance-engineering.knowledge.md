# 性能工程专业知识

## 性能分析基础

### 性能指标体系
```rust
// 关键性能指标定义
pub struct PerformanceMetrics {
    // 延迟指标
    pub p50_latency: Duration,      // 50分位延迟
    pub p95_latency: Duration,      // 95分位延迟  
    pub p99_latency: Duration,      // 99分位延迟
    pub max_latency: Duration,      // 最大延迟
    
    // 吞吐量指标
    pub requests_per_second: f64,   // 每秒请求数
    pub tasks_per_minute: f64,      // 每分钟任务数
    
    // 资源使用指标
    pub cpu_usage_percent: f32,     // CPU使用率
    pub memory_usage_bytes: u64,    // 内存使用量
    pub heap_allocations: u64,      // 堆分配次数
    pub gc_pressure: f32,           // GC压力
    
    // 系统指标
    pub file_descriptors: u32,      // 文件描述符数量
    pub network_connections: u32,   // 网络连接数
    pub disk_io_ops: u64,          // 磁盘IO操作数
}
```

### 性能基准测试
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion, Throughput};
use std::time::Duration;

// 微基准测试
fn hash_map_benchmark(c: &mut Criterion) {
    let mut group = c.benchmark_group("hash_map");
    
    // 不同大小的性能测试
    for size in [100, 1000, 10000].iter() {
        group.throughput(Throughput::Elements(*size as u64));
        group.bench_with_input(
            BenchmarkId::new("insert", size),
            size,
            |b, &size| {
                b.iter(|| {
                    let mut map = HashMap::new();
                    for i in 0..size {
                        map.insert(black_box(i), black_box(i * 2));
                    }
                    map
                });
            },
        );
    }
    group.finish();
}

// 集成基准测试
fn end_to_end_benchmark(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    
    c.bench_function("process_request_e2e", |b| {
        b.to_async(&rt).iter(|| async {
            let request = create_test_request();
            let response = process_request(black_box(request)).await;
            black_box(response)
        });
    });
}

criterion_group!(benches, hash_map_benchmark, end_to_end_benchmark);
criterion_main!(benches);
```

## 内存优化技术

### 内存分配优化
```rust
use std::alloc::{GlobalAlloc, Layout, System};
use std::sync::atomic::{AtomicUsize, Ordering};

// 自定义分配器监控
pub struct MonitoringAllocator;

static ALLOCATED: AtomicUsize = AtomicUsize::new(0);
static DEALLOCATED: AtomicUsize = AtomicUsize::new(0);

unsafe impl GlobalAlloc for MonitoringAllocator {
    unsafe fn alloc(&self, layout: Layout) -> *mut u8 {
        let ptr = System.alloc(layout);
        if !ptr.is_null() {
            ALLOCATED.fetch_add(layout.size(), Ordering::Relaxed);
        }
        ptr
    }
    
    unsafe fn dealloc(&self, ptr: *mut u8, layout: Layout) {
        System.dealloc(ptr, layout);
        DEALLOCATED.fetch_add(layout.size(), Ordering::Relaxed);
    }
}

#[global_allocator]
static GLOBAL: MonitoringAllocator = MonitoringAllocator;

// 内存使用统计
pub fn get_memory_stats() -> (usize, usize) {
    (
        ALLOCATED.load(Ordering::Relaxed),
        DEALLOCATED.load(Ordering::Relaxed),
    )
}
```

### 零拷贝技术
```rust
use std::io::{IoSlice, IoSliceMut};
use bytes::{Buf, BufMut, Bytes, BytesMut};

// 零拷贝缓冲区管理
pub struct ZeroCopyBuffer {
    data: Bytes,
}

impl ZeroCopyBuffer {
    pub fn new(data: Bytes) -> Self {
        Self { data }
    }
    
    // 零拷贝分片
    pub fn slice(&self, range: std::ops::Range<usize>) -> Bytes {
        self.data.slice(range)
    }
    
    // 零拷贝克隆
    pub fn clone(&self) -> Self {
        Self {
            data: self.data.clone(), // Bytes的clone是零拷贝的
        }
    }
}

// 向量化I/O
pub async fn vectored_write(
    writer: &mut impl tokio::io::AsyncWrite + Unpin,
    buffers: &[Bytes],
) -> tokio::io::Result<usize> {
    let io_slices: Vec<IoSlice> = buffers
        .iter()
        .map(|b| IoSlice::new(b))
        .collect();
    
    writer.write_vectored(&io_slices).await
}
```

### 内存池技术
```rust
use std::sync::{Arc, Mutex};
use std::collections::VecDeque;

// 对象池
pub struct ObjectPool<T> {
    objects: Arc<Mutex<VecDeque<T>>>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
}

impl<T> ObjectPool<T> {
    pub fn new<F>(capacity: usize, factory: F) -> Self 
    where 
        F: Fn() -> T + Send + Sync + 'static,
    {
        let mut objects = VecDeque::with_capacity(capacity);
        for _ in 0..capacity {
            objects.push_back(factory());
        }
        
        Self {
            objects: Arc::new(Mutex::new(objects)),
            factory: Box::new(factory),
        }
    }
    
    pub fn acquire(&self) -> PooledObject<T> {
        let obj = self.objects
            .lock()
            .unwrap()
            .pop_front()
            .unwrap_or_else(|| (self.factory)());
            
        PooledObject {
            object: Some(obj),
            pool: self.objects.clone(),
        }
    }
}

pub struct PooledObject<T> {
    object: Option<T>,
    pool: Arc<Mutex<VecDeque<T>>>,
}

impl<T> Drop for PooledObject<T> {
    fn drop(&mut self) {
        if let Some(obj) = self.object.take() {
            self.pool.lock().unwrap().push_back(obj);
        }
    }
}

impl<T> std::ops::Deref for PooledObject<T> {
    type Target = T;
    
    fn deref(&self) -> &Self::Target {
        self.object.as_ref().unwrap()
    }
}
```

## CPU优化技术

### 缓存友好的数据结构
```rust
// Structure of Arrays (SoA) 模式
#[derive(Debug)]
pub struct ParticleSystemSoA {
    positions_x: Vec<f32>,
    positions_y: Vec<f32>,
    positions_z: Vec<f32>,
    velocities_x: Vec<f32>,
    velocities_y: Vec<f32>,
    velocities_z: Vec<f32>,
    masses: Vec<f32>,
}

impl ParticleSystemSoA {
    // 缓存友好的批量操作
    pub fn update_positions(&mut self, dt: f32) {
        for i in 0..self.positions_x.len() {
            self.positions_x[i] += self.velocities_x[i] * dt;
            self.positions_y[i] += self.velocities_y[i] * dt;
            self.positions_z[i] += self.velocities_z[i] * dt;
        }
    }
}

// 内存对齐优化
#[repr(align(64))]  // 64字节对齐，匹配缓存行大小
pub struct CacheLineAligned<T>(pub T);

#[repr(C)]
#[derive(Debug)]
pub struct OptimizedStruct {
    // 按大小排序减少内存填充
    large_field: u64,
    medium_field: u32,
    small_field: u16,
    tiny_field: u8,
    // 编译器会自动添加7字节填充使结构体大小为16
}
```

### SIMD优化
```rust
// 使用portable_simd进行向量化计算
#![feature(portable_simd)]
use std::simd::{f32x8, Simd};

pub fn simd_dot_product(a: &[f32], b: &[f32]) -> f32 {
    assert_eq!(a.len(), b.len());
    
    let chunks = a.len() / 8;
    let mut sum = f32x8::splat(0.0);
    
    // 向量化主循环
    for i in 0..chunks {
        let offset = i * 8;
        let va = f32x8::from_slice(&a[offset..offset + 8]);
        let vb = f32x8::from_slice(&b[offset..offset + 8]);
        sum += va * vb;
    }
    
    // 处理剩余元素
    let mut scalar_sum = sum.reduce_sum();
    for i in (chunks * 8)..a.len() {
        scalar_sum += a[i] * b[i];
    }
    
    scalar_sum
}

// 使用目标特定内在函数
#[cfg(target_arch = "x86_64")]
pub fn optimized_sum(data: &[f32]) -> f32 {
    use std::arch::x86_64::*;
    
    unsafe {
        let mut sum = _mm256_setzero_ps();
        let chunks = data.len() / 8;
        
        for i in 0..chunks {
            let offset = i * 8;
            let values = _mm256_loadu_ps(data.as_ptr().add(offset));
            sum = _mm256_add_ps(sum, values);
        }
        
        // 水平求和
        let sum_high = _mm256_extractf128_ps(sum, 1);
        let sum_low = _mm256_castps256_ps128(sum);
        let sum128 = _mm_add_ps(sum_high, sum_low);
        
        let sum_high64 = _mm_movehl_ps(sum128, sum128);
        let sum64 = _mm_add_ps(sum128, sum_high64);
        
        let sum_high32 = _mm_shuffle_ps(sum64, sum64, 0x1);
        let result = _mm_add_ss(sum64, sum_high32);
        
        _mm_cvtss_f32(result)
    }
}
```

### 分支预测优化
```rust
// 分支预测提示
#[inline(always)]
pub fn likely(condition: bool) -> bool {
    // 现代编译器通常能自动优化
    condition
}

#[inline(always)]
pub fn unlikely(condition: bool) -> bool {
    condition
}

// 减少分支的算法设计
pub fn branchless_min(a: i32, b: i32) -> i32 {
    // 无分支实现
    a ^ ((a ^ b) & -((a > b) as i32))
}

pub fn branchless_abs(x: i32) -> i32 {
    // 无分支绝对值
    let mask = x >> 31;
    (x + mask) ^ mask
}

// 查找表替代复杂分支
const SINE_TABLE: [f32; 256] = {
    let mut table = [0.0; 256];
    let mut i = 0;
    while i < 256 {
        table[i] = (i as f32 * std::f32::consts::PI * 2.0 / 256.0).sin();
        i += 1;
    }
    table
};

pub fn fast_sine(x: f32) -> f32 {
    let normalized = (x / (2.0 * std::f32::consts::PI)) % 1.0;
    let index = (normalized * 256.0) as usize & 255;
    SINE_TABLE[index]
}
```

## 异步性能优化

### 异步任务调度优化
```rust
use tokio::runtime::{Builder, Runtime};
use tokio::task::JoinSet;
use std::future::Future;

// 自定义运行时配置
pub fn create_optimized_runtime() -> Runtime {
    Builder::new_multi_thread()
        .worker_threads(num_cpus::get())
        .thread_name("echowave-worker")
        .thread_stack_size(2 * 1024 * 1024)  // 2MB栈
        .enable_all()
        .build()
        .expect("Failed to create runtime")
}

// 工作窃取优化
pub struct TaskManager {
    local_queue: tokio::sync::mpsc::UnboundedSender<BoxedTask>,
    join_set: JoinSet<()>,
}

type BoxedTask = Box<dyn Future<Output = ()> + Send + 'static>;

impl TaskManager {
    pub fn spawn_local<F>(&self, task: F) 
    where 
        F: Future<Output = ()> + Send + 'static,
    {
        self.local_queue.send(Box::new(task)).unwrap();
    }
    
    // 批量处理任务减少调度开销
    pub async fn process_batch(&mut self, batch_size: usize) {
        let mut tasks = Vec::with_capacity(batch_size);
        
        // 收集批量任务
        for _ in 0..batch_size {
            if let Ok(task) = self.local_queue.try_recv() {
                tasks.push(task);
            } else {
                break;
            }
        }
        
        // 并行执行批量任务
        for task in tasks {
            self.join_set.spawn(task);
        }
    }
}
```

### I/O性能优化
```rust
use tokio::io::{AsyncRead, AsyncWrite, BufReader, BufWriter};
use tokio::fs::OpenOptions;

// 批量I/O操作
pub struct BatchWriter<W> {
    writer: BufWriter<W>,
    batch: Vec<Bytes>,
    batch_size: usize,
}

impl<W: AsyncWrite + Unpin> BatchWriter<W> {
    pub fn new(writer: W, batch_size: usize) -> Self {
        Self {
            writer: BufWriter::with_capacity(64 * 1024, writer),
            batch: Vec::with_capacity(batch_size),
            batch_size,
        }
    }
    
    pub async fn write(&mut self, data: Bytes) -> tokio::io::Result<()> {
        self.batch.push(data);
        
        if self.batch.len() >= self.batch_size {
            self.flush_batch().await?;
        }
        
        Ok(())
    }
    
    async fn flush_batch(&mut self) -> tokio::io::Result<()> {
        for data in self.batch.drain(..) {
            self.writer.write_all(&data).await?;
        }
        self.writer.flush().await
    }
}

// 预分配文件空间
pub async fn create_preallocated_file(
    path: &str, 
    size: u64
) -> tokio::io::Result<tokio::fs::File> {
    let file = OpenOptions::new()
        .create(true)
        .write(true)
        .truncate(true)
        .open(path)
        .await?;
    
    // 预分配空间减少文件系统碎片
    file.set_len(size).await?;
    
    Ok(file)
}
```

## 编译时优化

### Profile Guided Optimization (PGO)
```toml
# Cargo.toml中的PGO配置
[profile.release]
lto = "fat"              # 链接时优化
codegen-units = 1        # 单个代码生成单元
panic = "abort"          # panic时abort而非unwind
opt-level = 3            # 最高优化级别

# PGO构建步骤
[profile.pgo-build]
inherits = "release"
debug = 1                # 保留调试信息用于分析

[profile.pgo-use]
inherits = "release"
# 使用profile数据的配置
```

### 编译时计算
```rust
// const fn和常量计算
const fn fibonacci(n: usize) -> usize {
    match n {
        0 | 1 => n,
        _ => {
            let mut a = 0;
            let mut b = 1;
            let mut i = 2;
            while i <= n {
                let temp = a + b;
                a = b;
                b = temp;
                i += 1;
            }
            b
        }
    }
}

// 编译时生成查找表
const FIBONACCI_TABLE: [usize; 20] = {
    let mut table = [0; 20];
    let mut i = 0;
    while i < 20 {
        table[i] = fibonacci(i);
        i += 1;
    }
    table
};

// 宏生成优化代码
macro_rules! generate_fast_parser {
    ($($variant:ident => $value:expr),*) => {
        pub fn parse_fast(input: &str) -> Option<MyEnum> {
            match input {
                $(stringify!($variant) => Some(MyEnum::$variant),)*
                _ => None,
            }
        }
    };
}

generate_fast_parser! {
    Variant1 => 1,
    Variant2 => 2,
    Variant3 => 3
}
```

## 性能监控和分析

### 运行时性能监控
```rust
use std::time::{Duration, Instant};
use std::collections::HashMap;
use std::sync::Arc;
use parking_lot::Mutex;

pub struct PerformanceProfiler {
    metrics: Arc<Mutex<HashMap<String, MetricData>>>,
}

#[derive(Debug, Clone)]
struct MetricData {
    count: u64,
    total_time: Duration,
    min_time: Duration,
    max_time: Duration,
    histogram: [u64; 10], // 简单直方图
}

impl PerformanceProfiler {
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(Mutex::new(HashMap::new())),
        }
    }
    
    pub async fn profile<F, T>(&self, name: &str, f: F) -> T 
    where 
        F: Future<Output = T>,
    {
        let start = Instant::now();
        let result = f.await;
        let duration = start.elapsed();
        
        self.record_metric(name, duration);
        result
    }
    
    fn record_metric(&self, name: &str, duration: Duration) {
        let mut metrics = self.metrics.lock();
        let entry = metrics.entry(name.to_string())
            .or_insert_with(|| MetricData {
                count: 0,
                total_time: Duration::ZERO,
                min_time: Duration::MAX,
                max_time: Duration::ZERO,
                histogram: [0; 10],
            });
            
        entry.count += 1;
        entry.total_time += duration;
        entry.min_time = entry.min_time.min(duration);
        entry.max_time = entry.max_time.max(duration);
        
        // 更新直方图
        let bucket = ((duration.as_millis() / 10).min(9)) as usize;
        entry.histogram[bucket] += 1;
    }
    
    pub fn get_report(&self) -> PerformanceReport {
        let metrics = self.metrics.lock();
        PerformanceReport {
            metrics: metrics.clone(),
            timestamp: Instant::now(),
        }
    }
}

#[derive(Debug)]
pub struct PerformanceReport {
    metrics: HashMap<String, MetricData>,
    timestamp: Instant,
}

impl PerformanceReport {
    pub fn print_summary(&self) {
        println!("Performance Report:");
        for (name, data) in &self.metrics {
            let avg = data.total_time.as_millis() / data.count as u128;
            println!("  {}: {} calls, avg: {}ms, min: {}ms, max: {}ms",
                name,
                data.count,
                avg,
                data.min_time.as_millis(),
                data.max_time.as_millis()
            );
        }
    }
}
```

### 内存分析工具
```rust
// 内存使用跟踪
#[derive(Debug)]
pub struct MemoryTracker {
    allocations: AtomicUsize,
    deallocations: AtomicUsize,
    peak_usage: AtomicUsize,
    current_usage: AtomicUsize,
}

impl MemoryTracker {
    pub fn record_allocation(&self, size: usize) {
        self.allocations.fetch_add(1, Ordering::Relaxed);
        let current = self.current_usage.fetch_add(size, Ordering::Relaxed) + size;
        
        // 更新峰值使用量
        let mut peak = self.peak_usage.load(Ordering::Relaxed);
        while current > peak {
            match self.peak_usage.compare_exchange_weak(
                peak,
                current,
                Ordering::Relaxed,
                Ordering::Relaxed,
            ) {
                Ok(_) => break,
                Err(x) => peak = x,
            }
        }
    }
    
    pub fn record_deallocation(&self, size: usize) {
        self.deallocations.fetch_add(1, Ordering::Relaxed);
        self.current_usage.fetch_sub(size, Ordering::Relaxed);
    }
    
    pub fn get_stats(&self) -> MemoryStats {
        MemoryStats {
            allocations: self.allocations.load(Ordering::Relaxed),
            deallocations: self.deallocations.load(Ordering::Relaxed),
            current_usage: self.current_usage.load(Ordering::Relaxed),
            peak_usage: self.peak_usage.load(Ordering::Relaxed),
        }
    }
}

#[derive(Debug)]
pub struct MemoryStats {
    pub allocations: usize,
    pub deallocations: usize,
    pub current_usage: usize,
    pub peak_usage: usize,
}
```