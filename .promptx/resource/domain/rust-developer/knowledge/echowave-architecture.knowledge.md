# EchoWave架构专业知识

## 项目架构概览

### 工作区结构
```
echowave-client/
├── agent/                 # WSL 守护服务 (Linux only)
├── crates/
│   ├── protocol/         # 共享消息协议
│   └── core/            # 核心业务逻辑
├── desktop/             # Tauri 前端应用
│   └── src-tauri/       # Tauri 后端
└── legacy/              # 原 Electron 项目参考
```

### Cargo工作区配置
```toml
[workspace]
members = [
    "agent",
    "crates/protocol", 
    "crates/core",
    "desktop/src-tauri",
]

[workspace.dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
tracing = "0.1"
anyhow = "1.0"
thiserror = "1.0"
reqwest = { version = "0.11", features = ["json"] }
```

## 核心架构组件

### 1. Agent (WSL守护进程)
```rust
// agent/src/main.rs
#![cfg(target_os = "linux")]  // Linux专用

use tokio::runtime::Runtime;
use tracing::{info, error};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    // 初始化日志系统
    tracing_subscriber::init();
    
    // 启动守护进程
    let daemon = AgentDaemon::new().await?;
    daemon.run().await?;
    
    Ok(())
}

pub struct AgentDaemon {
    docker_manager: DockerManager,
    nomad_client: NomadClient,
    tailscale_manager: TailscaleManager,
    message_handler: MessageFrameHandler,
}
```

**主要职责**：
- Docker容器管理
- Nomad任务调度
- Tailscale网络管理
- 与Core的stdio通信

### 2. Core (业务逻辑核心)
```rust
// crates/core/src/lib.rs
pub struct EchoWaveCore {
    config: Config,
    agent_communicator: AgentCommunicator,
    system_checker: SystemChecker,
    task_manager: TaskManager,
    user_manager: UserManager,
}

impl EchoWaveCore {
    pub async fn new() -> anyhow::Result<Self> {
        let config = Config::load().await?;
        let agent_communicator = AgentCommunicator::new().await?;
        
        Ok(Self {
            config,
            agent_communicator,
            system_checker: SystemChecker::new(),
            task_manager: TaskManager::new(),
            user_manager: UserManager::new(),
        })
    }
    
    pub async fn start_task_reception(&mut self) -> anyhow::Result<()> {
        // 启动守护进程
        self.agent_communicator.start_daemon().await?;
        
        // 启动任务轮询
        self.task_manager.start_polling().await?;
        
        Ok(())
    }
}
```

**主要职责**：
- 业务逻辑协调
- 系统环境检查
- 用户认证管理
- 任务状态管理

### 3. Protocol (通信协议)
```rust
// crates/protocol/src/lib.rs
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct MessageFrame {
    pub id: String,
    pub timestamp: u64,
    pub message_type: MessageType,
    pub payload: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum MessageType {
    Command(CommandMessage),
    Response(ResponseMessage),
    Event(EventMessage),
    Log(LogMessage),
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CommandMessage {
    pub action: String,
    pub params: serde_json::Value,
}
```

**主要职责**：
- 定义Core ↔ Agent通信协议
- 消息序列化/反序列化
- 协议版本管理

### 4. Desktop (Tauri前端)
```rust
// desktop/src-tauri/src/main.rs
use tauri::{Manager, State};
use crates_core::EchoWaveCore;

#[tauri::command]
async fn start_task_reception(
    core: State<'_, tokio::sync::Mutex<EchoWaveCore>>
) -> Result<(), String> {
    let mut core = core.lock().await;
    core.start_task_reception()
        .await
        .map_err(|e| e.to_string())
}

fn main() {
    tauri::Builder::default()
        .setup(|app| {
            let core = EchoWaveCore::new();
            app.manage(tokio::sync::Mutex::new(core));
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            start_task_reception,
            stop_task_reception,
            get_system_status,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

## 关键业务流程

### 1. 系统环境检查流程
```rust
pub struct SystemChecker {
    checks: Vec<Box<dyn SystemCheck>>,
}

#[async_trait]
pub trait SystemCheck: Send + Sync {
    async fn check(&self) -> CheckResult;
    fn name(&self) -> &str;
    fn can_auto_fix(&self) -> bool;
    async fn auto_fix(&self) -> anyhow::Result<()>;
}

// 6项系统检查
pub struct PlatformCheck;    // 操作系统检查
pub struct OSVersionCheck;   // 系统版本检查  
pub struct PowerShellCheck;  // PowerShell检查
pub struct VirtualCheck;     // BIOS虚拟化检查
pub struct WSLCheck;         // WSL2检查
pub struct MirrorCheck;      // 任务引擎检查
```

### 2. 任务接收流程
```rust
pub struct TaskManager {
    polling_interval: Duration,
    is_receiving: Arc<AtomicBool>,
    current_task: Arc<Mutex<Option<Task>>>,
}

impl TaskManager {
    pub async fn start_polling(&self) -> anyhow::Result<()> {
        let interval = self.polling_interval;
        let is_receiving = self.is_receiving.clone();
        
        tokio::spawn(async move {
            let mut ticker = tokio::time::interval(interval);
            
            while is_receiving.load(Ordering::Relaxed) {
                ticker.tick().await;
                
                match Self::poll_server_for_tasks().await {
                    Ok(Some(task)) => {
                        info!("Received new task: {}", task.id);
                        // 处理任务分配
                    }
                    Ok(None) => {
                        debug!("No tasks available");
                    }
                    Err(e) => {
                        error!("Failed to poll for tasks: {}", e);
                    }
                }
            }
        });
        
        Ok(())
    }
}
```

### 3. WSL集成管理
```rust
pub struct WSLManager {
    distro_name: String,
    config_manager: WSLConfigManager,
}

impl WSLManager {
    pub async fn optimize_configuration(&self) -> anyhow::Result<()> {
        let config = WSLConfig {
            processors: "70%".to_string(),
            memory: "80%".to_string(), 
            swap: "8GB".to_string(),
        };
        
        self.config_manager.apply_config(config).await?;
        info!("WSL configuration optimized");
        Ok(())
    }
    
    pub async fn execute_privileged_command(&self, cmd: &str) -> anyhow::Result<String> {
        let full_cmd = format!("wsl -d {} sh -c '{}'", self.distro_name, cmd);
        
        let output = tokio::process::Command::new("cmd")
            .args(&["/C", &full_cmd])
            .output()
            .await?;
            
        if !output.status.success() {
            return Err(anyhow::anyhow!(
                "WSL command failed: {}", 
                String::from_utf8_lossy(&output.stderr)
            ));
        }
        
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    }
}
```

## 数据模型

### 用户凭据模型
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct UserCredentials {
    pub phone: String,
    pub token: String,
    pub auto_login: bool,
    pub created_at: SystemTime,
    pub expires_at: SystemTime,  // 30天有效期
}

impl UserCredentials {
    pub fn is_expired(&self) -> bool {
        SystemTime::now() > self.expires_at
    }
    
    pub fn save_to_file(&self, path: &Path) -> anyhow::Result<()> {
        let json = serde_json::to_string_pretty(self)?;
        std::fs::write(path, json)?;
        Ok(())
    }
}
```

### 系统状态模型
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemStatus {
    pub checks: HashMap<String, CheckStatus>,
    pub is_ready_for_tasks: bool,
    pub mirror_status: MirrorStatus,
    pub daemon_status: DaemonStatus,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CheckStatus {
    pub status: CheckState,
    pub message: String,
    pub can_auto_fix: bool,
    pub last_checked: SystemTime,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum CheckState {
    Pending,
    Checking,
    Success,
    Error,
    Unsatisfied,
    HasUpdate,
    Updating,
}
```

### 任务模型
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct Task {
    pub id: String,
    pub task_type: TaskType,
    pub priority: TaskPriority,
    pub estimated_duration: Duration,
    pub payload: serde_json::Value,
    pub created_at: SystemTime,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum TaskType {
    Computation,
    DataProcessing,
    ModelTraining,
    Rendering,
}

#[derive(Debug, Serialize, Deserialize, PartialOrd, Ord, PartialEq, Eq)]
pub enum TaskPriority {
    Low,
    Normal, 
    High,
    Critical,
}
```

## 配置管理

### 多环境配置
```rust
#[derive(Debug, Deserialize)]
pub struct Config {
    pub environment: Environment,
    pub api: ApiConfig,
    pub wsl: WSLConfig,
    pub agent: AgentConfig,
}

#[derive(Debug, Deserialize)]
pub enum Environment {
    Development,
    Testing,
    Production,
}

#[derive(Debug, Deserialize)]
pub struct ApiConfig {
    pub base_url: String,
    pub timeout: Duration,
    pub retry_attempts: u32,
    pub retry_delay: Duration,
}

impl Config {
    pub async fn load() -> anyhow::Result<Self> {
        let env = std::env::var("ECHOWAVE_ENV")
            .unwrap_or_else(|_| "production".to_string());
            
        let config_file = format!("config/{}.toml", env);
        let content = tokio::fs::read_to_string(config_file).await?;
        let config: Config = toml::from_str(&content)?;
        
        Ok(config)
    }
}
```

## 错误处理策略

### 统一错误类型
```rust
use thiserror::Error;

#[derive(Error, Debug)]
pub enum EchoWaveError {
    #[error("System check failed: {check} - {reason}")]
    SystemCheckFailed { check: String, reason: String },
    
    #[error("WSL operation failed: {0}")]
    WSLError(#[from] WSLError),
    
    #[error("Agent communication error: {0}")]
    AgentError(#[from] AgentError),
    
    #[error("Network error: {0}")]
    NetworkError(#[from] reqwest::Error),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
    
    #[error("Authentication failed: {reason}")]
    AuthError { reason: String },
}

// 特定模块错误
#[derive(Error, Debug)]
pub enum WSLError {
    #[error("WSL not installed")]
    NotInstalled,
    
    #[error("WSL version not supported: {version}")]
    UnsupportedVersion { version: String },
    
    #[error("Command execution failed: {command}")]
    CommandFailed { command: String },
}
```

## 日志和监控

### 结构化日志
```rust
use tracing::{info, warn, error, debug, instrument};

impl TaskManager {
    #[instrument(skip(self), fields(task_id = %task.id))]
    pub async fn process_task(&self, task: Task) -> anyhow::Result<TaskResult> {
        info!(
            task_type = ?task.task_type,
            priority = ?task.priority,
            "Starting task processing"
        );
        
        let start_time = std::time::Instant::now();
        
        let result = match self.execute_task(&task).await {
            Ok(result) => {
                let duration = start_time.elapsed();
                info!(
                    duration_ms = %duration.as_millis(),
                    "Task completed successfully"
                );
                result
            }
            Err(e) => {
                error!(
                    error = %e,
                    duration_ms = %start_time.elapsed().as_millis(),
                    "Task processing failed"
                );
                return Err(e);
            }
        };
        
        Ok(result)
    }
}
```

### 性能监控
```rust
use std::time::{Duration, Instant};
use tracing::info;

pub struct PerformanceMonitor {
    metrics: HashMap<String, PerformanceMetric>,
}

#[derive(Debug)]
pub struct PerformanceMetric {
    pub total_calls: u64,
    pub total_duration: Duration,
    pub min_duration: Duration,
    pub max_duration: Duration,
    pub last_duration: Duration,
}

impl PerformanceMonitor {
    pub async fn measure<F, T>(&mut self, name: &str, f: F) -> T 
    where
        F: Future<Output = T>,
    {
        let start = Instant::now();
        let result = f.await;
        let duration = start.elapsed();
        
        self.record_metric(name, duration);
        
        if duration > Duration::from_millis(100) {
            warn!(
                operation = %name,
                duration_ms = %duration.as_millis(),
                "Slow operation detected"
            );
        }
        
        result
    }
}
```