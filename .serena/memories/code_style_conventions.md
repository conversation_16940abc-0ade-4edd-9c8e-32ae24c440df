# EchoWave Client - Code Style and Conventions

## Frontend (Vue/TypeScript)
- **Code Style**: ESLint 9.23.0 with TypeScript ESLint plugin 8.28.0
- **Formatting**: Prettier 3.5.3 with Tailwind CSS plugin
- **Vue Style**: Composition API pattern preferred
- **Component Structure**: Uses shadcn-vue components in `src/components/ui/`
- **Type Safety**: Strict TypeScript with `vue-tsc --noEmit` for type checking
- **Import Style**: ES6 imports with path aliases via Vite config

## Rust Code Style
- **Edition**: 2021
- **Error Handling**: Use `anyhow` for general errors, `thiserror` for custom error types
- **Logging**: Use `tracing` crate instead of `println!` for all logging
- **Async**: Tokio-based async/await patterns
- **Workspace Dependencies**: Shared dependencies defined in root `Cargo.toml`
- **Platform Specific**: Use `#![cfg(target_os = "linux")]` for agent module

## Naming Conventions
- **Rust**: snake_case for functions/variables, PascalCase for types
- **TypeScript**: camelCase for variables/functions, PascalCase for types/components
- **File Names**: kebab-case for Vue components, snake_case for Rust modules
- **Constants**: SCREAMING_SNAKE_CASE

## Project Structure Patterns
- **Modular Architecture**: Clear separation between rendering, core, agent, and helper modules
- **Communication Protocol**: MessageFrame binary protocol for inter-module communication
- **State Management**: Centralized in core module, exposed via adapters
- **Type Safety**: Shared types between Rust and TypeScript using serde serialization