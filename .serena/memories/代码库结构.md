# EchoWave 客户端 - 代码库结构

## 根目录结构
```
project/
├── Cargo.toml              # Rust 工作区配置
├── Makefile                # 项目级构建命令
├── CLAUDE.md               # 开发指南和架构文档
├── desktop/                # Tauri 前端应用
├── agent/                  # WSL 守护进程（仅限 Linux）
├── crates/                 # 共享 Rust 库
├── helper-svc/             # Windows 服务模块（开发中）
├── docs/                   # 架构和设计文档
└── legacy/                 # 参考 Electron 实现
```

## 模块架构

### 桌面模块（desktop/）
- **前端**: `src/` 中的 Vue 3 + TypeScript
- **后端**: `src-tauri/` 中的 Tauri Rust 后端
- **组件**: `src/components/ui/` 中的 UI 组件
- **视图**: `src/views/` 中的页面组件
- **状态**: `src/stores/` 中的 Pinia 状态管理
- **配置**: `tauri.conf.json`、`package.json`、`vite.config.ts`

### Rust 工作区（crates/）
- **protocol/**: MessageFrame 通信协议
- **domain/**: 核心业务逻辑（从 core 重命名）
- **shared/**: 通用工具和类型

### Agent 模块（agent/）
- **平台**: 仅限 Linux/WSL（`#![cfg(target_os = "linux")]`）
- **功能**: Tailscale、Docker、Nomad 的进程管理
- **构建**: 用于 WSL 部署的自定义 Makefile

### 辅助服务（helper-svc/）
- **平台**: 仅限 Windows
- **功能**: 管理员权限操作
- **状态**: 开发中

## 通信架构
- **前端 ↔ 核心**: Tauri IPC（invoke/events）
- **核心 ↔ Agent**: 基于 stdio 的 MessageFrame 协议
- **核心 ↔ Helper**: Named Pipe + MessageFrame（计划中）
- **核心 ↔ 服务器**: HTTP REST API

## 关键配置文件
- `Cargo.toml`: 工作区和共享依赖
- `desktop/tauri.conf.json`: Tauri 应用配置
- `desktop/package.json`: 前端依赖和脚本
- `desktop/components.json`: shadcn-vue 组件配置