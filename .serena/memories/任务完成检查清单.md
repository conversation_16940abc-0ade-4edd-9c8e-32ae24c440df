# EchoWave 客户端 - 任务完成检查清单

## 代码质量检查

### 前端检查（desktop/）
1. **类型检查**: `pnpm build`（包含 `vue-tsc --noEmit`）
2. **代码格式化**: `pnpm prettier`
3. **测试**: `pnpm test`（Vitest）
4. **代码检查**: ESLint 在构建过程中自动运行

### Rust 检查
1. **编译**: `cargo build`（所有工作区 crates）
2. **测试**: `cargo test`（所有工作区测试）
3. **Clippy**: `cargo clippy`（如果可用）
4. **格式化**: `cargo fmt`（如果可用）

## 构建验证
1. **开发构建**: `pnpm tauri dev` 应该无错误启动
2. **生产构建**: `pnpm tauri build` 应该成功完成
3. **跨模块通信**: 验证模块间 MessageFrame 协议正常工作

## 平台特定考虑
- **Agent 模块**: 仅在 Linux/WSL 上构建和运行
- **Windows 服务**: 目前处于开发阶段
- **Tauri 应用**: 目标为 Windows NSIS 安装程序

## 文档更新
- 如果引入新的开发模式，更新 CLAUDE.md
- 如果有 UI 变更，更新组件文档
- 确保 README.md 反映任何新的设置要求

## 最终验证
- 测试完整的应用程序启动流程
- 验证系统检查通过（操作系统、PowerShell、虚拟化、WSL、镜像）
- 确认任务接受/执行工作流程正常运行