# EchoWave Client - Essential Development Commands

## Primary Development Commands

### Frontend Development (desktop/)
```bash
cd desktop
pnpm install                    # Install dependencies
pnpm dev                        # Start Vite dev server
pnpm tauri dev                  # Start Tauri development mode with hot reload
pnpm tauri dev --no-watch       # Start Tauri dev mode without file watching
pnpm build                      # Build frontend (includes TypeScript checking)
pnpm prettier                   # Format code
pnpm test                       # Run Vitest tests
```

### Rust Development (project root)
```bash
cargo build                     # Build all workspace crates
cargo test                      # Run all tests
cargo run -p agent              # Run agent binary (Linux/WSL only)
```

### Build and Release
```bash
cd desktop
pnpm tauri build                # Build production Tauri app (Windows NSIS installer)
pnpm tauri build --debug        # Build debug version
pnpm tauri clean                # Clean build artifacts
```

### Agent-Specific (WSL environment)
```bash
cd agent
make build-debug                # Build and move to WSL mount point
cargo build                     # Build agent binary
```

### Project-Level Commands
```bash
make start                      # Start development (runs desktop/make start)
cd desktop && make start        # Start Tauri dev with debug logging
cd desktop && make web          # Start only web dev server
```

## Key Environment Variables
- `RUST_LOG=debug` - Enable debug logging for Rust components
- `RUST_LOG=trace` - Enable verbose logging