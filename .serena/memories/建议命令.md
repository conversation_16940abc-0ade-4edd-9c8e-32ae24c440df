# EchoWave 客户端 - 重要开发命令

## 主要开发命令

### 前端开发（desktop/）
```bash
cd desktop
pnpm install                    # 安装依赖
pnpm dev                        # 启动 Vite 开发服务器
pnpm tauri dev                  # 启动 Tauri 开发模式（支持热重载）
pnpm tauri dev --no-watch       # 启动 Tauri 开发模式（不监听文件变化）
pnpm build                      # 构建前端（包含 TypeScript 检查）
pnpm prettier                   # 格式化代码
pnpm test                       # 运行 Vitest 测试
```

### Rust 开发（项目根目录）
```bash
cargo build                     # 构建所有工作区 crates
cargo test                      # 运行所有测试
cargo run -p agent              # 运行 agent 二进制文件（仅限 Linux/WSL）
```

### 构建和发布
```bash
cd desktop
pnpm tauri build                # 构建生产版 Tauri 应用（Windows NSIS 安装程序）
pnpm tauri build --debug        # 构建调试版本
pnpm tauri clean                # 清理构建产物
```

### Agent 专用（WSL 环境）
```bash
cd agent
make build-debug                # 构建并移动到 WSL 挂载点
cargo build                     # 构建 agent 二进制文件
```

### 项目级命令
```bash
make start                      # 开始开发（运行 desktop/make start）
cd desktop && make start        # 启动带调试日志的 Tauri 开发
cd desktop && make web          # 仅启动 web 开发服务器
```

## 关键环境变量
- `RUST_LOG=debug` - 为 Rust 组件启用调试日志
- `RUST_LOG=trace` - 启用详细日志