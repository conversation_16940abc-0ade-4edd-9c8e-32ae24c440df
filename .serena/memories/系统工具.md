# EchoWave 客户端 - 系统工具（macOS/Darwin）

## 标准 Unix 命令
- `ls` - 列出目录内容
- `cd` - 更改目录
- `grep` - 搜索文本模式
- `find` - 查找文件和目录
- `git` - 版本控制操作
- `make` - 构建自动化

## macOS 特定说明
- 系统基于 Darwin（BSD Unix 变体）
- 文件路径使用正斜杠
- 区分大小写的文件系统（在大多数配置中）
- 通过 Homebrew 进行包管理（如果可用）

## 项目特定工具要求
- **Node.js/pnpm**: 用于前端开发
- **Rust/Cargo**: 用于后端开发
- **WSL**: agent 模块所需（在 macOS 上不直接可用）
- **Docker**: 用于任务执行环境

## 开发环境
- 当前工作目录: `/Users/<USER>/Projects/echowave-client`
- Git 仓库: 是（分支: dev，main 分支可用）
- 平台考虑: Agent 模块需要 Linux/WSL，在 macOS 上可能需要替代测试策略

## 文件系统结构
- 项目使用标准 Unix 风格路径
- 通过 Rust 的路径处理保持跨平台兼容性
- Tauri 处理桌面应用部署的平台差异