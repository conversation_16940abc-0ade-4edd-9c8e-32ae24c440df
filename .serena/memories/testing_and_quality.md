# EchoWave Client - Testing and Quality Assurance

## Testing Framework
- **Frontend**: Vitest 3.2.3 for unit and integration tests
- **Rust**: Built-in `cargo test` framework
- **Test Location**: `desktop/src/test/` for frontend tests

## Code Quality Tools

### Frontend Quality
- **TypeScript**: Strict type checking with `vue-tsc --noEmit`
- **ESLint**: 9.23.0 with TypeScript and Vue plugins
- **Prettier**: 3.5.3 with Tailwind CSS plugin for formatting
- **Build Validation**: Type checking included in `pnpm build`

### Rust Quality
- **Compiler**: Rust's strict compiler catches many issues
- **Testing**: `cargo test` runs all workspace tests
- **Logging**: Structured logging with tracing crate
- **Error Handling**: Consistent use of anyhow/thiserror

## Testing Commands
```bash
# Frontend testing
cd desktop
pnpm test                    # Run Vitest tests
pnpm test --ui              # Run with UI interface
pnpm test --coverage        # Generate coverage reports

# Rust testing
cargo test                  # Run all workspace tests
cargo test -p agent         # Test specific crate
cargo test -- --nocapture  # Show test output
```

## Quality Gates
1. TypeScript compilation must pass
2. All tests must pass
3. Code must be properly formatted
4. No ESLint errors in frontend code
5. Rust code must compile without warnings
6. Application must start and basic flows must work

## Continuous Quality
- Pre-build type checking prevents runtime type errors
- Structured logging aids in debugging and monitoring
- Cross-module communication protocol ensures interface consistency