# EchoWave Client - Task Completion Checklist

## Code Quality Checks

### Frontend Checks (desktop/)
1. **Type Checking**: `pnpm build` (includes `vue-tsc --noEmit`)
2. **Code Formatting**: `pnpm prettier`
3. **Testing**: `pnpm test` (Vitest)
4. **Linting**: ESLint runs automatically during build process

### Rust Checks
1. **Compilation**: `cargo build` (all workspace crates)
2. **Testing**: `cargo test` (all workspace tests)
3. **Clippy**: `cargo clippy` (if available)
4. **Format**: `cargo fmt` (if available)

## Build Verification
1. **Development Build**: `pnpm tauri dev` should start without errors
2. **Production Build**: `pnpm tauri build` should complete successfully
3. **Cross-Module Communication**: Verify MessageFrame protocol works between modules

## Platform-Specific Considerations
- **Agent Module**: Only builds and runs on Linux/WSL
- **Windows Service**: Currently in development
- **Tauri App**: Targets Windows NSIS installer

## Documentation Updates
- Update CLAUDE.md if new development patterns are introduced
- Update component documentation if UI changes are made
- Ensure README.md reflects any new setup requirements

## Final Verification
- Test the complete application startup flow
- Verify system checks pass (OS, PowerShell, virtualization, WSL, mirror)
- Confirm task acceptance/execution workflow functions properly