# EchoWave Client - System Utilities (macOS/Darwin)

## Standard Unix Commands
- `ls` - List directory contents
- `cd` - Change directory
- `grep` - Search text patterns
- `find` - Find files and directories
- `git` - Version control operations
- `make` - Build automation

## macOS-Specific Notes
- System is Darwin-based (BSD Unix variant)
- File paths use forward slashes
- Case-sensitive filesystem (in most configurations)
- Package management via Homebrew (if available)

## Project-Specific Tool Requirements
- **Node.js/pnpm**: For frontend development
- **Rust/Cargo**: For backend development
- **WSL**: Required for agent module (not available on macOS directly)
- **Docker**: For task execution environment

## Development Environment
- Current working directory: `/Users/<USER>/Projects/echowave-client`
- Git repository: Yes (branch: dev, main branch available)
- Platform considerations: Agent module requires Linux/WSL, may need alternative testing strategy on macOS

## File System Structure
- Project uses standard Unix-style paths
- Cross-platform compatibility maintained through Rust's path handling
- <PERSON><PERSON> handles platform differences for desktop app deployment