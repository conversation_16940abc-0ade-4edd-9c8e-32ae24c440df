# EchoWave Client - Codebase Structure

## Root Directory Structure
```
project/
├── Cargo.toml              # Rust workspace configuration
├── Makefile                # Project-level build commands
├── CLAUDE.md               # Development guidelines and architecture docs
├── desktop/                # Tauri frontend application
├── agent/                  # WSL daemon process (Linux only)
├── crates/                 # Shared Rust libraries
├── helper-svc/             # Windows service module (in development)
├── docs/                   # Architecture and design documents
└── legacy/                 # Reference Electron implementation
```

## Module Architecture

### Desktop Module (desktop/)
- **Frontend**: Vue 3 + TypeScript in `src/`
- **Backend**: Tauri Rust backend in `src-tauri/`
- **Components**: UI components in `src/components/ui/`
- **Views**: Page components in `src/views/`
- **Stores**: Pinia state management in `src/stores/`
- **Config**: `tauri.conf.json`, `package.json`, `vite.config.ts`

### Rust Workspace (crates/)
- **protocol/**: MessageFrame communication protocol
- **domain/**: Core business logic (renamed from core)
- **shared/**: Common utilities and types

### Agent Module (agent/)
- **Platform**: Linux/WSL only (`#![cfg(target_os = "linux")]`)
- **Function**: Process management for Tailscale, Docker, Nomad
- **Build**: Custom Makefile for WSL deployment

### Helper Service (helper-svc/)
- **Platform**: Windows only
- **Function**: Admin privilege operations
- **Status**: In development

## Communication Architecture
- **Frontend ↔ Core**: Tauri IPC (invoke/events)
- **Core ↔ Agent**: stdio-based MessageFrame protocol
- **Core ↔ Helper**: Named Pipe + MessageFrame (planned)
- **Core ↔ Server**: HTTP REST API

## Key Configuration Files
- `Cargo.toml`: Workspace and shared dependencies
- `desktop/tauri.conf.json`: Tauri app configuration
- `desktop/package.json`: Frontend dependencies and scripts
- `desktop/components.json`: shadcn-vue component configuration