# EchoWave 客户端 - 技术栈

## 架构
四模块混合 Rust/TypeScript 架构：

### 前端（桌面模块）
- **框架**: <PERSON><PERSON>（WebView + Rust 后端）
- **UI 框架**: Vue 3 与 TypeScript
- **样式**: TailwindCSS 4.1.10
- **状态管理**: Pinia 3.0.3
- **UI 组件**: shadcn-vue（reka-ui 2.3.2）、lucide-vue-next
- **构建工具**: Vite 6.0.3
- **包管理器**: pnpm

### 后端（Rust 模块）
- **语言**: Rust（基于工作区的项目）
- **异步运行时**: Tokio 1.45.1
- **序列化**: serde 1.0.219、serde_json 1.0.140
- **错误处理**: anyhow 1.0.98、thiserror 2.0.12
- **日志记录**: tracing 0.1.41、tracing-subscriber 0.3.18
- **压缩**: zstd 0.13.3（用于 MessageFrame 协议）
- **HTTP 客户端**: reqwest（用于服务器通信）

### 开发工具
- **TypeScript**: 5.6.2
- **ESLint**: 9.23.0 配合 TypeScript 和 Vue 插件
- **Prettier**: 3.5.3
- **测试**: Vitest 3.2.3
- **构建目标**: Windows NSIS 安装程序

### 模块分解
1. **渲染模块（desktop）**: Vue 3 + Tauri 前端
2. **核心模块（crates/domain）**: 业务逻辑协调
3. **代理模块（agent）**: WSL 守护进程用于进程管理（仅限 Linux）
4. **Windows 服务模块（helper-svc）**: 管理员权限操作（开发中）
5. **协议模块（crates/protocol）**: 通过 MessageFrame 进行模块间通信