# EchoWave 客户端 - 代码风格和规范

## 前端（Vue/TypeScript）
- **代码风格**: ESLint 9.23.0 配合 TypeScript ESLint 插件 8.28.0
- **格式化**: Prettier 3.5.3 配合 Tailwind CSS 插件
- **Vue 风格**: 优先使用组合式 API 模式
- **组件结构**: 在 `src/components/ui/` 中使用 shadcn-vue 组件
- **类型安全**: 严格的 TypeScript 配合 `vue-tsc --noEmit` 进行类型检查
- **导入风格**: ES6 导入，通过 Vite 配置使用路径别名

## Rust 代码风格
- **版本**: 2021
- **错误处理**: 一般错误使用 `anyhow`，自定义错误类型使用 `thiserror`
- **日志记录**: 使用 `tracing` crate 而不是 `println!` 进行所有日志记录
- **异步**: 基于 Tokio 的 async/await 模式
- **工作区依赖**: 在根 `Cargo.toml` 中定义共享依赖
- **平台特定**: agent 模块使用 `#![cfg(target_os = "linux")]`

## 命名约定
- **Rust**: 函数/变量使用 snake_case，类型使用 PascalCase
- **TypeScript**: 变量/函数使用 camelCase，类型/组件使用 PascalCase
- **文件名**: Vue 组件使用 kebab-case，Rust 模块使用 snake_case
- **常量**: SCREAMING_SNAKE_CASE

## 项目结构模式
- **模块化架构**: 渲染、核心、代理和辅助模块之间清晰分离
- **通信协议**: 模块间通信使用 MessageFrame 二进制协议
- **状态管理**: 在核心模块中集中管理，通过适配器暴露
- **类型安全**: 使用 serde 序列化在 Rust 和 TypeScript 之间共享类型