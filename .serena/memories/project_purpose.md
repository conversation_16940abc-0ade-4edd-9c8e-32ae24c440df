# EchoWave Client - Project Purpose

EchoWave is a Windows desktop client application for distributed computing task management. It connects to a remote service to accept and execute computing tasks on user machines.

## Core Functionality

- **Task Management**: Accept and execute distributed computing tasks
- **System Environment Checking**: Verify prerequisites (OS, PowerShell, BIOS virtualization, WSL2, task engine)
- **Process Management**: Manage Tailscale, Docker, and Nomad processes in WSL environment
- **User Authentication**: Handle user login and credential management
- **Automatic Updates**: Support for application and image updates
- **Task Monitoring**: Real-time task status and progress tracking

## Business Model

The application allows users to earn rewards by providing computing resources for distributed tasks. Users can enable/disable automatic task acceptance and monitor their earnings through the interface.