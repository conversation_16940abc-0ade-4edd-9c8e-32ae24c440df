# EchoWave 客户端 - 测试和质量保证

## 测试框架
- **前端**: Vitest 3.2.3 用于单元和集成测试
- **Rust**: 内置 `cargo test` 框架
- **测试位置**: 前端测试位于 `desktop/src/test/`

## 代码质量工具

### 前端质量
- **TypeScript**: 使用 `vue-tsc --noEmit` 进行严格类型检查
- **ESLint**: 9.23.0 配合 TypeScript 和 Vue 插件
- **Prettier**: 3.5.3 配合 Tailwind CSS 插件进行格式化
- **构建验证**: `pnpm build` 中包含类型检查

### Rust 质量
- **编译器**: Rust 的严格编译器捕获许多问题
- **测试**: `cargo test` 运行所有工作区测试
- **日志**: 使用 tracing crate 进行结构化日志
- **错误处理**: 一致使用 anyhow/thiserror

## 测试命令
```bash
# 前端测试
cd desktop
pnpm test                    # 运行 Vitest 测试
pnpm test --ui              # 使用 UI 界面运行
pnpm test --coverage        # 生成覆盖率报告

# Rust 测试
cargo test                  # 运行所有工作区测试
cargo test -p agent         # 测试特定 crate
cargo test -- --nocapture  # 显示测试输出
```

## 质量门槛
1. TypeScript 编译必须通过
2. 所有测试必须通过
3. 代码必须正确格式化
4. 前端代码中无 ESLint 错误
5. Rust 代码必须无警告编译
6. 应用程序必须启动且基本流程必须工作

## 持续质量
- 预构建类型检查防止运行时类型错误
- 结构化日志有助于调试和监控
- 跨模块通信协议确保接口一致性