# EchoWave Client - Tech Stack

## Architecture
Four-module hybrid Rust/TypeScript architecture:

### Frontend (Desktop Module)
- **Framework**: <PERSON><PERSON> (WebView + Rust backend)
- **UI Framework**: Vue 3 with TypeScript
- **Styling**: TailwindCSS 4.1.10
- **State Management**: Pinia 3.0.3
- **UI Components**: shadcn-vue (reka-ui 2.3.2), lucide-vue-next
- **Build Tool**: Vite 6.0.3
- **Package Manager**: pnpm

### Backend (Rust Modules)
- **Language**: Rust (workspace-based project)
- **Async Runtime**: Tokio 1.45.1
- **Serialization**: serde 1.0.219, serde_json 1.0.140
- **Error Handling**: anyhow 1.0.98, thiserror 2.0.12
- **Logging**: tracing 0.1.41, tracing-subscriber 0.3.18
- **Compression**: zstd 0.13.3 (for MessageFrame protocol)
- **HTTP Client**: reqwest (for server communication)

### Development Tools
- **TypeScript**: 5.6.2
- **ESLint**: 9.23.0 with TypeScript and Vue plugins
- **Prettier**: 3.5.3
- **Testing**: Vitest 3.2.3
- **Build Target**: Windows NSIS installer

### Module Breakdown
1. **Rendering Module (desktop)**: Vue 3 + Tauri frontend
2. **Core Module (crates/domain)**: Business logic coordination
3. **Agent Module (agent)**: WSL daemon for process management (Linux only)
4. **Windows Service Module (helper-svc)**: Admin privilege operations (in development)
5. **Protocol Module (crates/protocol)**: Inter-module communication via MessageFrame