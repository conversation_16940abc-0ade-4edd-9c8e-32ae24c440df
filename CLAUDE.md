# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

EchoWave 是一个使用 Rust/TypeScript 混合架构构建的 Windows 客户端应用程序，采用四模块架构设计：

- **渲染模块 (desktop)**: 基于 Tauri 的前端 (Vue 3 + TypeScript) 和 Rust 后端
- **客户端核心模块 (crates/domain)**: 业务逻辑中心，负责协调所有操作
- **任务引擎代理模块 (agent)**: WSL 服务守护进程 (Rust)，管理系统操作
- **Windows 服务模块 (helper-svc)**: 处理需要管理员权限的操作 (开发中)
- **通信协议 (crates/protocol)**: 统一的消息帧协议，支持模块间通信

## 开发命令

### 前端 (桌面应用)

```bash
cd desktop
pnpm dev                    # 启动开发服务器
pnpm build                  # 构建前端
pnpm tauri dev             # 启动 Tauri 开发模式
pnpm tauri build           # 构建 Tauri 应用程序
pnpm prettier              # 格式化代码
pnpm test                  # 运行测试
```

### Rust 后端

```bash
# 从项目根目录
cargo build                # 构建所有工作区 crates
cargo test                 # 运行测试
cargo run -p agent。       # 运行守护程序二进制文件
cargo run -p domain          # 运行编排器二进制文件(domain 以 lib 形式提供，不支持直接运行)

# 守护程序专用 (在 WSL 中)
cd agent
make build                 # 构建并移动到 Windows 路径
```

### 代码检查和类型检查

```bash
cd desktop
pnpm build                 # 包含 vue-tsc --noEmit 进行类型检查
```

## 架构

### 四模块架构职责划分

#### 渲染模块 (desktop) 职责
- **UI 渲染**: 使用 Vue 3 渲染用户界面
- **状态展示**: 根据核心模块状态更新 UI
- **用户交互**: 响应用户操作，发送命令到核心模块
- **不包含**: 任何业务逻辑、系统调用、直接的网络请求

#### 客户端核心模块 (domain) 职责
- **业务逻辑**: 所有业务规则和流程控制
- **状态管理**: 维护应用的全局状态
- **模块协调**: 通过适配器协调其他模块
- **不包含**: UI 逻辑、直接的系统调用、进程管理

#### 任务引擎代理模块 (agent) 职责
- **进程管理**: 管理 Tailscale、Docker、Nomad 进程
- **数据收集**: 收集任务引擎运行数据
- **环境限制**: 仅在 Linux/WSL 环境运行
- **不包含**: 业务逻辑、Windows 操作、UI 相关

#### Windows 服务模块 (helper-svc) 职责
- **权限操作**: 处理需要管理员权限的操作
- **系统功能**: Windows 功能开启/关闭
- **更新安装**: 协助完成系统级更新
- **不包含**: 业务逻辑、状态维护、任务管理

### 工作区结构

项目使用 Cargo 工作区，包含以下成员：

- `agent/` - WSL 守护服务 (Linux Only)
- `crates/protocol/` - 共享消息协议 (基于 MessageFrame)
- `crates/domain/` - 核心业务逻辑 (独立于 UI)
- `desktop/src-tauri/` - Tauri 后端
- `helper-svc/` - Windows 服务 (开发中)

### 桌面应用程序

- **前端**: Vue 3 + TypeScript, Vite, TailwindCSS
- **后端**: Tauri + Rust
- **UI 组件**: `src/components/ui/` 中的自定义组件库 (基于 shadcn-vue)
- **状态管理**: Action/Subscribe 机制连接 domain
- **路由**: Vue Router
- **样式**: TailwindCSS 自定义主题

### 关键配置

- **窗口**: 360x585px，不可调整大小，始终置顶，跳过任务栏
- **构建目标**: Windows NSIS 安装程序
- **包管理器**: pnpm 工作区支持
- **构建工具**: Vite + 自定义 TypeScript 别名解析

### 守护程序服务

- **平台**: 仅限 Linux (WSL) (`#![cfg(target_os = "linux")]`)
- **运行时**: Tokio 异步运行时
- **功能**: 守护进程管理，Docker 集成，Nomad 调度，Tailscale 网络
- **日志**: 使用 tracing crate 的结构化日志
- **生命周期**: PID 文件管理，优雅关闭

### 共享依赖

所有 crate 使用工作区级别的依赖管理：

- `tokio` - 异步运行时
- `serde` - 序列化
- `tracing` - 日志记录
- `anyhow`/`thiserror` - 错误处理
- `reqwest` - HTTP 客户端

### 通信架构

#### 模块间通信机制

- **渲染模块 ↔ 核心模块**: Tauri IPC (invoke/onEvent&Channel)，类型安全，自动序列化
- **核心模块 ↔ Agent**: 基于 stdio 的 MessageFrame 二进制协议
- **核心模块 ↔ Windows 服务**: Named Pipe + MessageFrame 协议 (开发中)
- **核心模块 ↔ 服务端**: HTTP REST API (node/get, node/register, node/enable, node/disable)

#### MessageFrame 协议 (crates/protocol)

统一的二进制消息帧格式，用于模块间通信：

- **Header (16 字节)**: 识别码(3) + 标志位(1) + 操作 ID(4) + 数据长度(4) + 校验和(4)
- **Payload**: JSON 序列化的业务数据，支持 ZSTD 压缩
- **EventCode**: 内部控制事件 (Exit=0x3f, Flush=0x31, Data=0x01)
- **关键 API**: `send_message_frame()` 和 `read_message_frame()`

#### 全链路追踪 (trace_id)

- 每个用户操作生成唯一的 UUID v4 作为 trace_id
- trace_id 在所有模块间传递，贯穿整个请求生命周期
- 所有日志必须包含 trace_id，支持问题快速定位
- 错误发生时通过 trace_id 查看完整调用链

### 核心业务概念

#### 基础业务模型

- **镜像状态**: 镜像在客户端启动时就运行，无需用户手动启动
- **任务执行状态**: `is_executing` 表示是否正在执行计算任务（通过轮询服务端获知）
- **守护进程状态**: Tailscale/Docker/Nomad 仅在开始接单时启动，停止接单时关闭
- **接单流程**: 开始接单 → 启动守护进程 → 轮询任务 → 任务分配 → 停止接单 → 关闭守护进程

#### 系统环境检查（继承自 Legacy 项目）

客户端需要检查 6 个系统环境设置项，只有全部通过才能开始接单：

1. **操作系统检查** (`platform`)

   - 检查是否为支持的 Windows 平台
   - 状态: checkout/loading/success/error

2. **系统版本检查** (`os`)

   - 检查 Windows 版本是否符合要求
   - 状态: checkout/loading/success/error

3. **PowerShell 检查** (`powershell`)

   - 检查 PowerShell 是否已安装
   - 支持自动安装功能
   - 状态: checkout/loading/success/error

4. **BIOS 虚拟化检查** (`virtual`)

   - 检查虚拟化是否在 BIOS 中启用
   - 提供"设置"按钮打开 Windows 功能对话框
   - 状态: checkout/loading/success/error

5. **WSL2 检查** (`wsl`)

   - 检查 WSL2 是否安装和配置
   - 支持自动下载和安装 WSL MSI 包
   - 包括 WSL 配置优化（CPU 70%，内存 80%，8GB 交换空间）
   - 状态: checkout/loading/success/error/unsatisfied

6. **任务引擎检查** (`mirror`)
   - 检查 EchoWave 镜像是否安装和运行
   - 支持自动安装、更新和版本管理
   - 监控镜像运行状态和任务分配情况
   - 状态: checkout/loading/success/error/hasUpdate/updating

#### 任务管理机制

- **自动接单开关**: 用户可控制是否自动接单，设置会持久化保存
- **开机自启动**: 支持开机自启动配置（继承功能）
- **空闲检测**: 通过 Nomad 状态检查节点是否空闲（running=0, pending=0）
- **任务状态监控**: 实时监控任务执行状态和进度
- **智能更新**: 仅在节点空闲时执行自动更新

#### 用户凭据和设置管理

- **凭据存储**: `{userData}/user-credentials.json`
  - 手机号、token、自动登录设置
  - 30 天自动过期机制
- **用户设置**: `{userData}/user-settings.json`
  - 自动接单等用户偏好设置
  - 前后端设置实时同步

#### 网络和服务端通信

- **多环境支持**: 开发/测试/生产环境配置
- **API 端点**:
  - 节点注册: `/api/node/register`
  - 节点状态: `/api/node/get`
  - 开始接单: `/api/node/enable`
  - 停止接单: `/api/node/disable`
- **心跳机制**: 定期轮询服务端获取任务状态
- **自动更新**: 支持应用程序和镜像的自动更新机制

#### 网络服务增强功能（2025-01-25 已实现）

- **Tailscale 延迟检测**:
  - 通过 Agent 适配器获取实时网络延迟信息
  - 支持可配置的检测间隔和启用/禁用开关
  - 延迟数据集成到连接历史记录中
  - 支持延迟警告阈值（200ms）和错误阈值（500ms）的配置化日志记录

- **Agent 连接状态管理**:
  - 扩展连接状态枚举：`Connected`、`Disconnected`、`Connecting`、`Reconnecting`、`Error`、`RestartingWslMirror`
  - 实时状态通知机制，支持 UI 层订阅状态变化
  - 用户友好的状态描述和进度信息
  - 连接失败次数统计和重连计数器

- **三层故障恢复机制**:
  - **第一层**: Agent 自动重连（最多3次，指数退避算法）
  - **第二层**: WSL镜像重启（连续失败3次后触发）
  - **第三层**: 完整系统检查验证（确保重启成功并验证Agent连接恢复）

- **网络服务配置**:
  - `latency_check_interval`: 延迟检测间隔配置（默认30秒）
  - `latency_check_enabled`: 延迟检测功能开关（默认启用）
  - `latency_warning_threshold_ms`: 延迟警告阈值（默认200ms）
  - `latency_error_threshold_ms`: 延迟错误阈值（默认500ms）
  - `max_reconnect_attempts`: 最大重连次数（默认3次）
  - `reconnect_backoff_multiplier`: 重连退避因子（默认2.0）

- **性能优化**:
  - Agent连接状态缓存机制（5秒缓存）
  - 智能跳过逻辑：网络未连接或功能禁用时自动跳过检测
  - 独立的延迟检测间隔，避免过度频繁的网络请求
  - 异步执行，不阻塞主监控循环

### 应用生命周期阶段

#### 详细业务流程（基于 Legacy 项目分析）

1. **应用启动和初始化阶段**

   - domain 全局上下文创建，加载多环境配置
   - 用户凭据检查和 Token 验证
   - 单实例控制，防止重复启动
   - WSL 配置优化设置（limitedWSLConfig）
   - 系统托盘和窗口初始化

2. **系统环境检查阶段**

   - 按序执行 6 项系统检查（平台 → 版本 →PowerShell→ 虚拟化 →WSL→ 镜像）
   - 各检查项状态实时更新 UI 显示
   - 自动安装缺失组件（PowerShell、WSL、镜像）
   - 仅当所有检查通过(6/6)才允许接单

3. **用户认证和登录阶段**

   - 检查是否有有效凭据（30 天有效期）
   - 支持命令行参数自动登录（`--username --password`）
   - 登录成功后跳转到主页面 (`/main-page`)
   - 保存用户凭据和设置到本地文件

4. **WSL 和镜像管理阶段**

   - WSL 诊断和配置优化
   - echowave-engine 镜像启动和版本检查
   - 镜像自动更新机制（仅在空闲时更新）
   - 通过心跳保持镜像运行状态

5. **接单准备和守护进程管理阶段**

   - 检查自动接单开关状态
   - 按序启动 Tailscale→Docker→Nomad 守护进程
   - 节点注册到服务端 (`/api/node/register`)
   - 获取唯一节点 ID 和网络配置

6. **任务轮询和执行阶段**

   - 定期轮询服务端任务状态 (`/api/node/get`)
   - 开始接单 API 调用 (`/api/node/enable`)
   - 监控 Nomad 任务分配状态
   - 实时更新任务列表和执行进度

7. **状态监控和显示阶段**

   - UI 状态显示：空闲/等待任务/执行中
   - 网络连接状态监控
   - 系统资源使用情况
   - 任务执行日志收集和显示

8. **自动更新管理阶段**

   - 应用程序自动更新检查（2 分钟间隔）
   - 镜像版本检查和智能更新
   - 仅在节点空闲时执行更新
   - 更新进度显示和用户确认机制

9. **优雅关闭流程**
   - 停止接单 API 调用 (`/api/node/disable`)
   - 等待当前任务完成
   - 按序关闭守护进程（Nomad→Docker→Tailscale）
   - 恢复 WSL 原始配置
   - 保存用户设置和清理资源

## Legacy 项目参考

### 业务逻辑参考点

重构时应参考 `legacy/` 目录中的原始 Electron 项目的以下关键业务逻辑：

#### 系统检查模块 (`legacy/BackL0/Operations/`)

- `CheckOS.mjs` - 操作系统版本检查逻辑
- `CheckPwsh.mjs` - PowerShell 安装检查和自动安装
- `CheckVirtual.mjs` - BIOS 虚拟化检查
- `CheckWSL.mjs` - WSL2 安装和配置检查
- `CheckMirror.mjs` - 镜像安装和运行状态检查
- `CheckMirrorNodeStatus.mjs` - 节点空闲状态检查（Nomad 任务监控）

#### 镜像管理模块

- `RunMirror.mjs` - 镜像启动和心跳保持机制
- `StopMirror.mjs` - 镜像停止和清理
- `InstallMirror.mjs` - 镜像自动安装流程
- `UpdateMirror.mjs` - 镜像版本更新机制
- `MirrorVersionManager.js` - 版本管理和比较逻辑

#### 网络和 API 通信

- `legacy/BackL1/` - HTTP 服务器和 API 路由实现
- `legacy/src/renderer/src/mixins/service.ts` - 前端 API 调用封装
- `legacy/src/renderer/src/services/` - 服务端通信模块

#### 用户界面逻辑

- `legacy/src/renderer/src/views/main/mainPage.vue` - 主页面业务逻辑
- `legacy/src/renderer/src/views/main/taskList.vue` - 任务列表显示
- `legacy/src/renderer/src/components/auto-update.vue` - 自动更新 UI

#### 配置和设置管理

- `legacy/Utils/limitedWSLConfig.mjs` - WSL 配置优化
- `legacy/src/main/index.mjs` - 多环境配置和应用生命周期
- 用户凭据和设置的持久化存储模式

### 关键业务常量和配置

```javascript
// 系统检查项配置（来自legacy项目）
const SYSTEM_CHECKS = [
  { alias: "platform", label: "操作系统检查" },
  { alias: "os", label: "系统版本检查" },
  { alias: "powershell", label: "PowerShell检查" },
  { alias: "virtual", label: "BIOS虚拟化检查" },
  { alias: "wsl", label: "WSL2检查" },
  { alias: "mirror", label: "任务引擎检查" },
];

// API端点配置
const API_ENDPOINTS = {
  nodeRegister: "/api/node/register",
  nodeGet: "/api/node/get",
  nodeEnable: "/api/node/enable",
  nodeDisable: "/api/node/disable",
};

// WSL配置优化参数
const WSL_CONFIG = {
  processors: "70%", // CPU占用70%
  memory: "80%", // 内存占用80%
  swap: "8GB", // 8GB交换空间
};
```

### 重构注意事项

1. **保持业务逻辑一致性**: 确保系统检查、任务管理等核心业务逻辑与 legacy 项目保持一致
2. **API 兼容性**: 保持与现有服务端 API 的兼容性
3. **用户体验连续性**: UI 布局和交互方式应保持用户熟悉的体验
4. **配置文件格式**: 用户凭据和设置文件格式应保持兼容以便升级迁移
5. **错误模式**: 继承 legacy 项目中已经验证的错误处理和重试机制

## 开发说明

### 前端开发

- 使用 `components/ui/` 目录中的现有 UI 组件
- 遵循 Vue 3 Composition API 模式
- 利用 `@vueuse/core` 的通用组合式函数
- `lib/utils/http/` 中提供自定义 HTTP 客户端

### Rust 开发

- 遵循工作区依赖模式
- 使用 `tracing` 记录日志，而不是 `println!`
- 使用 `anyhow`/`thiserror` 实现适当的错误处理
- 使用 Tokio 进行异步操作

### 构建配置

- Release 构建使用 LTO 并剥离符号以进行优化
- 前端构建前需要 TypeScript 编译
- 守护程序构建到 WSL 并通过 Makefile 复制到 Windows 路径

## 测试

### 前端测试

```bash
cd desktop
pnpm test                  # 运行 Vitest 测试
pnpm test --ui            # 运行带 UI 界面的测试
pnpm test --coverage      # 运行测试并生成覆盖率报告
```

### Rust 测试

```bash
cargo test                # 运行所有工作区测试
cargo test -p agent       # 运行 agent 测试
cargo test -p domain        # 运行 domain 测试
cargo test -p protocol    # 运行 protocol 测试
```

### 调试命令

```bash
# 前端调试
cd desktop
pnpm dev                  # 启动开发服务器，支持热重载
pnpm tauri dev           # 启动 Tauri 开发模式，可以打开开发者工具

# Rust 调试
RUST_LOG=debug cargo run -p agent       # 带调试日志运行 agent
RUST_LOG=trace cargo run -p domain        # 带详细日志运行 domain

# 单个测试调试
cargo test test_name -- --nocapture     # 运行单个测试并显示输出
```

### 日志和追踪

#### 日志格式
所有模块统一使用 JSON 格式日志，包含以下字段：
- `timestamp`: ISO 8601 时间戳
- `level`: 日志级别 (TRACE/DEBUG/INFO/WARN/ERROR)
- `module`: 模块名称
- `trace_id`: 请求追踪 ID
- `message`: 日志消息
- `target`: 代码位置
- `file`: 源文件
- `line`: 行号

## 部署

应用程序构建为支持中文语言和按机器安装模式的 Windows NSIS 安装程序。

## 重要文档

### 核心架构文档

#### 运行和生命周期文档

- `docs/运行流程图.md` - **主要参考文档** - EchoWave 客户端运行流程和系统状态概览
- `docs/生命周期流程图.md` - 详细的生命周期流程图，包含 Vue Channel 订阅机制和错误处理

#### 架构设计文档

- `docs/重构实施计划.md` - 从 Electron 到 Tauri 的完整重构架构设计和实施计划

### 项目配置

- `desktop/components.json` - shadcn-vue UI 组件配置
- `desktop/tauri.conf.json` - Tauri 应用配置（窗口尺寸、构建目标等）
- `Cargo.toml` - 工作区配置和共享依赖