# 空闲检测功能说明

## 功能概述

在程序启动后的30秒内，如果检测到用户处于空闲状态（无任何输入操作），程序将自动隐藏主窗口。

## 实现原理

1. **空闲检测**: 使用 `user-idle` crate 获取系统级别的用户空闲时间
2. **定时检查**: 程序启动30秒后检查用户是否在过去30秒内有活动
3. **自动隐藏**: 如果检测到空闲状态，自动隐藏主窗口到系统托盘

## 技术实现

### 依赖项
- `user-idle = "0.6.0"` - 跨平台的用户空闲时间检测库

### 核心模块
- `idle_watcher.rs` - 空闲监听服务模块
- 集成在 `lib.rs` 的应用启动流程中

### 关键逻辑

```rust
// 检查用户是否处于空闲状态（空闲时间 >= 30秒）
fn check_user_idle_state() -> Result<bool, String> {
    match UserIdle::get_time() {
        Ok(idle_time) => {
            let idle_seconds = idle_time.as_seconds();
            info!("当前用户空闲时间: {}秒", idle_seconds);
            
            // 如果空闲时间 >= 30秒，认为是空闲状态
            Ok(idle_seconds >= 30)
        }
        Err(e) => {
            Err(format!("获取用户空闲时间失败: {:?}", e))
        }
    }
}
```

## 工作流程

1. **程序启动**: Tauri 应用启动时初始化空闲监听器
2. **等待30秒**: 使用 `tokio::time::sleep(Duration::from_secs(30))` 等待
3. **检查空闲状态**: 调用 `UserIdle::get_time()` 获取用户空闲时间
4. **判断条件**: 如果空闲时间 >= 30秒，则认为用户处于空闲状态
5. **执行隐藏**: 调用 `window.hide()` 隐藏主窗口

## 平台支持

该功能支持以下平台：
- ✅ Windows
- ✅ macOS  
- ✅ Linux (X11)
- ⚠️ Linux (Wayland) - 有限支持

## 配置选项

当前实现为固定配置：
- 启动延迟时间: 30秒
- 空闲阈值: 30秒
- 检测频率: 一次性检测

## 日志输出

程序会输出以下日志信息：
- `开始启动30秒内空闲状态检测`
- `当前用户空闲时间: {X}秒`
- `检测到用户在启动30秒内处于空闲状态，自动隐藏窗口`
- `检测到用户在启动30秒内有活动，保持窗口显示`

## 测试方法

1. 启动应用程序
2. 保持30秒内不进行任何键盘或鼠标操作
3. 观察窗口是否自动隐藏
4. 通过系统托盘可以重新显示窗口

## 扩展功能

当前实现包含但未启用的扩展功能：
- 持续空闲监听 (`start_continuous_idle_watch`)
- 用户活动监听器 (`register_activity_listeners`)

这些功能可以在后续版本中根据需求启用。

## 注意事项

1. 该功能仅在应用启动的前30秒内生效
2. 空闲检测基于系统级别的用户输入活动
3. 窗口隐藏后可通过托盘图标重新显示
4. 功能实现为异步操作，不会阻塞主线程 