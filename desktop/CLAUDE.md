# CLAUDE.md - 桌面渲染模块

此文件为 Claude Code 在处理桌面渲染模块代码时提供指导。

## 模块概述

桌面渲染模块 (desktop) 是 EchoWave 客户端的用户界面层，基于 Tauri + Vue 3 + TypeScript 构建。作为四模块架构中的渲染层，严格遵循职责分离原则。

## 模块职责

### ✅ 包含的职责

- **UI 渲染**: 使用 Vue 3 渲染所有用户界面元素
- **状态展示**: 根据 core 模块推送的状态更新界面
- **用户交互**: 响应用户操作，将命令发送到 core 模块
- **视觉反馈**: 加载状态、进度条、通知提示等
- **路由管理**: 页面导航和路由守卫

### ❌ 不包含的职责

- **业务逻辑**: 所有业务规则和决策由 core 模块处理
- **系统调用**: 不直接调用系统 API，通过 core 模块代理
- **网络请求**: 不直接发起 HTTP 请求，由 core 统一处理
- **进程管理**: 不管理外部进程，由 agent 模块负责
- **状态计算**: 不进行复杂的状态计算，仅展示 core 的状态

## 项目结构

```
desktop/
├── src/                        # Vue 前端源码
│   ├── assets/                 # 静态资源
│   │   ├── icons/             # SVG 图标
│   │   └── images/            # 图片资源
│   ├── commands/              # Tauri 命令类型定义
│   ├── components/            # Vue 组件
│   │   ├── ui/               # 基础 UI 组件库 (基于 shadcn-vue)
│   │   └── CustomTitleBar.vue # 自定义标题栏
│   ├── composables/           # Vue 组合式函数
│   ├── lib/                   # 工具库
│   │   └── utils/            # 工具函数
│   ├── routes/               # 路由配置
│   ├── stores/               # Pinia 状态存储
│   ├── views/                # 页面组件
│   │   ├── launch/           # 启动页
│   │   ├── login/            # 登录页
│   │   ├── dashboard/        # 主控制面板
│   │   └── settings/         # 设置页
│   └── main.ts               # 应用入口
├── src-tauri/                 # Tauri 后端
│   ├── src/
│   │   ├── commands/         # Tauri 命令实现
│   │   ├── idle_watcher.rs   # 空闲检测
│   │   ├── state.rs          # 应用状态管理
│   │   └── tray.rs           # 系统托盘
│   └── tauri.conf.json       # Tauri 配置
└── package.json              # 项目配置
```

## 开发规范

### 组件开发

- 使用 Vue 3 Composition API 和 `<script setup>` 语法
- 组件文件名使用 PascalCase，如 `TaskStatus.vue`
- 私有组件放在 `_components` 文件夹中
- 优先使用 `components/ui/` 中的基础组件

### 状态管理

- 使用 Pinia 管理全局状态
- Store 文件放在 `stores/` 目录
- 通过 `subscription-manager.ts` 订阅 core 状态更新
- 不在组件中直接修改全局状态

### 样式规范

- 使用 TailwindCSS 进行样式开发
- 全局样式定义在 `views/global.css`
- 组件样式使用 `<style scoped>`
- 遵循设计系统的颜色和间距规范

### TypeScript 规范

- 所有代码必须有明确的类型定义
- 使用 `commands/types.ts` 中的类型与 Rust 后端通信
- 避免使用 `any` 类型
- 接口命名使用 `I` 前缀，如 `IUserInfo`

## 核心功能模块

### 1. 启动页 (launch)

- 展示系统环境检查进度
- 显示 6 项检查的状态（平台、版本、PowerShell、虚拟化、WSL、镜像）
- 所有检查通过后自动跳转到登录页

### 2. 登录页 (login)

- 支持密码登录和验证码登录
- 记住密码功能
- 自动登录处理
- 登录成功后跳转到主控制面板

### 3. 主控制面板 (dashboard)

- 显示接单状态和任务执行情况
- 展示网络连接状态
- 实时日志显示
- 系统资源监控
- 快速操作按钮（开始/停止接单）

### 4. 设置页 (settings)

- 用户偏好设置
- 自动接单开关
- 开机自启动配置
- 应用更新管理

## 与 Core 模块通信

### 命令调用 (invoke)

```typescript
// 调用 core 模块的命令
import { invoke } from "@tauri-apps/api/core";
import { IResponse } from "@/commands/types";

const response = await invoke<IResponse<any>>("command_name", {
  param1: value1,
});
```

### 事件订阅 (subscribe)

```typescript
// 订阅 core 模块的状态更新
import { onMounted, onUnmounted } from "vue";
import { subscriptionManager } from "@/stores/subscription-manager";

onMounted(() => {
  subscriptionManager.subscribe("system_status", (data) => {
    // 处理状态更新
  });
});

onUnmounted(() => {
  subscriptionManager.unsubscribe("system_status");
});
```

## UI 组件库

项目使用基于 shadcn-vue 的自定义组件库，位于 `components/ui/` 目录：

- **Button**: 按钮组件，支持异步操作
- **Card**: 卡片容器组件
- **Form**: 表单组件系列
- **Input**: 输入框组件，包含密码输入
- **Switch**: 开关组件
- **Badge**: 徽章组件
- **Tooltip**: 工具提示
- **Dropdown Menu**: 下拉菜单
- **Tabs**: 标签页组件
- **Alert**: 警告提示
- **Sonner**: Toast 通知组件

## 开发命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器（仅前端）
pnpm dev

# 启动 Tauri 开发模式（前端 + 后端）
pnpm tauri dev

# 构建前端
pnpm build

# 构建完整应用
pnpm tauri build

# 代码格式化
pnpm prettier

# 运行测试
pnpm test

# 类型检查
pnpm build  # 包含 vue-tsc --noEmit
```

## 构建配置

### 窗口配置 (tauri.conf.json)

- 尺寸: 360x585px
- 不可调整大小
- 始终置顶
- 跳过任务栏
- 自定义标题栏

### 构建目标

- Windows: NSIS 安装程序
- 支持中文语言
- 按机器安装模式

## 注意事项

1. **严格的职责分离**: 渲染模块只负责 UI，不处理业务逻辑
2. **类型安全**: 确保所有与 Rust 后端的通信都有正确的类型定义
3. **响应式设计**: 虽然窗口大小固定，但组件应考虑不同内容长度
4. **错误处理**: 友好地展示来自 core 的错误信息
5. **性能优化**: 合理使用 Vue 的响应式特性，避免不必要的重渲染
6. **用户体验**: 保持与 legacy 项目一致的交互模式

## 调试技巧

1. **开发者工具**: 在 Tauri 开发模式下可以打开 Chrome DevTools
2. **Vue DevTools**: 安装 Vue DevTools 扩展进行状态调试
3. **日志输出**: 使用 `lib/utils/logger.ts` 进行日志记录
4. **网络调试**: 通过 core 模块的日志查看 API 请求详情

## 常见问题

1. **状态不同步**: 检查 subscription-manager 是否正确订阅事件
2. **命令调用失败**: 确认命令名称和参数类型与 Rust 端一致
3. **样式问题**: 检查 TailwindCSS 配置和类名拼写
4. **构建失败**: 运行类型检查确保没有 TypeScript 错误

## 相关文档

- Vue 3 文档: https://vuejs.org/
- Tauri 文档: https://tauri.app/
- TailwindCSS 文档: https://tailwindcss.com/
- shadcn-vue 文档: https://www.shadcn-vue.com/