import { defineConfig, type UserConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import svgr from "vite-svg-loader";
import tailwindcss from "@tailwindcss/vite";
import fs from "node:fs/promises";
import path from "node:path";
import { fileURLToPath, URL } from "node:url";
import type { CompilerOptions } from "typescript";
import "vitest/config";

// @ts-expect-error process is a nodejs global
const __HOST__ = process.env.TAURI_DEV_HOST;
const __DEV_PORT__ = 5173;
const __WS_PORT__ = 5174;
const __PROJECT__ = process.cwd();

const parseProject = async () => {
    return fs.readFile(path.join(__PROJECT__, "/package.json")).then<{
        version: string;
    }>((buf) => JSON.parse(buf.toString()));
};
const parseTSAlias = async () => {
    try {
        const { baseUrl = ".", paths = {} }: CompilerOptions = await fs
            .readFile(path.join(__PROJECT__, "/tsconfig.json"))
            .then((r) => {
                const content = r
                    .toString()
                    .replace(/^\s+\/\*.+\*\/\s*$/gm, "")
                    .replace(/\/\/.*$/gm, "")
                    .replace(/,(\s*[\]}])/gms, "$1");
                return JSON.parse(content).compilerOptions;
            });
        return Object.keys(paths).reduce(
            (alias, key) => {
                const aliasDir = paths[key][0]?.replace("/*", "");
                alias[key.toString().replace("/*", "")] = fileURLToPath(
                    new URL(`${baseUrl}/${aliasDir}`, import.meta.url),
                );
                return alias;
            },
            { "@": fileURLToPath(new URL("./src", import.meta.url)) } as Record<
                string,
                string
            >,
        );
    } catch (e) {
        console.error("[Vite] Parse tsconfig failed, return empty alias\n", e);
        process.exit(1);
    }
};

// https://vitejs.dev/config/
export default defineConfig(async () => {
    const project = await parseProject();
    return {
        define: {
            __ENDPOINT__: '""',
            __VERSION__: JSON.stringify(project.version),
            __BUILD_TIMESTAMP__: Date.now(),
            // 让内联测试能被 Tree shaking
            "import.meta.vitest": "undefined",
        },
        plugins: [vue(), svgr(), tailwindcss()],

        // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
        //
        // 1. prevent vite from obscuring rust errors
        clearScreen: false,

        // 2. tauri expects a fixed port, fail if that port is not available
        server: {
            port: __DEV_PORT__,
            strictPort: true,
            host: __HOST__ || false,
            hmr: __HOST__
                ? {
                      protocol: "ws",
                      host: __HOST__,
                      port: __WS_PORT__,
                  }
                : undefined,
            watch: {
                // 3. tell vite to ignore watching `src-tauri`
                ignored: ["**/src-tauri/**"],
            },
        },
        resolve: {
            alias: await parseTSAlias(),
        },
    } satisfies UserConfig;
});
