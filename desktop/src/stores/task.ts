import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import {
    type CoreEvent,
    EVENT_NAMES,
    TaskAcceptanceStatus,
    TaskExecutionStatus,
    TaskState,
    Uuid,
} from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";
import { executeCommand } from "@/commands";

// 来自 crates\domain\src\services\task_acceptance_service.rs

export const useTaskStore = defineStore("task", () => {
    // =================== 状态定义 ===================

    // Core 状态（来自 StateProjection）
    const state = ref<TaskState>({
        acceptance: TaskAcceptanceStatus.Stopped,
        execution: TaskExecutionStatus.Idle,
        last_task_start_at: undefined,
    });

    // 订阅ID
    const subscriptionId = ref<number | null>(null);

    // =================== 计算属性 ===================

    const isAccepting = computed(
        () => state.value.acceptance === TaskAcceptanceStatus.Accepting,
    );

    const isExecuting = computed(
        () => state.value.execution === TaskExecutionStatus.Executing,
    );

    const canToggleAcceptance = computed(() => {
        const currentAcceptance = state.value.acceptance;
        return (
            currentAcceptance === TaskAcceptanceStatus.Accepting ||
            currentAcceptance === TaskAcceptanceStatus.Stopped
        );
    });

    const acceptanceStatusText = computed(() => {
        switch (state.value.acceptance) {
            case TaskAcceptanceStatus.Accepting:
                return "正在接单";
            case TaskAcceptanceStatus.Stopped:
                return "已停止";
            case TaskAcceptanceStatus.Starting:
                return "启动中...";
            case TaskAcceptanceStatus.Stopping:
                return "停止中...";
            default:
                return "未知状态";
        }
    });

    const executionStatusText = computed(() => {
        switch (state.value.execution) {
            case TaskExecutionStatus.Idle:
                return "空闲";
            case TaskExecutionStatus.Executing:
                return "执行中";
            default:
                return "未知状态";
        }
    });

    // =================== 数据转换方法 ===================

    // 转换 Rust enum 数据为 TypeScript 格式
    const transformTaskState = (rawData: TaskState): TaskState => {
        const transformedState: TaskState = {
            acceptance: rawData.acceptance || TaskAcceptanceStatus.Stopped,
            execution: "Idle" as TaskExecutionStatus,
            last_task_start_at: undefined,
        };

        // 处理 TaskExecutionStatus 的转换
        if (
            typeof rawData.execution === "object" &&
            rawData.execution !== null
        ) {
            if ("Executing" in rawData.execution) {
                // Rust 格式: { Executing: { start_time: "2024-01-01T10:00:00Z" } }
                const executingData = (
                    rawData.execution as {
                        Executing: { start_time: string };
                    }
                ).Executing;
                transformedState.execution = TaskExecutionStatus.Executing;
                // 更新 last_task_start_at
                transformedState.last_task_start_at =
                    executingData.start_time || new Date().toISOString();
            }
        }

        return transformedState;
    };

    // =================== 事件处理方法 ===================

    // 处理任务状态更新事件
    const handleTaskStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "Task")
            return;

        try {
            if (event.data && typeof event.data === "object") {
                // 使用转换函数处理 Rust enum 数据
                const transformedState = transformTaskState(event.data);
                Object.assign(state.value, transformedState);
                console.debug("Task state updated:", {
                    raw: event.data,
                    transformed: transformedState,
                });
            } else {
                console.warn("Task state update event has invalid data format");
            }
        } catch (err) {
            console.error("Error handling task state update:", err);
        }
    };

    // =================== 订阅管理 ===================

    // 设置任务状态订阅
    const setupSubscription = async () => {
        if (subscriptionId.value) {
            console.warn("Task subscription already exists");
            return;
        }

        try {
            subscriptionId.value = await subscriptionManager.subscribe(
                EVENT_NAMES.TASK_STATE_UPDATED,
                handleTaskStateUpdate,
            );

            console.log("Task store subscribed to state updates");
        } catch (err) {
            console.error("Failed to setup task subscription:", err);
        }
    };

    // =================== 任务操作方法 ===================

    // 切换任务接单状态
    const toggleAcceptance = async (trace_id: Uuid) => {
        if (!canToggleAcceptance.value) {
            console.warn(
                "Cannot toggle task acceptance in current state:",
                state.value.acceptance,
            );
            return;
        }

        try {
            await executeCommand({
                type: "ToggleTaskAcceptance",
                trace_id,
            });
            // 状态切换成功后，状态会通过事件更新
            console.log("Task acceptance toggle request sent successfully");
        } catch (error) {
            console.error("Failed to toggle task acceptance:", error);
            throw error;
        }
    };

    // 手动启动接单（当前为停止状态时）
    const startAcceptance = async (trace_id: Uuid) => {
        if (state.value.acceptance !== TaskAcceptanceStatus.Stopped) {
            console.warn("Can only start acceptance when stopped");
            return;
        }
        await toggleAcceptance(trace_id);
    };

    // 手动停止接单（当前为接单状态时）
    const stopAcceptance = async (trace_id: Uuid) => {
        if (state.value.acceptance !== TaskAcceptanceStatus.Accepting) {
            console.warn("Can only stop acceptance when accepting");
            return;
        }
        await toggleAcceptance(trace_id);
    };

    // =================== 生命周期管理 ===================

    setupSubscription();

    // =================== 返回 Store API ===================

    const setState = (newState: TaskState) => {
        // 使用转换函数处理可能的 Rust enum 数据
        const transformedState = transformTaskState(newState);
        state.value = transformedState;
    };

    return {
        // Core 状态
        state: readonly(state),
        setState,

        // 计算属性
        isAccepting,
        isExecuting,
        canToggleAcceptance,
        acceptanceStatusText,
        executionStatusText,

        // 操作方法
        toggleAcceptance,
        startAcceptance,
        stopAcceptance,
    };
});
