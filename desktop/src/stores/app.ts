import { defineStore } from "pinia";
import { ref, readonly } from "vue";
import { subscriptionManager } from "./subscription-manager";
import { useUserStore } from "./user";
import { EVENT_NAMES, ProjectedState } from "~/commands/types";
import { executeCommand, generateTraceId } from "@/commands";
import { useSystemStore } from "./system";
import { useSettingsStore } from "./settings";
import { useNetworkStore } from "./network";
import { useTaskStore } from "./task";
import { useComputedStore } from "./computed";
import { useDeviceStore } from "./device";

export const useAppStore = defineStore("app", () => {
    const isReady = ref(false);

    const userStore = useUserStore();
    const systemStore = useSystemStore();
    const settingsStore = useSettingsStore();
    const networkStore = useNetworkStore();
    const taskStore = useTaskStore();
    const computedStore = useComputedStore();
    const deviceStore = useDeviceStore();

    const projectedState = readonly<ProjectedState>({
        user: userStore.state,
        system: systemStore.state as ProjectedState["system"],
        settings: settingsStore.state,
        network: networkStore.state as ProjectedState["network"],
        task: taskStore.state,
        device: deviceStore.state,
        computed: computedStore.state,
    });

    const initialize = async () => {
        if (isReady.value) return;
        try {
            console.log("🚀 Initializing application state...");
            await subscriptionManager.cleanup();
            await subscriptionManager.subscribe(
                EVENT_NAMES.ALL_STATE_UPDATED,
                (event) => {
                    if (
                        event.type !== "StateUpdated" ||
                        event.state_type !== "All"
                    ) {
                        return;
                    }
                    console.log("🔄 Received state update:", event);
                    userStore.setState(event.data.user!);
                    systemStore.setState(event.data.system!);
                    settingsStore.setState(event.data.settings!);
                    networkStore.setState(event.data.network!);
                    taskStore.setState(event.data.task!);
                    deviceStore.setState(event.data.device!);
                    computedStore.setState(event.data.computed!);
                },
            );
            await executeCommand({
                type: "GetCurrentState",
                trace_id: generateTraceId(),
            });
            isReady.value = true;
            console.log("✅ Application state initialized.");
        } catch (err) {
            console.error("❌ Failed to initialize application state:", err);
        }
    };
    const cleanup = async () => {
        subscriptionManager.cleanup();
    };
    return {
        isReady,
        initialize,
        projectedState,
        cleanup,
    };
});
