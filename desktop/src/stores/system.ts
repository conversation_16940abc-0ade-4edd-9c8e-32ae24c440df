import { defineStore } from "pinia";
import { ref, readonly } from "vue";
import {
    type CoreEvent,
    EVENT_NAMES,
    SystemState,
    Uuid,
} from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";
import { executeCommand } from "~/commands";

// 来自 crates\domain\src\services\system_check.rs

export const useSystemStore = defineStore("system", () => {
    // =================== 状态定义 ===================

    // Core 状态（来自 StateProjection）
    const state = ref<SystemState>({
        check_results: [],
        powershell_status: {
            is_installed: false,
            version: undefined,
            installation_progress: 0,
        },
        last_check_time: undefined,
        wsl_mirror_status: {
            is_downloaded: false,
            is_installed: false,
            download_progress: 0,
            install_progress: 0,
            version: undefined,
        },
        wsl_installer_status: {
            is_installed: false,
            is_downloaded: false,
            download_progress: 0,
            install_progress: 0,
            version: undefined,
        },
        is_check_running: false,
    });

    // 处理系统状态更新事件
    const handleSystemStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "System")
            return;

        try {
            if (event.data && typeof event.data === "object") {
                // 更新 Core 状态
                Object.assign(state.value, event.data);
                console.debug("System state updated:", event.data);
            } else {
                console.warn(
                    "System state update event has invalid data format",
                );
            }
        } catch (err) {
            console.error("Error handling system state update:", err);
        }
    };

    // =================== 订阅管理 ===================

    // 设置系统状态订阅
    const setupSubscription = async () => {
        try {
            await subscriptionManager.subscribe(
                EVENT_NAMES.SYSTEM_STATE_UPDATED,
                handleSystemStateUpdate,
            );

            console.log("System store subscribed to state updates");
        } catch (err) {
            console.error("Failed to setup system subscription:", err);
        }
    };

    // =================== 系统操作方法 ===================

    // 触发系统检查
    const triggerSystemCheck = async (trace_id: Uuid) => {
        try {
            await executeCommand({
                type: "TriggerSystemCheck",
                trace_id,
            });
            console.log("System check triggered successfully");
        } catch (error) {
            console.error("Failed to trigger system check:", error);
            throw error;
        }
    };

    // =================== 生命周期管理 ===================

    setupSubscription();

    // =================== 返回 Store API ===================

    const setState = (newState: SystemState) => {
        Object.assign(state.value, newState);
    };

    return {
        // Core 状态
        state: readonly(state),
        setState,

        // 操作方法
        triggerSystemCheck,
    };
});
