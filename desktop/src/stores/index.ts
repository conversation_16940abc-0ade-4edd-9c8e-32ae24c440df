import { createPinia } from "pinia";

// 创建Pinia实例
export const pinia = createPinia();

// =================== Store 导出 ===================

// 导出所有重构后的 store
export { useAppStore } from "./app";
export { useUserStore } from "./user";
export { useTaskStore } from "./task";
export { useSystemStore } from "./system";
export { useSettingsStore } from "./settings";
export { useNetworkStore } from "./network";
export { useComputedStore } from "./computed";

// 导出订阅管理器
export {
    subscriptionManager,
    SubscriptionManager,
} from "./subscription-manager";
