import { defineStore } from "pinia";
import { ref, readonly } from "vue";
import { type CoreEvent, EVENT_NAMES, Uuid } from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";
import { executeCommand } from "@/commands";

// 来自 crates\domain\src\services\settings_service.rs
export interface UserSettingsState {
    auto_accept_tasks: boolean;
    auto_start_on_boot: boolean;
    data_root_path: string;
}

export const useSettingsStore = defineStore("settings", () => {
    // =================== 状态定义 ===================

    // Core 状态（来自 StateProjection）
    const state = ref<UserSettingsState>({
        auto_accept_tasks: false,
        auto_start_on_boot: false,
        data_root_path: "",
    });

    // 订阅ID
    const subscriptionId = ref<number | null>(null);

    // =================== 计算属性 ===================

    // =================== 事件处理方法 ===================

    // 处理设置状态更新事件
    const handleSettingsStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "Settings")
            return;

        try {
            if (event.data && typeof event.data === "object") {
                // 更新 Core 状态
                Object.assign(state.value, event.data);
                console.debug("Settings state updated:", event.data);
            } else {
                console.warn(
                    "Settings state update event has invalid data format",
                );
                return;
            }
        } catch (err) {
            console.error("Error handling settings state update:", err);
        }
    };

    // =================== 订阅管理 ===================

    // 设置设置状态订阅
    const setupSubscription = async () => {
        if (subscriptionId.value) {
            console.warn("Settings subscription already exists");
            return;
        }

        try {
            subscriptionId.value = await subscriptionManager.subscribe(
                EVENT_NAMES.SETTINGS_STATE_UPDATED,
                handleSettingsStateUpdate,
            );

            console.log("Settings store subscribed to state updates");
        } catch (err) {
            console.error("Failed to setup settings subscription:", err);
        }
    };

    // =================== 设置操作方法 ===================

    // 更新完整设置
    const updateSettings = async (
        newSettings: UserSettingsState,
        trace_id: Uuid,
    ) => {
        try {
            await executeCommand({
                type: "UpdateSettings",
                settings: newSettings,
                trace_id,
            });
            console.log("Settings update request sent successfully");
        } catch (error) {
            console.error("Failed to update settings:", error);
            throw error;
        } finally {
        }
    };

    // 更新自动接单设置
    const updateAutoAcceptSetting = async (
        enabled: boolean,
        trace_id: Uuid,
    ) => {
        const newSettings: UserSettingsState = {
            ...state.value,
            auto_accept_tasks: enabled,
        };
        await updateSettings(newSettings, trace_id);
    };

    // 更新开机自启设置
    const updateAutoStartOnBootSetting = async (
        enabled: boolean,
        trace_id: Uuid,
    ) => {
        const newSettings: UserSettingsState = {
            ...state.value,
            auto_start_on_boot: enabled,
        };
        await updateSettings(newSettings, trace_id);
    };

    // 更新 Distro 安装路径设置
    const updateDataRootPathSetting = async (path: string, trace_id: Uuid) => {
        const newSettings: UserSettingsState = {
            ...state.value,
            data_root_path: path,
        };
        await updateSettings(newSettings, trace_id);
    };

    // =================== 生命周期管理 ===================

    setupSubscription();

    // =================== 返回 Store API ===================

    const setState = (newState: UserSettingsState) => {
        state.value = newState;
    };

    return {
        // Core 状态
        state: readonly(state),
        setState,

        // 操作方法
        updateAutoAcceptSetting,
        updateAutoStartOnBootSetting,
        updateDataRootPathSetting,
    };
});
