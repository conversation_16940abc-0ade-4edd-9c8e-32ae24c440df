// 状态订阅管理器
// 负责管理状态订阅、事件分发和错误处理

import { Channel } from "@tauri-apps/api/core";
import { type CoreEvent, type EventName, Uuid } from "~/commands/types";
import { generateTraceId, subscribeEvent, unsubscribeEvent } from "~/commands";

// 订阅者信息
interface Subscriber {
    id: number;
    traceId: Uuid;
    eventName: EventName;
    callback: (event: CoreEvent) => void;
}

// 订阅管理器类
export class SubscriptionManager {
    private subscribers = new Map<number, Subscriber>();

    // 单例实例
    private static instance?: SubscriptionManager;

    constructor() {
        if (SubscriptionManager.instance) {
            return SubscriptionManager.instance;
        }
        SubscriptionManager.instance = this;
    }

    // 获取单例实例
    static getInstance(): SubscriptionManager {
        if (!SubscriptionManager.instance) {
            SubscriptionManager.instance = new SubscriptionManager();
        }
        return SubscriptionManager.instance;
    }

    // 订阅事件
    subscribe = async (
        eventName: EventName,
        callback: (event: CoreEvent) => void,
    ) => {
        const channel = new Channel<CoreEvent>();
        channel.onmessage = (event) => {
            callback(event);
        };
        const traceId = generateTraceId();
        const subscriberId = await subscribeEvent(eventName, traceId, channel);
        this.subscribers.set(subscriberId, {
            id: subscriberId,
            eventName,
            callback,
            traceId,
        });
        return subscriberId;
    };

    // 取消订阅
    unsubscribe = async (subscriberId: number): Promise<void> => {
        const subscriber = this.subscribers.get(subscriberId);
        if (!subscriber) {
            console.warn(`Subscriber ${subscriberId} not found`);
            return;
        }
        await unsubscribeEvent(
            subscriber.eventName,
            subscriber.id,
            subscriber.traceId,
        );

        this.subscribers.delete(subscriberId);

        console.log(`Subscriber ${subscriberId} unsubscribed`);
    };

    // 清理所有订阅
    async cleanup(): Promise<void> {
        // 关闭所有订阅
        console.log("Cleaning up all subscriptions");

        for (const subscriberId of this.subscribers.keys()) {
            await this.unsubscribe(subscriberId);
        }

        console.log("SubscriptionManager cleaned up");
    }
}

// 创建全局实例
export const subscriptionManager = SubscriptionManager.getInstance();

// 便捷函数
export const createEventSubscription = async (
    eventName: EventName,
    callback: (event: CoreEvent) => void,
): Promise<number> => {
    return await subscriptionManager.subscribe(eventName, callback);
};

export const removeEventSubscription = async (
    subscriptionId: number,
): Promise<void> => {
    await subscriptionManager.unsubscribe(subscriptionId);
};

export default SubscriptionManager;
