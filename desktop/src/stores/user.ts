import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { type CoreEvent, EVENT_NAMES, UserState, Uuid } from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";
import { executeCommand } from "@/commands";

// file:crates\domain\src\services\user_service.rs

export const useUserStore = defineStore("user", () => {
    // =================== 状态定义 ===================

    // Core 状态（来自 StateProjection）
    const state = ref<UserState>({
        is_logged_in: false,
        is_logging_in: false,
        next_code_send_available_at: 0,
        user_info: undefined,
        wallet_balance: 0.0,
        last_sync_time: Date.now(),
    });

    // 订阅ID
    const subscriptionId = ref<number | null>(null);

    // =================== 计算属性 ===================

    const isLoggedIn = computed(() => state.value.is_logged_in);
    const userInfo = computed(() => state.value.user_info);
    const walletBalance = computed(() => state.value.wallet_balance);
    const lastSyncTime = computed(() => state.value.last_sync_time);

    // =================== 事件处理方法 ===================

    // 处理用户状态更新事件
    const handleUserStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "User")
            return;

        try {
            if (event.data && typeof event.data === "object") {
                // 更新 Core 状态
                Object.assign(state.value, event.data);
                console.debug("User state updated:", event.data);
            } else {
                console.warn("User state update event has invalid data format");
            }
        } catch (err) {
            console.error("Error handling user state update:", err);
        }
    };

    // =================== 订阅管理 ===================

    // 设置用户状态订阅
    const setupSubscription = async () => {
        if (subscriptionId.value) {
            console.warn("User subscription already exists");
            return;
        }

        try {
            subscriptionId.value = await subscriptionManager.subscribe(
                EVENT_NAMES.USER_STATE_UPDATED,
                handleUserStateUpdate,
            );

            console.log("User store subscribed to state updates");
        } catch (err) {
            console.error("Failed to setup user subscription:", err);
        }
    };

    // =================== 用户操作方法 ===================

    // 用户登录
    const login = async (
        args:
            | {
                  type: "password";
                  username: string;
                  password: string;
              }
            | {
                  type: "otp";
                  username: string;
                  otp: string;
              },
        trace_id: Uuid,
    ) => {
        try {
            if (args.type === "password") {
                const { username, password } = args;
                await executeCommand({
                    type: "Login",
                    username: username,
                    password: password,
                    trace_id,
                });
            } else if (args.type === "otp") {
                const { username, otp } = args;
                await executeCommand({
                    type: "LoginWithCode",
                    phone: username,
                    code: otp,
                    trace_id,
                });
            }
            // 登录成功后，状态会通过事件更新
            console.log("Login request sent successfully");
        } catch (error) {
            console.error("Login failed:", error);
            throw error;
        } finally {
        }
    };

    // 用户登出
    const logout = async (trace_id: Uuid) => {
        try {
            await executeCommand({
                type: "Logout",
                trace_id,
            });
            // 登出成功后，状态会通过事件更新
            console.log("Logout request sent successfully");
        } catch (error) {
            console.error("Logout failed:", error);
            throw error;
        } finally {
        }
    };
    // 发送验证码
    const sendVerificationCode = async (phone: string, trace_id: Uuid) => {
        await executeCommand({
            type: "SendVerificationCode",
            phone: phone,
            trace_id,
        });
    };
    const openRegisterPage = async (trace_id: Uuid) => {
        await executeCommand({
            type: "OpenExternalPage",
            page_type: "Register",
            trace_id,
        });
    };
    const openForgotPasswordPage = async (trace_id: Uuid) => {
        await executeCommand({
            type: "OpenExternalPage",
            page_type: "ForgotPassword",
            trace_id,
        });
    };

    // =================== 生命周期管理 ===================

    setupSubscription();

    // =================== 返回 Store API ===================

    const setState = (newState: UserState) => {
        state.value = newState;
    };

    return {
        // Core 状态
        state: readonly(state),
        setState,
        isLoggedIn,
        userInfo,
        walletBalance,
        lastSyncTime,

        // 操作方法
        login,
        logout,
        sendVerificationCode,
        openRegisterPage,
        openForgotPasswordPage,
    };
});
