import { CoreEvent, EVENT_NAMES } from "~/commands/types";
import { defineStore } from "pinia";
import { ref } from "vue";
import { subscriptionManager } from "./subscription-manager";

export const useLogsStore = defineStore("logs", () => {
    const logs = ref<Extract<CoreEvent, { type: "LogMessage" }>[]>([]);

    // 订阅ID
    const subscriptionId = ref<number | null>(null);

    const setupSubscription = async () => {
        if (subscriptionId.value) {
            console.warn("Logs subscription already exists");
            return;
        }

        try {
            subscriptionId.value = await subscriptionManager.subscribe(
                EVENT_NAMES.LOG_MESSAGE,
                (event) => {
                    if (event.type !== "LogMessage") return;
                    logs.value.push(event);
                },
            );
            console.log("Logs store subscribed to logs");
        } catch (err) {
            console.error("Failed to setup logs subscription:", err);
        }
    };
    setupSubscription();

    return {
        logs,
    };
});
