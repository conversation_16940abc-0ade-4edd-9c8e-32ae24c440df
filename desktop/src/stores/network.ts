import { defineStore } from "pinia";
import { ref, computed, readonly } from "vue";
import { type CoreEvent, EVENT_NAMES, NetworkState } from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";

// 来自 crates\domain\src\services\network_service.rs

export const useNetworkStore = defineStore("network", () => {
    // =================== 状态定义 ===================

    // Core 状态（来自 StateProjection）
    const state = ref<NetworkState>({
        internet_connectivity: false,
        tailscale_latency: undefined,
        last_update_time: undefined,
        connection_history: [],
    });

    // =================== 计算属性 ===================

    // 网络连接状态文本
    const connectivityStatusText = computed(() => {
        return state.value.internet_connectivity ? "已连接" : "未连接";
    });

    // 整体网络状态
    const overallNetworkStatus = computed(() => {
        if (!state.value.internet_connectivity) return "offline";
        if (state.value.tailscale_latency === undefined) return "connecting";
        if (state.value.tailscale_latency > 1000) return "poor";
        if (state.value.tailscale_latency > 500) return "fair";
        return "good";
    });

    // 网络状态描述
    const networkStatusDescription = computed(() => {
        switch (overallNetworkStatus.value) {
            case "offline":
                return "网络未连接";
            case "connecting":
                return "连接服务器中...";
            case "poor":
                return "网络质量较差";
            case "fair":
                return "网络质量一般";
            case "good":
                return "网络连接良好";
            default:
                return "网络状态未知";
        }
    });

    // Tailscale 延迟状态文本
    const tailscaleLatencyText = computed(() => {
        const latency = state.value.tailscale_latency;
        if (latency === undefined) return "未连接";
        if (latency > 1000) return `${latency}ms (较慢)`;
        if (latency > 500) return `${latency}ms (一般)`;
        return `${latency}ms (良好)`;
    });

    // 最后更新时间格式化
    const lastUpdateTimeFormatted = computed(() => {
        if (!state.value.last_update_time) return "从未更新";

        try {
            const date = new Date(state.value.last_update_time);
            const now = new Date();
            const diffMs = now.getTime() - date.getTime();
            const diffMinutes = Math.floor(diffMs / (1000 * 60));

            if (diffMinutes < 1) return "刚刚";
            if (diffMinutes < 60) return `${diffMinutes}分钟前`;
            if (diffMinutes < 1440)
                return `${Math.floor(diffMinutes / 60)}小时前`;
            return date.toLocaleString("zh-CN");
        } catch {
            return "时间格式错误";
        }
    });

    // 网络是否可用于工作
    const isNetworkReadyForWork = computed(() => {
        return (
            state.value.internet_connectivity &&
            state.value.tailscale_latency !== undefined &&
            state.value.tailscale_latency < 2000
        ); // 延迟小于2秒认为可工作
    });

    // 网络质量评分 (0-100)
    const networkQualityScore = computed(() => {
        if (!state.value.internet_connectivity) return 0;
        if (state.value.tailscale_latency === undefined) return 20;

        const latency = state.value.tailscale_latency;
        if (latency > 2000) return 30;
        if (latency > 1000) return 50;
        if (latency > 500) return 70;
        if (latency > 200) return 85;
        return 100;
    });

    // =================== 事件处理方法 ===================

    // 处理网络状态更新事件
    const handleNetworkStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "Network")
            return;

        try {
            if (event.data && typeof event.data === "object") {
                // 更新 Core 状态
                Object.assign(state.value, event.data);
                console.debug("Network state updated:", event.data);
            } else {
                console.warn(
                    "Network state update event has invalid data format",
                );
            }
        } catch (err) {
            console.error("Error handling network state update:", err);
        }
    };

    // =================== 订阅管理 ===================

    // 设置网络状态订阅
    const setupSubscription = async () => {
        try {
            await subscriptionManager.subscribe(
                EVENT_NAMES.NETWORK_STATE_UPDATED,
                handleNetworkStateUpdate,
            );

            console.log("Network store subscribed to state updates");
        } catch (err) {
            console.error("Failed to setup network subscription:", err);
        }
    };

    // 获取延迟等级
    const getLatencyLevel = ():
        | "excellent"
        | "good"
        | "fair"
        | "poor"
        | "disconnected" => {
        const latency = state.value.tailscale_latency;
        if (latency === undefined) return "disconnected";
        if (latency <= 100) return "excellent";
        if (latency <= 300) return "good";
        if (latency <= 800) return "fair";
        return "poor";
    };

    // =================== 生命周期管理 ===================

    setupSubscription();

    // =================== 返回 Store API ===================

    const setState = (newState: NetworkState) => {
        Object.assign(state.value, newState);
    };

    return {
        // Core 状态
        state: readonly(state),
        setState,

        // 计算属性
        connectivityStatusText,
        overallNetworkStatus,
        networkStatusDescription,
        tailscaleLatencyText,
        lastUpdateTimeFormatted,
        isNetworkReadyForWork,
        networkQualityScore,

        // 操作方法
        getLatencyLevel,
    };
});
