import { defineStore } from "pinia";
import { ref, readonly } from "vue";
import {
    ComputedProperties,
    type CoreEvent,
    EVENT_NAMES,
} from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";

// 来自 crates\domain\src\context.rs ComputedProperties

export const useComputedStore = defineStore("computed", () => {
    // =================== 状态定义 ===================

    // Core 状态（来自 StateProjection）
    const state = ref<ComputedProperties>({
        is_ready_for_work: false,
        overall_health_status: "unknown",
        last_computed_at: new Date().toISOString(),
    });

    // =================== 事件处理方法 ===================

    // 处理计算属性更新事件
    const handleComputedStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "Computed")
            return;

        try {
            if (event.data && typeof event.data === "object") {
                // 只更新传入的属性，保持其他属性不变

                // 更新 Core 状态
                Object.assign(state.value, event.data);

                console.debug("Computed state updated:", event.data);
            } else {
                console.warn(
                    "Computed state update event has invalid data format",
                );
                return;
            }
        } catch (err) {
            console.error("Error handling computed state update:", err);
        }
    };

    // =================== 订阅管理 ===================

    // 设置计算属性订阅
    const setupSubscription = async () => {
        try {
            await subscriptionManager.subscribe(
                EVENT_NAMES.COMPUTED_STATE_UPDATED,
                handleComputedStateUpdate,
            );

            console.log("Computed store subscribed to state updates");
        } catch (err) {
            console.error("Failed to setup computed subscription:", err);
        }
    };

    // =================== 生命周期管理 ===================

    setupSubscription();

    // =================== 返回 Store API ===================

    const setState = (newState: ComputedProperties) => {
        Object.assign(state.value, newState);
    };

    return {
        // Core 状态
        state: readonly(state),
        setState,
    };
});
