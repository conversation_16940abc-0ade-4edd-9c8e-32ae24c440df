import { defineStore } from "pinia";
import { ref } from "vue";
import { CoreEvent, DeviceInfo, EVENT_NAMES } from "~/commands/types";
import { subscriptionManager } from "./subscription-manager";

export const useDeviceStore = defineStore("device", () => {
    const state = ref<DeviceInfo>({
        machine_id: "",
        device_name: "",
        platform: "",
        arch: "",
        version: "0.0.0",
        distro_version: "0.0.0",
        agent_version: "0.0.0",
        wsl_version: "0.0.0",
        is_new_device: undefined,
    });

    const handleDeviceStateUpdate = (event: CoreEvent) => {
        if (event.type !== "StateUpdated" || event.state_type !== "Device")
            return;
        if (event.data && typeof event.data === "object") {
            Object.assign(state.value, event.data);
            console.debug("Device state updated:", event.data);
        } else {
            console.warn("Device state update event has invalid data format");
        }
    };

    const setupSubscription = async () => {
        await subscriptionManager.subscribe(
            EVENT_NAMES.DEVICE_STATE_UPDATED,
            handleDeviceStateUpdate,
        );
        console.log("Device store subscribed to state updates");
    };
    setupSubscription();

    const setState = (deviceInfo: DeviceInfo) => {
        Object.assign(state.value, deviceInfo);
    };

    return {
        state,
        setState,
    };
});
