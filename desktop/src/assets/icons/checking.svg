<svg viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none">
  <defs>
    <!-- 左半边渐变 (180-360度) -->
    <linearGradient id="left-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#4ECE71" />
      <stop offset="100%" stop-color="#B0FFCB" />
    </linearGradient>
    <!-- 右半边渐变 (0-180度) -->
    <linearGradient id="right-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#4ECE71" />
      <stop offset="100%" stop-color="#04A73A" />
    </linearGradient>
    <!-- 创建遮罩来分割左右两边 -->
    <mask id="left-mask">
      <rect x="0" y="0" width="6" height="12" fill="white" />
    </mask>
    <mask id="right-mask">
      <rect x="6" y="0" width="6" height="12" fill="white" />
    </mask>
  </defs>

  <!-- 左半边 -->
  <path id="Ellipse-Left"
    d="M12 6C12 2.6863 9.3137 0 6 0C2.6863 0 0 2.6863 0 6C0 9.3137 2.6863 12 6 12C9.3137 12 12 9.3137 12 6ZM9.89436 5.0786Q10 5.52685 10 6Q10 6.47315 9.89436 6.92141Q9.81801 7.24539 9.68648 7.55636Q9.54878 7.88193 9.35936 8.17396Q9.13136 8.52549 8.82842 8.82843Q8.52548 9.13137 8.17393 9.35939Q7.88191 9.54879 7.55636 9.68648Q7.24536 9.81803 6.92134 9.89438Q6.47312 10 6 10Q5.52689 10 5.07867 9.89438Q4.75465 9.81803 4.44364 9.68648Q4.11806 9.54877 3.82603 9.35936Q3.4745 9.13135 3.17157 8.82843Q2.86864 8.52549 2.64063 8.17396Q2.45122 7.88193 2.31352 7.55636Q2.18198 7.24537 2.10563 6.92136Q2 6.47313 2 6Q2 5.52687 2.10563 5.07863Q2.18198 4.75463 2.31352 4.44364Q2.45122 4.11808 2.64062 3.82607Q2.86863 3.47452 3.17157 3.17157Q3.47452 2.86863 3.82607 2.64062Q4.11808 2.45122 4.44364 2.31352Q4.75462 2.18198 5.07861 2.10563Q5.52686 2 6 2Q6.47313 2 6.92136 2.10563Q7.24537 2.18198 7.55636 2.31352Q7.88193 2.45122 8.17395 2.64063Q8.52549 2.86864 8.82842 3.17157Q9.13139 3.47454 9.35941 3.82611Q9.54879 4.11811 9.68648 4.44364Q9.81801 4.75461 9.89436 5.0786Z"
    fill="url(#left-gradient)" fill-rule="evenodd" mask="url(#left-mask)"
    transform="matrix(-1,2.66316e-07,-2.66316e-07,-1,12,12)" />

  <!-- 右半边 -->
  <path id="Ellipse-Right"
    d="M12 6C12 2.6863 9.3137 0 6 0C2.6863 0 0 2.6863 0 6C0 9.3137 2.6863 12 6 12C9.3137 12 12 9.3137 12 6ZM9.89436 5.0786Q10 5.52685 10 6Q10 6.47315 9.89436 6.92141Q9.81801 7.24539 9.68648 7.55636Q9.54878 7.88193 9.35936 8.17396Q9.13136 8.52549 8.82842 8.82843Q8.52548 9.13137 8.17393 9.35939Q7.88191 9.54879 7.55636 9.68648Q7.24536 9.81803 6.92134 9.89438Q6.47312 10 6 10Q5.52689 10 5.07867 9.89438Q4.75465 9.81803 4.44364 9.68648Q4.11806 9.54877 3.82603 9.35936Q3.4745 9.13135 3.17157 8.82843Q2.86864 8.52549 2.64063 8.17396Q2.45122 7.88193 2.31352 7.55636Q2.18198 7.24537 2.10563 6.92136Q2 6.47313 2 6Q2 5.52687 2.10563 5.07863Q2.18198 4.75463 2.31352 4.44364Q2.45122 4.11808 2.64062 3.82607Q2.86863 3.47452 3.17157 3.17157Q3.47452 2.86863 3.82607 2.64062Q4.11808 2.45122 4.44364 2.31352Q4.75462 2.18198 5.07861 2.10563Q5.52686 2 6 2Q6.47313 2 6.92136 2.10563Q7.24537 2.18198 7.55636 2.31352Q7.88193 2.45122 8.17395 2.64063Q8.52549 2.86864 8.82842 3.17157Q9.13139 3.47454 9.35941 3.82611Q9.54879 4.11811 9.68648 4.44364Q9.81801 4.75461 9.89436 5.0786Z"
    fill="url(#right-gradient)" fill-rule="evenodd" mask="url(#right-mask)"
    transform="matrix(-1,2.66316e-07,-2.66316e-07,-1,12,12)" />
</svg>