import { ref, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores'

/**
 * 窗口焦点管理Hook
 * 监听窗口焦点变化，并在焦点恢复时触发状态刷新
 */
export function useWindowFocus() {
  const isFocused = ref(true)
  const lastFocusTime = ref(new Date().toISOString())
  const lastBlurTime = ref<string | null>(null)
  
  const appStore = useAppStore()

  // 焦点获得处理
  const handleFocus = () => {
    const wasBlurred = !isFocused.value
    isFocused.value = true
    lastFocusTime.value = new Date().toISOString()
    
    console.log('Window focused')
    
    // 如果启用了窗口焦点刷新，且之前失去过焦点
    if (wasBlurred && appStore.stateSyncConfig.enableWindowFocusRefresh) {
      triggerStateRefresh('window-focus')
    }
  }

  // 焦点失去处理
  const handleBlur = () => {
    isFocused.value = false
    lastBlurTime.value = new Date().toISOString()
    
    console.log('Window blurred')
  }

  // 触发状态刷新
  const triggerStateRefresh = async (reason: string) => {
    try {
      console.log(`Triggering state refresh due to: ${reason}`)
      
      // 这里可以调用各个store的刷新方法
      // 或者发送特定的Tauri命令来刷新状态
      
      // 示例：刷新网络状态
      const { useNetworkStore } = await import('@/stores/network')
      const networkStore = useNetworkStore()
      await networkStore.refreshNetworkStatus()
      
      appStore.updateLastUpdateTime()
    } catch (error) {
      console.error('Failed to refresh state:', error)
    }
  }

  // 获取焦点持续时间
  const getFocusDuration = () => {
    if (!isFocused.value || !lastBlurTime.value) return null
    
    const focusTime = new Date(lastFocusTime.value).getTime()
    const blurTime = new Date(lastBlurTime.value).getTime()
    
    return Math.max(0, focusTime - blurTime)
  }

  // 获取失焦持续时间
  const getBlurDuration = () => {
    if (isFocused.value || !lastBlurTime.value) return null
    
    const now = new Date().getTime()
    const blurTime = new Date(lastBlurTime.value).getTime()
    
    return now - blurTime
  }

  // 检查是否长时间失焦
  const isLongTimeBlurred = (thresholdMs: number = 300000) => { // 5分钟
    const blurDuration = getBlurDuration()
    return blurDuration !== null && blurDuration > thresholdMs
  }

  onMounted(() => {
    // 添加事件监听器
    window.addEventListener('focus', handleFocus)
    window.addEventListener('blur', handleBlur)
    
    // 初始化状态
    isFocused.value = document.hasFocus()
    
    console.log('Window focus hook mounted')
  })

  onUnmounted(() => {
    // 移除事件监听器
    window.removeEventListener('focus', handleFocus)
    window.removeEventListener('blur', handleBlur)
    
    console.log('Window focus hook unmounted')
  })

  return {
    isFocused,
    lastFocusTime,
    lastBlurTime,
    getFocusDuration,
    getBlurDuration,
    isLongTimeBlurred,
    triggerStateRefresh
  }
}

/**
 * 页面可见性管理Hook
 * 监听页面可见性变化，并在页面变为可见时触发状态刷新
 */
export function usePageVisibility() {
  const isVisible = ref(!document.hidden)
  const lastVisibleTime = ref(new Date().toISOString())
  const lastHiddenTime = ref<string | null>(null)
  
  const appStore = useAppStore()

  // 可见性变化处理
  const handleVisibilityChange = () => {
    const wasHidden = !isVisible.value
    isVisible.value = !document.hidden
    
    if (isVisible.value) {
      lastVisibleTime.value = new Date().toISOString()
      console.log('Page became visible')
      
      // 如果启用了可见性刷新，且之前被隐藏过
      if (wasHidden && appStore.stateSyncConfig.enableVisibilityRefresh) {
        triggerStateRefresh('page-visibility')
      }
    } else {
      lastHiddenTime.value = new Date().toISOString()
      console.log('Page became hidden')
    }
  }

  // 触发状态刷新
  const triggerStateRefresh = async (reason: string) => {
    try {
      console.log(`Triggering state refresh due to: ${reason}`)
      
      // 刷新用户状态
      const { useUserStore } = await import('@/stores/user')
      const userStore = useUserStore()
      if (userStore.isLoggedIn) {
        await userStore.refreshWalletBalance()
      }
      
      appStore.updateLastUpdateTime()
    } catch (error) {
      console.error('Failed to refresh state:', error)
    }
  }

  // 获取可见持续时间
  const getVisibleDuration = () => {
    if (!isVisible.value || !lastHiddenTime.value) return null
    
    const visibleTime = new Date(lastVisibleTime.value).getTime()
    const hiddenTime = new Date(lastHiddenTime.value).getTime()
    
    return Math.max(0, visibleTime - hiddenTime)
  }

  // 获取隐藏持续时间
  const getHiddenDuration = () => {
    if (isVisible.value || !lastHiddenTime.value) return null
    
    const now = new Date().getTime()
    const hiddenTime = new Date(lastHiddenTime.value).getTime()
    
    return now - hiddenTime
  }

  onMounted(() => {
    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    console.log('Page visibility hook mounted')
  })

  onUnmounted(() => {
    // 移除事件监听器
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    
    console.log('Page visibility hook unmounted')
  })

  return {
    isVisible,
    lastVisibleTime,
    lastHiddenTime,
    getVisibleDuration,
    getHiddenDuration,
    triggerStateRefresh
  }
}

/**
 * 组合窗口状态管理Hook
 * 结合窗口焦点和页面可见性管理
 */
export function useWindowState() {
  const focus = useWindowFocus()
  const visibility = usePageVisibility()
  
  // 窗口是否活跃（既有焦点又可见）
  const isActive = ref(focus.isFocused.value && visibility.isVisible.value)
  
  // 监听状态变化
  const updateActiveState = () => {
    isActive.value = focus.isFocused.value && visibility.isVisible.value
  }

  // 手动触发状态刷新
  const refreshState = async (reason: string = 'manual') => {
    await Promise.all([
      focus.triggerStateRefresh(reason),
      visibility.triggerStateRefresh(reason)
    ])
  }

  // 获取窗口状态摘要
  const getWindowStateSummary = () => {
    return {
      isFocused: focus.isFocused.value,
      isVisible: visibility.isVisible.value,
      isActive: isActive.value,
      lastFocusTime: focus.lastFocusTime.value,
      lastVisibleTime: visibility.lastVisibleTime.value,
      focusDuration: focus.getFocusDuration(),
      visibleDuration: visibility.getVisibleDuration()
    }
  }

  return {
    // 焦点相关
    ...focus,
    
    // 可见性相关
    ...visibility,
    
    // 组合状态
    isActive,
    updateActiveState,
    refreshState,
    getWindowStateSummary
  }
}
