// usePageActive.ts
import { ref, computed, onMounted, onBeforeUnmount } from "vue";

/**
 * 监听页面是否可见 & 是否获得焦点
 * @returns {
 *   isVisible: Ref<boolean>,
 *   isFocused: Ref<boolean>,
 *   isActive: Ref<boolean>
 * }
 */
export function useWindowActive() {
    const isVisible = ref(document.visibilityState === "visible");
    const isFocused = ref(document.hasFocus());

    const updateVisibility = () => {
        isVisible.value = document.visibilityState === "visible";
    };
    const updateFocus = () => {
        isFocused.value = document.hasFocus();
    };

    onMounted(() => {
        document.addEventListener("visibilitychange", updateVisibility);
        window.addEventListener("focus", updateFocus);
        window.addEventListener("blur", updateFocus);
    });

    onBeforeUnmount(() => {
        document.removeEventListener("visibilitychange", updateVisibility);
        window.removeEventListener("focus", updateFocus);
        window.removeEventListener("blur", updateFocus);
    });

    const isActive = computed(() => isVisible.value && isFocused.value);

    return { isVisible, isFocused, isActive };
}
