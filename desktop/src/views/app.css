body {
    background-color: var(--background);
}

#app {
    height: 100%;
    width: 100%;
}

@layer utilities {
    .ani-dot {
        --base-witdh: 0.3em;
        display: inline-block;
        width: calc(var(--base-witdh) * 3);
        overflow: hidden;
        letter-spacing: 1px;
        animation: dot 3s infinite step-start;
        height: 1em;
    }
}

@keyframes dot {
    0% {
        width: 0;
        margin-right: calc(var(--base-witdh) * 3);
    }

    33% {
        width: var(--base-witdh);
        margin-right: calc(var(--base-witdh) * 2);
    }

    66% {
        width: calc(var(--base-witdh) * 2);
        margin-right: var(--base-witdh);
    }

    100% {
        width: calc(var(--base-witdh) * 3);
        margin-right: 0;
    }
}