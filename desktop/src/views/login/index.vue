<script setup lang="ts">
import logotype from "~/assets/images/logotype.png";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "~/components/ui/tabs";
import PasswordLogin from "./_components/PasswordLogin.vue";
import OptLogin from "./_components/OptLogin.vue";
import z from "zod";
import { useForm } from "vee-validate";
import { toTypedSchema } from "@vee-validate/zod";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { ref, computed, watch } from "vue";
import { toast } from "vue-sonner";
import { useRouter } from "vue-router";
import { useUserStore } from "~/stores/user";
import { generateTraceId } from "~/commands";

const router = useRouter();
const userStore = useUserStore();

const isLoggingIn = computed(() => userStore.state.is_logging_in);

// 监听登录状态变化，成功后跳转到主页
watch(
    () => userStore.state.is_logged_in,
    (newValue) => {
        if (newValue) {
            toast.success("登录成功！");
            router.replace("/dashboard");
        }
    },
    {
        immediate: true,
    },
);

const formError = ref("");
const passwordSchema = z.object({
    type: z.literal("password"),
    phone: z
        .string({ message: "请输入正确的 11 位手机号" })
        .length(11, { message: "请输入正确的 11 位手机号" })
        .regex(/^1[3-9]\d{9}$/, {
            message: "请输入正确的 11 位手机号",
        }),
    password: z.string({ message: "请输入密码" }),
});
const otpSchema = z.object({
    type: z.literal("otp"),
    phone: z
        .string({ message: "请输入正确的 11 位手机号" })
        .length(11, { message: "请输入正确的 11 位手机号" }),
    otp: z
        .string({ message: "请输入 6 位验证码" })
        .length(6, { message: "请输入 6 位验证码" }),
});
const formSchema = z.union([passwordSchema, otpSchema]);

const { handleSubmit, controlledValues, isSubmitting } = useForm({
    validationSchema: toTypedSchema(formSchema),
    initialValues: {
        type: "password",
    },
});

const onSubmitPassword = handleSubmit(async (values) => {
    try {
        formError.value = "";
        console.log("onSubmitPassword", values);
        toast.dismiss();

        // 调用登录API
        if (values.type == "password") {
            await userStore.login(
                {
                    type: "password",
                    username: values.phone,
                    password: values.password,
                },
                generateTraceId(),
            );
        }
    } catch (e) {
        if (e instanceof Error) {
            formError.value = e.message;
        } else {
            formError.value = String(e);
        }
    }
});

const onSubmitOtp = handleSubmit(async (values) => {
    try {
        formError.value = "";
        console.log("onSubmitOtp", values);
        toast.dismiss();

        // TODO: 实现验证码登录逻辑
        // 目前使用手机号作为用户名
        if (values.type == "otp") {
            await userStore.login(
                {
                    type: "otp",
                    username: values.phone,
                    otp: values.otp,
                },
                generateTraceId(),
            );
        }
    } catch (e) {
        if (e instanceof Error) {
            formError.value = e.message;
        } else {
            formError.value = String(e);
        }
    }
});

const onSendOtp = async () => {
    const phone = controlledValues.value.phone;
    const result = otpSchema.shape.phone.safeParse(phone);
    if (result.success) {
        try {
            await userStore.sendVerificationCode(
                controlledValues.value.phone!,
                generateTraceId(),
            );
            toast.success("验证码发送成功");
        } catch (e) {
            if (e instanceof Error) {
                toast.error(e.message);
            } else {
                toast.error(String(e));
            }
        }
    } else {
        toast.error(result.error.message);
    }
};
const openRegisterPage = async () => {
    await userStore.openRegisterPage(generateTraceId());
};
const openForgotPasswordPage = async () => {
    await userStore.openForgotPasswordPage(generateTraceId());
};
</script>
<template>
    <section class="flow-root px-8">
        <img :src="logotype" alt="logotype" class="mt-9" />
        <main class="mt-6">
            <!-- 显示登录错误信息 -->
            <Alert
                v-if="formError.length > 0"
                variant="destructive"
                class="mb-2"
            >
                <AlertTitle>
                    <span>登录失败</span>
                </AlertTitle>
                <AlertDescription>{{ formError }}</AlertDescription>
            </Alert>

            <!-- 显示登录中状态 -->
            <Alert v-if="isLoggingIn" variant="default" class="mb-2">
                <AlertTitle>
                    <span>正在登录...</span>
                </AlertTitle>
                <AlertDescription
                    >请稍候，正在验证您的身份信息</AlertDescription
                >
            </Alert>

            <Tabs
                v-model="controlledValues.type"
                default-value="password"
                :disabled="isLoggingIn"
            >
                <TabsList>
                    <TabsTrigger value="password" :disabled="isLoggingIn"
                        >密码登录</TabsTrigger
                    >
                    <TabsTrigger value="otp" :disabled="isLoggingIn"
                        >验证码登录</TabsTrigger
                    >
                </TabsList>
                <TabsContent value="password" class="mt-4">
                    <PasswordLogin
                        @submit-password="onSubmitPassword"
                        @open-forgot-password="openForgotPasswordPage"
                        :isSubmitting="isSubmitting || isLoggingIn"
                    />
                </TabsContent>
                <TabsContent value="otp" class="mt-4">
                    <OptLogin
                        @submit-otp="onSubmitOtp"
                        @send-otp="onSendOtp"
                        :isSubmitting="isSubmitting || isLoggingIn"
                    />
                </TabsContent>
            </Tabs>
            <!-- 注册链接 -->
            <div class="mt-6 text-center text-gray-400">
                还没有账号？
                <!-- todo: 应该通过浏览器打开 Web 注册页面，而不是通过 RouterLink -->
                <!-- todo: 忘记密码也是通过浏览器打开 Web 页面 -->
                <button
                    type="button"
                    @click="openRegisterPage"
                    class="text-green-400 hover:underline"
                    :class="{ 'pointer-events-none opacity-50': isLoggingIn }"
                >
                    去注册 &gt;
                </button>
            </div>
        </main>
    </section>
</template>
