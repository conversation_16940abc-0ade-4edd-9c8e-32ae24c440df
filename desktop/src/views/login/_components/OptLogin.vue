<script setup lang="ts">
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { FormField, FormItem, FormMessage } from "~/components/ui/form";
import { useUserStore } from "~/stores";
import { computed, ref, watch } from "vue";
import { useIntervalFn } from "@vueuse/core";

defineProps<{
    onSubmitOtp(): void;
    onSendOtp(): void;
    isSubmitting: boolean;
}>();

const now = ref(Math.floor(Date.now() / 1e3));
const userStore = useUserStore();

const codeSendWithSeconds = computed(() => {
    return Math.max(0, userStore.state.next_code_send_available_at - now.value);
});
const interval = useIntervalFn(() => {
    now.value = Math.floor(Date.now() / 1e3);
}, 1000);
watch(
    () => codeSendWithSeconds.value,
    (waitSeconds) => {
        if (waitSeconds > 0) {
            interval.resume();
        } else {
            interval.pause();
        }
    },
    { immediate: true },
);
</script>
<template>
    <form @submit.prevent="onSubmitOtp">
        <!-- 手机号 -->
        <FormField v-slot="{ componentField, meta }" name="phone">
            <FormItem>
                <div class="relative mt-4 flex items-center space-x-2">
                    <Label for="phoneOtp" class="absolute left-3 w-12"
                        >+86</Label
                    >
                    <Input
                        id="phoneOtp"
                        v-bind="componentField"
                        type="tel"
                        autocomplete="off"
                        placeholder="请输入手机号"
                        class="box-content flex-1 rounded-none py-1 pl-18"
                    />
                </div>
                <FormMessage v-if="meta.touched" class="mt-1" />
            </FormItem>
        </FormField>

        <!-- 验证码 -->
        <FormField v-slot="{ componentField, meta }" name="otp">
            <FormItem>
                <div class="relative mt-6 flex items-center space-x-2">
                    <Label for="otp" class="absolute left-3 w-12">验证码</Label>
                    <Input
                        id="otp"
                        v-bind="componentField"
                        type="text"
                        min="4"
                        max="6"
                        placeholder="请输入验证码"
                        class="mr-0 box-content flex-1 rounded-none py-1 pr-24 pl-18"
                    />
                    <Button
                        type="button"
                        @click="onSendOtp"
                        variant="outline"
                        :disabled="codeSendWithSeconds > 0"
                        class="bg-secondary hover:bg-secondary/90 absolute right-1 w-24 cursor-pointer rounded-none whitespace-nowrap"
                    >
                        <template v-if="codeSendWithSeconds > 0">
                            {{ codeSendWithSeconds }}s
                        </template>
                        <template v-else>发送验证码</template>
                    </Button>
                </div>
                <FormMessage v-if="meta.touched" class="mt-1" />
            </FormItem>
        </FormField>

        <!-- 登录按钮 -->
        <Button
            type="submit"
            class="mt-16 h-12 w-full text-lg"
            :disabled="isSubmitting"
        >
            登录<span v-if="isSubmitting" class="ani_dot">...</span>
        </Button>
    </form>
</template>
