<script setup lang="ts">
import { Input, PasswordInput } from "~/components/ui/input";
import { Button } from "~/components/ui/button";
import { Label } from "~/components/ui/label";
import { FormField, FormItem, FormMessage } from "~/components/ui/form";

defineProps<{
    onSubmitPassword(): void;
    onOpenForgotPassword(): void;
    isSubmitting: boolean;
}>();
</script>
<template>
    <form @submit.prevent="onSubmitPassword">
        <!-- 手机号 -->
        <FormField v-slot="{ componentField, meta }" name="phone">
            <FormItem>
                <div class="relative mt-4 flex items-center space-x-2">
                    <Label for="phone" class="absolute left-3 w-12">+86</Label>
                    <Input
                        id="phone"
                        v-bind="componentField"
                        type="tel"
                        placeholder="请输入手机号"
                        autocomplete="off"
                        class="box-content flex-1 rounded-none py-1 pl-18"
                    />
                </div>
                <FormMessage v-if="meta.touched" />
            </FormItem>
        </FormField>

        <!-- 密码 -->
        <FormField v-slot="{ componentField, meta }" name="password">
            <FormItem>
                <div class="relative mt-6 flex items-center space-x-2">
                    <Label for="password" class="absolute left-3 w-12"
                        >密码</Label
                    >
                    <PasswordInput
                        id="password"
                        v-bind="componentField"
                        type="password"
                        placeholder="请输入密码"
                        class="box-content flex-1 rounded-none py-1 pl-18"
                        autocomplete="off"
                    />
                </div>
                <FormMessage v-if="meta.touched" />
            </FormItem>
        </FormField>

        <!-- 忘记密码 -->
        <div class="mt-4 flex items-center justify-end">
            <button
                type="button"
                @click="onOpenForgotPassword"
                class="text-secondary text-sm hover:underline"
            >
                忘记密码?
            </button>
        </div>

        <!-- 登录按钮 -->
        <Button
            type="submit"
            class="mt-16 h-12 w-full text-lg"
            :disabled="isSubmitting"
        >
            <template v-if="!isSubmitting">登录</template>
            <template v-else>
                <span class="align-text-top">登录中</span>
                <i class="ani-dot -ml-1 translate-y-1.5">...</i>
            </template>
        </Button>
    </form>
</template>
