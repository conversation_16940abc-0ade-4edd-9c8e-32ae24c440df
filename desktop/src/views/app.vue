<script setup lang="ts">
import "./global.css";
import "./app.css";
import "vue-sonner/style.css";
import { Toaster } from "@/components/ui/sonner";
// import CustomTitleBar from "~/components/CustomTitleBar.vue";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { onMounted, onBeforeUnmount } from "vue";
import { useAppStore } from "~/stores/app";

dayjs.locale("zh-cn");

const appStore = useAppStore();

onMounted(() => {
    appStore.initialize();
});

onBeforeUnmount(() => {
    appStore.cleanup();
});

window.addEventListener("beforeunload", () => {
    appStore.cleanup();
});
</script>

<template>
    <div class="flex h-screen flex-col bg-[#0a2133]">
        <!-- <CustomTitleBar /> -->
        <div class="flex-1">
            <RouterView />
        </div>
    </div>
    <Toaster position="top-center" richColors />
</template>
