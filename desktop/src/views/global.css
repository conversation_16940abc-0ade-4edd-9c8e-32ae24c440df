@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.141 0.005 285.823);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.141 0.005 285.823);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.141 0.005 285.823);
  --primary: oklch(0.21 0.006 285.885);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.967 0.001 286.375);
  --secondary-foreground: oklch(0.21 0.006 285.885);
  --muted: oklch(0.967 0.001 286.375);
  --muted-foreground: oklch(0.552 0.016 285.938);
  --accent: oklch(0.967 0.001 286.375);
  --accent-foreground: oklch(0.21 0.006 285.885);
  --destructive: oklch(0.6936 0.1964 23.76);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.92 0.004 286.32);
  --input: oklch(0.92 0.004 286.32);
  --ring: oklch(0.705 0.015 286.067);
  --radius: 0.3rem;

  --font-display: 'Figtree', 'MiSans VF', 'PingFang SC', 'ui-sans-serif',
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', sans-serif;
  --font-monospace: ui-monospace, 'Rec Mono Semicasual', 'Roboto Mono',
    'Courier Prime', 'MiSans VF', monospace;
}

.dark {
  --background: oklch(0.2395 0.0451 244.99);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.141 0.005 285.823);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.141 0.005 285.823);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.7971 0.1856 158.1);
  --primary-foreground: oklch(0.21 0.006 285.885);
  --secondary: oklch(0.7178 0.1019 243.65);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.4885 0.1112 246.89);
  --muted-foreground: oklch(0.3147 0.0665 246.01);
  --accent: oklch(0.274 0.006 286.033);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.6936 0.1964 23.76);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.274 0.006 286.033);
  --input: oklch(0.7178 0.1019 243.65);
  --ring: oklch(0.7178 0.1019 243.65);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    font-size: 14px;
  }

  body {
    margin: 0;
    box-sizing: border-box;
    font-family: var(--font-display), sans-serif;
    background-color: var(--background);
    color: var(--foreground);
    padding: 0 0 env(safe-area-inset-bottom);
  }

  html,
  body {
    width: 100%;
    height: 100%;
  }
}

@layer utilities {
  .debug {
    outline: 1px dashed red;

    * {
      outline: 1px dashed red;
    }
  }
}