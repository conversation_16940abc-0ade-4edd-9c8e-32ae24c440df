<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import Logotype from "~/assets/images/logotype.svg";
import { Button } from "~/components/ui/button";
import { Card, CardTitle } from "~/components/ui/card";
import { Label } from "~/components/ui/label";
import { ArrowLeftIcon } from "lucide-vue-next";
import { useRouter } from "vue-router";
import { useSettingsStore } from "~/stores/settings";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { executeCommand, generateTraceId, pickFolder } from "~/commands";

const router = useRouter();
const settingsStore = useSettingsStore();

// 当前标签
const activeTab = ref<string>("settings");

// 数据目录相关
const isSelecting = ref(false);
const errorMessage = ref<string>(""); // 错误信息
const tempPath = ref<string>(""); // 临时选择的路径
const hasChanges = ref(false); // 是否有未应用的更改

// 获取当前数据目录
const currentPath = computed(() => settingsStore.state.data_root_path);

// 显示的路径（如果有临时路径则显示临时路径，否则显示当前路径）
const displayPath = computed(() =>
    hasChanges.value ? tempPath.value : currentPath.value,
);

// 选择数据目录
const selectDataDirectory = async () => {
    try {
        isSelecting.value = true;
        errorMessage.value = ""; // 清除之前的错误

        const result = await pickFolder("选择数据目录");

        console.log("选择结果:", result);

        // 设置临时路径和状态
        if (result.is_readable && result.is_writable) {
            tempPath.value = result.path;
            hasChanges.value = true;
        } else if (!result.is_readable) {
            errorMessage.value = "选择的文件夹不可读";
        } else if (!result.is_writable) {
            errorMessage.value = "选择的文件夹不可写";
        }
    } catch (error: any) {
        console.error("选择文件夹失败:", error);

        // 设置错误信息
        if (error.code === "USER_CANCELLED") {
            errorMessage.value = ""; // 用户取消不显示错误
        } else {
            errorMessage.value = error.message || "选择文件夹时发生错误";
        }

        // 清除临时状态
        tempPath.value = "";
        hasChanges.value = false;
    } finally {
        isSelecting.value = false;
    }
};

// 应用更改
const applyChanges = async () => {
    if (!hasChanges.value) {
        return;
    }
    // 这里应该调用设置存储来保存新路径
    // settingsStore.setDistroInstallPath(tempPath.value);
    console.log("应用路径:", tempPath.value);
    try {
        const result = await executeCommand({
            type: "SetDataRootPath",
            new_path: tempPath.value,
            trace_id: generateTraceId(),
        });
        if (result.type === "Success") {
            console.log("应用成功");
        } else {
            console.log("应用失败, 原因:", result.message);
            errorMessage.value = result.message;
            return;
        }
        // 清除临时状态
        tempPath.value = "";
        hasChanges.value = false;
        errorMessage.value = "";
    } catch (error) {
        console.error("应用失败:", error);
        errorMessage.value = "应用失败";
    }
};

// 取消更改
const cancelChanges = () => {
    tempPath.value = "";
    hasChanges.value = false;
    errorMessage.value = "";
};

// 应用信息
const appInfo = {
    name: "EchoWave",
    version: "1.0.0",
    build: "2024.01.01",
    description: "专业的任务管理工具箱",
};

// 系统信息
const systemInfo = ref({
    os: "Windows 11",
    arch: "x64",
    runtime: "Node.js",
});

// 获取系统信息
onMounted(() => {
    // 这里可以通过 Tauri API 获取真实的系统信息
    // 暂时使用模拟数据
});
</script>

<template>
    <section class="bg-background flex h-full flex-col overflow-hidden">
        <!-- 头部区域 - 简化设计 -->
        <div
            class="flex items-center justify-between border-b border-white/10 px-4 py-3"
        >
            <Button
                variant="link"
                size="sm"
                @click="router.replace('/dashboard')"
                class="p-0 text-white/80 hover:text-white hover:no-underline"
            >
                <ArrowLeftIcon class="h-4 w-4" />
                返回
            </Button>
            <div class="flex items-center gap-2">
                <Logotype class="h-6" />
            </div>
        </div>

        <!-- 标签页区域 -->
        <Tabs
            v-model="activeTab"
            default-value="settings"
            class="flex flex-1 flex-col"
        >
            <TabsList class="mx-4 mt-3 mb-0">
                <TabsTrigger
                    value="settings"
                    class="text-white/70 data-[state=active]:text-white"
                >
                    设置
                </TabsTrigger>
                <TabsTrigger
                    value="about"
                    class="text-white/70 data-[state=active]:text-white"
                >
                    关于
                </TabsTrigger>
            </TabsList>

            <!-- 设置页面内容 -->
            <TabsContent
                value="settings"
                class="flex-1 overflow-y-auto px-4 py-4"
            >
                <div class="space-y-4">
                    <!-- 数据目录设置卡片 -->
                    <div class="space-y-4">
                        <div>
                            <Label
                                for="distro_install_path"
                                class="text-sm text-white/80"
                            >
                                数据目录
                            </Label>

                            <!-- 当前路径显示 -->
                            <Input
                                id="distro_install_path"
                                autocomplete="off"
                                class="w-full rounded-none border-0 border-b border-white/10 px-0 pt-1 font-mono text-sm text-white focus-visible:ring-0"
                                :class="{ 'border-blue-400': hasChanges }"
                                :model-value="displayPath"
                                readonly
                            />

                            <!-- 错误信息显示 -->
                            <div
                                v-if="errorMessage"
                                class="mt-1 text-xs text-red-400"
                            >
                                {{ errorMessage }}
                            </div>

                            <!-- 按钮组 -->
                            <div class="mt-2 flex items-center gap-2">
                                <!-- 没有更改时显示更改按钮 -->
                                <Button
                                    v-if="!hasChanges"
                                    @click="selectDataDirectory"
                                    variant="link"
                                    size="sm"
                                    :disabled="isSelecting"
                                    class="box-shadow-none p-0 text-white/80 hover:text-white disabled:opacity-50"
                                >
                                    {{ isSelecting ? "选择中..." : "更改..." }}
                                </Button>

                                <!-- 有更改时显示应用和取消按钮 -->
                                <template v-else>
                                    <Button
                                        @click="applyChanges"
                                        variant="link"
                                        size="sm"
                                        class="box-shadow-none p-0 text-green-400 hover:text-green-300"
                                    >
                                        应用
                                    </Button>
                                    <Button
                                        @click="cancelChanges"
                                        variant="link"
                                        size="sm"
                                        class="box-shadow-none p-0 text-white/60 hover:text-white/80"
                                    >
                                        取消
                                    </Button>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </TabsContent>

            <!-- 关于页面内容 -->
            <TabsContent value="about" class="flex-1 overflow-y-auto px-4 py-4">
                <div class="space-y-4">
                    <!-- 应用信息卡片 -->
                    <Card class="border-white/10 bg-white/5 p-4">
                        <div class="space-y-4">
                            <div>
                                <CardTitle class="mb-2 text-base text-white">
                                    {{ appInfo.name }}
                                </CardTitle>
                                <p class="mb-4 text-sm text-white/70">
                                    {{ appInfo.description }}
                                </p>
                            </div>

                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <div class="mb-1 text-xs text-white/60">
                                        版本
                                    </div>
                                    <div class="text-white">
                                        {{ appInfo.version }}
                                    </div>
                                </div>
                                <div>
                                    <div class="mb-1 text-xs text-white/60">
                                        构建
                                    </div>
                                    <div class="text-white">
                                        {{ appInfo.build }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </Card>

                    <!-- 系统信息卡片 -->
                    <Card class="border-white/10 bg-white/5 p-4">
                        <div class="space-y-4">
                            <CardTitle class="text-base text-white">
                                系统信息
                            </CardTitle>

                            <div class="space-y-3 text-sm">
                                <div class="flex items-center justify-between">
                                    <span class="text-white/70">操作系统</span>
                                    <span class="text-white">{{
                                        systemInfo.os
                                    }}</span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <span class="text-white/70">架构</span>
                                    <span class="text-white">{{
                                        systemInfo.arch
                                    }}</span>
                                </div>

                                <div class="flex items-center justify-between">
                                    <span class="text-white/70">运行时</span>
                                    <span class="text-white">{{
                                        systemInfo.runtime
                                    }}</span>
                                </div>
                            </div>
                        </div>
                    </Card>

                    <!-- 开发信息卡片 -->
                    <Card class="border-white/10 bg-white/5 p-4">
                        <div class="space-y-4">
                            <CardTitle class="text-base text-white">
                                开发信息
                            </CardTitle>

                            <div class="space-y-2 text-sm text-white/70">
                                <p>© 2025 EchoWave Team</p>
                                <p>...</p>

                                <div class="border-t border-white/10 pt-2">
                                    <p class="text-xs text-white/60">
                                        使用 Vue 3 + TypeScript 开发
                                    </p>
                                </div>
                            </div>
                        </div>
                    </Card>
                </div>
            </TabsContent>
        </Tabs>
    </section>
</template>

<style scoped>
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
    width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}
</style>
