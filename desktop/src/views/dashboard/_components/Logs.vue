<script setup lang="ts">
import { CopyIcon } from "lucide-vue-next";
import {
    ref,
    computed,
    onMounted,
    onBeforeUnmount,
    nextTick,
    watch,
} from "vue";
import dayjs from "dayjs";
import { toast } from "vue-sonner";
import { useLogsStore } from "~/stores/logs";
import type { LogLevel } from "~/commands/types";
import Button from "~/components/ui/button/button.vue";
import Tooltip from "~/components/ui/tooltip/Tooltip.vue";
import TooltipContent from "~/components/ui/tooltip/TooltipContent.vue";
import TooltipTrigger from "~/components/ui/tooltip/TooltipTrigger.vue";
import TooltipProvider from "~/components/ui/tooltip/TooltipProvider.vue";
import Badge from "~/components/ui/badge/Badge.vue";

const logsStore = useLogsStore();
const scrollContainer = ref<HTMLElement | null>(null);
const autoScroll = ref(true);

let scrollAnimationFrame: number | null = null;
let copyTimeout: number | null = null;

// 获取日志数据
const logs = computed(() => logsStore.logs);

// 根据日志级别获取 Badge 变体
const getLogLevelVariant = (level: LogLevel) => {
    switch (level) {
        case "Error":
            return "destructive";
        case "Warn":
            return "warning";
        case "Info":
            return "default";
        case "Debug":
            return "secondary";
        case "Trace":
            return "outline";
        default:
            return "secondary";
    }
};

// 根据日志级别获取样式类（支持 dark 模式）
const getLogLevelClass = (level: LogLevel) => {
    switch (level) {
        case "Error":
            return "text-red-600 dark:text-red-400";
        case "Warn":
            return "text-yellow-600 dark:text-yellow-400";
        case "Info":
            return "text-blue-600 dark:text-blue-400";
        case "Debug":
            return "text-gray-600 dark:text-gray-400";
        case "Trace":
            return "text-gray-500 dark:text-gray-500";
        default:
            return "text-gray-700 dark:text-gray-300";
    }
};

// 格式化时间戳
const formatTimestamp = (timestamp: string) => {
    return dayjs(timestamp).format("HH:mm:ss");
};

// 获取模块名称（取点号前的部分）
const getModuleName = (module: string) => {
    return module.split(".")[0];
};
const logLevelMap = {
    Error: "ERR",
    Warn: "WRN",
    Info: "INF",
    Debug: "DBG",
    Trace: "TRC",
};

// 截断长消息
const truncateMessage = (message: string, maxLength: number = 150) => {
    if (message.length <= maxLength) {
        return message;
    }
    return message.substring(0, maxLength) + "...";
};

// 检查消息是否被截断
const isMessageTruncated = (message: string, maxLength: number = 60) => {
    return message.length > maxLength;
};

// 平滑滚动到底部
const smoothScrollToBottom = () => {
    if (!scrollContainer.value || !autoScroll.value) return;

    if (scrollAnimationFrame) {
        cancelAnimationFrame(scrollAnimationFrame);
    }

    const start = scrollContainer.value.scrollTop;
    const end =
        scrollContainer.value.scrollHeight - scrollContainer.value.clientHeight;
    const duration = 300;
    const startTime = performance.now();

    const animate = (currentTime: number) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeProgress = progress * (2 - progress); // easeOutQuad

        if (scrollContainer.value) {
            scrollContainer.value.scrollTop =
                start + (end - start) * easeProgress;
        }

        if (progress < 1) {
            scrollAnimationFrame = requestAnimationFrame(animate);
        }
    };

    scrollAnimationFrame = requestAnimationFrame(animate);
};

// 处理滚动事件
const handleScroll = () => {
    if (!scrollContainer.value) return;

    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
    const distanceToBottom = scrollHeight - (scrollTop + clientHeight);

    // 距离底部50px内视为自动滚动区域
    autoScroll.value = distanceToBottom < 50;
};

// 监听日志变化，自动滚动到底部
const handleLogUpdate = () => {
    if (autoScroll.value) {
        nextTick(smoothScrollToBottom);
    }
};

// 构建完整的日志行内容
const buildFullLineContent = (log: (typeof logs.value)[0]) => {
    let line = "";
    line += `${formatTimestamp(log.timestamp)} `;
    line += `${getModuleName(log.module)} `;
    line += log.message;
    return line;
};

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            showCopyFeedback();
        } else {
            // 降级方案
            const textarea = document.createElement("textarea");
            textarea.value = text;
            textarea.style.position = "fixed";
            textarea.style.opacity = "0";
            textarea.style.top = "0";
            textarea.style.left = "0";
            document.body.appendChild(textarea);
            textarea.focus();
            textarea.select();
            try {
                // 使用新的 API
                const successful = document.execCommand("copy");
                if (successful) {
                    showCopyFeedback();
                } else {
                    throw new Error("Copy command failed");
                }
            } catch (execErr) {
                console.error("复制失败:", execErr);
                toast.error("复制失败，请手动复制");
            }
            document.body.removeChild(textarea);
        }
    } catch (err) {
        console.error("复制失败:", err);
        toast.error("复制失败，请手动复制");
    }
};

// 显示复制反馈
const showCopyFeedback = () => {
    toast.success("已复制到剪贴板", {
        duration: 2000,
        position: "top-center",
    });
};

// 复制所有日志
const copyAllLogs = () => {
    const allLogsText = logs.value
        .map((log) => buildFullLineContent(log))
        .join("\n");
    copyToClipboard(allLogsText);
};

// 处理双击选择整行
const handleDoubleClick = (event: MouseEvent, log: (typeof logs.value)[0]) => {
    // 防止事件冒泡
    event.preventDefault();
    event.stopPropagation();

    const fullLineContent = buildFullLineContent(log);

    // 选中整行内容
    const selection = window.getSelection();
    if (selection) {
        selection.removeAllRanges();

        // 选中当前行元素
        const target = event.currentTarget as HTMLElement;
        if (target) {
            const range = document.createRange();
            range.selectNodeContents(target);
            selection.addRange(range);
        }

        // 复制到剪贴板
        copyToClipboard(fullLineContent);
    }
};

// 处理行选择和复制（保留原有的文本选择功能）
const handleRowSelection = (
    _event: MouseEvent,
    log: (typeof logs.value)[0],
) => {
    if (copyTimeout) {
        clearTimeout(copyTimeout);
    }

    const selection = window.getSelection();
    const selectedText = selection?.toString().trim();

    if (selectedText) {
        const range = selection?.getRangeAt(0);
        const rect = range?.getBoundingClientRect();

        if (rect) {
            // 400ms后自动复制整行
            copyTimeout = window.setTimeout(() => {
                const fullLineContent = buildFullLineContent(log);
                copyToClipboard(fullLineContent);
            }, 400);
        }
    }
};

// 组件挂载时监听日志变化
onMounted(() => {
    // 监听日志变化
    watch(
        () => logs.value.length,
        () => {
            handleLogUpdate();
        },
        { immediate: true },
    );
});

onBeforeUnmount(() => {
    if (scrollAnimationFrame) {
        cancelAnimationFrame(scrollAnimationFrame);
    }
    if (copyTimeout) {
        clearTimeout(copyTimeout);
    }
});
</script>

<template>
    <section class="flex h-full flex-col">
        <div class="-mt-1 flex items-center justify-between border-b px-5">
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">
                日志信息
            </h3>
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger as-child>
                        <Button
                            @click="copyAllLogs"
                            variant="ghost"
                            size="icon"
                            class="h-8 w-8"
                        >
                            <CopyIcon class="size-4" />
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>复制所有日志</p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        </div>

        <!-- 日志列表 -->
        <div class="relative flex-1 overflow-hidden">
            <div
                ref="scrollContainer"
                @scroll="handleScroll"
                class="scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500 relative h-full overflow-y-auto"
            >
                <div class="absolute top-0 px-2 py-1">
                    <div
                        v-for="log in logs"
                        :key="`${log.timestamp}-${log.module}-${log.message}`"
                        @click="handleRowSelection($event, log)"
                        class="user-select-all flex min-h-5 cursor-text items-start gap-0.5 rounded px-2 py-0.5 font-mono text-xs leading-tight transition-colors select-text hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                        <span
                            class="w-14 flex-shrink-0 text-xs text-gray-500 tabular-nums dark:text-gray-400"
                        >
                            {{ formatTimestamp(log.timestamp) }}
                        </span>
                        <span
                            :variant="getLogLevelVariant(log.level)"
                            size="sm"
                            class="h-4 min-w-8 flex-shrink-0 rounded-none py-0 text-center text-xs font-medium"
                        >
                            {{ logLevelMap[log.level] || "INF" }}
                        </span>
                        <span
                            class="w-12 flex-shrink-0 truncate text-xs text-gray-600 dark:text-gray-400"
                        >
                            {{ getModuleName(log.module) }}
                        </span>
                        <TooltipProvider v-if="isMessageTruncated(log.message)">
                            <Tooltip>
                                <TooltipTrigger as-child>
                                    <span
                                        :class="[
                                            'flex-1 cursor-help text-xs leading-tight break-all',
                                            getLogLevelClass(log.level),
                                        ]"
                                    >
                                        {{ truncateMessage(log.message) }}
                                    </span>
                                </TooltipTrigger>
                                <TooltipContent class="max-w-96">
                                    <p class="text-xs whitespace-pre-wrap">
                                        {{ log.message }}
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <span
                            v-else
                            :class="[
                                'flex-1 text-xs leading-tight break-all',
                                getLogLevelClass(log.level),
                            ]"
                        >
                            {{ log.message }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Toast notifications will be handled by vue-sonner -->
        </div>
    </section>
</template>

<style scoped>
/* 双击选择效果 */
.user-select-all {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

.user-select-all:active {
    background-color: rgba(59, 130, 246, 0.1);
}

.dark .user-select-all:active {
    background-color: rgba(59, 130, 246, 0.2);
}

/* 自定义滚动条样式 */
.scrollbar-thin {
    scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
    width: 6px;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
}

/* Dark mode scrollbar */
.dark .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #4b5563;
}

.dark .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
    background-color: #6b7280;
}
</style>
