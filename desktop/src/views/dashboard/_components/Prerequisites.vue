<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from "vue";
import { useSystemStore } from "~/stores/system";
import { Button } from "~/components/ui/button";
import {
    AlertCircleIcon,
    ClockIcon,
    DownloadIcon,
    CheckCircle2Icon,
    Loader2Icon,
    SettingsIcon,
    RecycleIcon,
    FolderIcon,
    HelpCircleIcon,
    PhoneIcon,
} from "lucide-vue-next";
import { generateTraceId } from "~/commands";
import {
    SystemCheckKind,
    CheckStatus,
    type CheckResult,
    ManualAction,
} from "~/commands/types";
import CheckingIcon from "~/assets/icons/checking.svg";
import { clsx } from "~/lib/utils";
import { useRouter } from "vue-router";

const systemStore = useSystemStore();
console.log(systemStore);
// 弹窗显示状态
const showDetailModal = ref(false);

// 计算属性
const checkResults = computed(() => systemStore.state.check_results);
const isChecking = computed(() =>
    systemStore.state.check_results.some(
        (r) =>
            r.status === CheckStatus.Checking ||
            r.status === CheckStatus.AutoFixing,
    ),
);

// 计算通过的检查数量
const passedChecks = computed(() => {
    return checkResults.value.filter(
        (result) => result.status === CheckStatus.Passed,
    ).length;
});

// 计算整体状态
const overallStatus = computed(() => {
    if (isChecking.value) return "checking";
    if (passedChecks.value === checkResults.value.length) return "all-passed";
    if (passedChecks.value === 0) return "all-failed";
    return "partial";
});

// 状态颜色
const statusColor = computed(() => {
    switch (overallStatus.value) {
        case "checking":
            return "text-blue-500";
        case "all-passed":
            return "text-green-500";
        case "all-failed":
            return "text-red-500";
        case "partial":
            return "text-yellow-500";
        default:
            return "text-primary";
    }
});

// 系统检查项配置
const systemCheckItems = [
    {
        kind: SystemCheckKind.Platform,
        label: "操作系统检查",
        description: "检查操作系统是否为支持的Windows平台",
    },
    {
        kind: SystemCheckKind.OsVersion,
        label: "系统版本检查",
        description: "检查Windows版本是否符合要求",
    },
    {
        kind: SystemCheckKind.PowerShell,
        label: "PowerShell检查",
        description: "检查PowerShell是否已安装",
    },
    {
        kind: SystemCheckKind.Virtualization,
        label: "BIOS虚拟化检查",
        description: "检查虚拟化是否在BIOS中启用",
    },
    {
        kind: SystemCheckKind.WslEnvironment,
        label: "WSL2检查",
        description: "检查WSL2是否安装和配置",
    },
    {
        kind: SystemCheckKind.WslMirror,
        label: "任务引擎检查",
        description: "检查EchoWave镜像是否安装和运行",
    },
];

// 获取检查项状态
const getCheckItemResult = (kind: SystemCheckKind): CheckResult | null => {
    return checkResults.value.find((r) => r.kind === kind) || null;
};

// 获取详情中的进度信息
const getProgressFromDetails = (result: CheckResult | null): number | null => {
    if (!result?.details || typeof result.details !== "object") return null;
    const details = result.details as any;
    return details.progress ?? null;
};

// 获取详情中的状态文本
const getStatusTextFromDetails = (
    result: CheckResult | null,
): string | null => {
    if (!result?.details || typeof result.details !== "object") return null;
    const details = result.details as any;
    return details.status_text ?? null;
};

// 获取检查项图标
const getCheckItemIcon = (result: CheckResult | null) => {
    if (!result) return ClockIcon;

    const progress = getProgressFromDetails(result);
    // 如果有进度信息，显示下载图标
    if (progress !== null) {
        return DownloadIcon;
    }

    switch (result.status) {
        case CheckStatus.Passed:
            return CheckCircle2Icon;
        case CheckStatus.Checking:
        case CheckStatus.AutoFixing:
            return Loader2Icon;
        case CheckStatus.AutoFixFailed:
            return AlertCircleIcon;
        // case CheckStatus.AutoFixFailed:
        //     return CheckFailureIcon;
        case CheckStatus.NotStarted:
            return ClockIcon;
    }
};

// 获取检查项状态颜色
const getCheckItemColor = (result: CheckResult | null) => {
    if (!result) return "text-gray-400";

    const progress = getProgressFromDetails(result);
    // 如果有进度信息，显示蓝色（下载中）
    if (progress !== null) {
        return "text-blue-400";
    }

    switch (result.status) {
        case CheckStatus.Passed:
            return "text-green-400";
        case CheckStatus.Checking:
        case CheckStatus.AutoFixing:
            return "text-blue-400";
        case CheckStatus.AutoFixFailed:
            return "text-yellow-400";
        // case CheckStatus.AutoFixFailed:
        //     return "text-red-400";
        default:
            return "text-gray-400";
    }
};

// 获取检查项状态文本
const getCheckItemStatusText = (result: CheckResult | null) => {
    if (!result) return "待检查";

    const progress = getProgressFromDetails(result);
    const statusText = getStatusTextFromDetails(result);

    // 如果有进度信息，显示进度
    if (progress !== null) {
        return `下载中 ${progress}%`;
    }

    // 如果有自定义状态文本，使用它
    if (statusText) {
        return statusText;
    }

    // 使用 result.message 如果可用
    if (result.message && result.message.trim()) {
        return result.message;
    }

    // 默认状态文本
    switch (result.status) {
        case CheckStatus.Passed:
            return "通过";
        case CheckStatus.AutoFixFailed:
            return "失败";
        case CheckStatus.Checking:
            return "检查中";
        case CheckStatus.NotStarted:
            return "未开始";
        case CheckStatus.AutoFixing:
            return "配置中";
    }
};

// 检查是否应该显示动画
const shouldShowAnimation = (result: CheckResult | null) => {
    if (!result) return false;

    const progress = getProgressFromDetails(result);
    // 有进度信息时显示脉冲动画
    if (progress !== null) {
        return true;
    }

    // 检查中状态显示旋转动画
    return (
        (result.status === CheckStatus.Checking ||
            result.status === CheckStatus.AutoFixing) &&
        isChecking.value
    );
};

// 增强的检查项计算属性 - 预计算所有状态以避免模板中的重复函数调用
const enhancedCheckItems = computed(() => {
    return systemCheckItems.map((item) => {
        const result = getCheckItemResult(item.kind);
        const progress = getProgressFromDetails(result);
        const icon = getCheckItemIcon(result);
        const color = getCheckItemColor(result);
        const statusText = getCheckItemStatusText(result);
        const showAnimation = shouldShowAnimation(result);
        const manualAction = getManualActionInfo(result?.manual_action || null);

        // 预计算图标类名
        const iconClasses = clsx("h-5 w-5", color, {
            "animate-spin": showAnimation && icon === Loader2Icon,
            "animate-pulse": showAnimation && icon === DownloadIcon,
        });

        // 预计算状态文本类名
        const statusClasses = clsx(
            "flex-1 truncate px-2 py-0.5 text-right bg-opacity-20",
            color,
        );

        return {
            ...item,
            result,
            progress,
            icon,
            color,
            statusText,
            showAnimation,
            iconClasses,
            statusClasses,
            manualAction,
        };
    });
});

// 打开详情弹窗
const openDetailModal = () => {
    showDetailModal.value = true;
};

// 关闭详情弹窗
const closeDetailModal = () => {
    showDetailModal.value = false;
};

// 触发系统检查
const triggerCheck = async () => {
    try {
        await systemStore.triggerSystemCheck(generateTraceId());
    } catch (error) {
        console.error("Failed to trigger system check:", error);
    }
};

const router = useRouter();

// 获取手动操作按钮信息
const getManualActionInfo = (action: ManualAction | null) => {
    if (!action) return null;

    switch (action) {
        case ManualAction.OpenWindowsFeatures:
            return {
                text: "打开Windows功能",
                icon: SettingsIcon,
                variant: "outline" as const,
                action: () => handleManualAction(action),
            };
        case ManualAction.RestartRequired:
            return {
                text: "重启系统",
                icon: RecycleIcon,
                variant: "default" as const,
                action: () => handleManualAction(action),
            };
        case ManualAction.ChangeInstallPath:
            return {
                text: "更改路径",
                icon: FolderIcon,
                variant: "outline" as const,
                action: () => handleManualAction(action),
            };
        case ManualAction.ContactSupport:
            return {
                text: "联系支持",
                icon: PhoneIcon,
                variant: "outline" as const,
                action: () => handleManualAction(action),
            };
        case ManualAction.OpenFaqPage:
            return {
                text: "查看帮助",
                icon: HelpCircleIcon,
                variant: "outline" as const,
                action: () => handleManualAction(action),
            };
        default:
            return null;
    }
};

// 处理手动操作
const handleManualAction = async (action: ManualAction) => {
    try {
        console.log("处理手动操作:", action);
        // TODO: 实现具体的手动操作逻辑
        switch (action) {
            case ManualAction.OpenWindowsFeatures:
                // 打开Windows功能对话框
                break;
            case ManualAction.RestartRequired:
                // 重启系统
                break;
            case ManualAction.ChangeInstallPath:
                // 更改安装路径
                router.replace("/settings");
                break;
            case ManualAction.ContactSupport:
                // 联系技术支持
                break;
            case ManualAction.OpenFaqPage:
                // 打开FAQ页面
                break;
        }
    } catch (error) {
        console.error("手动操作失败:", error);
    }
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === "Escape" && showDetailModal.value) {
        closeDetailModal();
    }
};

// 生命周期钩子
onMounted(() => {
    document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
    document.removeEventListener("keydown", handleKeydown);
});
</script>
<template>
    <div class="mt-10">
        <!-- 系统检查概览 -->
        <div
            @click="openDetailModal"
            :class="
                clsx(
                    'flex h-[38px] w-[320px] cursor-pointer items-center border border-x-0 border-current text-current transition-opacity hover:opacity-80',
                    statusColor,
                )
            "
        >
            <div
                class="ml-3 flex h-full w-[75%] items-center leading-2 text-white"
            >
                <span>系统环境设置项</span>
            </div>
            <div
                class="polygon flex h-full w-[25%] items-center justify-end pr-4"
            >
                <span class="leading-2 font-bold text-gray-950">
                    {{ passedChecks }}/{{ checkResults.length }}
                </span>
            </div>
        </div>

        <!-- 详情弹窗 -->
        <Teleport to="body">
            <div
                v-if="showDetailModal"
                class="fixed inset-0 z-50 flex h-screen w-screen items-center justify-center"
                @click.self="closeDetailModal"
            >
                <!-- 背景遮罩 -->
                <div class="absolute inset-0 bg-[#0A2133]"></div>

                <!-- 弹窗内容 -->
                <div
                    class="relative z-10 mx-4 flex h-full w-full max-w-[365px] flex-col justify-between px-6 py-12"
                >
                    <!-- 检查项列表 -->
                    <div class="space-y-6">
                        <div
                            v-for="item in enhancedCheckItems"
                            :key="item.kind"
                            class="flex flex-col gap-2"
                        >
                            <!-- 主要信息行 -->
                            <div class="flex items-center gap-3">
                                <!-- 状态图标 -->
                                <div class="mr-0.5">
                                    <component
                                        :is="item.icon"
                                        :class="clsx(item.iconClasses)"
                                        :size="20"
                                    />
                                </div>

                                <!-- 检查项信息 -->
                                <div
                                    class="flex min-w-0 flex-1 items-center gap-2"
                                >
                                    <span
                                        class="w-32 truncate font-medium text-white"
                                        >{{ item.label }}</span
                                    >
                                    <span
                                        :class="item.statusClasses"
                                        :title="item.statusText"
                                    >
                                        {{ item.statusText }}
                                    </span>
                                </div>
                            </div>

                            <!-- 手动操作按钮 -->
                            <div
                                v-if="item.manualAction"
                                class="ml-6 flex items-center gap-2"
                            >
                                <Button
                                    @click="item.manualAction.action"
                                    :variant="item.manualAction.variant"
                                    size="sm"
                                    class="flex items-center gap-1"
                                >
                                    <component
                                        :is="item.manualAction.icon"
                                        :size="14"
                                    />
                                    {{ item.manualAction.text }}
                                </Button>
                            </div>

                            <!-- 错误信息详情 -->
                            <div
                                v-if="
                                    item.result &&
                                    item.result.message &&
                                    item.result.status ===
                                        CheckStatus.AutoFixFailed
                                "
                                class="ml-6 truncate text-sm text-gray-400"
                                :title="item.result.message"
                            >
                                {{ item.result.message }}
                            </div>
                        </div>
                    </div>

                    <!-- 底部操作区域 -->
                    <div class="mt-12 flex flex-col items-center gap-3">
                        <Button @click="closeDetailModal" class="w-32"
                            >关闭</Button
                        >
                        <Button
                            @click="triggerCheck"
                            :disabled="isChecking"
                            variant="link"
                            class="flex-1"
                        >
                            重新检查
                        </Button>
                    </div>
                </div>
            </div>
        </Teleport>
    </div>
</template>
<style lang="css">
.polygon {
    width: 25%;
    clip-path: polygon(30% 0, 100% 0, 100% 100%, 0 100%);
    background-color: currentColor;
    border-radius: 0 var(--radius-xs) var(--radius-xs) 0;
}
</style>
