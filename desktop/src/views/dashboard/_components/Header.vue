<script setup lang="ts">
import Logotype from "~/assets/images/logotype.svg";
import WalletIcon from "~/assets/images/wallet.svg";
import SignalStrengthIndicator from "./SignalStrengthIndicator.vue";
import avatar from "~/assets/images/avatar.png";
import { Button } from "~/components/ui/button";
import { computed, onMounted, toRaw } from "vue";
import { useUserStore } from "~/stores/user";
import { useNetworkStore } from "~/stores/network";
import { useRouter } from "vue-router";
import {
    Tooltip,
    TooltipTrigger,
    TooltipContent,
    TooltipProvider,
} from "@/components/ui/tooltip";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings } from "lucide-vue-next";
import { generateTraceId } from "~/commands";

const userStore = useUserStore();
const networkStore = useNetworkStore();
const router = useRouter();

// 计算属性
const isLoggedIn = computed(() => userStore.isLoggedIn);
const walletBalance = computed(() => userStore.state.wallet_balance);
const username = computed(
    () =>
        userStore.state.user_info?.username || userStore.state.user_info?.phone,
);
const tailscaleLatency = computed(() => networkStore.state.tailscale_latency);
const internetConnectivity = computed(
    () => networkStore.state.internet_connectivity,
);

// 格式化余额显示
const formattedBalance = computed(() => {
    return new Intl.NumberFormat("zh-CN", {
        style: "currency",
        currency: "CNY",
        minimumFractionDigits: 2,
    }).format(walletBalance.value);
});

// 网络状态指示器的信号强度
const signalStrength = computed(() => {
    if (!internetConnectivity.value) return 0;
    if (tailscaleLatency?.value === undefined) return 0;
    if (tailscaleLatency.value < 50) return 4;
    if (tailscaleLatency.value < 100) return 3;
    if (tailscaleLatency.value < 200) return 2;
    return 1;
});

// 网络状态文本
const networkStatusText = computed(() => {
    if (!internetConnectivity.value) return "网络断开";
    if (tailscaleLatency.value === null) return "连接中...";
    return `已连接 (${tailscaleLatency.value}ms)`;
});

// 处理提现按钮点击
const handleWithdraw = () => {
    // TODO: 实现提现功能
    console.log("Withdraw clicked");
};

// 处理设置页面跳转
const handleSettings = () => {
    router.push("/settings");
};

// 处理登出
const handleLogout = async () => {
    try {
        await userStore.logout(generateTraceId());
        router.push("/login");
    } catch (error) {
        console.error("Logout failed:", error);
    }
};

// 组件挂载时检查登录状态
onMounted(() => {
    if (!isLoggedIn.value) {
        router.push("/login");
    }
});
</script>
<template>
    <header
        class="border-b-primary/30 box-border flex h-[64px] items-center justify-between border-b bg-[#0a2133] px-[14px] py-[20px]"
    >
        <Logotype />
        <div class="flex items-center gap-4">
            <!-- 网络状况 -->
            <div class="flex items-center gap-2">
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger>
                            <SignalStrengthIndicator
                                :bars="signalStrength"
                                :class="
                                    internetConnectivity
                                        ? 'text-primary'
                                        : 'text-red-500'
                                "
                            />
                        </TooltipTrigger>
                        <TooltipContent>
                            {{ networkStatusText }}
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>

            <!-- 钱包 -->
            <div class="flex items-center">
                <WalletIcon />
                <span class="font-monospace ml-[12px] leading-normal font-bold">
                    {{ formattedBalance }}
                </span>
                <Button
                    variant="ghost"
                    size="sm"
                    class="text-primary ml-1"
                    @click="handleWithdraw"
                    :disabled="!isLoggedIn"
                >
                    提现
                </Button>
            </div>

            <!-- 个人信息 -->
            <DropdownMenu>
                <DropdownMenuTrigger class="outline-hidden">
                    <div class="relative size-8 cursor-pointer">
                        <img
                            :src="avatar"
                            :alt="username || 'user avatar'"
                            class="hover:ring-primary size-full rounded-full bg-black transition-all hover:ring-2"
                        />
                        <!-- 在线状态指示器 -->
                        <div
                            v-if="isLoggedIn"
                            class="absolute -right-0.5 -bottom-0.5 h-3 w-3 rounded-full border-2 border-white bg-green-500"
                        ></div>
                    </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" class="w-48">
                    <DropdownMenuLabel class="font-normal">
                        <div class="flex flex-col space-y-1">
                            <p class="text-sm leading-none font-medium">
                                {{ username || "未知用户" }}
                            </p>
                            <p
                                class="text-muted-foreground text-xs leading-none"
                            >
                                {{ isLoggedIn ? "在线" : "离线" }}
                            </p>
                        </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        class="cursor-pointer"
                        @click="handleSettings"
                    >
                        <Settings class="mr-2 h-4 w-4" />
                        设置
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                        variant="destructive"
                        class="cursor-pointer"
                        @click="handleLogout"
                    >
                        退出登录
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    </header>
</template>
