<script setup lang="ts">
const props = defineProps<{
    bars: 1 | 2 | 3 | 4 | 0;
    class?: string;
}>();
</script>
<template>
    <svg
        width="16.000000"
        height="13.000000"
        viewBox="0 0 16 13"
        fill="none"
        :class="props.class"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
    >
        <path
            id="Vector"
            d="M8.75 12.66C8.85 12.55 8.93 12.43 8.98 12.29C9.03 12.15 9.06 12 9.06 11.85C9.06 11.7 9.03 11.55 8.98 11.41C8.93 11.28 8.85 11.15 8.75 11.04C8.65 10.94 8.53 10.85 8.4 10.8C8.27 10.74 8.14 10.71 8 10.71C7.71 10.71 7.44 10.83 7.24 11.04C7.04 11.26 6.93 11.55 6.93 11.85C6.93 12.15 7.04 12.45 7.24 12.66C7.44 12.87 7.71 12.99 8 12.99C8.14 12.99 8.27 12.97 8.4 12.91C8.53 12.85 8.65 12.77 8.75 12.66Z"
            :class="bars >= 1 ? 'fill-primary' : 'fill-secondary'"
            fill-opacity="1.000000"
            fill-rule="evenodd"
        />
        <path
            id="Vector"
            d="M8 8.85C7.28 8.85 6.58 9.14 6.05 9.65C5.89 9.8 5.69 9.88 5.48 9.87C5.27 9.86 5.07 9.76 4.93 9.59C4.79 9.43 4.72 9.21 4.73 8.98C4.74 8.76 4.83 8.55 4.98 8.39C5.81 7.59 6.88 7.14 8 7.14C9.11 7.14 10.18 7.57 11.01 8.37C11.33 8.68 11.35 9.22 11.06 9.57C10.9 9.77 10.69 9.88 10.47 9.88C10.29 9.88 10.1 9.79 9.94 9.65C9.41 9.14 8.71 8.85 8 8.85Z"
            :class="bars >= 2 ? 'fill-primary' : 'fill-secondary'"
            fill-opacity="1.000000"
            fill-rule="evenodd"
        />
        <path
            id="Vector"
            d="M12.16 7.25C11.01 6.11 9.54 5.48 8 5.48C6.45 5.48 4.96 6.11 3.84 7.25C3.52 7.57 3.01 7.57 2.72 7.22C2.4 6.85 2.4 6.31 2.72 5.99C4.16 4.54 6.02 3.77 8 3.77C9.97 3.77 11.83 4.57 13.27 5.99C13.59 6.31 13.62 6.85 13.3 7.19C13.14 7.37 12.93 7.48 12.72 7.48C12.53 7.48 12.32 7.39 12.16 7.25Z"
            :class="bars >= 3 ? 'fill-primary' : 'fill-secondary'"
            fill-opacity="1.000000"
            fill-rule="evenodd"
        />
        <path
            id="Vector"
            d="M14.63 4.6C12.85 2.74 10.47 1.71 8 1.71C5.52 1.71 3.14 2.74 1.36 4.57C1.04 4.91 0.53 4.91 0.21 4.57C-0.08 4.22 -0.08 3.68 0.24 3.37C2.34 1.19 5.09 0 8 0C10.9 0 13.65 1.19 15.75 3.34C16.07 3.65 16.07 4.19 15.78 4.54C15.71 4.63 15.62 4.7 15.51 4.75C15.41 4.8 15.3 4.83 15.19 4.82C15 4.82 14.79 4.74 14.63 4.6Z"
            :class="bars >= 4 ? 'fill-primary' : 'fill-secondary'"
            fill-opacity="1.000000"
            fill-rule="evenodd"
        />
    </svg>
</template>
