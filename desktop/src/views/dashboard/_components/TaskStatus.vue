<script setup lang="ts">
import { computed } from "vue";
import { TaskAcceptanceStatus, TaskExecutionStatus } from "~/commands/types";
import { useTaskStore } from "~/stores/task";
import { useLogsStore } from "~/stores/logs";
import { Button } from "~/components/ui/button";
import { toast } from "vue-sonner";
import dayjs from "dayjs";
import RunningIcon from "./RunningIcon.vue";
import StoppedIcon from "./StoppedIcon.vue";

const taskStore = useTaskStore();
const logsStore = useLogsStore();

// 状态计算
const statusConfig = computed(() => {
    const { acceptance, execution } = taskStore.state;

    if (acceptance === TaskAcceptanceStatus.Stopped) {
        return {
            text: "空闲",
            icon: "stopped",
            bgColor: "bg-slate-600",
            textColor: "text-white",
            showTime: false,
        };
    }

    if (acceptance === TaskAcceptanceStatus.Starting) {
        return {
            text: "启动中...",
            icon: "running",
            bgColor: "bg-blue-100",
            textColor: "text-blue-600",
            showTime: false,
        };
    }

    if (acceptance === TaskAcceptanceStatus.Stopping) {
        return {
            text: "停止中...",
            icon: "running",
            bgColor: "bg-orange-100",
            textColor: "text-orange-600",
            showTime: false,
        };
    }

    if (acceptance === TaskAcceptanceStatus.Accepting) {
        if (execution === TaskExecutionStatus.Executing) {
            return {
                text: "运行中",
                icon: "running",
                bgColor: "bg-green-100",
                textColor: "text-green-600",
                showTime: true,
            };
        } else {
            return {
                text: "等待任务中",
                icon: "running",
                bgColor: "bg-sky-100",
                textColor: "text-sky-600",
                showTime: false,
            };
        }
    }

    return {
        text: "未知状态",
        icon: "stopped",
        bgColor: "bg-gray-100",
        textColor: "text-gray-600",
        showTime: false,
    };
});

// 格式化任务开始时间
const formatTaskStartTime = computed(() => {
    if (!taskStore.state.last_task_start_at) return "-";
    return dayjs(taskStore.state.last_task_start_at).format(
        "YYYY-MM-DD HH:mm:ss",
    );
});

// 导出日志功能
const exportLogs = async () => {
    try {
        toast.info("正在导出日志...");

        // 构建日志内容
        const logContent = logsStore.logs
            .map((log) => {
                const timestamp = dayjs(log.timestamp).format(
                    "YYYY-MM-DD HH:mm:ss",
                );
                const level = log.level.toUpperCase().padEnd(5);
                const module = log.module.split(".")[0];
                return `${timestamp} [${level}] ${module}: ${log.message}`;
            })
            .join("\n");

        // 创建文件名
        const filename = `echowave_logs_${dayjs().format("YYYYMMDD_HHmmss")}.txt`;

        // 创建并下载文件
        const blob = new Blob([logContent], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast.success("日志导出成功");
    } catch (error) {
        console.error("导出日志失败:", error);
        toast.error("导出日志失败，请重试");
    }
};
</script>

<template>
    <!-- 状态显示区域 -->
    <section class="my-4 px-5">
        <div class="flex items-center justify-between">
            <!-- 状态标签 -->
            <div class="flex items-center">
                <div
                    class="inline-flex items-center rounded-full px-3 py-1 text-sm font-medium"
                    :class="[statusConfig.bgColor, statusConfig.textColor]"
                >
                    <StoppedIcon
                        v-if="statusConfig.icon === 'stopped'"
                        class="mr-2 h-3 w-3"
                    />
                    <RunningIcon v-else class="mr-2 h-3 w-3" />
                    {{ statusConfig.text }}
                </div>
            </div>

            <!-- 导出按钮 -->
            <Button
                @click="exportLogs"
                variant="link"
                size="sm"
                class="text-blue-600 hover:text-blue-700"
            >
                导出日志
            </Button>
        </div>

        <!-- 任务开始时间 -->
        <div v-if="statusConfig.showTime" class="mt-2 text-sm text-gray-500">
            任务开始时间: {{ formatTaskStartTime }}
        </div>
    </section>
</template>

<style scoped></style>
