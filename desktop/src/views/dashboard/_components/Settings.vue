<script setup lang="ts">
import { CircleHelpIcon } from "lucide-vue-next";
import { Label } from "~/components/ui/label";
import { Switch } from "~/components/ui/switch";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "~/components/ui/tooltip";
import { computed } from "vue";
import { useSettingsStore } from "~/stores/settings";

const settingsStore = useSettingsStore();

// 计算属性
const autoAcceptTasks = computed(() => settingsStore.state.auto_accept_tasks);

const autoStartOnBoot = computed(() => settingsStore.state.auto_start_on_boot);
</script>
<template>
    <section class="mt-4 w-[300px] px-5 pr-8">
        <!-- 设置项 -->
        <div class="item-center flex justify-between">
            <!-- 自动接单设置 -->
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <Label class="flex items-center gap-2">
                        <Switch :model-value="autoAcceptTasks" />
                        <span class="leading-2">自动接单</span>
                    </Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <CircleHelpIcon class="size-[16px] cursor-help text-gray-400 hover:text-gray-600" />
                            </TooltipTrigger>
                            <TooltipContent align="start" side="top"
                                class="bg-secondary [&_.ui-tooltip-arrow]:bg-secondary [&_.ui-tooltip-arrow]:fill-secondary text-white">
                                <p class="w-[200px] text-sm text-wrap break-words">
                                    <span>开启后，程序启动后会自动接单。需要系统检查全部通过才能生效。</span>
                                </p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>

            <!-- 开机自启动设置 -->
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <Label class="flex items-center gap-2">
                        <Switch :model-value="autoStartOnBoot" />
                        <span class="leading-2">开机自启动</span>
                    </Label>
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger as-child>
                                <CircleHelpIcon class="size-[16px] cursor-help text-gray-400 hover:text-gray-600" />
                            </TooltipTrigger>
                            <TooltipContent align="start" side="top"
                                class="bg-secondary [&_.ui-tooltip-arrow]:bg-secondary [&_.ui-tooltip-arrow]:fill-secondary text-white">
                                <p class="w-[200px] text-sm text-wrap break-words">
                                    <span>开启后，系统启动时会自动运行EchoWave客户端。</span>
                                </p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>
        </div>
    </section>
</template>
