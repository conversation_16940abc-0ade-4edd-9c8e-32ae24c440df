<script setup lang="ts">
import { computed } from "vue";
import { useDeviceStore } from "~/stores/device";

const deviceStore = useDeviceStore();
const deviceId = computed(() => deviceStore.state.machine_id.slice(-8));
const webVersion = __VERSION__;
const clientVersion = deviceStore.state.version;
const distroVersion = computed(() => {
    if (deviceStore.state.distro_version === "0.0.0") {
        return "";
    }
    return deviceStore.state.distro_version.split(".").slice(0, 2).join(".");
});
</script>
<template>
    <footer class="m-[16px] flex h-[20px] justify-between font-[Regular]">
        <span>设备号：{{ deviceId }}</span>
        <div class="text-sm text-[#9a9a9a]">
            <span>客户端：v{{ clientVersion }}</span>
            <span v-if="distroVersion !== ''"> _{{ distroVersion }} </span>
        </div>
    </footer>
</template>
