<script setup lang="ts">
import Header from "./_components/Header.vue";
import Footer from "./_components/Footer.vue";
import Prerequisites from "./_components/Prerequisites.vue";
import TaskStatus from "./_components/TaskStatus.vue";
import Logs from "./_components/Logs.vue";
import { Button } from "~/components/ui/button";
import { computed, onMounted } from "vue";
import { useWindowActive } from "~/composables/useWindowActive";
import { clsx } from "~/lib/utils";
import Settings from "./_components/Settings.vue";
import { useUserStore } from "~/stores/user";
import { useTaskStore } from "~/stores/task";
import { useRouter } from "vue-router";
import { executeCommand, generateTraceId } from "~/commands";
import { TaskAcceptanceStatus } from "~/commands/types";
import { useComputedStore } from "~/stores/computed";

const router = useRouter();
const userStore = useUserStore();
const taskStore = useTaskStore();
const computedStore = useComputedStore();

// 窗口活动状态
const { isActive } = useWindowActive();

// 计算属性
const isLoggedIn = computed(() => userStore.state.is_logged_in);
const isAccepting = computed(() => taskStore.isAccepting);
const isStarting = computed(
    () => taskStore.state.acceptance === TaskAcceptanceStatus.Starting,
);
const isStopping = computed(
    () => taskStore.state.acceptance === TaskAcceptanceStatus.Stopping,
);
const isReadyForWork = computed(() => computedStore.state.is_ready_for_work);

// 按钮文本
const buttonText = computed(() => {
    if (isStarting.value) return "开始接单中...";
    if (isStopping.value) return "停止接单中...";
    if (isAccepting.value) return "暂停接单";
    return "开始接单";
});

// 处理接单按钮点击
const handleToggleAcceptance = async () => {
    try {
        await taskStore.toggleAcceptance(generateTraceId());
    } catch (error) {
        console.error("Failed to toggle task acceptance:", error);
    }
};

// 组件挂载时检查登录状态
onMounted(() => {
    if (!isLoggedIn.value) {
        router.push("/login");
    }
    // 触发系统检查
    executeCommand({
        type: "TriggerSystemCheck",
        trace_id: generateTraceId(),
    });
});
</script>
<template>
    <section
        :class="
            clsx(
                'flex h-full flex-col',
                isActive ? 'wave-effect' : 'wave-static',
            )
        "
    >
        <Header />
        <main class="flex flex-1 flex-col justify-center">
            <Prerequisites />
            <Settings />
            <!-- 状态: 已停止接单、等待接单、运行中 -->
            <TaskStatus />
            <!-- 日志信息 -->
            <Logs />
            <!-- 主按钮 -->
            <Button
                class="mx-auto my-4 w-32"
                :disabled="!isReadyForWork || isStarting || isStopping"
                @click="handleToggleAcceptance"
                >{{ buttonText }}</Button
            >
        </main>
        <Footer />
    </section>
</template>
<style lang="css">
.wave-effect {
    background: url("~/assets/images/line-green.gif") no-repeat bottom;
}

.wave-static {
    background: url("~/assets/images/line-green-static.png") no-repeat bottom;
}
</style>
