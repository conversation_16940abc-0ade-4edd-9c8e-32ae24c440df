<script setup lang="ts">
import Logomark from "~/assets/images/logomark.svg";
import { Loader2Icon } from "lucide-vue-next";
import { useAppStore } from "~/stores/app";
import { useUserStore } from "~/stores/user";
import { watchEffect } from "vue";
import { useRouter } from "vue-router";

const appStore = useAppStore();
const userStore = useUserStore();
const router = useRouter();

watchEffect(() => {
    if (!appStore.isReady) return;
    if (userStore.state.is_logging_in || userStore.state.last_sync_time === 0)
        return;
    if (userStore.state.is_logged_in) {
        router.replace("/dashboard");
    } else {
        router.replace("/login");
    }
});
</script>

<template>
    <section class="relative h-screen overflow-hidden">
        <!-- 渐变背景 -->
        <div
            class="absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900"
        ></div>

        <!-- 动态背景装饰 -->
        <div class="absolute inset-0">
            <div
                class="absolute top-1/4 left-1/4 h-72 w-72 animate-pulse rounded-full bg-blue-500/10 blur-3xl"
            ></div>
            <div
                class="absolute right-1/4 bottom-1/4 h-96 w-96 animate-pulse rounded-full bg-purple-500/10 blur-3xl delay-1000"
            ></div>
        </div>

        <!-- 主要内容 -->
        <div
            class="relative z-10 flex h-full flex-col items-center justify-center px-8"
        >
            <!-- Logo区域 -->
            <div class="animate-fade-in flex flex-col items-center space-y-8">
                <div class="relative">
                    <div
                        class="absolute inset-0 scale-110 rounded-full bg-white/20 blur-xl"
                    ></div>
                    <Logomark
                        class="animate-float relative size-24 text-white drop-shadow-2xl"
                    />
                </div>

                <!-- 标题 -->
                <div class="space-y-2 text-center">
                    <h1 class="text-3xl font-bold tracking-wide text-white">
                        EchoWave
                    </h1>
                    <p class="text-lg font-medium text-slate-300">
                        分布式算力供给端
                    </p>
                </div>
            </div>

            <!-- 加载区域 -->
            <div
                class="animate-fade-in-delay mt-16 flex flex-col items-center space-y-4"
            >
                <div class="relative">
                    <div
                        class="absolute inset-0 scale-150 rounded-full bg-blue-500/20 blur-md"
                    ></div>
                    <Loader2Icon
                        class="relative size-10 animate-spin text-blue-400"
                    />
                </div>
                <p class="text-base font-medium text-slate-300">
                    正在启动中...
                </p>
            </div>
        </div>
    </section>
</template>

<style scoped>
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fade-in-delay {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-10px);
    }
}

.animate-fade-in {
    animation: fade-in 0.8s ease-out;
}

.animate-fade-in-delay {
    animation: fade-in-delay 0.8s ease-out 0.3s both;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}
</style>
