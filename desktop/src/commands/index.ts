import { invoke, Channel } from "@tauri-apps/api/core";
import type {
    CoreCommand,
    CommandResponse,
    Uuid,
    CoreEvent,
    FolderPickerResult,
} from "./types";

export const greet = (name: string): Promise<string> =>
    invoke("greet", { name });

export const subscribeEvent = (
    eventName: string,
    traceId: Uuid,
    onEvent: Channel<CoreEvent>,
): Promise<number> =>
    invoke("subscribe_event", { eventName, traceId, onEvent });

export const unsubscribeEvent = (
    eventName: string,
    eventId: number,
    traceId: Uuid,
): Promise<void> =>
    invoke("unsubscribe_event", { eventName, eventId, traceId });

export const cleanupSubscriptions = (): Promise<void> =>
    invoke("cleanup_subscriptions");

// Core 命令执行函数
export const executeCommand = async (
    command: CoreCommand,
): Promise<CommandResponse> => {
    return invoke("execute_command", { command });
};

export const getGlobalTraceId = (): Promise<Uuid> =>
    invoke("get_global_trace_id");

// UUID 生成函数
export const generateTraceId = (): Uuid => {
    if (typeof crypto !== "undefined" && crypto.randomUUID) {
        return crypto.randomUUID();
    }
    // 降级方案
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
        /[xy]/g,
        function (c) {
            const r = (Math.random() * 16) | 0;
            const v = c === "x" ? r : (r & 0x3) | 0x8;
            return v.toString(16);
        },
    ) as Uuid;
};

export const pickFolder = (title: string): Promise<FolderPickerResult> =>
    invoke("pick_folder", { title });

export const validateFolderPath = (path: string): Promise<string> =>
    invoke("validate_folder_path", { path });
