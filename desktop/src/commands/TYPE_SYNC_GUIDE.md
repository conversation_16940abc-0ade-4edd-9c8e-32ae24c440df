# 类型同步指南

本文档旨在帮助快速完成 Rust 后端与 TypeScript 前端之间的类型定义同步。

## 概述

EchoWave 项目采用 Rust (Tauri) + Vue 3 架构，需要保持前后端类型定义的一致性。主要的类型定义文件：

- **后端**: `@crates/domain/src/context.rs` - 核心上下文和类型定义
- **前端**: `@desktop/src/commands/types.ts` - TypeScript 类型定义

## 快速同步流程

### 1. 识别类型源文件

主要需要同步的 Rust 类型来源：

```
crates/domain/src/
├── context.rs                    # 核心类型（主要）
├── services/
│   ├── user_service.rs           # UserState, UserInfo
│   ├── system_check.rs           # SystemState, CheckResult, CheckStatus
│   ├── settings_service.rs       # UserSettingsState
│   ├── task_acceptance_service.rs # TaskState, TaskAcceptanceStatus
│   └── network_service.rs        # NetworkState
```

### 2. 核心类型映射表

| Rust 类型               | TypeScript 类型         | 文件位置                   | 备注         |
| ----------------------- | ----------------------- | -------------------------- | ------------ |
| `StateMessage`          | `StateMessage`          | context.rs                 | 状态消息枚举 |
| `ProjectedState`        | `ProjectedState`        | context.rs                 | 投射状态聚合 |
| `ComputedProperties`    | `ComputedProperties`    | context.rs                 | 计算属性     |
| `CoreCommand`           | `CoreCommand`           | context.rs                 | 命令枚举     |
| `CoreEvent`             | `CoreEvent`             | context.rs                 | 事件枚举     |
| `CommandResponse`       | `CommandResponse`       | context.rs                 | 命令响应     |
| `ExternalPageType`      | `ExternalPageType`      | context.rs                 | 外部页面类型 |
| `UserState`             | `UserState`             | user_service.rs            | 用户状态     |
| `UserInfo`              | `UserInfo`              | user_service.rs            | 用户信息     |
| `SystemState`           | `SystemState`           | system_check.rs            | 系统状态     |
| `CheckResult`           | `CheckResult`           | system_check.rs            | 检查结果     |
| `CheckStatus`           | `CheckStatus`           | system_check.rs            | 检查状态枚举 |
| `TaskState`             | `TaskState`             | task_acceptance_service.rs | 任务状态     |
| `TaskAcceptanceStatus`  | `TaskAcceptanceStatus`  | task_acceptance_service.rs | 任务接受状态 |
| `TaskExecutionStatus`   | `TaskExecutionStatus`   | task_acceptance_service.rs | 任务执行状态 |
| `NetworkState`          | `NetworkState`          | network_service.rs         | 网络状态     |
| `UserSettingsState`     | `UserSettingsState`     | settings_service.rs        | 用户设置状态 |
| `SystemConditions`      | `SystemConditions`      | system_check.rs            | 系统条件状态 |
| `DeviceInfo`            | `DeviceInfo`            | device_service.rs          | 设备信息     |
| `ConnectionHistoryItem` | `ConnectionHistoryItem` | network_service.rs         | 连接历史项   |
| `ProgressInfo`          | `ProgressInfo`          | system_check.rs            | 进度信息     |
| `ManualAction`          | `ManualAction`          | system_check.rs            | 手动操作枚举 |

### 3. 常见同步问题

#### 3.1 枚举类型转换

**Rust 简单枚举**:

```rust
pub enum ExternalPageType {
    Register,
    ForgotPassword,
    Faq
}
```

**TypeScript 联合类型**:

```typescript
export type ExternalPageType = "Register" | "ForgotPassword" | "Faq";
```

**Rust 复杂枚举**:

```rust
pub enum TaskAcceptanceStatus {
    Stopped,
    Starting,
    Accepting,
    Error(String),
}
```

**TypeScript 常量枚举**:

```typescript
export const enum TaskAcceptanceStatus {
    Stopped = "Stopped",
    Starting = "Starting",
    Accepting = "Accepting",
    Error = "Error",
}
```

#### 3.2 结构体字段映射

**Rust 结构体**:

```rust
pub struct UserState {
    pub is_logged_in: bool,
    pub user_info: Option<UserInfo>,
    pub wallet_balance: f64,
}
```

**TypeScript 接口**:

```typescript
export interface UserState {
    is_logged_in: boolean;
    user_info: UserInfo | null;
    wallet_balance: number;
}
```

#### 3.3 重新导出类型处理

Rust 中通过 `pub use` 重新导出的类型需要追踪到源定义：

```rust
// context.rs 中的重新导出
pub use crate::services::{
    user_service::UserState,
    system_check::SystemState,
    // ...
};
```

需要查看对应的服务模块获取完整定义。

### 4. 同步检查清单

#### 4.1 新增类型检查

- [ ] 检查 Rust 中是否有新的枚举/结构体
- [ ] 检查是否有新的字段添加到现有结构体
- [ ] 检查是否有新的枚举变体
- [ ] **Device 相关**: 验证 DeviceInfo 字段完整性
- [ ] **Device 相关**: 检查 ProjectedState 中的 device 字段
- [ ] **Device 相关**: 验证 DeviceStateChanged 消息类型

#### 4.2 修改类型检查

- [ ] 检查现有字段类型是否发生变化
- [ ] 检查字段是否被移除或重命名
- [ ] 检查枚举变体是否发生变化
- [ ] **SystemState**: 验证 is_check_running 字段
- [ ] **嵌套类型**: 检查所有嵌套状态类型的字段完整性

#### 4.3 一致性验证

- [ ] 确保所有 `Option<T>` 映射为 `T | null` 或 `T | undefined`
- [ ] 确保所有 `Vec<T>` 映射为 `T[]`
- [ ] 确保所有 `String` 映射为 `string`
- [ ] 确保所有 `bool` 映射为 `boolean`
- [ ] 确保所有 `u32/u64/f64` 映射为 `number`
- [ ] **时间戳**: 确保所有时间字段为 `number` 类型
- [ ] **复杂枚举**: 验证复杂枚举的对象联合类型映射

#### 4.4 事件和常量验证

- [ ] 检查 EVENT_NAMES 对象的完整性
- [ ] 验证所有事件名称字符串的一致性
- [ ] 确保 StateType 联合类型包含所有状态类型
- [ ] **新增**: 验证 DEVICE_STATE_UPDATED 事件常量

#### 4.5 构建验证

- [ ] 运行 `pnpm build` 确保 TypeScript 编译通过
- [ ] 检查是否有类型错误
- [ ] 验证前端应用正常启动
- [ ] 测试状态更新事件的处理

### 5. 特殊类型注意事项

#### 5.1 时间类型

```rust
// Rust
pub last_sync_time: String, // RFC3339 格式
```

```typescript
// TypeScript
last_sync_time: string; // ISO 8601 格式
```

#### 5.2 错误类型

```rust
// Rust 复杂枚举
pub enum TaskAcceptanceStatus {
    Error(String),
}
```

由于 TypeScript 无法完美映射 Rust 的复杂枚举，简化为：

```typescript
// TypeScript
export const enum TaskAcceptanceStatus {
    Error = "Error",
}
```

#### 5.3 JSON 值类型

```rust
// Rust
pub details: Option<serde_json::Value>,
```

```typescript
// TypeScript
details?: unknown; // 或 unknown
```

#### 5.4 设备信息类型

```rust
// Rust DeviceInfo 结构体
pub struct DeviceInfo {
    pub machine_id: String,
    pub device_name: String,
    pub platform: String,
    pub arch: String,
    pub version: String,
    pub mirror_version: String,
    pub is_new_device: bool,
}
```

```typescript
// TypeScript 接口
export interface DeviceInfo {
    machine_id: string;
    device_name: string;
    platform: string;
    arch: string;
    version: string;
    mirror_version: string;
    is_new_device: boolean;
}
```

#### 5.5 状态消息复杂枚举

```rust
// Rust 复杂枚举（包含数据）
pub enum StateMessage {
    UserStateChanged(UserState, TraceId),
    DeviceStateChanged(DeviceInfo, TraceId),
}
```

```typescript
// TypeScript 对象联合类型
export type StateMessage =
    | { type: "UserStateChanged"; data: UserState; trace_id: Uuid }
    | { type: "DeviceStateChanged"; data: DeviceInfo; trace_id: Uuid };
```

#### 5.6 新增字段的默认值处理

对于新增的字段，需要考虑向后兼容性：

```typescript
// 新增字段使用可选标记
export interface SystemState {
    // 现有字段...
    check_results: CheckResult[];
    // 新增字段
    is_check_running?: boolean; // 可选，提供默认值 false
}
```

在前端代码中处理：

```typescript
// 安全访问新增字段
const isRunning = systemState.is_check_running ?? false;
```

### 6. 自动化建议

#### 6.1 IDE 配置

建议在 IDE 中设置以下功能来提高同步效率：

1. **文件监听**: 监听 Rust 文件变更
2. **类型检查**: 启用 TypeScript 严格模式
3. **快捷跳转**: 配置从 TypeScript 跳转到对应 Rust 定义

#### 6.2 自动化工具推荐

**ts-rs 集成**:

```rust
// 在 Rust 结构体上添加注解
#[derive(Serialize, Deserialize, TS)]
#[ts(export)]
pub struct UserState {
    // ...
}
```

这样可以自动生成 TypeScript 类型定义文件。

**类型生成脚本**:

```bash
# 创建自动同步脚本
#!/bin/bash
cd crates/core && cargo test --features ts-rs
cp target/bindings/*.ts ../../desktop/src/commands/generated/
```

#### 6.3 持续集成最佳实践

**GitHub Actions 配置**:

```yaml
- name: Type Sync Check
  run: |
      # 检查 Rust 类型变更
      cargo check -p core
      # 检查 TypeScript 编译
      cd desktop && pnpm build
      # 验证类型一致性
      pnpm run type-check
```

**Pre-commit Hook**:

```bash
#!/bin/sh
# 在提交前自动检查类型同步
cargo check -p core && cd desktop && pnpm build
```

### 7. 常用命令

```bash
# 前端类型检查
cd desktop && pnpm build

# 后端编译检查
cargo check -p core

# 运行前端开发服务器
cd desktop && pnpm dev

# 运行 Tauri 开发模式
cd desktop && pnpm tauri dev
```

### 8. 疑难问题解决

#### 8.1 重复定义错误

如果遇到 TypeScript 重复定义错误，检查：

- 是否有重复的接口/类型定义
- 是否有命名冲突

#### 8.2 类型不匹配错误

如果遇到类型不匹配：

- 确认 Rust 字段名与 TypeScript 字段名一致
- 确认类型映射正确（Option<T> → T | null）
- 确认枚举值字符串一致

#### 8.3 缺失类型错误

如果遇到缺失类型：

- 检查是否有新增的 Rust 类型未同步
- 检查 import/export 是否正确

---

**最后更新**: 2025-07-16  
**同步版本**: v0.7.0  
**维护者**: Claude Code Assistant

## 本次同步记录 (v0.7.0)

### 主要变更内容

#### 1. 新增 Device 相关类型

- **DeviceInfo 接口**: 添加了完整的设备信息类型定义

    - `machine_id: string` - 机器唯一标识
    - `device_name: string` - 设备名称
    - `platform: string` - 操作系统平台
    - `arch: string` - 系统架构
    - `version: string` - 系统版本
    - `mirror_version: string` - 镜像版本
    - `is_new_device: boolean` - 是否为新设备

- **ProjectedState 更新**: 添加了 `device: DeviceInfo` 字段
- **StateMessage 扩展**: 添加了 `DeviceStateChanged` 变体
- **StateType 扩展**: 添加了 `"Device"` 选项
- **事件常量更新**: 添加了 `DEVICE_STATE_UPDATED: "device_state_updated"`

#### 2. SystemState 字段完善

- **新增字段**: `is_check_running: boolean` - 标识系统检查是否正在运行
- **嵌套类型完善**: 确保所有嵌套类型字段完整
    - `CheckResult` - 检查结果类型
    - `PowerShellStatus` - PowerShell 状态
    - `WSLMirrorStatus` - WSL 镜像状态
    - `WSLInstallerStatus` - WSL 安装器状态

#### 3. 枚举和常量同步

- **SystemCheckKind**: 确保包含所有 Rust 变体
- **CheckStatus**: 完善所有检查状态
- **TaskAcceptanceStatus/TaskExecutionStatus**: 验证枚举完整性

### 特殊处理说明

#### 复杂枚举类型处理

对于 Rust 中包含数据的复杂枚举变体，在 TypeScript 中简化处理：

```rust
// Rust 复杂枚举
pub enum StateMessage {
    UserStateChanged(UserState, TraceId),
    SystemStateChanged(SystemState, TraceId),
    // ...
}
```

```typescript
// TypeScript 对象联合类型
export type StateMessage =
    | { type: "UserStateChanged"; data: UserState; trace_id: Uuid }
    | { type: "SystemStateChanged"; data: SystemState; trace_id: Uuid };
// ...
```

#### 新增字段默认值处理

- 所有新增的可选字段使用 `| undefined` 或 `| null` 标记
- 布尔字段提供明确的默认值说明
- 时间戳字段统一使用 `string` 类型（ISO 8601 格式）

## 下次同步时的快速检查

1. 读取 `@crates/domain/src/context/state.rs`
2. 读取 `@crates/domain/src/context/event.rs`
3. 读取 `@crates/domain/src/context/command.rs`
4. 读取各个服务模块的状态定义
5. 对比 `@desktop/src/commands/types.ts`
6. 检查 Device 相关类型的完整性
7. 验证所有事件常量的同步
8. 运行 `pnpm build` 验证
9. 更新此文档的同步版本号
