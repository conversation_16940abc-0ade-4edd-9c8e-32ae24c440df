// Core 命令类型定义

export type ExternalPageType = "Register" | "ForgotPassword" | "Faq";

// 用户状态 - 对应 Rust UserState
export interface UserState {
    is_logged_in: boolean;
    is_logging_in: boolean;
    next_code_send_available_at: number;
    user_info: UserInfo | undefined;
    wallet_balance: number;
    last_sync_time: number;
}

// 用户信息 - 对应 Rust UserInfo
export interface UserInfo {
    user_id: string | undefined;
    username: string | undefined;
    phone: string;
    email: string | undefined;
    avatar: string | undefined;
}

// 系统状态 - 对应 Rust SystemState
export interface SystemState {
    check_results: CheckResult[];
    powershell_status: PowerShellStatus;
    wsl_mirror_status: WSLMirrorStatus;
    wsl_installer_status: WSLInstallerStatus;
    last_check_time: string | undefined;
    is_check_running: boolean;
}

// PowerShell 状态 - 对应 Rust PowerShellStatus
export interface PowerShellStatus {
    is_installed: boolean;
    version: string | undefined;
    installation_progress: number;
}

// WSL 镜像状态 - 对应 Rust WSLMirrorStatus
export interface WSLMirrorStatus {
    is_downloaded: boolean;
    is_installed: boolean;
    download_progress: number;
    install_progress: number;
    version: string | undefined;
}

// WSL 安装器状态 - 对应 Rust WSLInstallerStatus
export interface WSLInstallerStatus {
    is_installed: boolean;
    is_downloaded: boolean;
    download_progress: number;
    install_progress: number;
    version: string | undefined;
}

// 手动操作 - 对应 Rust ManualAction
export const enum ManualAction {
    OpenWindowsFeatures = "OpenWindowsFeatures",
    RestartRequired = "RestartRequired",
    ChangeInstallPath = "ChangeInstallPath",
    ContactSupport = "ContactSupport",
    OpenFaqPage = "OpenFaqPage",
}

// 系统条件 - 对应 Rust SystemConditions
export interface SystemConditions {
    user_logged_in: boolean;
    all_checks_passed: boolean;
    currently_accepting: boolean;
}

// 任务状态 - 对应 Rust TaskState
export interface TaskState {
    acceptance: TaskAcceptanceStatus;
    execution: TaskExecutionStatus;
    last_task_start_at: string | undefined;
}

// 网络状态 - 对应 Rust NetworkState
export interface NetworkState {
    tailscale_latency: number | undefined;
    internet_connectivity: boolean;
    last_update_time: string | undefined;
    connection_history: ConnectionHistoryItem[];
}

// 连接历史项 - 对应 Rust ConnectionHistoryItem
export interface ConnectionHistoryItem {
    timestamp: string;
    status: string;
    latency: number | undefined;
}

// 设备信息 - 对应 Rust DeviceInfo
export interface DeviceInfo {
    machine_id: string;
    device_name: string;
    platform: string;
    arch: string;
    version: string;
    distro_version: string;
    agent_version: string;
    wsl_version: string;
    is_new_device: boolean | undefined;
}

// 用户设置状态 - 对应 Rust UserSettingsState
export interface UserSettingsState {
    auto_accept_tasks: boolean;
    auto_start_on_boot: boolean;
    data_root_path: string;
}

// 计算属性 - 对应 Rust ComputedProperties
export interface ComputedProperties {
    is_ready_for_work: boolean;
    overall_health_status: string;
    last_computed_at: string;
}

// 投射状态 - 对应 Rust ProjectedState
export interface ProjectedState {
    readonly user: Readonly<UserState>;
    readonly system: Readonly<SystemState>;
    readonly task: Readonly<TaskState>;
    readonly network: Readonly<NetworkState>;
    readonly settings: Readonly<UserSettingsState>;
    readonly computed: Readonly<ComputedProperties>;
    readonly device: Readonly<DeviceInfo>;
}

export const EVENT_NAMES = {
    USER_STATE_UPDATED: "user_state_updated",
    SYSTEM_STATE_UPDATED: "system_state_updated",
    TASK_STATE_UPDATED: "task_state_updated",
    NETWORK_STATE_UPDATED: "network_state_updated",
    SETTINGS_STATE_UPDATED: "settings_state_updated",
    COMPUTED_STATE_UPDATED: "computed_state_updated",
    DEVICE_STATE_UPDATED: "device_state_updated",
    ALL_STATE_UPDATED: "all_state_updated",
    LOG_MESSAGE: "log_message",
    ERROR: "error",
} as const;
// 事件名称常量

export type EventName = (typeof EVENT_NAMES)[keyof typeof EVENT_NAMES];

export type Uuid = `${string}-${string}-${string}-${string}-${string}`;

// =================== 基础类型 ===================

// 系统检查类型枚举 - 对应 Rust SystemCheckKind
export const enum SystemCheckKind {
    Platform = "Platform",
    OsVersion = "OsVersion",
    PowerShell = "PowerShell",
    Virtualization = "Virtualization",
    WslEnvironment = "WslEnvironment",
    WslMirror = "WslMirror",
}

// 检查状态枚举
export const enum CheckStatus {
    NotStarted = "NotStarted",
    Checking = "Checking",
    AutoFixing = "AutoFixing",
    Passed = "Passed",
    AutoFixFailed = "AutoFixFailed",
}

// 检查结果
export interface CheckResult {
    kind: SystemCheckKind;
    status: CheckStatus;
    message: string;
    auto_fixable: boolean;
    details?: unknown; // serde_json::Value
}

// 任务接受状态
export const enum TaskAcceptanceStatus {
    Stopped = "Stopped",
    Starting = "Starting",
    Accepting = "Accepting",
    Stopping = "Stopping",
}

// 任务执行状态
export const enum TaskExecutionStatus {
    Idle = "Idle",
    Executing = "Executing",
}

// =================== 核心状态类型 ===================

// =================== 消息类型 ===================

// 状态消息类型 - 对应 Rust StateMessage
export type StateMessage =
    | { type: "UserStateChanged"; data: UserState; trace_id: Uuid }
    | { type: "SystemStateChanged"; data: SystemState; trace_id: Uuid }
    | { type: "TaskStateChanged"; data: TaskState; trace_id: Uuid }
    | { type: "NetworkStateChanged"; data: NetworkState; trace_id: Uuid }
    | { type: "SettingsStateChanged"; data: UserSettingsState; trace_id: Uuid }
    | { type: "DeviceStateChanged"; data: DeviceInfo; trace_id: Uuid }
    | { type: "InitialState"; data: ProjectedState; trace_id: Uuid }
    | {
          type: "ComputedStateChanged";
          property: string;
          value: any;
          trace_id: Uuid;
      };

// 状态类型枚举
export type StateType =
    | "User"
    | "System"
    | "Task"
    | "Network"
    | "Settings"
    | "Device"
    | "Computed"
    | "All";

export interface StructuredLogEntry {
    timestamp: number;
    level: string;
    module: string;
    target: string;
    message: string;
    source: string | undefined;
    trace_id: Uuid | undefined;
    span_id: Uuid | undefined;
    file: string | undefined;
    line: number | undefined;
    fields: Record<string, String>;
}

// 日志级别
export type LogLevel = "Error" | "Warn" | "Info" | "Debug" | "Trace";

export type UserEvent = {
    state_type: "User";
    data: UserState;
    trace_id: Uuid;
};
export type SystemEvent = {
    state_type: "System";
    data: SystemState;
    trace_id: Uuid;
};
export type TaskEvent = {
    state_type: "Task";
    data: TaskState;
    trace_id: Uuid;
};
export type NetworkEvent = {
    state_type: "Network";
    data: NetworkState;
    trace_id: Uuid;
};
export type SettingsEvent = {
    state_type: "Settings";
    data: UserSettingsState;
    trace_id: Uuid;
};
export type DeviceEvent = {
    state_type: "Device";
    data: DeviceInfo;
    trace_id: Uuid;
};
export type ComputedEvent = {
    state_type: "Computed";
    data: ComputedProperties;
    trace_id: Uuid;
};
export type AllEvent = {
    state_type: "All";
    data: ProjectedState;
    trace_id: Uuid;
};

// Core事件 - 对应 Rust CoreEvent
export type CoreEvent =
    | ({
          type: "StateUpdated";
      } & (
          | UserEvent
          | SystemEvent
          | TaskEvent
          | NetworkEvent
          | SettingsEvent
          | DeviceEvent
          | ComputedEvent
          | AllEvent
      ))
    | {
          type: "LogMessage";
          level: LogLevel;
          message: string;
          module: string;
          timestamp: string;
          trace_id?: Uuid;
      }
    | {
          type: "Error";
          module: string;
          message: string;
          trace_id: Uuid;
      };

// Core命令 - 对应 Rust CoreCommand
export type CoreCommand =
    | { type: "TriggerSystemCheck"; trace_id: Uuid }
    | { type: "Login"; username: string; password: string; trace_id: Uuid }
    | { type: "LoginWithCode"; phone: string; code: string; trace_id: Uuid }
    | { type: "SendVerificationCode"; phone: string; trace_id: Uuid }
    | { type: "Logout"; trace_id: Uuid }
    | { type: "RefreshWalletBalance"; trace_id: Uuid }
    | { type: "OpenExternalPage"; page_type: ExternalPageType; trace_id: Uuid }
    | { type: "ToggleTaskAcceptance"; trace_id: Uuid }
    | { type: "RefreshNetworkStatus"; trace_id: Uuid }
    | { type: "UpdateSettings"; settings: UserSettingsState; trace_id: Uuid }
    | { type: "SetDataRootPath"; new_path: string; trace_id: Uuid }
    | { type: "GetCurrentState"; trace_id: Uuid }
    | { type: "UpdateWindowsFocus"; focused: boolean; trace_id: Uuid }
    | { type: "PushLog"; entry: StructuredLogEntry }
    | { type: "PushBatchLogs"; entries: StructuredLogEntry[] };

// 命令响应 - 对应 Rust CommandResponse
export type CommandResponse =
    | { type: "Success" }
    | { type: "Error"; message: string };

// =================== 事件处理类型 ===================

// 事件监听器回调类型
export interface EventCallbacks {
    onStateUpdated?: (
        event: Extract<CoreEvent, { type: "StateUpdated" }>,
    ) => void;
    onLogMessage?: (event: Extract<CoreEvent, { type: "LogMessage" }>) => void;
    onError?: (event: Extract<CoreEvent, { type: "Error" }>) => void;
}

// 订阅配置
export interface SubscriptionConfig {
    eventName: string;
    traceId: Uuid;
    callback: (event: CoreEvent) => void;
    bufferSize?: number;
}

export interface FolderPickerResult {
    path: string;
    is_readable: boolean;
    is_writable: boolean;
}

// =================== 实用工具类型 ===================

// 状态更新回调
export type StateUpdateCallback<T> = (newState: T, oldState: T) => void;

// 状态验证器
export type StateValidator<T> = (state: T) => boolean;

// 状态转换器
export type StateTransformer<TFrom, TTo> = (from: TFrom) => TTo;
