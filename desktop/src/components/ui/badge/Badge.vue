<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { Primitive, type PrimitiveProps } from 'reka-ui'
import { clsx, clsv, VariantProps } from '~/lib/utils'

const badgeVariants = clsv('ui-badge', {
  variants: {
    variant: {
      default: 'variant-default',
      secondary: 'variant-secondary',
      destructive: 'variant-destructive',
      outline: 'variant-outline',
      success: 'variant-success',
      warning: 'variant-warning',
    },
    size: {
      default: 'size-default',
      sm: 'size-sm',
      lg: 'size-lg',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export type BadgeVariants = VariantProps<typeof badgeVariants>

export interface Props extends PrimitiveProps {
  variant?: BadgeVariants['variant']
  size?: BadgeVariants['size']
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  as: 'span',
})
</script>

<template>
  <Primitive 
    data-slot="badge" 
    :as="as" 
    :as-child="asChild"
    :class="clsx(badgeVariants({ variant, size }), props.class)"
  >
    <slot />
  </Primitive>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-badge) {
    & {
      @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    }

    /* Variants */
    &.variant-default {
      @apply border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
    }

    &.variant-secondary {
      @apply border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
    }

    &.variant-destructive {
      @apply border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
    }

    &.variant-outline {
      @apply text-foreground;
    }

    &.variant-success {
      @apply border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100 dark:hover:bg-green-800;
    }

    &.variant-warning {
      @apply border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-100 dark:hover:bg-yellow-800;
    }

    /* Sizes */
    &.size-default {
      @apply px-2.5 py-0.5 text-xs h-5 leading-5;
    }

    &.size-sm {
      @apply px-1.5 py-0 text-xs h-4 leading-4;
    }

    &.size-lg {
      @apply px-3 py-1 text-sm;
    }
  }
}
</style>