<script setup lang="ts">
import { computed, ref } from 'vue';
import Button, { type Props as ButtonProps } from './button.vue';
import { Loader2Icon } from 'lucide-vue-next';

const props = defineProps<
    ButtonProps & {
        onClick: (event: MouseEvent) => Promise<unknown>;
        icon?: boolean
        disabled?: boolean;
    }
>();

const localDisabledRef = ref(false);

const handleClick = async (event: MouseEvent) => {
    try {
        localDisabledRef.value = true;
        await props.onClick(event);
    } catch (e) {
        throw e
    } finally {
        localDisabledRef.value = false;
    }
};

const delegatedProps = computed(() => {
    const { onClick, disabled, ...delegated } = props;

    return delegated;
});
const disabled = computed(() => props.disabled || localDisabledRef.value);
</script>
<template>
    <Button v-bind="delegatedProps" @click="handleClick" :disabled="disabled">
        <Loader2Icon v-if="disabled && props.icon !== false" class="mr-1 h-4 w-4 animate-spin" />
        <slot />
    </Button>
</template>
