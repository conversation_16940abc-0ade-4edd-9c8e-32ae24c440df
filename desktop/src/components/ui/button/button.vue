<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { Primitive, type PrimitiveProps } from 'reka-ui'
import { clsx, clsv, VariantProps } from '~/lib/utils'

const buttonVariants = clsv('ui-button', {
  variants: {
    variant: {
      default: 'variant-default',
      destructive: 'variant-destructive',
      outline: 'variant-outline',
      secondary: 'variant-secondary',
      ghost: 'variant-ghost',
      link: 'variant-link',
    },
    size: {
      default: 'size-default',
      sm: 'size-sm',
      lg: 'size-lg',
      icon: 'size-icon',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export type ButtonVariants = VariantProps<typeof buttonVariants>

export interface Props extends PrimitiveProps {
  variant?: ButtonVariants['variant']
  size?: ButtonVariants['size']
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<Props>(), {
  as: 'button',
})
</script>

<template>
  <Primitive data-slot="button" :as="as" :as-child="asChild"
    :class="clsx(buttonVariants({ variant, size }), props.class)">
    <slot />
  </Primitive>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-button) {
    & {
      @apply inline-flex items-center justify-center leading-1 gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*=\'size-\'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive;
    }

    /* Variants */
    &.variant-default {
      @apply bg-primary text-primary-foreground shadow-xs hover:bg-primary/90;
    }

    &.variant-destructive {
      @apply bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40;
    }

    &.variant-outline {
      @apply border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground;
    }

    &.variant-secondary {
      @apply bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80;
    }

    &.variant-ghost {
      @apply hover:bg-accent hover:text-accent-foreground;
    }

    &.variant-link {
      @apply text-primary underline-offset-4 hover:underline;
    }

    /* Sizes */

    &.size-default {
      @apply h-9 px-4 py-2 has-[>svg]:px-3;
    }

    &.size-sm {
      @apply h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5;
    }

    &.size-lg {
      @apply h-10 rounded-md px-6 has-[>svg]:px-4;
    }

    &.size-icon {
      @apply size-9;
    }
  }
}
</style>