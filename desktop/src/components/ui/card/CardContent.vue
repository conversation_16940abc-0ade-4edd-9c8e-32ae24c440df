<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div data-slot="card-content" :class="clsx('ui-card-content', props.class)">
    <slot />
  </div>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card-content) {
    @apply px-6;
  }
}
</style>