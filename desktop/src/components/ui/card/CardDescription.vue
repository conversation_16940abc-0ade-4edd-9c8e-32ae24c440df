<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <p data-slot="card-description" :class="clsx('ui-card-description', props.class)">
    <slot />
  </p>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card-description) {
    @apply text-muted-foreground text-sm;
  }
}
</style>