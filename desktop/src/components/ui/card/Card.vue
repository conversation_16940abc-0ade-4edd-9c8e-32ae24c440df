<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div data-slot="card" :class="clsx(
    'ui-card',
    props.class,
  )
    ">
    <slot />
  </div>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card) {
    @apply bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm;
  }
}
</style>