<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div data-slot="card-footer" :class="clsx('ui-card-footer', props.class)">
    <slot />
  </div>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card-footer) {
    @apply flex items-center px-6 [.border-t]:pt-6;
  }
}
</style>