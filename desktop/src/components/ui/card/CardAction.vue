<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div data-slot="card-action" :class="clsx('ui-card-action', props.class)">
    <slot />
  </div>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card-action) {
    @apply col-start-2 row-span-2 row-start-1 self-start justify-self-end;
  }
}
</style>