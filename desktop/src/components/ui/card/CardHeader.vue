<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div data-slot="card-header" :class="clsx('@container/card-header ui-card-header', props.class)">
    <slot />
  </div>
</template>

<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card-header) {
    @apply grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6;
  }
}
</style>