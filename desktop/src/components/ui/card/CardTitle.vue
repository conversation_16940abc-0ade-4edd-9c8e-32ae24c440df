<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <h3 data-slot="card-title" :class="clsx('ui-card-title', props.class)">
    <slot />
  </h3>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-card-title) {
    @apply leading-none font-semibold;
  }
}
</style>