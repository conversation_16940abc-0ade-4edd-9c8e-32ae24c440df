<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { Label, type LabelProps } from 'reka-ui'
import { clsx } from '~/lib/utils'

const props = defineProps<LabelProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')
</script>

<template>
  <Label data-slot="label" v-bind="delegatedProps" :class="clsx(
    'ui-label',
    props.class,
  )
    ">
    <slot />
  </Label>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-label) {
    @apply flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50;
  }
}
</style>