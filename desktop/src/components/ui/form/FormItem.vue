<script lang="ts" setup>
import { useId } from 'reka-ui'
import { type HTMLAttributes, provide } from 'vue'
import { clsx } from '~/lib/utils'
import { FORM_ITEM_INJECTION_KEY } from './injectionKeys'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const id = useId()
provide(FORM_ITEM_INJECTION_KEY, id)
</script>

<template>
  <div data-slot="form-item" :class="clsx('ui-form-item', props.class)">
    <slot />
  </div>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-form-item) {
    @apply grid gap-2;
  }
}
</style>