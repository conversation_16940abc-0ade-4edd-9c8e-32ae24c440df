<script lang="ts" setup>
import type { HTMLAttributes } from 'vue'
import { clsx } from '~/lib/utils'
import { useFormField } from './useFormField'

const props = defineProps<{
  class?: HTMLAttributes['class']
}>()

const { formDescriptionId } = useFormField()
</script>

<template>
  <p
    :id="formDescriptionId"
    data-slot="form-description"
    :class="clsx('text-muted-foreground text-sm', props.class)"
  >
    <slot />
  </p>
</template>
