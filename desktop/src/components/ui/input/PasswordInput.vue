<script setup lang="ts">
import { ref, useAttrs, type HTMLAttributes } from 'vue';
import { clsx } from '~/lib/utils';
import { EyeClosedIcon, EyeIcon } from 'lucide-vue-next';

const props = defineProps<{
    class?: HTMLAttributes['class'];
}>();

const model = defineModel<string | number>();
const maskRef = ref(true);

const attrs = useAttrs()
</script>

<template>
    <div class="ui-password">
        <input v-model="model" data-slot="input" :type="maskRef === true ? 'password' : 'text'"
            :class="clsx('ui-password-input', props.class)" v-bind="attrs" />
        <button type="button" v-if="model" @click="maskRef = !maskRef">
            <EyeClosedIcon v-if="maskRef === false" class="ui-password-icon" />
            <EyeIcon v-if="maskRef === true" class="ui-password-icon" />
        </button>
    </div>
</template>
<style>
@reference "~/views/global.css";

@layer components {
    :where(.ui-password) {
        @apply relative inline-flex w-full;

        .ui-password-input {
            @apply file:text-foreground placeholder:text-secondary selection:bg-primary selection:text-primary-foreground border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive;

            &::-ms-reveal,
            &::-ms-clear {
                display: none;
            }
        }

        .ui-password-icon {
            @apply absolute right-2 h-5 w-5 cursor-pointer stroke-gray-300 select-none;
            top: 50%;
            transform: translateY(-50%);
        }
    }
}
</style>
