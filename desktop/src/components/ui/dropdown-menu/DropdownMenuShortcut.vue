<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { clsx } from "~/lib/utils";

const props = defineProps<{
    class?: HTMLAttributes["class"];
}>();
</script>

<template>
    <span
        data-slot="dropdown-menu-shortcut"
        :class="clsx('ui-dropdown-menu-shortcut', props.class)"
    >
        <slot />
    </span>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
    :where(.ui-dropdown-menu-shortcut) {
        @apply text-muted-foreground ml-auto text-xs tracking-widest;
    }
}
</style>
