<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { reactiveOmit } from "@vueuse/core";
import {
    DropdownMenuLabel,
    type DropdownMenuLabelProps,
    useForwardProps,
} from "reka-ui";
import { clsx } from "~/lib/utils";

const props = defineProps<
    DropdownMenuLabelProps & {
        class?: HTMLAttributes["class"];
        inset?: boolean;
    }
>();

const delegatedProps = reactiveOmit(props, "class", "inset");
const forwardedProps = useForwardProps(delegatedProps);
</script>

<template>
    <DropdownMenuLabel
        data-slot="dropdown-menu-label"
        :data-inset="inset ? '' : undefined"
        v-bind="forwardedProps"
        :class="clsx('ui-dropdown-menu-label', props.class)"
    >
        <slot />
    </DropdownMenuLabel>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
    :where(.ui-dropdown-menu-label) {
        @apply px-2 py-1.5 text-sm font-medium data-[inset]:pl-8;
    }
}
</style>
