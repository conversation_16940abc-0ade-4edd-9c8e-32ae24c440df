<script setup lang="ts">
import type { HTMLAttributes } from "vue";
import { reactiveOmit } from "@vueuse/core";
import {
    DropdownMenuSeparator,
    type DropdownMenuSeparatorProps,
} from "reka-ui";
import { clsx } from "~/lib/utils";

const props = defineProps<
    DropdownMenuSeparatorProps & {
        class?: HTMLAttributes["class"];
    }
>();

const delegatedProps = reactiveOmit(props, "class");
</script>

<template>
    <DropdownMenuSeparator
        data-slot="dropdown-menu-separator"
        v-bind="delegatedProps"
        :class="clsx('ui-dropdown-menu-separator', props.class)"
    />
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
    :where(.ui-dropdown-menu-separator) {
        @apply bg-border -mx-1 my-1 h-px;
    }
}
</style>
