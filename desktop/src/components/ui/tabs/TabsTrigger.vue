<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { TabsTrigger, type TabsTriggerProps, useForwardProps } from 'reka-ui'
import { clsx } from '~/lib/utils'

const props = defineProps<TabsTriggerProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')

const forwardedProps = useForwardProps(delegatedProps)
</script>

<template>
  <TabsTrigger data-slot="tabs-trigger" v-bind="forwardedProps" :class="clsx(
    `ui-tabs-trigger`,
    props.class,
  )">
    <slot />
  </TabsTrigger>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-tabs-trigger) {
    @apply text-white hover:text-primary data-[state=active]:text-primary inline-flex h-[calc(100%-1px)] flex-1 items-center gap-1.5 rounded-none border-b-2 border-transparent data-[state=active]:border-primary py-2 font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 cursor-pointer;
  }
}
</style>