<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { TabsContent, type TabsContentProps } from 'reka-ui'
import { clsx } from '~/lib/utils'

const props = defineProps<TabsContentProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')
</script>

<template>
  <TabsContent data-slot="tabs-content" :class="clsx('flex-1 outline-none', props.class)" v-bind="delegatedProps">
    <slot />
  </TabsContent>
</template>
