<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { TabsList, type TabsListProps } from 'reka-ui'
import { clsx } from '~/lib/utils'

const props = defineProps<TabsListProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')
</script>

<template>
  <TabsList data-slot="tabs-list" v-bind="delegatedProps" :class="clsx(
    'ui-tabs-list',
    props.class,
  )">
    <slot />
  </TabsList>
</template>
<style lang="css">
@reference "~/views/global.css";

@layer components {
  :where(.ui-tabs-list) {
    @apply inline-flex w-fit items-center justify-center space-x-6;
  }
}
</style>
