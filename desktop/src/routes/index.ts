import { createRouter, createWebHistory } from 'vue-router';
import type { Component } from 'vue';

const PAGE_MAP = import.meta.glob([
    '../views/**/*.vue',
    '!../views/app.vue',
    '!../views/**/_*/*.vue',
]) as {
    [P: string]: () => Promise<{
        default: Component;
        [M: string]: unknown;
    }>;
};
const routeMap = new Map<
    string,
    {
        relationPath: string;
        urlPath: string;
        component: Component;
    }
>();

for (const relationPath of Object.keys(PAGE_MAP)) {
    // 忽略下划线开头的目录
    if (/\/_[\w/-]+/gm.test(relationPath)) continue;
    const urlPath = relationPath
        .replace(/^[.]+\/views\//, '')
        .replace(/\/index\.vue$/, '')
        .replace(/\.vue$/, '')
        .replace('_', '-')
        .replace(/([\w-]+)\/([\w-]+)$/gm, (_, v1: string, v2: string) => {
            return v1.replace('-', '').toLowerCase() === v2.toLowerCase()
                ? `${v1}`
                : _;
        });
    routeMap.set(relationPath, {
        relationPath,
        urlPath: urlPath == 'launch' ? '/' : `/${urlPath}`,
        component: PAGE_MAP[relationPath],
    });
}
export const router = createRouter({
    history: createWebHistory(),
    routes: [
        ...Array.from(routeMap.values()).map((it) => ({
            path: it.urlPath,
            component: it.component,
        })),
        {
            path: '/:pathMatch(.*)',
            component: () => import('./not-found.vue'),
        },
    ],
});
