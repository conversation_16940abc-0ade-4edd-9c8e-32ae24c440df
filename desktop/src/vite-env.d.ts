/// <reference types="vite/client" />

declare module "*.vue" {
  import type { DefineComponent } from "vue";
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-empty-object-type
  const component: DefineComponent<{}, {}, any>;
  export default component;
}
interface ImportMeta {
  readonly vitest?: typeof import('vitest')
}
declare const __ENDPOINT__: string;
declare const __VERSION__: string;
declare const __BUILD_TIMESTAMP__: number;