{"name": "echowave-client", "private": true, "version": "0.6.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "prettier": "prettier --write **/*.{js,jsx,tsx,ts,less,md,json}", "test": "vitest"}, "dependencies": {"@tauri-apps/api": "2", "@tauri-apps/plugin-opener": "2", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "dayjs": "1.11.13", "lucide-vue-next": "0.515.0", "pinia": "^3.0.3", "reka-ui": "^2.3.2", "tailwindcss": "4.1.10", "tw-animate-css": "1.3.4", "vee-validate": "^4.15.1", "vue": "3.5.13", "vue-router": "4.5.1", "vue-sonner": "^2.0.0", "zod": "3.25.64"}, "devDependencies": {"@eslint/js": "9.23.0", "@tailwindcss/vite": "4.1.10", "@tauri-apps/cli": "2", "@types/node": "24.0.1", "@typescript-eslint/eslint-plugin": "8.28.0", "@typescript-eslint/parser": "8.28.0", "@typescript/lib-dom": "npm:@types/web@0.0.153", "@vitejs/plugin-vue": "5.2.1", "eslint": "9.23.0", "eslint-plugin-vue": "10.2.0", "globals": "16.0.0", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.12", "typescript": "~5.6.2", "typescript-eslint": "8.28.0", "vite": "6.0.3", "vite-svg-loader": "5.1.0", "vitest": "3.2.3", "vue-tsc": "2.1.10"}}