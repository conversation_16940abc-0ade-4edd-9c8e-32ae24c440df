use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::Arc;
use std::time::Duration;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Wry};
use tokio::time::{interval, sleep};
use tracing::{error, info, warn};
use user_idle::UserIdle;

/// 空闲状态监听器
pub struct IdleWatcher {
    app_handle: AppHandle<Wry>,
    is_stopped: Arc<AtomicBool>,
}

impl IdleWatcher {
    /// 创建新的空闲监听器
    pub fn new(app_handle: AppHandle<Wry>) -> Self {
        Self {
            app_handle,
            is_stopped: Arc::new(AtomicBool::new(false)),
        }
    }

    /// 启动空闲监听，在启动30秒内检测用户活动
    pub async fn start_initial_idle_check(&self) {
        let app_handle = self.app_handle.clone();
        let is_stopped = self.is_stopped.clone();
        
        info!("开始启动30秒内空闲状态检测");
        
        // 30秒定时器
        tokio::spawn(async move {
            // 等待30秒
            sleep(Duration::from_secs(30)).await;
            
            if is_stopped.load(Ordering::Relaxed) {
                info!("空闲检测已被停止");
                return;
            }
            
            // 检查用户是否在过去30秒内有活动
            match Self::check_user_idle_state() {
                Ok(is_idle) => {
                    if is_idle {
                        info!("检测到用户在启动30秒内处于空闲状态，自动隐藏窗口");
                        Self::hide_main_window(&app_handle).await;
                    } else {
                        info!("检测到用户在启动30秒内有活动，保持窗口显示");
                    }
                }
                Err(e) => {
                    error!("检查用户空闲状态失败: {}", e);
                }
            }
        });
    }

    /// 启动持续的空闲监听（可选，用于后续扩展）
    pub async fn start_continuous_idle_watch(&self) {
        let app_handle = self.app_handle.clone();
        let is_stopped = self.is_stopped.clone();
        
        info!("开始持续空闲状态监听");
        
        tokio::spawn(async move {
            let mut check_interval = interval(Duration::from_secs(5));
            let mut idle_duration = 0u64;
            let idle_threshold = 30; // 30秒空闲阈值
            
            loop {
                check_interval.tick().await;
                
                if is_stopped.load(Ordering::Relaxed) {
                    info!("持续空闲监听已停止");
                    break;
                }
                
                match Self::check_user_idle_state() {
                    Ok(is_idle) => {
                        if is_idle {
                            idle_duration += 5;
                            if idle_duration >= idle_threshold {
                                info!("检测到用户空闲超过{}秒，自动隐藏窗口", idle_threshold);
                                Self::hide_main_window(&app_handle).await;
                                // 重置计数器，避免重复隐藏
                                idle_duration = 0;
                            }
                        } else {
                            idle_duration = 0;
                        }
                    }
                    Err(e) => {
                        warn!("检查用户空闲状态失败: {}", e);
                    }
                }
            }
        });
    }

    /// 检查用户是否处于空闲状态（空闲时间 >= 30秒）
    fn check_user_idle_state() -> Result<bool, String> {
        match UserIdle::get_time() {
            Ok(idle_time) => {
                let idle_seconds = idle_time.as_seconds();
                info!("当前用户空闲时间: {}秒", idle_seconds);
                
                // 如果空闲时间 >= 30秒，认为是空闲状态
                Ok(idle_seconds >= 30)
            }
            Err(e) => {
                Err(format!("获取用户空闲时间失败: {:?}", e))
            }
        }
    }

    /// 隐藏主窗口
    async fn hide_main_window(app_handle: &AppHandle<Wry>) {
        if let Some(window) = app_handle.get_webview_window("main") {
            match window.hide() {
                Ok(_) => {
                    info!("成功隐藏主窗口");
                }
                Err(e) => {
                    error!("隐藏主窗口失败: {}", e);
                }
            }
        } else {
            warn!("无法获取主窗口引用");
        }
    }

    /// 停止空闲监听
    pub fn stop(&self) {
        info!("停止空闲状态监听");
        self.is_stopped.store(true, Ordering::Relaxed);
    }

    /// 注册用户活动监听器（当窗口获得焦点时停止空闲监听）
    pub fn register_activity_listeners(&self) {
        info!("注册用户活动监听器");
        // TODO: 在后续版本中实现窗口焦点监听
        // 当前版本简化处理，只在有用户交互时自动停止监听
    }
}

/// 为 App 提供便捷的空闲监听功能
pub async fn setup_idle_watcher(app_handle: AppHandle<Wry>) -> Result<(), String> {
    info!("设置空闲状态监听器");
    
    let idle_watcher = IdleWatcher::new(app_handle);
    
    // 注册活动监听器
    idle_watcher.register_activity_listeners();
    
    // 启动初始的30秒空闲检测
    idle_watcher.start_initial_idle_check().await;
    
    // 可选：启动持续监听（根据需求决定是否开启）
    // idle_watcher.start_continuous_idle_watch().await;
    
    Ok(())
} 