use domain::context::command::CoreCommand;
use std::ops::Deref;
use std::sync::{Arc, Mutex};
use tauri::{App, Manager, Wry, ipc::Channel};
use tokio::sync::mpsc;

// 导入Core模块
use domain::config::{AppMode, ContextConfig};
use domain::context::{GlobalContext, create_context, event::CoreEvent, get_global_trace_id};
use uuid::Uuid;

pub fn get_app_mode() -> AppMode {
    let mode = option_env!("BUILD_MODE").unwrap_or_else(|| "");
    match mode {
        "dev" => AppMode::Development,
        "development" => AppMode::Development,
        "prod" => AppMode::Production,
        "production" => AppMode::Production,
        "test" => AppMode::Testing,
        "testing" => AppMode::Testing,
        _ => AppMode::Development,
    }
}

/// Tauri 事件订阅者管理器 - 适配层
pub struct EventSubscribers {
    channels: Arc<Mutex<Vec<(String, Channel<CoreEvent>)>>>,
    /// 在核心订阅管理器中的订阅者ID
    pub(crate) core_subscriber_id: u32,
}

impl EventSubscribers {
    pub fn new(core_subscriber_id: u32) -> Self {
        Self {
            channels: Arc::new(Mutex::new(Vec::new())),
            core_subscriber_id,
        }
    }

    /// 添加新的 Tauri Channel 订阅者
    #[tracing::instrument(skip(self, global_context, channel), fields(trace_id = %trace_id))]
    pub fn add_channel_subscriber(
        &self,
        event_name: String,
        channel: Channel<CoreEvent>,
        trace_id: Uuid,
        global_context: Arc<GlobalContext>,
    ) -> anyhow::Result<()> {
        let mut guard = self
            .channels
            .lock()
            .map_err(|err| anyhow::anyhow!("Failed to lock channels: {}", err))?;

        let id = channel.id();
        tracing::debug!(
            "New Tauri channel subscriber added: {} with id: {}",
            event_name,
            id
        );

        // 检查是否是第一个订阅该事件类型的 Channel
        let is_first_subscriber = !guard
            .iter()
            .any(|(existing_event, _)| existing_event == &event_name);

        guard.push((event_name.clone(), channel));
        drop(guard);

        // 如果是第一个订阅者，通知核心订阅管理器
        if is_first_subscriber {
            let global_context = global_context.clone();
            let event_name_clone = event_name.clone();

            match global_context.subscribe_event(self.core_subscriber_id, event_name, trace_id) {
                Ok(_) => {
                    tracing::info!(
                        "Successfully subscribed to event in core: {}",
                        event_name_clone
                    );
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to subscribe to event in core {}: {}",
                        event_name_clone,
                        e
                    );
                }
            }
        }

        Ok(())
    }

    /// 移除 Tauri Channel 订阅者
    #[tracing::instrument(skip(self, global_context), fields(trace_id = %trace_id))]
    pub fn remove_channel_subscriber(
        &self,
        event_name: String,
        channel_id: u32,
        trace_id: Uuid,
        global_context: Arc<GlobalContext>,
    ) -> anyhow::Result<()> {
        let mut guard = self
            .channels
            .lock()
            .map_err(|err| anyhow::anyhow!("Failed to lock channels: {}", err))?;

        guard.retain(|(_event_name, channel)| channel.id() != channel_id);
        tracing::debug!(
            "Tauri channel subscriber removed: {} with id: {}",
            event_name,
            channel_id
        );

        // 检查是否还有订阅该事件类型的 Channel
        let has_remaining_subscribers = guard
            .iter()
            .any(|(existing_event, _)| existing_event == &event_name);
        drop(guard);

        // 如果没有剩余订阅者，通知核心取消订阅
        if !has_remaining_subscribers {
            match global_context.unsubscribe_event(
                self.core_subscriber_id,
                event_name.clone(),
                trace_id,
            ) {
                Ok(_) => {
                    tracing::info!(
                        "Successfully unsubscribed from event in core: {}",
                        event_name
                    );
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to unsubscribe from event in core {}: {}",
                        event_name,
                        e
                    );
                }
            }
        }

        Ok(())
    }

    pub fn cleanup(&self, global_context: Arc<GlobalContext>) -> anyhow::Result<()> {
        let mut guard = self
            .channels
            .lock()
            .map_err(|err| anyhow::anyhow!("Failed to lock channels: {}", err))?;
        guard.clear();
        drop(guard);

        global_context
            .unregister_subscriber(self.core_subscriber_id, get_global_trace_id())
            .map_err(|err| anyhow::anyhow!("Failed to unregister subscriber: {}", err))?;

        Ok(())
    }

    /// 向所有订阅者广播事件
    pub fn broadcast_event(&self, event: CoreEvent) -> anyhow::Result<()> {
        let mut guard = self
            .channels
            .lock()
            .map_err(|err| anyhow::anyhow!("Failed to lock channels: {}", err))?;
        let mut cleanup_channels = Vec::new();

        for (event_name, channel) in guard.iter() {
            if event.event_name() != event_name {
                continue;
            }
            match channel.send(event.clone()) {
                Ok(_) => (),
                Err(_) => {
                    tracing::warn!(
                        "Failed to send event to Tauri channel subscriber: {} with id: {}",
                        event_name,
                        channel.id()
                    );
                    cleanup_channels.push(channel.id());
                }
            }
        }

        if !cleanup_channels.is_empty() {
            guard.retain(|(_event_name, channel)| !cleanup_channels.contains(&channel.id()));
            tracing::debug!(
                "Cleaned up {} failed Tauri channels",
                cleanup_channels.len()
            );
        }

        Ok(())
    }

    /// 获取当前订阅者数量
    pub fn subscriber_count(&self) -> usize {
        self.channels
            .lock()
            .map(|channels| channels.len())
            .unwrap_or(0)
    }
}

pub struct AppStateInner {
    version: &'static str,
    app_mode: AppMode,
    /// 核心模块的全局上下文
    pub global_context: Arc<GlobalContext>,
    /// Tauri 事件订阅者管理器（适配层）
    pub event_subscribers: Arc<EventSubscribers>,
}

pub struct AppState {
    inner: Arc<AppStateInner>,
}

impl Deref for AppState {
    type Target = AppStateInner;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}
impl Clone for AppState {
    fn clone(&self) -> Self {
        Self {
            inner: Arc::clone(&self.inner),
        }
    }
}
impl AppState {
    // 仅用于通知类命令，无法得到返回值
    pub fn send_command(&self, command: CoreCommand) -> () {
        let state = self.inner.clone();
        tokio::spawn(async move {
            if let Err(err) = state.global_context.execute_command(command).await {
                tracing::error!("Failed to execute command: {:?}", err);
            }
        });
    }
}

pub fn build_app_state(app: &mut App<Wry>) -> anyhow::Result<()> {
    // 创建事件通道用于Core到Desktop的通信
    let (event_tx, mut event_rx) = mpsc::channel::<CoreEvent>(300);
    // 创建GlobalContext
    let config = ContextConfig {
        app_mode: get_app_mode(),
        ..Default::default()
    };
    let global_context = create_context(event_tx, config)
        .map_err(|e| anyhow::anyhow!("Failed to create global context: {}", e))?;
    tracing::info!("全局上下文创建完成");

    // 在核心订阅管理器中注册 Tauri 适配器作为订阅者
    let core_subscriber_id = global_context
        .register_subscriber("tauri", get_global_trace_id())
        .map_err(|err| {
            anyhow::anyhow!(
                "Unexpected response when registering Tauri subscriber, reason: {err:?}"
            )
        })?;
    tracing::info!(
        "Tauri adapter registered with core subscription manager: {}",
        core_subscriber_id
    );

    // 创建事件订阅者管理器
    let event_subscribers = EventSubscribers::new(core_subscriber_id);

    let state = AppStateInner {
        version: env!("CARGO_PKG_VERSION"),
        app_mode: get_app_mode(),
        global_context: Arc::new(global_context),
        event_subscribers: Arc::new(event_subscribers),
    };
    // 设置窗口标题
    if state.app_mode != AppMode::Production {
        let main_window = app
            .get_webview_window("main")
            .expect("Failed to get main window");
        let original_title = main_window.title().unwrap();
        let new_title = match state.app_mode {
            AppMode::Development => format!("{} (开发版)", original_title),
            AppMode::Testing => format!("{} (测试版)", original_title),
            AppMode::Production => original_title,
        };
        main_window.set_title(&new_title).unwrap();
    }

    // 启动事件监听器来处理Core模块的事件
    let event_subscribers = state.event_subscribers.clone();
    tokio::spawn(async move {
        while let Some(event) = event_rx.recv().await {
            // 将Core事件转发到前端 - 使用新的Channel机制
            if let Err(e) = event_subscribers.broadcast_event(event) {
                eprintln!("Failed to forward core event: {}", e);
            }
        }
    });

    tracing::info!(
        trace_id = %get_global_trace_id(),
        "EchoWave Desktop application, version: {desktop_version}, app_mode: {app_mode}, core_version: {core_version}",
        desktop_version = state.version,
        app_mode = state.app_mode.as_str(),
        core_version = state.global_context.config.version
    );
    app.manage(AppState {
        inner: Arc::new(state),
    });
    Ok(())
}
