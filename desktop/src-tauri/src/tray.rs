use tauri::tray::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TrayIconEvent};
use tauri::{
    <PERSON><PERSON>, Manager, Wry,
    menu::{MenuBuilder, MenuItem},
};

pub fn create_tray(app: &mut App<Wry>) -> anyhow::Result<()> {
    let show = MenuItem::with_id(app, "show", "显示窗口", true, None::<&str>)?;
    let hide = MenuItem::with_id(app, "hide", "隐藏窗口", true, None::<&str>)?;
    let check_update = MenuItem::with_id(app, "check-update", "检查更新", true, None::<&str>)?;
    let quit = MenuItem::with_id(app, "quit", "退出", true, None::<&str>)?;
    let menu = MenuBuilder::new(app)
        .item(&show)
        .item(&hide)
        .item(&check_update)
        .separator()
        .item(&quit)
        .build()?;
    let _tray = TrayIconBuilder::new()
        .menu(&menu)
        .tooltip("EchoWave")
        .icon(app.default_window_icon().unwrap().clone())
        .on_menu_event(|app, event| match event.id.as_ref() {
            "quit" => {
                println!("Quit");
                app.exit(0)
            }
            "show" => {
                if let Some(window) = app.get_webview_window("main") {
                    match window.is_visible() {
                        Ok(true) => {
                            // 窗口已可见，检查是否被最小化
                            if window.is_minimized().unwrap_or(false) {
                                if let Err(e) = window.unminimize() {
                                    tracing::warn!("恢复窗口失败: {}", e);
                                }
                            }
                            if let Err(e) = window.set_focus() {
                                tracing::warn!("设置窗口焦点失败: {}", e);
                            }
                        }
                        Ok(false) => {
                            // 窗口隐藏，需要显示
                            if let Err(e) = window.show() {
                                tracing::warn!("显示窗口失败: {}", e);
                            } else if let Err(e) = window.set_focus() {
                                tracing::warn!("设置窗口焦点失败: {}", e);
                            }
                        }
                        Err(e) => {
                            tracing::error!("检查窗口可见性失败: {}", e);
                            // 尝试显示窗口
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                    tracing::info!("通过托盘显示窗口");
                } else {
                    tracing::error!("无法获取主窗口");
                }
            }
            "hide" => {
                if let Some(window) = app.get_webview_window("main") {
                    if let Ok(false) = window.is_visible() {
                        let _ = window.hide();
                    }
                } else {
                    tracing::error!("无法获取主窗口")
                }
            }

            "check-update" => {
                println!("CheckUpdate");
            }
            _ => {
                println!("menu item {:?} not handled", event.id);
            }
        })
        .on_tray_icon_event(|tray, event| match event {
            TrayIconEvent::Click {
                button: MouseButton::Left,
                ..
            } => {
                let app = tray.app_handle();
                if let Some(window) = app.get_webview_window("main") {
                    match window.is_visible() {
                        Ok(true) => {
                            // 窗口已可见，检查是否被最小化
                            if window.is_minimized().unwrap_or(false) {
                                if let Err(e) = window.unminimize() {
                                    tracing::warn!("恢复窗口失败: {}", e);
                                }
                            }
                            if let Err(e) = window.set_focus() {
                                tracing::warn!("设置窗口焦点失败: {}", e);
                            }
                        }
                        Ok(false) => {
                            // 窗口隐藏，需要显示
                            if let Err(e) = window.show() {
                                tracing::warn!("显示窗口失败: {}", e);
                            } else if let Err(e) = window.set_focus() {
                                tracing::warn!("设置窗口焦点失败: {}", e);
                            }
                        }
                        Err(e) => {
                            tracing::error!("检查窗口可见性失败: {}", e);
                            // 尝试显示窗口
                            let _ = window.show();
                            let _ = window.set_focus();
                        }
                    }
                    tracing::info!("通过托盘点击显示窗口");
                } else {
                    tracing::error!("无法获取主窗口");
                }
            }
            _ => {}
        })
        .build(app)?;
    Ok(())
}
