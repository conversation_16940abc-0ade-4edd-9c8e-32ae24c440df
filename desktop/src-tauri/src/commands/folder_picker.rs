use serde::{Deserialize, Serialize};
use std::path::Path;

#[cfg(not(target_os = "macos"))]
use tauri_plugin_dialog::DialogExt;

#[derive(Debug, Serialize, Deserialize)]
pub struct FolderPickerResult {
    pub path: String,
    pub is_readable: bool,
    pub is_writable: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FolderPickerError {
    pub code: String,
    pub message: String,
}

// 敏感路径列表（Windows）
#[cfg(target_os = "windows")]
const WINDOWS_SENSITIVE_PATHS: &[&str] = &[
    "C:\\Windows",
    "C:\\Program Files",
    "C:\\Program Files (x86)",
    "C:\\System32",
    "C:\\$Recycle.Bin",
    "C:\\Users\\<USER>\\Users\\Default",
    "C:\\PerfLogs",
    "C:\\Recovery",
];

// 敏感路径列表（Unix-like）
#[cfg(not(target_os = "windows"))]
const UNIX_SENSITIVE_PATHS: &[&str] = &[
    "/bin",
    "/sbin",
    "/usr/bin",
    "/usr/sbin",
    "/boot",
    "/dev",
    "/proc",
    "/sys",
    "/root",
    "/etc",
    "/var/log",
    "/usr/local/bin",
];

/// 检查路径是否为敏感路径
fn is_sensitive_path(path: &Path) -> bool {
    let path_str = path.to_string_lossy().to_lowercase();

    #[cfg(target_os = "windows")]
    {
        WINDOWS_SENSITIVE_PATHS.iter().any(|&sensitive| {
            let sensitive_lower = sensitive.to_lowercase();
            path_str.starts_with(&sensitive_lower) || path_str == sensitive_lower
        })
    }

    #[cfg(not(target_os = "windows"))]
    {
        UNIX_SENSITIVE_PATHS
            .iter()
            .any(|&sensitive| path_str.starts_with(sensitive) || path_str == sensitive)
    }
}

/// 检查文件夹的读写权限
fn check_folder_permissions(path: &Path) -> Result<(bool, bool), std::io::Error> {
    use std::fs;
    use uuid::Uuid;

    // 检查读权限
    let is_readable = match fs::read_dir(path) {
        Ok(_) => true,
        Err(e) => {
            tracing::warn!("无法读取目录 {}: {}", path.display(), e);
            false
        }
    };

    // 检查写权限 - 尝试创建临时文件
    let is_writable = {
        let test_file_name = format!(".echowave_write_test_{}", Uuid::new_v4());
        let test_file = path.join(&test_file_name);
        match fs::File::create(&test_file) {
            Ok(_) => {
                // 立即删除测试文件
                match fs::remove_file(&test_file) {
                    Ok(_) => tracing::debug!("成功删除测试文件"),
                    Err(e) => tracing::warn!("删除测试文件失败: {}", e),
                }
                true
            }
            Err(e) => {
                tracing::warn!("无法在目录 {} 中创建文件: {}", path.display(), e);
                false
            }
        }
    };

    Ok((is_readable, is_writable))
}

/// macOS 特定的原生文件夹选择器 - 直接使用 rfd 库
#[cfg(target_os = "macos")]
async fn pick_folder_native_macos(title: String) -> Result<FolderPickerResult, FolderPickerError> {
    tracing::info!("使用 macOS 原生文件夹选择器");

    let folder_dialog = rfd::FileDialog::new()
        .set_title(&title)
        .set_can_create_directories(true);

    // 在主线程上执行以确保正确的 NSApplication 集成
    let selected_path = tokio::task::spawn_blocking(move || folder_dialog.pick_folder())
        .await
        .map_err(|e| FolderPickerError {
            code: "THREAD_ERROR".to_string(),
            message: format!("线程执行失败: {}", e),
        })?;

    match selected_path {
        Some(path_buf) => {
            let path_str = path_buf.to_string_lossy().to_string();
            tracing::info!("用户选择了文件夹: {}", path_str);

            // 检查是否为敏感路径
            if is_sensitive_path(&path_buf) {
                tracing::warn!("选择的路径是敏感目录: {}", path_str);
                return Err(FolderPickerError {
                    code: "SENSITIVE_PATH".to_string(),
                    message: "选择的路径是系统敏感目录，不允许访问。请选择其他文件夹。".to_string(),
                });
            }

            // 检查文件夹权限
            match check_folder_permissions(&path_buf) {
                Ok((is_readable, is_writable)) => {
                    if !is_readable {
                        tracing::error!("没有读取文件夹的权限: {}", path_str);
                        return Err(FolderPickerError {
                            code: "NO_READ_PERMISSION".to_string(),
                            message: "没有读取该文件夹的权限，请选择其他文件夹或检查权限设置。"
                                .to_string(),
                        });
                    }

                    Ok(FolderPickerResult {
                        path: path_str,
                        is_readable,
                        is_writable,
                    })
                }
                Err(e) => {
                    tracing::error!("权限检查失败: {}", e);
                    Err(FolderPickerError {
                        code: "PERMISSION_CHECK_FAILED".to_string(),
                        message: format!("权限检查失败: {}", e),
                    })
                }
            }
        }
        None => {
            tracing::info!("用户取消了文件夹选择");
            Err(FolderPickerError {
                code: "USER_CANCELLED".to_string(),
                message: "用户取消了文件夹选择".to_string(),
            })
        }
    }
}

/// 文件夹选择器命令
#[tauri::command]
pub async fn pick_folder(
    window: tauri::WebviewWindow,
    title: Option<String>,
) -> Result<FolderPickerResult, FolderPickerError> {
    let title = title.unwrap_or_else(|| "选择文件夹".to_string());

    // macOS 使用原生 rfd 实现以获得正确的模态行为
    #[cfg(target_os = "macos")]
    {
        let _windows = window;
        return pick_folder_native_macos(title).await;
    }

    // 其他平台使用 Tauri 插件
    #[cfg(not(target_os = "macos"))]
    {
        tracing::info!("开始文件夹选择操作");

        let dialog_builder = window.dialog().file().set_title(title);

        let file_path_option = dialog_builder.blocking_pick_folder();

        match file_path_option {
            Some(file_path) => {
                // 将 FilePath 转换为 PathBuf
                let path_buf = match file_path {
                    tauri_plugin_dialog::FilePath::Path(path) => path,
                    tauri_plugin_dialog::FilePath::Url(_url) => {
                        return Err(FolderPickerError {
                            code: "URL_NOT_SUPPORTED".to_string(),
                            message: "不支持 URL 类型的路径".to_string(),
                        });
                    }
                };

                tracing::info!("用户选择了文件夹: {}", path_buf.display());

                match validate_folder_path(&path_buf).await {
                    Ok(()) => {}
                    Err(e) => {
                        return Err(e);
                    }
                }

                // 检查读写权限
                match check_folder_permissions(&path_buf) {
                    Ok((is_readable, is_writable)) => {
                        if !is_readable {
                            tracing::error!("没有读取文件夹的权限: {}", path_buf.display());
                            return Err(FolderPickerError {
                                code: "NO_READ_PERMISSION".to_string(),
                                message: "没有读取该文件夹的权限，请选择其他文件夹或检查权限设置。"
                                    .to_string(),
                            });
                        }

                        tracing::info!(
                            "文件夹选择成功: {}, 可读: {}, 可写: {}",
                            path_buf.display(),
                            is_readable,
                            is_writable
                        );

                        Ok(FolderPickerResult {
                            path: path_buf.to_string_lossy().to_string(),
                            is_readable,
                            is_writable,
                        })
                    }
                    Err(e) => {
                        tracing::error!("权限检查失败: {}", e);
                        Err(FolderPickerError {
                            code: "PERMISSION_CHECK_FAILED".to_string(),
                            message: format!("权限检查失败: {}", e),
                        })
                    }
                }
            }
            None => {
                tracing::info!("用户取消了文件夹选择");
                Err(FolderPickerError {
                    code: "USER_CANCELLED".to_string(),
                    message: "用户取消了文件夹选择".to_string(),
                })
            }
        }
    }
}

/// 验证指定路径的文件夹权限（用于程序内部验证）
#[tauri::command]
pub async fn validate_folder_path(path: &Path) -> Result<(), FolderPickerError> {
    tracing::info!("验证文件夹路径: {}", path.display());

    // 检查文件夹是否存在
    if !path.exists() {
        tracing::error!("文件夹不存在: {}", path.display());
        return Err(FolderPickerError {
            code: "PATH_NOT_EXISTS".to_string(),
            message: "指定的文件夹不存在".to_string(),
        });
    }

    if !path.is_dir() {
        tracing::error!("路径不是文件夹: {}", path.display());
        return Err(FolderPickerError {
            code: "NOT_A_DIRECTORY".to_string(),
            message: "指定的路径不是一个文件夹".to_string(),
        });
    }

    // 检查是否为敏感路径
    if is_sensitive_path(path) {
        tracing::warn!("路径是敏感目录: {}", path.display());
        return Err(FolderPickerError {
            code: "SENSITIVE_PATH".to_string(),
            message: "该路径是系统敏感目录，不允许访问。".to_string(),
        });
    }
    Ok(())
}
