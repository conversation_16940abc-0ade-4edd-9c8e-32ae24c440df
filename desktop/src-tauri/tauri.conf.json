{"$schema": "https://schema.tauri.app/config/2", "productName": "EchoWave", "mainBinaryName": "EchoWave", "version": "0.6.0", "identifier": "com.echowave.client", "build": {"devUrl": "http://localhost:5173", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "EchoWave 客户端", "width": 360, "height": 585, "minWidth": 360, "minHeight": 585, "resizable": false, "maximizable": false, "minimizable": false, "alwaysOnTop": true, "skipTaskbar": true, "theme": "Light", "backgroundColor": [10, 33, 51]}], "security": {"csp": null}}, "bundle": {"active": true, "targets": ["nsis"], "publisher": "成都回声涌现智能科技有限公司", "copyright": "版权描述", "category": "Business", "shortDescription": "一个短的描述文本", "longDescription": "一个长的描述文本", "windows": {"nsis": {"displayLanguageSelector": true, "languages": ["SimpChinese"], "installMode": "perMachine", "installerHooks": "./installer.nsh", "installerIcon": "./icons/icon.ico"}}, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}