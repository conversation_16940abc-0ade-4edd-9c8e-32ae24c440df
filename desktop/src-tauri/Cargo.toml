[package]
name = "echowave-client"
version = "0.6.0"
description = "回声涌现客户端"
authors = ["成都回声涌现智能科技有限公司"]
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "desktop"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri-plugin-opener = "2.4.0"
tauri-plugin-dialog = "2.0"
serde_json = "1"
serde = { version = "1.0", features = ["derive"] }
anyhow = "1.0.98"
tokio = { version = "1.45.1", features = ["full"] }
domain = { path = "../../crates/domain", package = "domain"}
uuid = { version = "1.0", features = ["v4", "serde"] }
tracing = {workspace = true}
tauri = { version = "2.6.2", features = ["tray-icon"] }
dirs = "5.0"
user-idle = "0.6.0"

[target.'cfg(any(target_os = "macos", windows, target_os = "linux"))'.dependencies]
tauri-plugin-single-instance = { version = "2.0.0", features = [] }

[target.'cfg(target_os="linux")'.dependencies]
libc = "0.2.172"

[target.'cfg(target_os="macos")'.dependencies]
libc = "0.2.172"
rfd = "0.15"
