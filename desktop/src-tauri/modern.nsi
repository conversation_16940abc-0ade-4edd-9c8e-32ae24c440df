; Modern NSIS Installer Script - English Only
; Uses Modern UI 2 for a contemporary look

!include "MUI2.nsh"
!include "LogicLib.nsh"
!include "WordFunc.nsh"

!define APPNAME "EchoWave"
!define COMPANYNAME "EchoWave"
!define DESCRIPTION "Modern Installer"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

; Basic settings
Name "${APPNAME}"
InstallDir "$PROGRAMFILES\${COMPANYNAME}\${APPNAME}"
RequestExecutionLevel admin
OutFile "modern_v4.exe"

; Modern UI Configuration
!define MUI_ABORTWARNING
!define MUI_ICON "${NSISDIR}\Contrib\Graphics\Icons\modern-install.ico"
!define MUI_UNICON "${NSISDIR}\Contrib\Graphics\Icons\modern-uninstall.ico"

; Header image
!define MUI_HEADERIMAGE
!define MUI_HEADERIMAGE_RIGHT
!define MUI_HEADERIMAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Header\nsis3-metro.bmp"
!define MUI_HEADERIMAGE_UNBITMAP "${NSISDIR}\Contrib\Graphics\Header\nsis3-metro.bmp"

; Welcome page image
!define MUI_WELCOMEFINISHPAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Wizard\nsis3-metro.bmp"
!define MUI_UNWELCOMEFINISHPAGE_BITMAP "${NSISDIR}\Contrib\Graphics\Wizard\nsis3-metro.bmp"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE ".\license.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
Page custom CustomDirectoryPage CustomDirectoryPageLeave
!insertmacro MUI_PAGE_INSTFILES
Page custom CustomFinishPage CustomFinishPageLeave

; Uninstaller pages
!insertmacro MUI_UNPAGE_WELCOME
!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES
!insertmacro MUI_UNPAGE_FINISH

; Language
!insertmacro MUI_LANGUAGE "English"

; Version Information
VIProductVersion "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"
VIAddVersionKey "ProductName" "${APPNAME}"
VIAddVersionKey "CompanyName" "${COMPANYNAME}"
VIAddVersionKey "LegalCopyright" "Copyright (c) ${COMPANYNAME}"
VIAddVersionKey "FileDescription" "${DESCRIPTION}"
VIAddVersionKey "FileVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}.0"

; Variables for custom pages
Var Dialog
Var Label_Title
Var Label_Description
Var Label_Path
Var Text_Path
Var Button_Browse
Var Label_Space
Var Text_Space
Var Label_Warning
Var SelectedPath
Var IsValidPath

Var FinishDialog
Var Finish_Label_Congratulations
Var Finish_Label_Description
Var Checkbox_AutoStart
Var Checkbox_DesktopShortcut
Var Checkbox_StartMenuShortcut
Var Finish_Label_Info
Var AutoStartChecked
Var DesktopShortcutChecked
Var StartMenuShortcutChecked

; Include function macros
!insertmacro WordReplace

; Custom Directory Page
Function CustomDirectoryPage
    !insertmacro MUI_HEADER_TEXT "Task Engine Configuration" "Select storage location for intelligent task engine"
    
    nsDialogs::Create 1018
    Pop $Dialog
    
    ${If} $Dialog == error
        Abort
    ${EndIf}
    
    ${NSD_CreateLabel} 0 0 100% 20u "Configure EchoWave Intelligent Task Engine"
    Pop $Label_Title
    
    ${NSD_CreateLabel} 0 25u 100% 40u "EchoWave uses advanced containerized task engine for complex computing tasks.$\r$\nPlease select a location with sufficient space to store task engine data and cache files.$\r$\nRecommended: Choose a disk location with more than 50GB free space."
    Pop $Label_Description
    
    ${NSD_CreateLabel} 0 75u 25% 15u "Storage Path:"
    Pop $Label_Path
    
    ${NSD_CreateText} 0 95u 75% 15u "C:\EchoWave\TaskEngine"
    Pop $Text_Path
    
    ${NSD_CreateButton} 77% 95u 20% 15u "Browse..."
    Pop $Button_Browse
    ${NSD_OnClick} $Button_Browse BrowseForFolder
    
    ${NSD_CreateLabel} 0 120u 30% 15u "Available Space:"
    Pop $Label_Space
    
    ${NSD_CreateText} 32% 120u 65% 15u "Checking..."
    Pop $Text_Space
    SendMessage $Text_Space ${EM_SETREADONLY} 1 0
    
    ${NSD_CreateLabel} 0 145u 100% 30u "Warning: Please ensure selected location has sufficient storage space."
    Pop $Label_Warning
    
    ${NSD_SetText} $Text_Path "C:\EchoWave\TaskEngine"
    StrCpy $SelectedPath "C:\EchoWave\TaskEngine"
    Call CheckDiskSpace
    
    nsDialogs::Show
FunctionEnd

Function BrowseForFolder
    nsDialogs::SelectFolderDialog "Select Task Engine Storage Location" "C:\"
    Pop $0
    
    ${If} $0 != error
        ${NSD_SetText} $Text_Path "$0\EchoWave\TaskEngine"
        StrCpy $SelectedPath "$0\EchoWave\TaskEngine"
        Call CheckDiskSpace
    ${EndIf}
FunctionEnd

Function CheckDiskSpace
    ; Simple validation - just check if path looks valid
    StrLen $0 $SelectedPath
    ${If} $0 > 3
        ${NSD_SetText} $Text_Space "Path looks valid - Please ensure 50GB+ available"
        StrCpy $IsValidPath "true"
    ${Else}
        ${NSD_SetText} $Text_Space "Invalid path"
        StrCpy $IsValidPath "false"
    ${EndIf}
FunctionEnd

Function CustomDirectoryPageLeave
    ${NSD_GetText} $Text_Path $SelectedPath
    
    Call CheckDiskSpace
    
    ${If} $IsValidPath == "false"
        MessageBox MB_ICONEXCLAMATION|MB_OK "Invalid path selected!$\r$\nPlease choose a valid location."
        Abort
    ${EndIf}
    
    CreateDirectory "$SelectedPath"
    Call SavePathToSettings
FunctionEnd

Function SavePathToSettings
    StrCpy $0 "$APPDATA\echowave_client"
    CreateDirectory "$0"
    
    StrCpy $1 '{"auto_accept_tasks": true,"distro_install_path": "'
    StrCpy $2 $SelectedPath
    ${WordReplace} $2 "\" "\\" "+" $2
    StrCpy $3 '"}'
    StrCpy $4 "$1$2$3"
    
    FileOpen $5 "$0\user-settings.json" w
    FileWrite $5 $4
    FileClose $5
    
    DetailPrint "Task engine path saved: $SelectedPath"
FunctionEnd

; Custom Finish Page
Function CustomFinishPage
    !insertmacro MUI_HEADER_TEXT "Installation Complete" "EchoWave has been successfully installed"
    
    nsDialogs::Create 1018
    Pop $FinishDialog
    
    ${If} $FinishDialog == error
        Abort
    ${EndIf}
    
    ${NSD_CreateLabel} 0 0 100% 20u "Congratulations! EchoWave has been successfully installed."
    Pop $Finish_Label_Congratulations
    
    ${NSD_CreateLabel} 0 25u 100% 30u "You can now use EchoWave's intelligent task engine for complex computing tasks.$\r$\nChoose the options below to complete your setup:"
    Pop $Finish_Label_Description
    
    ${NSD_CreateCheckbox} 10u 70u 90% 15u "Start EchoWave automatically when Windows starts"
    Pop $Checkbox_AutoStart
    ${NSD_SetState} $Checkbox_AutoStart ${BST_CHECKED}
    StrCpy $AutoStartChecked "1"
    ${NSD_OnClick} $Checkbox_AutoStart OnAutoStartClick
    
    ${NSD_CreateCheckbox} 10u 90u 90% 15u "Create desktop shortcut"
    Pop $Checkbox_DesktopShortcut
    ${NSD_SetState} $Checkbox_DesktopShortcut ${BST_CHECKED}
    StrCpy $DesktopShortcutChecked "1"
    ${NSD_OnClick} $Checkbox_DesktopShortcut OnDesktopShortcutClick
    
    ${NSD_CreateCheckbox} 10u 110u 90% 15u "Create Start Menu shortcuts"
    Pop $Checkbox_StartMenuShortcut
    ${NSD_SetState} $Checkbox_StartMenuShortcut ${BST_CHECKED}
    StrCpy $StartMenuShortcutChecked "1"
    ${NSD_OnClick} $Checkbox_StartMenuShortcut OnStartMenuShortcutClick
    
    ${NSD_CreateLabel} 10u 140u 90% 30u "Click 'Finish' to complete the installation and apply selected options.$\r$\nEchoWave will be ready to use after finishing the setup."
    Pop $Finish_Label_Info
    
    nsDialogs::Show
FunctionEnd

Function OnAutoStartClick
    ${NSD_GetState} $Checkbox_AutoStart $AutoStartChecked
FunctionEnd

Function OnDesktopShortcutClick
    ${NSD_GetState} $Checkbox_DesktopShortcut $DesktopShortcutChecked
FunctionEnd

Function OnStartMenuShortcutClick
    ${NSD_GetState} $Checkbox_StartMenuShortcut $StartMenuShortcutChecked
FunctionEnd

Function CustomFinishPageLeave
    ${If} $DesktopShortcutChecked == "1"
        DetailPrint "Creating desktop shortcut..."
        CreateShortCut "$DESKTOP\EchoWave.lnk" "$INSTDIR\config.ini"
    ${EndIf}
    
    ${If} $StartMenuShortcutChecked == "1"
        DetailPrint "Creating start menu shortcuts..."
        CreateDirectory "$SMPROGRAMS\EchoWave"
        CreateShortCut "$SMPROGRAMS\EchoWave\EchoWave.lnk" "$INSTDIR\config.ini"
        CreateShortCut "$SMPROGRAMS\EchoWave\Uninstall EchoWave.lnk" "$INSTDIR\Uninstall.exe"
    ${EndIf}
    
    ${If} $AutoStartChecked == "1"
        DetailPrint "Configuring auto-start..."
        Call SetAutoStart
    ${EndIf}
FunctionEnd

Function SetAutoStart
    WriteRegStr HKCU "SOFTWARE\Microsoft\Windows\CurrentVersion\Run" "EchoWave" "$INSTDIR\config.ini"
    
    ${If} ${Errors}
        DetailPrint "Warning: Could not set auto-start in registry"
        ClearErrors
    ${Else}
        DetailPrint "Auto-start configured successfully"
    ${EndIf}
FunctionEnd

; Install sections
Section "Core Application" SEC01
    SectionIn RO
    
    MessageBox MB_OK "Starting installation..."
    
    ; 1. Stop existing service if running
    DetailPrint "Checking for existing service..."
    nsExec::ExecToLog 'sc query "EchoWaveHelper"'
    Pop $0
    ${If} $0 == 0
        DetailPrint "Existing service found, stopping it..."
        nsExec::ExecToLog 'sc stop "EchoWaveHelper"'
        Pop $0
        Sleep 3000
        DetailPrint "Existing service stopped"
    ${Else}
        DetailPrint "No existing service found"
    ${EndIf}
    
    ; 2. Create installation directory and copy files
    DetailPrint "Creating installation directory..."
    CreateDirectory "$INSTDIR"
    SetOutPath $INSTDIR
    
    ; Delete existing file if it exists
    DetailPrint "Preparing to copy application files..."
    Delete "$INSTDIR\helper-svc.exe"
    
    DetailPrint "Copying application files..."
    File "..\resource\helper-svc.exe"
    
    ; 3. Write configuration files and registry entries BEFORE starting service
    DetailPrint "Writing configuration files..."
    WriteINIStr "$INSTDIR\config.ini" "Application" "Name" "${APPNAME}"
    WriteINIStr "$INSTDIR\config.ini" "Application" "Version" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteINIStr "$INSTDIR\config.ini" "Application" "Company" "${COMPANYNAME}"
    
    DetailPrint "Creating uninstaller..."
    WriteUninstaller "$INSTDIR\Uninstall.exe"
    
    DetailPrint "Registering application in system..."
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$INSTDIR\Uninstall.exe"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
    
    ; 4. Register and start Windows service (after all file operations)
    DetailPrint "Registering EchoWave Helper Service..."
    nsExec::ExecToLog '"$INSTDIR\helper-svc.exe" install'
    Pop $0
    ${If} $0 == 0
        DetailPrint "Service registered successfully"
        
        ; Wait a moment for service registration to complete
        DetailPrint "Waiting for service registration to complete..."
        Sleep 3000
        
        ; Try to start the service with better error handling
        DetailPrint "Starting EchoWaveHelper Service..."
        nsExec::ExecToLog 'sc start "EchoWaveHelper"'
        Pop $0
        ${If} $0 == 0
            DetailPrint "Service started successfully"
            
            ; Verify service is running
            Sleep 2000
            nsExec::ExecToLog 'sc query "EchoWaveHelper"'
            Pop $1
            ${If} $1 == 0
                DetailPrint "Service status verified"
                MessageBox MB_OK "Installation completed successfully!$\r$\nEchoWave Helper Service has been registered and started."
            ${Else}
                DetailPrint "Warning: Could not verify service status"
                MessageBox MB_ICONINFORMATION "Installation completed!$\r$\nService registered but status verification failed.$\r$\nThe service should be running normally."
            ${EndIf}
        ${ElseIf} $0 == 1056
            ; Service already running (error 1056)
            DetailPrint "Service is already running"
            MessageBox MB_OK "Installation completed successfully!$\r$\nEchoWave Helper Service was already running."
        ${ElseIf} $0 == 1053
            ; Service did not respond to start request (error 1053)
            DetailPrint "Service start timeout, trying alternative method..."
            nsExec::ExecToLog 'net start "EchoWaveHelper"'
            Pop $1
            ${If} $1 == 0
                DetailPrint "Service started using net command"
                MessageBox MB_OK "Installation completed successfully!$\r$\nEchoWave Helper Service has been started."
            ${Else}
                DetailPrint "Failed to start service with both methods"
                MessageBox MB_ICONEXCLAMATION "Service registered but failed to start automatically.$\r$\nError codes: sc=$0, net=$1$\r$\nYou can start the service manually from Services (services.msc)."
            ${EndIf}
        ${Else}
            DetailPrint "Service start failed with error code: $0"
            ; Try alternative start method
            DetailPrint "Trying alternative start method..."
            nsExec::ExecToLog 'net start "EchoWaveHelper"'
            Pop $1
            ${If} $1 == 0
                DetailPrint "Service started using net command"
                MessageBox MB_OK "Installation completed successfully!$\r$\nEchoWave Helper Service has been started."
            ${Else}
                DetailPrint "Both start methods failed: sc=$0, net=$1"
                MessageBox MB_ICONEXCLAMATION "Service registered but failed to start automatically.$\r$\nError codes: sc=$0, net=$1$\r$\nPlease try starting the service manually from Services (services.msc) or restart your computer."
            ${EndIf}
        ${EndIf}
    ${Else}
        DetailPrint "Service registration failed with error code: $0"
        MessageBox MB_ICONSTOP "Failed to register EchoWave Helper Service.$\r$\nError code: $0$\r$\nPlease run the installer as Administrator."
    ${EndIf}
SectionEnd

Section "Additional Components" SEC02
    ; Optional components
SectionEnd

; Section descriptions
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC01} "Core application files (required)"
  !insertmacro MUI_DESCRIPTION_TEXT ${SEC02} "Additional optional components"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; Uninstaller
Section "Uninstall"
    DetailPrint "Starting EchoWave complete uninstallation..."
    
    ; 1. Stop and unregister Windows service
    DetailPrint "Checking EchoWaveHelper Service status..."
    nsExec::ExecToLog 'sc query "EchoWaveHelper"'
    Pop $0
    ${If} $0 == 0
        DetailPrint "Service found, stopping EchoWaveHelper Service..."
        
        ; Try to stop the service with sc command
        nsExec::ExecToLog 'sc stop "EchoWaveHelper"'
        Pop $0
        ${If} $0 == 0
            DetailPrint "Service stopped successfully with sc command"
        ${Else}
            DetailPrint "sc stop failed (code: $0), trying net stop..."
            nsExec::ExecToLog 'net stop "EchoWaveHelper"'
            Pop $1
            ${If} $1 == 0
                DetailPrint "Service stopped successfully with net command"
            ${Else}
                DetailPrint "Warning: Both stop methods failed (sc: $0, net: $1)"
            ${EndIf}
        ${EndIf}
        
        ; Wait for service to fully stop
        DetailPrint "Waiting for service to stop completely..."
        Sleep 5000
        
        ; Verify service has stopped
        nsExec::ExecToLog 'sc query "EchoWaveHelper"'
        Pop $2
        ${If} $2 == 0
            DetailPrint "Service status checked"
        ${EndIf}
    ${Else}
        DetailPrint "EchoWaveHelper Service not found or already stopped"
    ${EndIf}
    
    ; Try to unregister using helper-svc.exe
    DetailPrint "Unregistering service using helper-svc.exe..."
    IfFileExists "$INSTDIR\helper-svc.exe" 0 TryDirectDelete
    nsExec::ExecToLog '"$INSTDIR\helper-svc.exe" uninstall'
    Pop $0
    ${If} $0 == 0
        DetailPrint "Service unregistered successfully using helper-svc.exe"
    ${Else}
        DetailPrint "helper-svc.exe uninstall failed (code: $0), trying direct deletion..."
        Goto TryDirectDelete
    ${EndIf}
    Goto ServiceCleanupDone
    
    TryDirectDelete:
    ; Try to delete service directly using sc delete
    DetailPrint "Attempting direct service deletion..."
    nsExec::ExecToLog 'sc delete "EchoWaveHelper"'
    Pop $0
    ${If} $0 == 0
        DetailPrint "Service deleted successfully using sc delete"
    ${Else}
        DetailPrint "Warning: Could not delete service directly (code: $0)"
        DetailPrint "Service may need manual removal from Services console"
    ${EndIf}
    
    ServiceCleanupDone:
    DetailPrint "Service cleanup completed"
    
    ; 2. Remove auto-start registry entry
    DetailPrint "Removing auto-start registry entry..."
    DeleteRegValue HKCU "SOFTWARE\Microsoft\Windows\CurrentVersion\Run" "EchoWave"
    ${If} ${Errors}
        DetailPrint "Auto-start registry entry not found or already removed"
        ClearErrors
    ${Else}
        DetailPrint "Auto-start registry entry removed successfully"
    ${EndIf}
    
    ; 3. Unregister WSL distribution
    DetailPrint "Checking for WSL distribution 'EchoWaveUbuntu'..."
    nsExec::ExecToLog 'wsl --list --quiet'
    Pop $0
    
    ${If} $0 == 0
        DetailPrint "WSL is available, checking for EchoWaveUbuntu distribution..."
        nsExec::ExecToLog 'wsl --unregister EchoWaveUbuntu'
        Pop $0
        ${If} $0 == 0
            DetailPrint "WSL distribution 'EchoWaveUbuntu' unregistered successfully"
        ${Else}
            DetailPrint "WSL distribution 'EchoWaveUbuntu' not found or already removed"
        ${EndIf}
    ${Else}
        DetailPrint "WSL not available or not installed"
    ${EndIf}
    
    ; 4. Read user settings and remove data directories
    DetailPrint "Reading user settings for data cleanup..."
    Call un.ReadAndCleanupUserData
    
    ; 5. Remove application data directory
    DetailPrint "Removing application data directory..."
    RMDir /r "$APPDATA\echowave_client"
    ${If} ${Errors}
        DetailPrint "Warning: Could not remove some application data files"
        ClearErrors
    ${Else}
        DetailPrint "Application data directory removed successfully"
    ${EndIf}
    
    ; 6. Remove shortcuts
    DetailPrint "Removing shortcuts..."
    Delete "$DESKTOP\EchoWave.lnk"
    Delete "$SMPROGRAMS\EchoWave\EchoWave.lnk"
    Delete "$SMPROGRAMS\EchoWave\Uninstall EchoWave.lnk"
    RMDir "$SMPROGRAMS\EchoWave"
    
    ; 7. Remove installation files
    DetailPrint "Removing installation files..."
    Delete "$INSTDIR\config.ini"
    Delete "$INSTDIR\helper-svc.exe"
    Delete "$INSTDIR\Uninstall.exe"
    RMDir "$INSTDIR"
    
    ; 8. Remove registry entries
    DetailPrint "Removing registry entries..."
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
    
    MessageBox MB_OK "EchoWave has been completely uninstalled!$\r$\n$\r$\nRemoved components:$\r$\n- Windows service$\r$\n- WSL distribution (if existed)$\r$\n- All data directories$\r$\n- Auto-start configuration$\r$\n- Shortcuts and registry entries"
SectionEnd

; Function to read user settings and cleanup data directories
Function un.ReadAndCleanupUserData
    ; Check if user-settings.json exists
    IfFileExists "$APPDATA\echowave_client\user-settings.json" 0 NoUserSettings
    
    DetailPrint "Found user-settings.json, reading distro_install_path..."
    
    ; Read the JSON file (simplified parsing)
    FileOpen $0 "$APPDATA\echowave_client\user-settings.json" r
    ${If} $0 != ""
        FileRead $0 $1 1024
        FileClose $0
        
        ; Simple pattern matching for distro_install_path
        Push $1
        Push '"distro_install_path":"'
        Call un.StrStr
        Pop $2
        
        ${If} $2 != ""
            ; Found the key, extract the path value
            StrLen $3 '"distro_install_path":"'
            StrCpy $4 $2 "" $3  ; Skip the key part
            
            ; Find the closing quote
            Push $4
            Push '"'
            Call un.StrStr
            Pop $5
            
            ${If} $5 != ""
                StrLen $6 $4
                StrLen $7 $5
                IntOp $8 $6 - $7
                StrCpy $9 $4 $8  ; Extract the path
                
                ; Replace escaped backslashes
                ${WordReplace} $9 "\\" "\" "+" $9
                
                DetailPrint "Found distro install path: $9"
                DetailPrint "Removing task engine data directory..."
                RMDir /r "$9"
                ${If} ${Errors}
                    DetailPrint "Warning: Could not remove task engine directory: $9"
                    ClearErrors
                ${Else}
                    DetailPrint "Task engine directory removed successfully: $9"
                ${EndIf}
            ${Else}
                DetailPrint "Could not parse distro_install_path value"
            ${EndIf}
        ${Else}
            DetailPrint "distro_install_path not found in settings"
        ${EndIf}
    ${Else}
        DetailPrint "Could not read user-settings.json"
    ${EndIf}
    Goto EndCleanup
    
    NoUserSettings:
    DetailPrint "user-settings.json not found, skipping task engine directory cleanup"
    
    EndCleanup:
FunctionEnd

; Function to find substring in string
Function un.StrStr
    Exch $R1 ; $R1=substring, $R0=string
    Exch
    Exch $R0
    Push $R2
    Push $R3
    Push $R4
    Push $R5
    StrLen $R3 $R1
    StrCpy $R4 0
    ; $R1=substring, $R0=string, $R3=strlen(substring)
    ; $R4=count
    ${Do}
        StrCpy $R5 $R0 $R3 $R4
        ${If} $R5 == $R1
            StrCpy $R0 $R0 "" $R4
            Pop $R5
            Pop $R4
            Pop $R3
            Pop $R2
            Pop $R1
            Exch $R0
            Return
        ${EndIf}
        IntOp $R4 $R4 + 1
        StrCpy $R5 $R0 1 $R4
        ${If} $R5 == ""
            StrCpy $R0 ""
            Pop $R5
            Pop $R4
            Pop $R3
            Pop $R2
            Pop $R1
            Exch $R0
            Return
        ${EndIf}
    ${Loop}
FunctionEnd 