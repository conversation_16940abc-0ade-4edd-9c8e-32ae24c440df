# EchoWave Client

## 目录结构

```text
project/
├── .git/
├── .gitignore
├── Cargo.toml                # 工作区的根配置文件

├── agent/                    # 🦀 任务引擎（WSL 镜像） 内的代理程序（Linux Only）
│   ├── Cargo.toml
│   └── src/
│       └── main.rs

├── crates/                   # 📦 存放所有共享库 crate 的地方
│   └── shared/               # 🦀 共享的通用实现 crate
│       ├── Cargo.toml
│       └── src/
│           └── lib.rs
│   └── protocol/             # 🦀 共享的消息协议 crate
│       ├── Cargo.toml
│       └── src/
│           └── lib.rs
│   └── domain/               # 🦀 核心负责协调流程与业务逻辑的 crate，这个 crate 旨在处理核心业务逻辑，不包含任何 UI 代码，以方便在不同的 UI 层使用（Tauri、Electron、TUI、CLI 等）
│       ├── Cargo.toml
│       └── src/
│           └── lib.rs

└── desktop/                  #  Tauri 桌面应用，采用 Vue3 作为前端框架，对 domain 的 UI 包装
    ├── node_modules/
    ├── public/
    ├── src/                  # 前端代码 (TS, Vue, HTML, CSS)
    ├── package.json          # 使用 Pnpm 作为包管理器
    ├── tauri.conf.json
    └── src-tauri/            # Tauri 的 Rust 后端
        ├── build.rs
        ├── Cargo.toml
        └── src/
            └── main.rs

└── helper-svc/                   # 🦀 windows 服务进程（Windows Only）
│   ├── Cargo.toml
│   └── src/
│       └── main.rs
```
