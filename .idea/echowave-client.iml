<?xml version="1.0" encoding="UTF-8"?>
<module type="EMPTY_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/crates/protocol/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/desktop/src-tauri/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/agent/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/crates/core/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/helper-svc/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/agent/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/crates/core/tests" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/crates/core/examples" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/desktop/src-tauri/target" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>