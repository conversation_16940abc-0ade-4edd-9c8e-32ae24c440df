# CLAUDE.md - Protocol 模块指南

这个文件为 Claude Code 提供 EchoWave Protocol 模块的开发指导。

## 模块概述

Protocol 是 EchoWave 架构中的核心通信协议模块，提供了统一的二进制消息帧格式，用于实现各模块间的可靠通信：

- **Core ↔ Agent**: 通过 STDIO（标准输入输出）进行进程间通信
- **Core ↔ Helper-svc**: 通过 Windows Named Pipe 进行服务通信（开发中）

## 架构设计

### MessageFrame 协议规范

Protocol 模块实现了一个高效的二进制消息帧协议，支持：

- **识别码保护**: 防止普通文本输出干扰数据传输
- **数据压缩**: 自动对大于 1KB 的数据启用 ZSTD 压缩
- **完整性校验**: CRC32 校验和确保数据传输正确性
- **请求响应模型**: 通过 operation_id 关联请求和响应
- **事件驱动**: 支持数据传输和内部控制事件

### 数据包结构（16 字节头部）

```
[识别码: 3字节][标志位: 1字节][操作ID: 4字节][数据长度: 4字节][校验和: 4字节][载荷: N字节]
```

**头部字段说明**:

- **识别码** (3 bytes): 固定值 `0x10 0xAA 0xFF`，用于区分二进制数据和文本输出
- **标志位** (1 byte):
  - bit 7: 压缩标志（1=已压缩）
  - bit 6: 响应标志（0=请求，1=响应）
  - bit 0-5: 事件码（最多支持 64 种事件）
- **操作 ID** (4 bytes): 大端序 u32，用于关联请求响应对
- **数据长度** (4 bytes): 大端序 u32，载荷部分的字节长度
- **校验和** (4 bytes): 大端序 u32，载荷的 CRC32 校验值
- **载荷** (N bytes): JSON 序列化的业务数据，可能经过 ZSTD 压缩

### 事件码定义

```rust
pub enum EventCode {
    Data = 0x01,      // 普通数据传输
    Reserved = 0x30,  // 保留事件
    Flush = 0x31,     // 要求立即推送缓冲区
    Exit = 0x3f,      // 通知对方准备退出
}
```

## 核心 API

### 发送消息帧

```rust
pub async fn send_message_frame<W>(
    writer: &mut W,
    event_code: EventCode,
    operation_id: u32,
    is_response: bool,
    bytes: &[u8],
) -> anyhow::Result<()>
```

### 读取消息帧

```rust
pub async fn read_message_frame<R>(
    reader: &mut R
) -> io::Result<Option<MessageFrame>>
```

## 业务事件体系

### Agent 事件 (AgentEvent)

**Core → Agent 请求**:

- `Shutdown`: 通知 Agent 关闭
- `Stop`: 停止守护进程（Docker、Nomad）
- `Start`: 启动守护进程，包含网络配置
- `WaitForTailscaleReady`: 等待 Tailscale 网络就绪
- `GetNodeStatus`: 获取节点运行状态
- `GetNetworkLatency`: 获取网络延迟信息
- `GetHealthStatus`: 健康检查

**Agent → Core 响应 (AgentResponse)**:

- `NodeStatus`: 节点状态数据（空闲状态、任务列表、节点信息）
- `NetworkLatency`: 网络延迟测量结果
- `TailscaleReady`: Tailscale 就绪状态
- `HealthStatus`: 健康检查结果
- `Success`: 操作成功（无额外数据）
- `Error`: 错误响应（错误码和消息）

**Agent → Core 推送**:

- `LogForward`: 单条日志转发
- `LogBatchForward`: 批量日志转发

### 日志事件 (LogEntry)

统一的日志格式，支持：

- 日志级别（Error/Warn/Info/Debug/Trace）
- 时间戳和追踪 ID
- 模块、文件、行号等调试信息
- 跨模块的日志聚合和追踪

### Helper Service 事件（开发中）

为 Windows 服务模块预留的事件定义。

## 开发注意事项

### 错误处理

1. **读取错误处理**:

   - EOF 表示连接关闭
   - 校验和错误表示数据损坏
   - 无效事件码会被忽略并继续读取

2. **写入错误处理**:
   - 使用 vectored I/O 提高性能
   - 处理 `Interrupted` 错误并重试
   - 写入 0 字节视为失败

### 性能优化

1. **自动压缩**:

   - 数据大于 1KB 时自动启用压缩
   - 使用 ZSTD level 3 平衡压缩率和速度

2. **批量处理**:

   - 支持批量日志转发减少通信开销
   - 使用缓冲读取器提高读取效率

3. **零拷贝设计**:
   - 使用 `IoSlice` 实现零拷贝写入
   - 直接在缓冲区上操作避免额外分配

### 安全考虑

1. **敏感信息保护**:

   - 使用 `SecretString` 保护密码和密钥
   - 序列化时自动处理敏感字段

2. **数据完整性**:
   - CRC32 校验确保数据未被篡改
   - 识别码防止注入攻击

### 测试策略

模块包含完整的单元测试覆盖：

- 基本读写功能测试
- 压缩/解压缩测试
- 错误场景测试（校验和错误、无效事件码等）
- 混合输出场景测试（文本+二进制）
- 边界条件测试（空载荷、大数据等）

## 使用示例

### 发送请求并接收响应

```rust
// 发送启动守护进程请求
let event = AgentEvent::Start {
    trace_id: Uuid::new_v4(),
    tailscale_ip: "**********".to_string(),
    docker_auth: None,
};

let bytes = serde_json::to_vec(&event)?;
send_message_frame(&mut writer, EventCode::Data, op_id, false, &bytes).await?;

// 读取响应
if let Some(frame) = read_message_frame(&mut reader).await? {
    let response: AgentResponse = serde_json::from_slice(&frame.bytes)?;
    match response {
        AgentResponse::Success { .. } => println!("启动成功"),
        AgentResponse::Error { message, .. } => eprintln!("启动失败: {}", message),
        _ => {}
    }
}
```

### 处理混合输出

```rust
// 循环读取，自动过滤文本输出
loop {
    match read_message_frame(&mut reader).await? {
        Some(frame) => {
            // 处理有效的消息帧
            handle_message_frame(frame);
        }
        None => {
            // 忽略文本输出或到达 EOF
            continue;
        }
    }
}
```

## 扩展指南

### 添加新事件类型

1. 在 `events/` 目录下定义新的事件结构
2. 实现必要的序列化/反序列化 trait
3. 添加对应的单元测试
4. 更新相关模块的事件处理逻辑

### 支持新的通信通道

1. 实现 `AsyncRead + AsyncBufRead` trait 用于读取
2. 实现 `AsyncWrite` trait 用于写入
3. 复用现有的 `send_message_frame` 和 `read_message_frame` API

## 故障排查

### 常见问题

1. **"无法读取消息帧"错误**:

   - 检查对端进程是否正常运行
   - 确认数据格式是否正确
   - 查看日志中的校验和错误

2. **性能问题**:

   - 检查是否频繁发送小数据包
   - 考虑使用批量接口（如 `LogBatchForward`）
   - 确认压缩功能是否正常工作

3. **兼容性问题**:
   - 确保所有模块使用相同版本的 protocol
   - 检查序列化格式是否一致
   - 验证字节序（统一使用大端序）
