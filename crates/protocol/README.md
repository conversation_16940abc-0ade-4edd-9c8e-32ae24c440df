# 数据交换协议

- 用于 core（客户端核心） 和 agent（任务引擎代理）通过 STDIO（标准输入输出）通信
- 用于 core（客户端核心）和 helper-svc（Windows 服务助手）通过 Windows Named Pipe 通信

## 数据包结构设计

字节序 为 大端序，整个 Header 部分共 16 字节

`[识别码: 3u8 ][ 标志位：1u8 ][ 操作码: 1u32 ][ 数据长度: 1u32 ][ Checksum: 1u32 ]`

**Header**

- 识别码：固定值 `0x10 0xAA 0xFF`, 用于区分普通文本输出还是数据包输出，即时大多时候都会传输二进制内容，但还是为了防止出现意外的文本而设计
- 标志位：用于标识事件，这是比较低级的标识
  - `bit 0`: is compression, 是否启用了 ZSTD 压缩
  - `bit 1`: is response, 0 为 请求、1 为响应
  - `bit 2-7`: event code, 内部事件
- 操作码：操作的 ID，用于关联请求、响应的一对一关系
- 数据长度：标识数据部分的长度
- Checksum: 数据部分(Payload)的校验和

**Payload**

- 数据：正文内容，JSON 格式