[package]
name = "protocol"
version = "0.1.0"
edition = "2024"

[dependencies]
# 从工作区继承依赖项，非常简洁
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
bytes = { workspace = true }
zstd = { workspace = true, optional = true } # 设为可选，按需开启
crc32fast = { workspace = true }
anyhow = { workspace = true }
tokio = {workspace = true}
tracing = { workspace = true }
pin-project-lite = { workspace = true }
uuid = {workspace = true}
secrecy = { workspace = true }

[dev-dependencies]
tracing-subscriber = { workspace = true }

[features]
# 定义一个特性来控制是否启用压缩
compression = ["dep:zstd"]
default = ["compression"]
