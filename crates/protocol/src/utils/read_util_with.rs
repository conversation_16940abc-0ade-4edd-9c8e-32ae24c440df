use pin_project_lite::pin_project;
use std::future::Future;
use std::marker::PhantomPinned;
use std::mem;
use std::pin::Pin;
use std::task::{Context, Poll, ready};
use tokio::io;
use tokio::io::AsyncBufRead;

pub trait AsyncBufReadUntilWithExt: AsyncBufRead {
    /// 参考 AsyncBufRead read_until
    fn read_until_with<'a, F, S>(
        &'a mut self,
        f: F,
        state: S,
        buf: &'a mut Vec<u8>,
    ) -> ReadUntilWith<'a, Self, F, S>
    where
        F: Fn(&mut S, &[u8]) -> Option<usize>,
        Self: Unpin,
    {
        read_until_with(self, f, state, buf)
    }
}

impl<R: AsyncBufRead + ?Sized> AsyncBufReadUntilWithExt for R {}

pin_project! {
    #[derive(Debug)]
    #[must_use = "futures do nothing unless you `.await` or poll them"]
    pub struct ReadUntilWith<'a, R, F, S>
    where
        R: ?Sized,
        F: Fn(&mut S, &[u8]) -> Option<usize>
    {
        reader: &'a mut R,
        delimiter: F,
        buf: &'a mut Vec<u8>,
        state: S,
        // The number of bytes appended to buf. This can be less than buf.len() if
        // the buffer was not empty when the operation was started.
        read: usize,
        // Make this future `!Unpin` for compatibility with async trait methods.
        #[pin]
        _pin: PhantomPinned,
    }
}

fn read_until_with<'a, R, F, S>(
    reader: &'a mut R,
    delimiter: F,
    state: S,
    buf: &'a mut Vec<u8>,
) -> ReadUntilWith<'a, R, F, S>
where
    R: AsyncBufRead + ?Sized + Unpin,
    F: Fn(&mut S, &[u8]) -> Option<usize>,
{
    ReadUntilWith {
        reader,
        delimiter,
        state,
        buf,
        read: 0,
        _pin: PhantomPinned,
    }
}

fn read_until_with_internal<R, F, S>(
    mut reader: Pin<&mut R>,
    cx: &mut Context<'_>,
    delimiter: &F,
    state: &mut S,
    buf: &mut Vec<u8>,
    read: &mut usize,
) -> Poll<io::Result<usize>>
where
    R: AsyncBufRead + ?Sized,
    F: Fn(&mut S, &[u8]) -> Option<usize>,
{
    loop {
        let (done, used) = {
            let available = ready!(reader.as_mut().poll_fill_buf(cx))?;
            if let Some(i) = delimiter(state, available) {
                buf.extend_from_slice(&available[..i]);
                (true, i)
            } else {
                buf.extend_from_slice(available);
                (false, available.len())
            }
        };
        reader.as_mut().consume(used);
        *read += used;
        if done || used == 0 {
            return Poll::Ready(Ok(mem::replace(read, 0)));
        }
    }
}

/// 在 `haystack` 中查找完整的 `needle` 字节序列。
///
/// # 参数
/// - `needle`: 要查找的字节序列（模式）。
/// - `haystack`: 要搜索的字节序列（数据）。
/// - `matched_len`: 可变引用，用于记录当前已匹配的字节数；
///   这允许在分段搜索时保存部分匹配状态。
///
/// # 返回值
/// 如果找到了完整的 `needle`，则返回 `Some(index)`，其中 `index` 是在 `haystack`
/// 中匹配结束后的位置；如果没有找到，则返回 `None`.
///
/// # 说明
/// 此函数采用简单的顺序扫描方式。对于每个字节：
/// - 如果与 `needle` 当前需要匹配的字节相同，则增加匹配计数；
/// - 如果匹配计数达到 `needle` 的长度，则表示匹配成功。
/// - 如果发生不匹配且之前已有部分匹配，则重置匹配计数为 0.
///
/// 此外，如果剩余的 `haystack` 长度不足以完成当前匹配，则提前退出循环。
#[allow(unused)]
fn memchr(needle: &[u8], haystack: &[u8], matched_len: &mut usize) -> Option<usize> {
    let needle_len = needle.len();
    // 如果 needle 为空，认为已经匹配，返回 0 位置。
    if needle_len == 0 {
        return Some(0);
    }
    for (i, &byte) in haystack.iter().enumerate() {
        if byte == needle[*matched_len] {
            *matched_len += 1;
            if *matched_len == needle_len {
                return Some(i + 1);
            }
        } else if *matched_len > 0 {
            // 当前字节不匹配且之前已有部分匹配，则重置匹配计数，如果出现重叠匹配的情况则设为 1
            *matched_len = if byte == needle[0] { 1 } else { 0 }
        }
    }
    None
}

impl<R, F, S> Future for ReadUntilWith<'_, R, F, S>
where
    R: AsyncBufRead + ?Sized + Unpin,
    F: Fn(&mut S, &[u8]) -> Option<usize>,
{
    type Output = io::Result<usize>;

    fn poll(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Self::Output> {
        let me = self.project();
        read_until_with_internal(
            Pin::new(*me.reader),
            cx,
            me.delimiter,
            me.state,
            me.buf,
            me.read,
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::pin::Pin;
    use tokio::io::{AsyncRead, AsyncReadExt};

    /// 测试空 needle 时应立即匹配，返回索引 0。
    #[test]
    fn test_empty_needle() {
        let needle: &[u8] = b"";
        let haystack: &[u8] = b"abcdef";
        let mut matched_len = 0;
        // 当 needle 为空时，视为匹配成功，返回 0 位置
        assert_eq!(memchr(needle, haystack, &mut matched_len), Some(0));
    }

    /// 测试在 haystack 中存在完整匹配的情况
    #[test]
    fn test_exact_match() {
        let needle: &[u8] = b"cde";
        let haystack: &[u8] = b"abcdef";
        let mut matched_len = 0;
        // needle "cde" 在 haystack 中分别对应索引 2,3,4，匹配完成后返回 i+1，即 5
        assert_eq!(memchr(needle, haystack, &mut matched_len), Some(5));
    }

    /// 测试 haystack 中不存在 needle 的情况
    #[test]
    fn test_no_match() {
        let needle: &[u8] = b"xyz";
        let haystack: &[u8] = b"abcdef";
        let mut matched_len = 0;
        // 未找到匹配，返回 None
        assert_eq!(memchr(needle, haystack, &mut matched_len), None);
    }

    /// 测试在部分匹配后出现不匹配，需要重置 matched_len，再找到完整匹配的情况
    #[test]
    fn test_partial_reset() {
        let needle: &[u8] = b"abc";
        // haystack 中第一次 "ab" 后跟随 "x" 使部分匹配失败，随后 "abc" 才能完整匹配
        let haystack: &[u8] = b"abxabc";
        let mut matched_len = 0;
        // 模拟过程：
        // - 索引 0: 'a' 匹配 → matched_len = 1
        // - 索引 1: 'b' 匹配 → matched_len = 2
        // - 索引 2: 'x' 不匹配 → 重置 matched_len = 0
        // - 索引 3: 'a' 匹配 → matched_len = 1
        // - 索引 4: 'b' 匹配 → matched_len = 2
        // - 索引 5: 'c' 匹配 → matched_len = 3，匹配完成，返回 5+1 = 6
        assert_eq!(memchr(needle, haystack, &mut matched_len), Some(6));
    }

    /// 测试分段搜索场景：通过多个 haystack 片段进行连续匹配
    #[test]
    fn test_segmented_search() {
        let needle: &[u8] = b"abc";
        let part1: &[u8] = b"ab";
        let part2: &[u8] = b"cdef";
        let mut matched_len = 0;

        // 第一段处理后，匹配了 "ab"，但未完成匹配，返回 None
        assert_eq!(memchr(needle, part1, &mut matched_len), None);
        // 第二段的首字节 'c' 与 needle 的第 3 个字节匹配，完成匹配后返回相对于 part2 的索引+1，即 Some(1)
        assert_eq!(memchr(needle, part2, &mut matched_len), Some(1));

        // 注意：分段搜索时，返回的索引是相对于当前片段的，
        // 调用者需要根据各片段在整体数据中的偏移量来计算最终匹配位置
    }

    /// 测试 ReadUntilWith 确保读取的数据末尾是 delimiter 返回的位置
    #[tokio::test]
    async fn test_read_until_with_delimiter_position() {
        // 创建测试数据
        let test_data = b"abc123xyz789";
        let mut cursor = std::io::Cursor::new(test_data.to_vec());
        let mut buf = Vec::new();

        // 定义一个简单的 delimiter 函数，查找 "xyz" 序列
        let needle = b"xyz";
        let state = 0; // 已匹配的字节数
        let delimiter = |matched_len: &mut usize, data: &[u8]| memchr(needle, data, matched_len);

        // 执行读取
        let bytes_read = read_until_with(&mut cursor, delimiter, state, &mut buf)
            .await
            .unwrap();

        // 验证读取的字节数
        assert_eq!(bytes_read, 9); // "abc123xyz" 的长度

        // 验证读取的内容
        assert_eq!(&buf, b"abc123xyz");

        // 验证剩余内容
        let mut remaining = Vec::new();
        cursor.read_to_end(&mut remaining).await.unwrap();
        assert_eq!(&remaining, b"789");
    }

    /// 测试多次调用 ReadUntilWith 时 delimiter 状态的正确维护
    #[tokio::test]
    async fn test_read_until_with_multiple_calls() {
        // 创建测试数据，包含多个分隔符
        let test_data = b"abc|def|ghi|jkl";
        let mut cursor = std::io::Cursor::new(test_data.to_vec());

        // 定义 delimiter 函数，查找 "|" 字符
        let needle = b"|";
        let state = 0;
        let delimiter = |matched_len: &mut usize, data: &[u8]| memchr(needle, data, matched_len);

        // 第一次读取
        let mut buf1 = Vec::new();
        let bytes_read1 = read_until_with(&mut cursor, delimiter, state, &mut buf1)
            .await
            .unwrap();
        assert_eq!(bytes_read1, 4); // "abc|" 的长度
        assert_eq!(&buf1, b"abc|");

        // 第二次读取
        let mut buf2 = Vec::new();
        let bytes_read2 = read_until_with(&mut cursor, delimiter, state, &mut buf2)
            .await
            .unwrap();
        assert_eq!(bytes_read2, 4); // "def|" 的长度
        assert_eq!(&buf2, b"def|");

        // 第三次读取
        let mut buf3 = Vec::new();
        let bytes_read3 = read_until_with(&mut cursor, delimiter, state, &mut buf3)
            .await
            .unwrap();
        assert_eq!(bytes_read3, 4); // "ghi|" 的长度
        assert_eq!(&buf3, b"ghi|");

        // 读取剩余内容（没有分隔符）
        let mut buf4 = Vec::new();
        let bytes_read4 = read_until_with(&mut cursor, delimiter, state, &mut buf4)
            .await
            .unwrap();
        assert_eq!(bytes_read4, 3); // "jkl" 的长度
        assert_eq!(&buf4, b"jkl");
    }

    /// 测试分段数据中的分隔符跨越边界的情况
    #[tokio::test]
    async fn test_read_until_with_delimiter_across_chunks() {
        // 模拟分段数据的读取器
        struct ChunkedReader {
            chunks: Vec<Vec<u8>>,
            current_chunk: usize,
            position: usize,
        }

        impl ChunkedReader {
            fn new(chunks: Vec<Vec<u8>>) -> Self {
                Self {
                    chunks,
                    current_chunk: 0,
                    position: 0,
                }
            }
        }

        impl AsyncRead for ChunkedReader {
            fn poll_read(
                mut self: Pin<&mut Self>,
                _cx: &mut Context<'_>,
                buf: &mut tokio::io::ReadBuf<'_>,
            ) -> Poll<io::Result<()>> {
                if self.current_chunk >= self.chunks.len() {
                    return Poll::Ready(Ok(()));
                }

                let chunk = &self.chunks[self.current_chunk];
                if self.position >= chunk.len() {
                    self.current_chunk += 1;
                    self.position = 0;
                    return self.poll_read(_cx, buf);
                }

                let bytes_to_copy = std::cmp::min(buf.remaining(), chunk.len() - self.position);

                buf.put_slice(&chunk[self.position..self.position + bytes_to_copy]);
                self.position += bytes_to_copy;

                Poll::Ready(Ok(()))
            }
        }

        impl AsyncBufRead for ChunkedReader {
            fn poll_fill_buf(
                self: Pin<&mut Self>,
                _cx: &mut Context<'_>,
            ) -> Poll<io::Result<&[u8]>> {
                let this = self.get_mut();

                if this.current_chunk >= this.chunks.len() {
                    return Poll::Ready(Ok(&[]));
                }

                let chunk = &this.chunks[this.current_chunk];
                Poll::Ready(Ok(&chunk[this.position..]))
            }

            fn consume(mut self: Pin<&mut Self>, amt: usize) {
                self.position += amt;
                if self.position >= self.chunks[self.current_chunk].len() {
                    self.current_chunk += 1;
                    self.position = 0;
                }
            }
        }

        // 创建跨越边界的测试数据
        let chunks = vec![
            b"Hello, ".to_vec(),
            b"Wor".to_vec(),
            b"ld! This is a ".to_vec(),
            b"te".to_vec(),
            b"st.".to_vec(),
        ];

        let mut reader = ChunkedReader::new(chunks);

        // 测试查找跨越边界的 "World"
        let needle = b"World";
        let state = 0;
        let delimiter = |matched_len: &mut usize, data: &[u8]| memchr(needle, data, matched_len);

        let mut buf = Vec::new();
        let bytes_read = read_until_with(&mut reader, delimiter, state, &mut buf)
            .await
            .unwrap();

        assert_eq!(bytes_read, 12); // "Hello, World" 的长度
        assert_eq!(&buf, b"Hello, World");

        // 读取剩余内容
        let mut remaining = Vec::new();
        reader.read_to_end(&mut remaining).await.unwrap();
        assert_eq!(&remaining, b"! This is a test.");
    }
}
