pub fn find_subarray(needle: &[u8], haystack: &[u8]) -> Option<usize> {
    let haystack_len = haystack.len();
    let needle_len = needle.len();
    if needle_len == 0 || haystack_len < needle_len {
        return None;
    }
    for i in 0..=haystack_len - needle_len {
        let end = i + needle_len;
        if &haystack[i..end] == needle {
            return Some(i);
        }
    }
    None
}

#[cfg(test)]
mod tests {
    use super::*;
    #[test]
    fn it_works() {
        assert_eq!(
            find_subarray(&[1, 2, 3], &[0, 1, 2, 4, 5, 6, 7, 1, 2, 3, 4]),
            Some(7)
        )
    }
}
