use std::io::IoSlice;

use crate::utils::{find_subarray::find_subarray, read_util_with::AsyncBufReadUntilWithExt};
use crc32fast::Hasher;
use tokio::io::{self, AsyncBufRead, AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};

const MAGIC: [u8; 3] = [0x10, 0xAA, 0xFF];

#[repr(u8)]
#[derive(Debug, Copy, Clone, PartialEq, Eq)]
pub enum EventCode {
    // 数据，指代 JSON 数据
    Data = 0x01,
    // 二进制数据块
    Binary = 0x02,
    // 之后的都应该是内部事件
    Reserved = 0x30,
    // 要求目标立即推送缓冲区的数据，目标保留
    Flush = 0x31,
    // 最大值，要求目标准备退出
    Exit = 0x3f,
}

impl TryFrom<u8> for EventCode {
    type Error = anyhow::Error;

    fn try_from(value: u8) -> Result<Self, Self::Error> {
        match value {
            0x01 => Ok(EventCode::Data),
            0x02 => Ok(EventCode::Binary),
            0x30 => Ok(EventCode::Reserved),
            0x31 => Ok(EventCode::Flush),
            0x3f => Ok(EventCode::Exit),
            _ => anyhow::bail!("Invalid event code {}", value),
        }
    }
}

fn crc32(bytes: &[u8]) -> u32 {
    let mut hasher = Hasher::new();
    hasher.update(bytes);
    hasher.finalize()
}
fn compress(bytes: &[u8], write: impl std::io::Write) -> io::Result<()> {
    zstd::stream::copy_encode(bytes, write, 3)
}
fn decompress(bytes: &[u8], write: impl std::io::Write) -> io::Result<()> {
    zstd::stream::copy_decode(bytes, write)
}

#[derive(Debug, PartialEq)]
pub struct MessageFrame {
    pub event_code: EventCode,
    pub operation_id: u32,
    pub is_response: bool,
    pub bytes: Vec<u8>,
}

pub async fn send_message_frame<W>(
    writer: &mut W,
    event_code: EventCode,
    operation_id: u32,
    is_response: bool,
    bytes: &[u8],
) -> anyhow::Result<()>
where
    W: AsyncWrite + Unpin,
{
    let mut head = [0; 16];

    // 1. Magic Code
    head[0..3].copy_from_slice(&MAGIC);

    // 2. Flags
    let mut flags: u8 = 0;
    let mut is_compressed = false;
    if cfg!(feature = "compression") && bytes.len() > 8096 && event_code == EventCode::Data {
        flags |= 0b1000_0000;
        is_compressed = true;
    }
    if is_response {
        flags |= 0b0100_0000;
    }
    flags |= event_code as u8;
    head[3] = flags;

    // 3. Operation Code - 使用大端序（Big Endian）
    head[4..8].copy_from_slice(&operation_id.to_be_bytes());

    // 4. Payload Length
    let mut compressed_bytes = Vec::with_capacity(bytes.len());
    if is_compressed {
        compress(bytes, &mut compressed_bytes).map_err(|err| {
            tracing::error!("无法压缩数据包，原因: {}", err);
            err
        })?;
    } else {
        compressed_bytes.extend_from_slice(bytes);
    }
    head[8..12].copy_from_slice(&(compressed_bytes.len() as u32).to_be_bytes());

    // 5. Payload Checksum
    let checksum = crc32(&compressed_bytes);
    head[12..16].copy_from_slice(&checksum.to_be_bytes());

    let mut bufs: &mut [_] = &mut [IoSlice::new(&head), IoSlice::new(&compressed_bytes)];
    IoSlice::advance_slices(&mut bufs, 0);
    while !bufs.is_empty() {
        match writer.write_vectored(bufs).await {
            Ok(0) => {
                tracing::error!("无法写入消息帧, 原因: 写入 0 字节");
                anyhow::bail!("Failed to send binary packet")
            }
            Ok(n) => IoSlice::advance_slices(&mut bufs, n),
            Err(ref e) if e.kind() == io::ErrorKind::Interrupted => {
                tracing::warn!("写入消息帧被中断, 原因: {}", e);
            }
            Err(e) => {
                tracing::error!("无法写入消息帧，原因: {}", e);
                return Err(e.into());
            }
        }
    }
    Ok(())
}

pub async fn read_message_frame<R>(reader: &mut R) -> io::Result<Option<MessageFrame>>
where
    R: AsyncRead + AsyncBufRead + Unpin,
{
    let mut buffer = Vec::new();
    let bytes_read = reader
        .read_until_with(find_packet_boundary, 0, &mut buffer)
        .await
        .map_err(|err| {
            tracing::error!("无法读取消息帧，原因: {}", err);
            err
        })?;

    if bytes_read == 0 {
        tracing::debug!("读取到 EOF");
        return Ok(None);
    }
    match find_subarray(&MAGIC, &buffer) {
        Some(pos) if pos > 0 => {
            let unexpected = String::from_utf8_lossy(&buffer[..pos]);
            tracing::warn!(unexpected_content = ?unexpected, "接收到意外输出");
            // 移除识别码前的数据
            buffer.drain(..pos);
        }
        Some(_) => (),
        // 没有找到则表示是文本输出因为匹配到 '\n' 而进入的
        None => {
            let unexpected = String::from_utf8_lossy(&buffer);
            tracing::warn!(unexpected_content = ?unexpected,"接收到意外输出");
            return Ok(None);
        }
    };

    // 读取剩下的 13 bytes 数据, read_until_with 能保证只有 Magic
    assert_eq!(buffer.len(), 3, "意外错误，read_until_with 出现致命错误");
    {
        // 上面的读取能保证至少有 3 个字节，因为能匹配 PACKET_NUMBER 至少都是 3 字节
        // 但是实际 buffer 中可能有更多的数据，此处读取 13 字节保证头一定是完整的
        let mut temp = [0; 13];
        reader.read_exact(&mut temp).await?;
        buffer.extend_from_slice(&temp);
    }

    // 处理数据包头部
    let head = &buffer[..16];
    let flags = head[3];
    let is_compressed = flags & 0b1000_0000 != 0;
    let is_response = flags & 0b0100_0000 != 0;
    let event_code = match EventCode::try_from(flags & 0b0011_1111) {
        Ok(event_code) => event_code,
        Err(err) => {
            tracing::warn!("无法解析事件码: {}", err);
            return Ok(None);
        }
    };

    let operation_id = u32::from_be_bytes((&head[4..8]).try_into().map_err(|err| {
        tracing::error!("无法解析消息帧操作码，原因: {err}");
        io::Error::new(io::ErrorKind::InvalidData, "Invalid operation id")
    })?);
    let payload_len = u32::from_be_bytes((&head[8..12]).try_into().map_err(|err| {
        tracing::error!("无法解析消息帧载荷长度，原因: {err}");
        io::Error::new(io::ErrorKind::InvalidData, "Invalid payload length")
    })?);
    let checksum = u32::from_be_bytes((&head[12..16]).try_into().map_err(|err| {
        tracing::error!("无法解析消息帧校验和，原因: {err}");
        io::Error::new(io::ErrorKind::InvalidData, "Invalid checksum")
    })?);
    // 移除 head 数据
    buffer.drain(..16);

    let payload = {
        let mut temp = vec![0; payload_len as usize - buffer.len()];
        reader.read_exact(&mut temp).await?;
        buffer.extend_from_slice(&temp);
        &buffer[..]
    };
    let actual_checksum = crc32(payload);
    if actual_checksum != checksum {
        tracing::error!(
            "消息帧的校验和不匹配，数据可能已损坏。Expected: {}, Got: {}",
            checksum,
            actual_checksum
        );
        return Err(io::Error::new(
            io::ErrorKind::InvalidData,
            "checksum mismatch",
        ));
    }
    let decompressed_bytes = if is_compressed {
        let mut decompressed = Vec::new();
        decompress(payload, &mut decompressed).map_err(|err| {
            tracing::error!("无法解压数据包，原因: {}", err);
            err
        })?;
        decompressed
    } else {
        payload.to_vec()
    };
    buffer.drain(..payload.len());
    Ok(Some(MessageFrame {
        event_code,
        operation_id,
        is_response,
        bytes: decompressed_bytes,
    }))
}

fn find_packet_boundary(matched_len: &mut usize, haystack: &[u8]) -> Option<usize> {
    let needle = &MAGIC;
    let needle_len = needle.len();
    for (i, &byte) in haystack.iter().enumerate() {
        // 正常顺序匹配
        if byte == needle[*matched_len] {
            *matched_len += 1;
            if *matched_len == needle_len {
                return Some(i + 1);
            }
        }
        // 是换行符
        else if byte == b'\n' {
            *matched_len = 0;
            return Some(i + 1);
        }
        // 匹配中断
        else if *matched_len > 0 {
            *matched_len = if byte == needle[0] { 1 } else { 0 }
        }
    }
    None
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::{
        io::Cursor,
        sync::atomic::{AtomicBool, Ordering},
    };
    use tokio::io::{AsyncRead, AsyncWrite, BufReader};

    // 创建一个简单的测试用异步读写器
    struct TestStream {
        data: Cursor<Vec<u8>>,
    }

    impl TestStream {
        fn new(data: Vec<u8>) -> Self {
            Self {
                data: Cursor::new(data),
            }
        }

        fn into_inner(self) -> Vec<u8> {
            self.data.into_inner()
        }

        #[allow(dead_code)]
        fn with_capacity(capacity: usize) -> Self {
            Self {
                data: Cursor::new(Vec::with_capacity(capacity)),
            }
        }
    }

    impl AsyncRead for TestStream {
        fn poll_read(
            mut self: std::pin::Pin<&mut Self>,
            cx: &mut std::task::Context<'_>,
            buf: &mut io::ReadBuf<'_>,
        ) -> std::task::Poll<io::Result<()>> {
            std::pin::Pin::new(&mut self.data).poll_read(cx, buf)
        }
    }

    impl AsyncWrite for TestStream {
        fn poll_write(
            mut self: std::pin::Pin<&mut Self>,
            cx: &mut std::task::Context<'_>,
            buf: &[u8],
        ) -> std::task::Poll<Result<usize, io::Error>> {
            std::pin::Pin::new(&mut self.data).poll_write(cx, buf)
        }

        fn poll_flush(
            mut self: std::pin::Pin<&mut Self>,
            cx: &mut std::task::Context<'_>,
        ) -> std::task::Poll<Result<(), io::Error>> {
            std::pin::Pin::new(&mut self.data).poll_flush(cx)
        }

        fn poll_shutdown(
            mut self: std::pin::Pin<&mut Self>,
            cx: &mut std::task::Context<'_>,
        ) -> std::task::Poll<Result<(), io::Error>> {
            std::pin::Pin::new(&mut self.data).poll_shutdown(cx)
        }
    }

    static INITIALIZED: AtomicBool = AtomicBool::new(false);
    fn init_tracing() {
        if INITIALIZED.load(Ordering::Relaxed) {
            return;
        }
        INITIALIZED.store(true, Ordering::Relaxed);
        let _ = tracing_subscriber::fmt::try_init();
    }

    #[tokio::test]
    async fn test_send_and_read_message_frame() {
        let test_data = b"Hello, World!";
        let mut writer = TestStream::new(Vec::new());

        // 发送消息帧
        let result =
            send_message_frame(&mut writer, EventCode::Data, 12345, false, test_data).await;

        assert!(result.is_ok(), "发送消息帧失败: {:?}", result.err());

        // 获取写入的数据
        let written_data = writer.into_inner();
        assert!(!written_data.is_empty(), "没有数据被写入");

        // 验证魔数
        assert_eq!(&written_data[0..3], &MAGIC, "魔数不匹配");

        // 创建读取器并读取消息帧
        let reader = TestStream::new(written_data);
        let mut buf_reader = BufReader::new(reader);
        let read_result = read_message_frame(&mut buf_reader).await;

        assert!(
            read_result.is_ok(),
            "读取消息帧失败: {:?}",
            read_result.err()
        );
        let message_frame = read_result.unwrap().unwrap();

        // 验证消息帧的各个字段
        assert_eq!(message_frame.event_code, EventCode::Data);
        assert_eq!(message_frame.operation_id, 12345);
        assert_eq!(message_frame.is_response, false);
        assert_eq!(message_frame.bytes, test_data, "读取的数据与原始数据不匹配");
    }

    #[tokio::test]
    async fn test_event_code_conversion() {
        // 测试有效的事件码转换
        assert_eq!(EventCode::try_from(0x01).unwrap(), EventCode::Data);
        assert_eq!(EventCode::try_from(0x02).unwrap(), EventCode::Binary);
        assert_eq!(EventCode::try_from(0x30).unwrap(), EventCode::Reserved);
        assert_eq!(EventCode::try_from(0x31).unwrap(), EventCode::Flush);
        assert_eq!(EventCode::try_from(0x3f).unwrap(), EventCode::Exit);

        // 测试无效的事件码
        assert!(EventCode::try_from(0x99).is_err());
    }

    #[tokio::test]
    async fn test_crc32_checksum() {
        let data1 = b"test data";
        let data2 = b"test data";
        let data3 = b"different data";

        // 相同数据应该产生相同的校验和
        assert_eq!(crc32(data1), crc32(data2));

        // 不同数据应该产生不同的校验和
        assert_ne!(crc32(data1), crc32(data3));
    }

    #[tokio::test]
    async fn test_read_message_frame_with_console_output() {
        // 创建一个包含控制台输出和消息帧的数据流
        let test_data = b"Test payload";
        let mut frame_data = Vec::new();

        // 先添加一些控制台输出
        frame_data.extend_from_slice(b"Some console output\n");

        // 然后添加消息帧
        let mut message_frame_bytes = Vec::new();
        send_message_frame(
            &mut message_frame_bytes,
            EventCode::Flush,
            54321,
            true,
            test_data,
        )
        .await
        .unwrap();
        frame_data.extend_from_slice(&message_frame_bytes);

        // 读取消息帧
        let reader = TestStream::new(frame_data);
        let mut buf_reader = BufReader::new(reader);

        // 第一次读取会遇到控制台输出，返回 None
        let result1 = read_message_frame(&mut buf_reader).await;
        assert!(result1.is_ok());
        assert!(result1.unwrap().is_none()); // 控制台输出被跳过

        // 第二次读取应该得到消息帧
        let result2 = read_message_frame(&mut buf_reader).await;
        assert!(result2.is_ok());
        let message_frame = result2.unwrap().unwrap();
        assert_eq!(message_frame.event_code, EventCode::Flush);
        assert_eq!(message_frame.operation_id, 54321);
        assert_eq!(message_frame.is_response, true);
        assert_eq!(message_frame.bytes, test_data);
    }

    #[tokio::test]
    async fn test_read_message_frame_checksum_mismatch() {
        let test_data = b"Test data";
        let mut corrupted_data = Vec::new();
        send_message_frame(
            &mut corrupted_data,
            EventCode::Data,
            12345,
            false,
            test_data,
        )
        .await
        .unwrap();

        // 损坏校验和字段（最后4个字节的头部）
        if corrupted_data.len() >= 16 {
            corrupted_data[12] = 0xFF;
            corrupted_data[13] = 0xFF;
            corrupted_data[14] = 0xFF;
            corrupted_data[15] = 0xFF;
        }

        let reader = TestStream::new(corrupted_data);
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        assert!(result.is_err());
        let error = result.unwrap_err();
        assert_eq!(error.kind(), io::ErrorKind::InvalidData);
    }

    #[tokio::test]
    async fn test_read_message_frame_invalid_event_code() {
        // 手动构造一个包含无效事件码的消息帧
        let mut frame_data = Vec::new();

        // 魔数
        frame_data.extend_from_slice(&MAGIC);

        // 标志位（包含无效的事件码）
        frame_data.push(0x99); // 无效的事件码

        // 操作ID
        frame_data.extend_from_slice(&12345u32.to_be_bytes());

        // 载荷长度
        let payload = b"test";
        frame_data.extend_from_slice(&(payload.len() as u32).to_be_bytes());

        // 校验和
        let checksum = crc32(payload);
        frame_data.extend_from_slice(&checksum.to_be_bytes());

        // 载荷
        frame_data.extend_from_slice(payload);

        let reader = TestStream::new(frame_data);
        let mut buf_reader = BufReader::new(reader);

        // 无效事件码应该返回 None，然后因为没有更多数据而到达 EOF
        let result = read_message_frame(&mut buf_reader).await;

        // 第一次调用应该返回 Ok(None) 因为事件码无效
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());

        // 第二次调用应该因为 EOF 而出错
        let result2 = read_message_frame(&mut buf_reader).await;
        match result2 {
            Ok(None) => {} // 也可能返回 None 表示 EOF
            Err(e) => assert_eq!(e.kind(), io::ErrorKind::UnexpectedEof),
            Ok(Some(_)) => panic!("不应该读取到有效的消息帧"),
        }
    }

    #[tokio::test]
    async fn test_read_message_frame_eof() {
        // 测试空数据流 - 应该返回 Ok(None) 表示 EOF
        let reader = TestStream::new(Vec::new());
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        // EOF 情况应该返回 Ok(None)，不是错误
        match result {
            Ok(None) => {} // 这是预期的行为
            Ok(Some(_)) => panic!("不应该从空流中读取到消息帧"),
            Err(_) => {
                // read_until_with 在遇到 EOF 时可能返回错误，这也是可接受的
                // 因为实际的 read_message_frame 实现在 EOF 时确实可能返回错误
            }
        }
    }

    #[tokio::test]
    async fn test_read_message_frame_incomplete_header() {
        // 测试不完整的头部数据
        let mut incomplete_data = Vec::new();
        incomplete_data.extend_from_slice(&MAGIC);
        incomplete_data.push(0x01); // 只有部分头部

        let reader = TestStream::new(incomplete_data);
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        assert!(result.is_err());
        // 应该是UnexpectedEof，因为无法读取完整的头部
        assert_eq!(result.unwrap_err().kind(), io::ErrorKind::UnexpectedEof);
    }

    #[test]
    fn test_find_packet_boundary() {
        let mut matched_len = 0;

        // 测试找到完整的魔数
        let data_with_magic = [0x10, 0xAA, 0xFF, 0x01, 0x02];
        let result = find_packet_boundary(&mut matched_len, &data_with_magic);
        assert_eq!(result, Some(3));

        // 重置状态
        matched_len = 0;

        // 测试找到换行符
        let data_with_newline = [0x48, 0x65, 0x6C, 0x6C, 0x6F, b'\n', 0x10];
        let result = find_packet_boundary(&mut matched_len, &data_with_newline);
        assert_eq!(result, Some(6));

        // 重置状态
        matched_len = 0;

        // 测试没有找到边界
        let data_no_boundary = [0x01, 0x02, 0x03, 0x04];
        let result = find_packet_boundary(&mut matched_len, &data_no_boundary);
        assert_eq!(result, None);

        // 测试部分匹配后重置
        matched_len = 0;
        let data_partial_match = [0x10, 0xAA, 0x00, 0x10, 0xAA, 0xFF];
        let result = find_packet_boundary(&mut matched_len, &data_partial_match);
        assert_eq!(result, Some(6));
    }

    #[cfg(feature = "compression")]
    #[tokio::test]
    async fn test_read_compressed_message_frame() {
        let _ = tracing_subscriber::fmt::try_init();
        // 创建一个大于8096字节的数据以触发压缩（根据代码中的实际阈值）
        let large_data = vec![0x42u8; 10000];
        let mut buffer = Vec::new();

        // 直接使用Vec作为writer
        let result =
            send_message_frame(&mut buffer, EventCode::Data, 99999, false, &large_data).await;
        assert!(result.is_ok(), "发送压缩消息帧失败: {:?}", result.err());

        // 验证压缩标志位被设置
        assert_eq!(buffer[3] & 0b1000_0000, 0b1000_0000, "压缩标志位应该被设置");

        let reader = TestStream::new(buffer);
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        assert!(result.is_ok());
        let message_frame = result.unwrap().unwrap();
        assert_eq!(message_frame.event_code, EventCode::Data);
        assert_eq!(message_frame.operation_id, 99999);
        assert_eq!(message_frame.is_response, false);
        assert_eq!(message_frame.bytes, large_data);
    }

    #[tokio::test]
    async fn test_read_response_flag() {
        let test_data = b"Response test";

        // 测试响应标志为true
        let mut frame_bytes = Vec::new();
        send_message_frame(&mut frame_bytes, EventCode::Data, 2001, true, test_data)
            .await
            .unwrap();

        // 验证响应标志位被设置
        assert_eq!(
            frame_bytes[3] & 0b0100_0000,
            0b0100_0000,
            "响应标志位应该被设置"
        );

        let reader = TestStream::new(frame_bytes);
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        assert!(result.is_ok());
        let message_frame = result.unwrap().unwrap();
        assert_eq!(message_frame.is_response, true);

        // 测试响应标志为false
        let mut frame_bytes = Vec::new();
        send_message_frame(&mut frame_bytes, EventCode::Data, 2002, false, test_data)
            .await
            .unwrap();

        // 验证响应标志位未被设置
        assert_eq!(frame_bytes[3] & 0b0100_0000, 0, "响应标志位不应该被设置");

        let reader = TestStream::new(frame_bytes);
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        assert!(result.is_ok());
        let message_frame = result.unwrap().unwrap();
        assert_eq!(message_frame.is_response, false);
    }

    #[tokio::test]
    async fn test_read_empty_payload() {
        init_tracing();
        // 测试空载荷
        let empty_data = b"";
        let mut frame_bytes = Vec::new();

        send_message_frame(&mut frame_bytes, EventCode::Flush, 3001, false, empty_data)
            .await
            .unwrap();
        let reader = TestStream::new(frame_bytes);
        let mut buf_reader = BufReader::new(reader);
        let result = read_message_frame(&mut buf_reader).await;

        assert!(result.is_ok());
        let message_frame = result.unwrap().unwrap();
        assert_eq!(message_frame.event_code, EventCode::Flush);
        assert_eq!(message_frame.operation_id, 3001);
        assert_eq!(message_frame.bytes.len(), 0);
    }

    #[tokio::test]
    async fn test_read_message_frame_with_mixed_console_output() {
        init_tracing();
        // 测试混合控制台输出的复杂场景
        let test_data = b"Important data";
        let mut complex_data = Vec::new();

        // 添加多行控制台输出
        complex_data.extend_from_slice(b"Starting process...\n");
        complex_data.extend_from_slice(b"Loading configuration\n");
        complex_data.extend_from_slice(b"Debug: some debug info\n");

        // 添加消息帧
        let mut message_frame_bytes = Vec::new();
        send_message_frame(
            &mut message_frame_bytes,
            EventCode::Exit,
            4001,
            true,
            test_data,
        )
        .await
        .unwrap();
        complex_data.extend_from_slice(&message_frame_bytes);

        // 再添加一些控制台输出
        complex_data.extend_from_slice(b"\nProcess completed\n");

        let reader = TestStream::new(complex_data);
        let mut buf_reader = BufReader::new(reader);
        let mut text_count = 0;
        let mut frame_count = 0;
        // 限制循环次数避免无限循环
        let mut attempts = 0;
        const MAX_ATTEMPTS: usize = 10;

        while attempts < MAX_ATTEMPTS {
            match read_message_frame(&mut buf_reader).await {
                Ok(Some(message_frame)) => {
                    assert_eq!(message_frame.event_code, EventCode::Exit);
                    assert_eq!(message_frame.operation_id, 4001);
                    assert_eq!(message_frame.is_response, true);
                    assert_eq!(message_frame.bytes, test_data);
                    frame_count += 1;
                }
                Ok(None) => {
                    text_count += 1;
                }
                Err(_) => {
                    // EOF 或其他错误，退出循环
                    break;
                }
            }
            attempts += 1;
        }

        assert_eq!(frame_count, 1, "应该有 1 次读取到消息帧");
        assert!(
            text_count >= 4,
            "应该至少有 4 次读取到控制台输出，实际: {}",
            text_count
        );
    }
}
