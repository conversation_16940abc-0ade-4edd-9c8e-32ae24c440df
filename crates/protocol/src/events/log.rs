use std::fmt::Display;

use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StructuredLogEntry {
    pub timestamp: u64,
    pub level: LogLevel,
    pub module: String,
    pub target: String,
    pub message: String,
    pub source: LogSource,
    pub trace_id: Option<uuid::Uuid>,
    pub span_id: Option<uuid::Uuid>,
    pub file: Option<String>,
    pub line: Option<u32>,
    pub fields: std::collections::HashMap<String, String>,
}

impl StructuredLogEntry {
    pub fn new(level: LogLevel, module: &str, target: &str, message: &str) -> Self {
        Self {
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis() as u64,
            level: level,
            module: module.to_string(),
            target: target.to_string(),
            message: message.to_string(),
            source: Default::default(),
            trace_id: None,
            span_id: None,
            file: None,
            line: None,
            fields: std::collections::HashMap::new(),
        }
    }

    pub fn with_source(mut self, source: LogSource) -> Self {
        self.source = source;
        self
    }

    pub fn with_file_info(mut self, file: &str, line: u32) -> Self {
        self.file = Some(file.to_string());
        self.line = Some(line);
        self
    }
    pub fn with_span_id(mut self, span_id: uuid::Uuid) -> Self {
        self.span_id = Some(span_id);
        self
    }
    pub fn with_trace_id(mut self, trace_id: uuid::Uuid) -> Self {
        self.trace_id = Some(trace_id);
        self
    }

    pub fn with_field(mut self, key: String, value: String) -> Self {
        self.fields.insert(key, value);
        self
    }
    pub fn with_fields(mut self, fields: std::collections::HashMap<String, String>) -> Self {
        self.fields = fields;
        self
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

impl LogLevel {
    pub fn as_str(&self) -> &'static str {
        match self {
            LogLevel::Error => "ERROR",
            LogLevel::Warn => "WARN",
            LogLevel::Info => "INFO",
            LogLevel::Debug => "DEBUG",
            LogLevel::Trace => "TRACE",
        }
    }
    pub fn from_str(level_str: &str) -> Self {
        match level_str.to_uppercase().as_str() {
            "ERROR" | "FATAL" => Self::Error,
            "WARN" | "WARNING" => Self::Warn,
            "DEBUG" => Self::Debug,
            "TRACE" => Self::Trace,
            _ => Self::Info,
        }
    }
}

impl From<tracing::Level> for LogLevel {
    fn from(value: tracing::Level) -> Self {
        match value {
            tracing::Level::ERROR => Self::Error,
            tracing::Level::WARN => Self::Warn,
            tracing::Level::INFO => Self::Info,
            tracing::Level::DEBUG => Self::Debug,
            tracing::Level::TRACE => Self::Trace,
        }
    }
}

impl Display for LogLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.write_str(self.as_str())
    }
}

/// 日志来源枚举，强制约束 source 类型，避免滥用
#[derive(
    Debug, Clone, Copy, Default, PartialEq, Eq, Hash, serde::Serialize, serde::Deserialize,
)]
pub enum LogSource {
    /// 核心业务逻辑
    #[default]
    Domain,
    /// 守护进程服务  
    Agent,
    /// 桌面适配器（Tauri）
    Framework,
    /// 桌面（Vue）
    Desktop,
    /// 容器运行时
    Runtime,
    /// 网络组件
    Network,
    /// 任务调度器
    Scheduler,
    /// 服务
    Service,
}

impl LogSource {
    /// 获取字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            LogSource::Domain => "domain",
            LogSource::Agent => "daemon",
            LogSource::Framework => "framework",
            LogSource::Desktop => "desktop",
            LogSource::Runtime => "runtime",
            LogSource::Network => "network",
            LogSource::Scheduler => "scheduler",
            LogSource::Service => "service",
        }
    }

    /// 根据 target 和其他信息推断 source
    pub fn from_target(target: &str, explicit_source: Option<&str>) -> LogSource {
        // 如果明确指定了 source，优先使用
        if let Some(source) = explicit_source {
            return source.parse().unwrap_or(LogSource::Domain);
        }

        // 根据 target 推断 source
        if target.starts_with("echowave_client::agent") || target.contains("agent") {
            LogSource::Agent
        } else if target.starts_with("echowave_client::framework") || target.contains("tauri") {
            LogSource::Framework
        } else if target.contains("vue") || target.contains("frontend") || target.contains("react")
        {
            LogSource::Desktop
        } else if target.contains("dockerd") {
            LogSource::Runtime
        } else if target.contains("tailscaled") {
            LogSource::Network
        } else if target.contains("nomad") {
            LogSource::Scheduler
        } else if target.starts_with("echowave_client::core")
            || target.starts_with("echowave_client::domain")
        {
            LogSource::Domain
        } else {
            // 默认 source
            LogSource::Domain
        }
    }
}

impl std::fmt::Display for LogSource {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

impl std::str::FromStr for LogSource {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> anyhow::Result<Self> {
        match s {
            "domain" => Ok(LogSource::Domain),
            "daemon" => Ok(LogSource::Agent),
            "framework" => Ok(LogSource::Framework),
            "desktop" => Ok(LogSource::Desktop),
            "runtime" => Ok(LogSource::Runtime),
            "network" => Ok(LogSource::Network),
            "scheduler" => Ok(LogSource::Scheduler),
            // 兼容旧的命名方式
            "agent" => Ok(LogSource::Agent),
            "ui-tauri" => Ok(LogSource::Framework),
            "tauri" => Ok(LogSource::Framework),
            "ui-vue" => Ok(LogSource::Desktop),
            "frontend" => Ok(LogSource::Desktop),
            "vue" => Ok(LogSource::Desktop),
            "react" => Ok(LogSource::Desktop),
            "docker" => Ok(LogSource::Runtime),
            "tailscale" => Ok(LogSource::Network),
            "nomad" => Ok(LogSource::Scheduler),
            _ => Err(anyhow::anyhow!("不支持的日志来源: {}", s)),
        }
    }
}
