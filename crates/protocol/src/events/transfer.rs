use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 二进制传输事件（控制消息）
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum TransferEvent {
    /// 开始文件传输
    StartTransfer {
        trace_id: Uuid,
        transfer_id: u32,
        file_size: u64,
        file_hash: String, // SHA256 hex string
        total_chunks: u32,
        chunk_size: u32,
    },

    /// 确认收到数据块
    ChunkReceived {
        trace_id: Uuid,
        transfer_id: u32,
        chunk_id: u32,
    },

    /// 传输完成
    TransferCompleted {
        trace_id: Uuid,
        transfer_id: u32,
        success: bool,
        error_message: Option<String>,
    },

    /// 请求重发数据块
    RequestRetransmission {
        trace_id: Uuid,
        transfer_id: u32,
        chunk_ids: Vec<u32>,
    },
}

impl TransferEvent {
    pub fn event_name(&self) -> &'static str {
        match self {
            TransferEvent::StartTransfer { .. } => "start_transfer",
            TransferEvent::ChunkReceived { .. } => "chunk_received",
            TransferEvent::TransferCompleted { .. } => "transfer_completed",
            TransferEvent::RequestRetransmission { .. } => "request_retransmission",
        }
    }

    pub fn trace_id(&self) -> Uuid {
        match self {
            TransferEvent::StartTransfer { trace_id, .. } => *trace_id,
            TransferEvent::ChunkReceived { trace_id, .. } => *trace_id,
            TransferEvent::TransferCompleted { trace_id, .. } => *trace_id,
            TransferEvent::RequestRetransmission { trace_id, .. } => *trace_id,
        }
    }

    pub fn transfer_id(&self) -> u32 {
        match self {
            TransferEvent::StartTransfer { transfer_id, .. } => *transfer_id,
            TransferEvent::ChunkReceived { transfer_id, .. } => *transfer_id,
            TransferEvent::TransferCompleted { transfer_id, .. } => *transfer_id,
            TransferEvent::RequestRetransmission { transfer_id, .. } => *transfer_id,
        }
    }
}

/// 数据块结构（二进制消息）
/// 使用 EventCode::Chunk + 二进制序列化发送
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkData<'a> {
    /// 传输会话ID
    pub transfer_id: u32,
    /// 数据块序号（从0开始）
    pub chunk_id: u32,
    /// 实际数据
    pub data: &'a [u8],
}

impl<'a> ChunkData<'a> {
    /// 创建新的数据块
    pub fn new(transfer_id: u32, chunk_id: u32, data: &'a [u8]) -> Self {
        Self {
            transfer_id,
            chunk_id,
            data,
        }
    }

    /// 获取数据块大小
    pub fn size(&self) -> usize {
        self.data.len()
    }

    /// 序列化为字节数组
    pub fn to_bytes(&self) -> Vec<u8> {
        // [transfer_id:u32][chunk_id:u32][data:u8xN]
        let mut bytes = Vec::with_capacity(self.data.len() + 8);
        bytes.extend_from_slice(&self.transfer_id.to_be_bytes());
        bytes.extend_from_slice(&self.chunk_id.to_be_bytes());
        bytes.extend_from_slice(self.data);
        bytes
    }

    /// 从字节数组反序列化
    pub fn from_bytes(bytes: &'a [u8]) -> anyhow::Result<Self> {
        if bytes.len() < 8 {
            anyhow::bail!("数据块长度不足，无法解析头部信息");
        }
        let transfer_id = u32::from_be_bytes(bytes[0..4].try_into().unwrap());
        let chunk_id = u32::from_be_bytes(bytes[4..8].try_into().unwrap());
        let data = &bytes[8..];
        Ok(Self {
            transfer_id,
            chunk_id,
            data,
        })
    }
}

/// 支持的分块大小常量
pub mod chunk_sizes {
    use serde::{Deserialize, Serialize};

    #[derive(Debug, Clone, Copy, Default, PartialEq, Eq, Serialize, Deserialize)]
    pub enum ChunkSize {
        Kb8 = 8 * 1024,
        #[default]
        Kb16 = 16 * 1024,
        Kb32 = 32 * 1024,
        Kb64 = 64 * 1024,
    }

    impl From<ChunkSize> for u32 {
        fn from(val: ChunkSize) -> Self {
            val as u32
        }
    }

    impl TryFrom<u32> for ChunkSize {
        type Error = anyhow::Error;

        fn try_from(value: u32) -> Result<Self, Self::Error> {
            match value {
                8192 => Ok(ChunkSize::Kb8),
                16384 => Ok(ChunkSize::Kb16),
                32768 => Ok(ChunkSize::Kb32),
                65536 => Ok(ChunkSize::Kb64),
                _ => anyhow::bail!("不支持的分块大小: {}", value),
            }
        }
    }
}
