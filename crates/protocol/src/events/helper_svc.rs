use super::log::StructuredLogEntry;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HelperSvcEvent {
    // === Core -> HelperSvc ===
    // 检查 Windows 功能状态
    CheckWindowsFeature {
        trace_id: Uuid,
        feature_name: String,
    },
    // 启用 Windows 功能
    EnableWindowsFeature {
        trace_id: Uuid,
        feature_name: String,
    },
    //
    GetAllWindowsFeatures {
        trace_id: Uuid,
    },
    // 检查虚拟化支持
    CheckVirtualization {
        trace_id: Uuid,
    },
    // 安装更新
    InstallUpdate {
        trace_id: Uuid,
        update_path: String,
    },
    // 回滚更新
    RollbackUpdate {
        trace_id: Uuid,
        backup_path: String,
    },
    // 重启系统
    RestartSystem {
        trace_id: Uuid,
        delay_seconds: u32,
    },
    // 日志转发事件
    LogForward(StructuredLogEntry),
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum HelperSvcResponse {
    Success {
        trace_id: Uuid,
        data: Option<serde_json::Value>,
    },
    Error {
        trace_id: Uuid,
        code: u32,
        message: String,
    },
    WindowsFeatureStatus {
        trace_id: Uuid,
        feature_name: String,
        status: FeatureStatus,
        restart_required: bool,
    },
    VirtualizationInfo {
        trace_id: Uuid,
        supported: bool,
        enabled: bool,
        details: VirtualizationDetails,
    },
    UpdateInstallResult {
        trace_id: Uuid,
        success: bool,
        message: String,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum FeatureStatus {
    Enabled,
    Disabled,
    Unknown,
    NotAvailable,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VirtualizationDetails {
    pub hyper_v_available: bool,
    pub wsl_available: bool,
    pub virtualization_enabled_in_firmware: bool,
    pub data_execution_prevention_available: bool,
    pub vm_monitor_mode_extensions: bool,
    pub second_level_address_translation: bool,
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;

    #[test]
    fn test_helper_event_serialization() {
        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::CheckVirtualization { trace_id };

        let json = serde_json::to_string(&event).unwrap();
        let deserialized: HelperSvcEvent = serde_json::from_str(&json).unwrap();

        match deserialized {
            HelperSvcEvent::CheckVirtualization { trace_id: t } => {
                assert_eq!(t, trace_id);
            }
            _ => panic!("Unexpected event type"),
        }
    }

    #[test]
    fn test_windows_feature_event() {
        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::EnableWindowsFeature {
            trace_id,
            feature_name: "Microsoft-Windows-Subsystem-Linux".to_string(),
        };

        let json = serde_json::to_string(&event).unwrap();
        assert!(json.contains("EnableWindowsFeature"));
        assert!(json.contains("Microsoft-Windows-Subsystem-Linux"));
    }

    #[test]
    fn test_helper_response_serialization() {
        let trace_id = Uuid::new_v4();
        let response = HelperSvcResponse::VirtualizationInfo {
            trace_id,
            supported: true,
            enabled: true,
            details: VirtualizationDetails {
                hyper_v_available: true,
                wsl_available: true,
                virtualization_enabled_in_firmware: true,
                data_execution_prevention_available: true,
                vm_monitor_mode_extensions: true,
                second_level_address_translation: true,
            },
        };

        let json = serde_json::to_string(&response).unwrap();
        let deserialized: HelperSvcResponse = serde_json::from_str(&json).unwrap();

        match deserialized {
            HelperSvcResponse::VirtualizationInfo {
                trace_id: t,
                supported,
                enabled,
                ..
            } => {
                assert_eq!(t, trace_id);
                assert!(supported);
                assert!(enabled);
            }
            _ => panic!("Unexpected response type"),
        }
    }

    #[test]
    fn test_feature_status_enum() {
        let statuses = vec![
            FeatureStatus::Enabled,
            FeatureStatus::Disabled,
            FeatureStatus::Unknown,
            FeatureStatus::NotAvailable,
        ];

        for status in statuses {
            let json = serde_json::to_string(&status).unwrap();
            let deserialized: FeatureStatus = serde_json::from_str(&json).unwrap();
            // 简单验证序列化往返成功
            let _ = serde_json::to_string(&deserialized).unwrap();
        }
    }
}
