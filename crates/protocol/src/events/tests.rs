//! 协议事件测试模块

use super::transfer::{ChunkData, chunk_sizes::ChunkSize};

#[cfg(test)]
mod transfer_tests {
    use super::*;

    #[test]
    fn test_chunk_data_serialization() {
        let data = b"Hello, World! This is test data for chunking.";
        let chunk = ChunkData {
            transfer_id: 12345,
            chunk_id: 67,
            data,
        };

        // 测试序列化
        let bytes = chunk.to_bytes();
        assert_eq!(bytes.len(), 8 + data.len());

        // 检查头部（前8字节）
        assert_eq!(&bytes[0..4], &12345u32.to_be_bytes());
        assert_eq!(&bytes[4..8], &67u32.to_be_bytes());
        
        // 检查数据部分
        assert_eq!(&bytes[8..], data);
    }

    #[test]
    fn test_chunk_data_deserialization() {
        let original_data = b"Test chunk data for deserialization";
        let transfer_id = 9876u32;
        let chunk_id = 543u32;

        // 手动构造字节数组
        let mut bytes = Vec::new();
        bytes.extend_from_slice(&transfer_id.to_be_bytes());
        bytes.extend_from_slice(&chunk_id.to_be_bytes());
        bytes.extend_from_slice(original_data);

        // 测试反序列化
        let chunk = ChunkData::from_bytes(&bytes).unwrap();
        assert_eq!(chunk.transfer_id, transfer_id);
        assert_eq!(chunk.chunk_id, chunk_id);
        assert_eq!(chunk.data, original_data);
    }

    #[test]
    fn test_chunk_data_roundtrip() {
        let original_data = b"Roundtrip test data";
        let original_chunk = ChunkData {
            transfer_id: 111,
            chunk_id: 222,
            data: original_data,
        };

        // 序列化然后反序列化
        let serialized = original_chunk.to_bytes();
        let deserialized = ChunkData::from_bytes(&serialized).unwrap();

        assert_eq!(deserialized.transfer_id, original_chunk.transfer_id);
        assert_eq!(deserialized.chunk_id, original_chunk.chunk_id);
        assert_eq!(deserialized.data, original_chunk.data);
    }

    #[test]
    fn test_chunk_data_from_bytes_error_cases() {
        // 测试字节数组太短的情况
        let short_bytes = vec![0u8; 7]; // 只有7字节，不足8字节头部
        let result = ChunkData::from_bytes(&short_bytes);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("数据块长度不足"));

        // 测试空字节数组
        let empty_bytes = vec![];
        let result = ChunkData::from_bytes(&empty_bytes);
        assert!(result.is_err());

        // 测试只有头部没有数据的情况
        let header_only = vec![0u8; 8];
        let result = ChunkData::from_bytes(&header_only);
        assert!(result.is_ok());
        let chunk = result.unwrap();
        assert_eq!(chunk.transfer_id, 0);
        assert_eq!(chunk.chunk_id, 0);
        assert_eq!(chunk.data.len(), 0);
    }

    #[test]
    fn test_chunk_data_large_data() {
        // 测试大数据块
        let large_data: Vec<u8> = (0..16384).map(|i| (i % 256) as u8).collect();
        let chunk = ChunkData {
            transfer_id: 999,
            chunk_id: 888,
            data: &large_data,
        };

        let serialized = chunk.to_bytes();
        let deserialized = ChunkData::from_bytes(&serialized).unwrap();

        assert_eq!(deserialized.transfer_id, 999);
        assert_eq!(deserialized.chunk_id, 888);
        assert_eq!(deserialized.data, large_data.as_slice());
    }

    #[test]
    fn test_chunk_size_conversion() {
        // 测试 ChunkSize 到 u32 的转换
        assert_eq!(u32::from(ChunkSize::Kb8), 8192);
        assert_eq!(u32::from(ChunkSize::Kb16), 16384);
        assert_eq!(u32::from(ChunkSize::Kb32), 32768);
        assert_eq!(u32::from(ChunkSize::Kb64), 65536);
    }

    #[test]
    fn test_chunk_size_try_from_u32() {
        // 测试从 u32 到 ChunkSize 的转换
        assert_eq!(ChunkSize::try_from(8192).unwrap(), ChunkSize::Kb8);
        assert_eq!(ChunkSize::try_from(16384).unwrap(), ChunkSize::Kb16);
        assert_eq!(ChunkSize::try_from(32768).unwrap(), ChunkSize::Kb32);
        assert_eq!(ChunkSize::try_from(65536).unwrap(), ChunkSize::Kb64);

        // 测试无效值
        assert!(ChunkSize::try_from(1024).is_err());
        assert!(ChunkSize::try_from(4096).is_err());
        assert!(ChunkSize::try_from(131072).is_err());
    }

    #[test]
    fn test_chunk_size_default() {
        // 测试默认值
        assert_eq!(ChunkSize::default(), ChunkSize::Kb16);
    }

    #[test]
    fn test_chunk_size_serde() {
        // 测试序列化和反序列化
        let chunk_size = ChunkSize::Kb32;
        let serialized = serde_json::to_string(&chunk_size).unwrap();
        let deserialized: ChunkSize = serde_json::from_str(&serialized).unwrap();
        assert_eq!(deserialized, chunk_size);
    }

    #[test]
    fn test_chunk_data_new() {
        let data = b"Test data";
        let chunk = ChunkData::new(100, 50, data);
        
        assert_eq!(chunk.transfer_id, 100);
        assert_eq!(chunk.chunk_id, 50);
        assert_eq!(chunk.data, data);
        assert_eq!(chunk.size(), data.len());
    }

    #[test]
    fn test_chunk_data_size() {
        let data = b"Size test data";
        let chunk = ChunkData::new(1, 1, data);
        assert_eq!(chunk.size(), data.len());
        
        let empty_chunk = ChunkData::new(1, 1, b"");
        assert_eq!(empty_chunk.size(), 0);
    }
}