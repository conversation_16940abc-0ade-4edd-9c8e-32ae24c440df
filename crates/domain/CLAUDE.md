# CLAUDE.md - 客户端核心模块 (crates/domain)

本文档为 Claude Code 提供 EchoWave 客户端核心模块的开发指南。

## 模块概述

EchoWave 客户端核心模块是整个应用程序的业务逻辑中心，负责：

- **状态管理**: 维护应用全局状态，通过状态投射机制向前端提供一致的状态视图
- **服务编排**: 协调所有业务服务的生命周期，包括用户、网络、系统检查、任务接单等服务
- **模块协调**: 通过适配器模式与其他模块（Agent、Helper、渲染层）进行通信
- **命令处理**: 接收并处理来自 UI 层的命令，执行相应的业务逻辑

## 架构设计

### 状态投射架构

核心模块采用状态投射（State Projection）架构，避免状态重复存储：

```text
┌─────────────────────────────────────────────────────────────┐
│                    前端 UI (Vue/Pinia)                      │
└─────────────────────────┬───────────────────────────────────┘
                          │ 异步消息通道 (平台无关)
┌─────────────────────────▼───────────────────────────────────┐
│                      StateProjection                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   状态投射接口   │ │   跨服务计算     │ │   消息通道管理   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│              业务服务层 (Services)                           │
│  SystemCheck │ User │ Network │ Task │ Settings │ Update    │
│  (权威状态)  │(权威状态)│(权威状态)│(权威状态)│(权威状态)│    │
└─────────────────────────┬───────────────────────────────────┘
                          │
┌─────────────────────────▼───────────────────────────────────┐
│                    适配器层 (Adapters)                       │
│   Agent │ Helper │ HTTP │ File                              │
│   (WSL) │ (Win)  │ (API)│ (本地)                            │
└─────────────────────────────────────────────────────────────┘
```

### 核心概念

#### Context（上下文）

- 全局唯一的应用上下文，管理整个应用的生命周期
- 提供命令处理、事件分发、状态订阅等核心功能
- 通过 `create_context()` 创建，`destroy_context()` 销毁

#### Services（服务）

- **最小服务集**（应用启动时）：Settings、Device、User
- **完整服务集**（登录后）：Network、SystemCheck、Update、TaskAcceptance
- 每个服务维护自己的权威状态，作为单一数据源

#### Adapters（适配器）

- **AgentAdapter**: 与 WSL 中的 Agent 模块通信（MessageFrame 协议）
- **HelperAdapter**: 与 Windows Helper 服务通信（Named Pipe）
- **HttpAdapter**: 与服务端 API 通信（HTTP/REST）
- **FileAdapter**: 本地文件操作

## 目录结构

```
crates/domain/
├── src/
│   ├── lib.rs                 # 模块入口，导出公共 API
│   ├── config.rs              # 配置管理（环境配置、构建模式等）
│   ├── error.rs               # 错误定义和处理
│   ├── context/               # 核心上下文模块
│   │   ├── mod.rs            # 上下文主逻辑
│   │   ├── command.rs        # 命令定义和处理
│   │   ├── event.rs          # 事件定义和分发
│   │   ├── state.rs          # 状态投射机制
│   │   └── subscription.rs   # 订阅管理
│   ├── services/              # 业务服务模块
│   │   ├── mod.rs            # 服务管理器
│   │   ├── user_service.rs   # 用户服务
│   │   ├── network_service.rs # 网络服务
│   │   ├── settings_service.rs # 设置服务
│   │   ├── device_service.rs  # 设备服务
│   │   ├── update_service.rs  # 更新服务
│   │   ├── task_acceptance_service.rs # 任务接单服务
│   │   └── system_check/      # 系统检查服务
│   │       ├── mod.rs        # 服务主逻辑
│   │       ├── checks/       # 具体检查项
│   │       └── types.rs      # 类型定义
│   ├── adapters/              # 适配器层
│   │   ├── agent.rs          # Agent 通信适配器
│   │   ├── helper.rs         # Helper 通信适配器
│   │   ├── http.rs           # HTTP 客户端适配器
│   │   └── file.rs           # 文件操作适配器
│   └── utils/                 # 工具模块
│       ├── actor.rs          # Actor 模式实现
│       ├── command.rs        # 命令执行工具
│       ├── downloader.rs     # 下载管理
│       └── wsl_manager.rs    # WSL 管理工具
└── Cargo.toml                 # 依赖配置
```

## 开发指南

### 命令处理流程

1. UI 层通过 Tauri IPC 发送命令
2. Context 接收命令并路由到相应服务
3. 服务执行业务逻辑，更新内部状态
4. 状态变化通过 StateProjection 投射到前端
5. UI 层接收状态更新并刷新界面

示例：

```rust
// 命令定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CoreCommand {
    UserLogin { phone: String, token: String },
    StartAcceptingTasks,
    // ...
}

// 命令处理
pub async fn handle_command(&self, cmd: CoreCommand) -> CoreResult<CommandResponse> {
    match cmd {
        CoreCommand::UserLogin { phone, token } => {
            self.services.user.login(phone, token).await?;
            Ok(CommandResponse::Success)
        }
        // ...
    }
}
```

### 服务开发规范

所有服务必须实现 `CoreService` trait：

```rust
pub trait CoreService: Send + Sync {
    fn name(&self) -> &str;
    fn status(&self) -> ServiceStatus;
    fn start(&self) -> ServiceFuture<'_, ()>;
    fn stop(&self) -> ServiceFuture<'_, ()>;
    fn health_check(&self) -> ServiceFuture<'_, bool>;
}
```

推荐的实现模式：

```rust
impl CoreService for MyService {
    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }
}

impl MyService {
    async fn start_impl(&self) -> CoreResult<()> {
        // 实际的异步实现
        Ok(())
    }
}
```

### 状态管理

每个服务维护自己的状态，通过 `watch::channel` 发布状态变化：

```rust
pub struct UserService {
    state: Arc<RwLock<UserState>>,
    state_tx: watch::Sender<UserState>,
    // ...
}

impl UserService {
    pub async fn login(&self, phone: String, token: String) -> CoreResult<()> {
        // 更新内部状态
        let mut state = self.state.write().await;
        state.is_logged_in = true;
        state.phone = Some(phone);

        // 发布状态变化
        self.state_tx.send_modify(|s| *s = state.clone());
        Ok(())
    }
}
```

### 错误处理

使用统一的错误类型 `CoreError`：

```rust
use crate::{CoreError, CoreResult};

// 返回错误
fn some_operation() -> CoreResult<()> {
    if condition_failed {
        return Err(CoreError::service_error("操作失败"));
    }
    Ok(())
}

// 错误传播
async fn complex_operation() -> CoreResult<String> {
    let result = some_operation()?;
    Ok("成功".to_string())
}
```

### 日志规范

使用 `tracing` crate 记录日志，包含 trace_id：

```rust
tracing::info!(
    trace_id = %trace_id,
    phone = %phone,
    "用户登录请求"
);

tracing::error!(
    trace_id = %trace_id,
    error = %err,
    "登录失败"
);
```

### 适配器使用

与其他模块通信时使用相应的适配器：

```rust
// 与 Agent 通信
let response = self.agent_adapter
    .send_command(AgentCommand::StartTailscale, trace_id)
    .await?;

// 调用 API
let result = self.http_adapter
    .post("/api/node/enable", &request_body)
    .await?;

// 读写文件
let settings = self.file_adapter
    .read_json::<UserSettings>(&settings_path)
    .await?;
```

## 测试

### 单元测试

```bash
cargo test -p core                     # 运行所有测试
cargo test -p core test_name          # 运行特定测试
cargo test -p core -- --nocapture     # 显示测试输出
```

### 集成测试

```bash
cargo test -p core --test '*'         # 运行所有集成测试
```

### 调试

```bash
RUST_LOG=debug cargo run --example basic   # 运行示例并显示调试日志
RUST_LOG=trace cargo test                  # 测试时显示详细日志
```

## 重要注意事项

### 生命周期管理

1. **服务启动顺序**：Settings → Device → User → Network → SystemCheck → Update
2. **服务停止顺序**：与启动顺序相反
3. **任务接单服务**：仅在系统检查通过且用户已登录后启动

### 状态一致性

1. 服务内部状态是权威数据源
2. 状态变化必须通过 `state_tx` 发布
3. 前端状态仅是后端状态的投射，不应独立修改

### 错误处理原则

1. 服务内部错误不应导致整个应用崩溃
2. 使用 `CoreError` 统一错误类型
3. 记录详细的错误日志，包含 trace_id

### 性能优化

1. 使用 `Arc` 共享大对象，避免不必要的克隆
2. 异步操作使用 `tokio::spawn` 避免阻塞
3. 合理使用缓存，如 `LruCache` 缓存频繁访问的数据

## 常见问题

### Q: 如何添加新的命令？

A: 在 `context/command.rs` 中添加命令定义，然后在 `handle_command` 方法中添加处理逻辑。

### Q: 如何添加新的服务？

A:

1. 在 `services/` 目录创建新服务模块
2. 实现 `CoreService` trait
3. 在 `ServiceManager` 中添加服务实例
4. 更新服务启动/停止逻辑

### Q: 如何调试适配器通信？

A: 设置环境变量 `RUST_LOG=core::adapters=trace` 查看详细的通信日志。

### Q: 状态更新没有反映到前端？

A: 检查：

1. 服务是否正确发送了状态更新（`state_tx.send_modify`）
2. StateProjection 是否包含了该状态字段
3. 前端是否正确订阅了状态变化
