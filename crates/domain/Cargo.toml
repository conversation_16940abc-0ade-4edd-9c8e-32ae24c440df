[package]
name = "domain"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
uuid = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tokio = { workspace = true }
thiserror = { workspace = true }
reqwest = { version = "0.12", features = ["json", "stream"] }
futures-util = "0.3"
url = "2.5"
protocol = { path = "../protocol" }
chrono = { workspace = true }
tokio-util = "0.7.15"
sha2 = "0.10.9"
dirs = "5.0"
base64 = "0.22"
dashmap = "6.1.0"
zstd = { workspace = true }
colored = "3.0.0"
webbrowser = "1.0"
futures = "0.3.31"
regex = "1.11.1"
md5 = "0.8.0"
rmp-serde = "1.3.0"
lnk = "0.6.1"
shared = { path = "../shared" }
rand = "0.9.2"
sysinfo = {workspace = true}

[dev-dependencies]
tempfile = "3.8"
tracing-test = "0.2"

[target.'cfg(target_os="linux")'.dependencies]
libc = {workspace = true}

[target.'cfg(target_os="macos")'.dependencies]
libc = {workspace = true}

[target.'cfg(target_os="windows")'.dependencies]
windows = { version = "0.61.1", features = [
    "Win32_System_Threading", "Win32_System_SystemInformation",
    "Win32_Globalization","Win32_Storage_FileSystem", "Win32_System_IO", "Win32_System_SubsystemForLinux", "Win32_System_Console", "Win32_System_Pipes"] }
winreg = "0.55.0"
wmi = "0.17.2"
windows-service = "0.8.0"
