//! 文件日志轮转功能的核心测试
//!
//! 这个测试文件专注于验证日志轮转的核心功能和安全性

use serde::Serialize;
use std::sync::{
    Arc,
    atomic::{AtomicU64, Ordering},
};
use std::time::{Duration, Instant};
use tempfile::TempDir;
use uuid::Uuid;

// 简化的测试结构体，模拟StructuredLogEntry
#[derive(Debug, Clone, Serialize)]
struct TestLogEntry {
    pub level: String,
    pub message: String,
    pub trace_id: Option<Uuid>,
    pub source: Option<String>,
    pub timestamp: u64,
}

impl TestLogEntry {
    fn new(level: &str, message: &str) -> Self {
        Self {
            level: level.to_string(),
            message: message.to_string(),
            trace_id: Some(Uuid::new_v4()),
            source: Some("core".to_string()),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        }
    }
}

/// 测试上下文
struct RotationTestContext {
    temp_dir: TempDir,
    write_count: Arc<AtomicU64>,
}

impl RotationTestContext {
    fn new() -> Self {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        Self {
            temp_dir,
            write_count: Arc::new(AtomicU64::new(0)),
        }
    }

    /// 模拟日志写入操作
    fn write_log(&self, entry: TestLogEntry) -> Result<(), std::io::Error> {
        let current_date = chrono::Local::now().format("%Y-%m-%d").to_string();
        let log_file_path = self
            .temp_dir
            .path()
            .join(format!("core-{}.jsonl", current_date));

        let log_line = serde_json::to_string(&entry)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidData, e))?;

        std::fs::write(&log_file_path, format!("{}\n", log_line))?;
        self.write_count.fetch_add(1, Ordering::Relaxed);

        Ok(())
    }

    /// 模拟大文件轮转
    fn simulate_rotation(&self, file_size_mb: usize) -> Result<Duration, std::io::Error> {
        let start_time = Instant::now();

        // 创建大内容来模拟文件大小轮转
        let large_content = "x".repeat(file_size_mb * 1024 * 1024);
        let entry = TestLogEntry::new("info", &large_content);

        self.write_log(entry)?;

        // 模拟轮转操作
        let current_date = chrono::Local::now().format("%Y-%m-%d").to_string();
        let timestamp = chrono::Local::now().format("%H%M%S").to_string();
        let old_file = self
            .temp_dir
            .path()
            .join(format!("core-{}.jsonl", current_date));
        let new_file = self
            .temp_dir
            .path()
            .join(format!("core-{}-{}.jsonl", current_date, timestamp));

        if old_file.exists() {
            std::fs::rename(&old_file, &new_file)?;
        }

        Ok(start_time.elapsed())
    }

    /// 获取临时目录中的文件数量
    fn count_files(&self) -> usize {
        std::fs::read_dir(self.temp_dir.path())
            .unwrap()
            .filter_map(|entry| entry.ok())
            .filter(|entry| entry.file_name().to_string_lossy().contains("core-"))
            .count()
    }
}

/// 基础轮转功能测试
#[cfg(test)]
mod basic_rotation_tests {
    use super::*;

    #[test]
    fn test_basic_log_write() {
        let ctx = RotationTestContext::new();
        let entry = TestLogEntry::new("info", "测试消息");

        let result = ctx.write_log(entry);
        assert!(result.is_ok(), "基本日志写入应该成功");

        let write_count = ctx.write_count.load(Ordering::Relaxed);
        assert_eq!(write_count, 1, "写入计数应该为1");
    }

    #[test]
    fn test_rotation_performance() {
        let ctx = RotationTestContext::new();

        // 测试不同大小的轮转性能
        let sizes = [1, 5, 10]; // MB
        let mut results = Vec::new();

        for &size in &sizes {
            let duration = ctx.simulate_rotation(size);
            assert!(duration.is_ok(), "轮转操作应该成功");

            let elapsed = duration.unwrap();
            results.push((size, elapsed));

            // 轮转不应该超过合理时间
            assert!(
                elapsed < Duration::from_secs(5),
                "{}MB 文件轮转不应该超过5秒，实际耗时: {:?}",
                size,
                elapsed
            );
        }

        println!("轮转性能测试结果:");
        for (size, duration) in results {
            println!("  {}MB: {:?}", size, duration);
        }
    }

    #[test]
    fn test_multiple_rotations() {
        let ctx = RotationTestContext::new();

        // 执行多次轮转
        for i in 1..=5 {
            let result = ctx.simulate_rotation(1); // 1MB each
            assert!(result.is_ok(), "第{}次轮转应该成功", i);

            let elapsed = result.unwrap();
            assert!(
                elapsed < Duration::from_secs(2),
                "第{}次轮转耗时过长: {:?}",
                i,
                elapsed
            );
        }

        // 验证生成了多个文件
        let file_count = ctx.count_files();
        assert!(
            file_count >= 3,
            "应该生成多个轮转文件，实际: {}",
            file_count
        );
    }
}

/// 并发安全测试
#[cfg(test)]
mod concurrency_tests {
    use super::*;
    use std::thread;

    #[test]
    fn test_concurrent_writes() {
        let ctx = Arc::new(RotationTestContext::new());
        let mut handles = Vec::new();

        // 创建多个线程并发写入
        for thread_id in 0..5 {
            let ctx_clone = ctx.clone();

            let handle = thread::spawn(move || {
                for i in 0..10 {
                    let entry = TestLogEntry::new("info", &format!("线程{}-消息{}", thread_id, i));

                    let result = ctx_clone.write_log(entry);
                    assert!(result.is_ok(), "并发写入应该成功");
                }
            });

            handles.push(handle);
        }

        // 等待所有线程完成
        for handle in handles {
            handle.join().expect("线程应该正常完成");
        }

        let total_writes = ctx.write_count.load(Ordering::Relaxed);
        assert_eq!(total_writes, 50, "应该完成50次写入");
    }

    #[test]
    fn test_concurrent_rotation_safety() {
        let ctx = Arc::new(RotationTestContext::new());
        let mut handles = Vec::new();

        // 创建多个线程并发执行轮转
        for thread_id in 0..3 {
            let ctx_clone = ctx.clone();

            let handle = thread::spawn(move || {
                for i in 0..3 {
                    let start = Instant::now();
                    let result = ctx_clone.simulate_rotation(1);
                    let elapsed = start.elapsed();

                    assert!(result.is_ok(), "线程{}轮转{}应该成功", thread_id, i);
                    assert!(
                        elapsed < Duration::from_secs(5),
                        "并发轮转不应该超时，线程{}-轮转{}",
                        thread_id,
                        i
                    );
                }
            });

            handles.push(handle);
        }

        // 等待所有线程完成
        for (i, handle) in handles.into_iter().enumerate() {
            match handle.join() {
                Ok(_) => {}
                Err(_) => panic!("线程{}执行失败", i),
            }
        }

        println!("并发轮转安全测试完成");
    }
}

/// 性能基准测试
#[cfg(test)]
mod performance_benchmarks {
    use super::*;

    #[test]
    fn test_write_performance() {
        let ctx = RotationTestContext::new();
        let start_time = Instant::now();

        // 高频写入测试
        let write_count = 1000;
        for i in 0..write_count {
            let entry = TestLogEntry::new("info", &format!("性能测试消息 {}", i));
            ctx.write_log(entry).expect("写入应该成功");
        }

        let duration = start_time.elapsed();
        let writes_per_second = write_count as f64 / duration.as_secs_f64();

        println!("写入性能: {:.2} 条/秒", writes_per_second);

        // 基本性能要求
        assert!(
            writes_per_second > 100.0,
            "写入性能应该超过100条/秒，实际: {:.2}",
            writes_per_second
        );
    }

    #[test]
    fn test_rotation_overhead() {
        let ctx = RotationTestContext::new();

        // 测试轮转开销
        let rotation_times = vec![1, 2, 5, 10]; // MB sizes
        let mut overhead_ratios = Vec::new();

        for &size in &rotation_times {
            // 测量轮转时间
            let rotation_duration = ctx.simulate_rotation(size).unwrap();

            // 测量等效的普通写入时间（作为基准）
            let start = Instant::now();
            for _ in 0..10 {
                let entry = TestLogEntry::new("info", "基准测试");
                ctx.write_log(entry).unwrap();
            }
            let baseline_duration = start.elapsed();

            let overhead_ratio =
                rotation_duration.as_millis() as f64 / baseline_duration.as_millis() as f64;
            overhead_ratios.push((size, overhead_ratio));

            println!("{}MB 轮转开销比率: {:.2}x", size, overhead_ratio);
        }

        // 轮转开销不应该过大
        for (size, ratio) in overhead_ratios {
            assert!(ratio < 100.0, "{}MB 轮转开销过大: {:.2}x", size, ratio);
        }
    }
}

/// 错误恢复测试
#[cfg(test)]
mod error_recovery_tests {
    use super::*;

    #[test]
    fn test_recovery_from_write_errors() {
        let ctx = RotationTestContext::new();

        // 模拟一些写入操作
        for i in 0..5 {
            let entry = TestLogEntry::new("info", &format!("消息 {}", i));
            let result = ctx.write_log(entry);
            assert!(result.is_ok(), "写入{}应该成功", i);
        }

        // 验证恢复能力
        let final_count = ctx.write_count.load(Ordering::Relaxed);
        assert_eq!(final_count, 5, "应该成功写入5条消息");
    }

    #[test]
    fn test_graceful_degradation() {
        let ctx = RotationTestContext::new();

        // 测试在各种条件下的优雅降级
        let test_cases = vec![
            ("小消息", "短消息".to_string()),
            ("中等消息", "x".repeat(1024)),
            ("大消息", "x".repeat(10240)),
        ];

        for (test_name, message) in test_cases {
            let entry = TestLogEntry::new("info", &message);
            let result = ctx.write_log(entry);
            assert!(result.is_ok(), "{} 写入应该成功", test_name);
        }

        println!("优雅降级测试完成");
    }
}

/// 日志文件管理测试
#[cfg(test)]
mod file_management_tests {
    use chrono::Datelike;

    use super::*;

    #[test]
    fn test_date_filename_parsing() {
        let test_cases = vec![
            ("core-2024-01-15.jsonl", Some((2024, 1, 15))),
            ("daemon-2024-12-31.jsonl", Some((2024, 12, 31))),
            ("core-2024-01-15-143022.jsonl", Some((2024, 1, 15))),
            ("invalid-file.txt", None),
            ("core-2024-13-15.jsonl", None), // 无效月份
        ];

        for (filename, expected) in test_cases {
            let result = parse_date_from_filename(filename);
            match expected {
                Some((year, month, day)) => {
                    assert!(result.is_some(), "应该能解析日期: {}", filename);
                    let date = result.unwrap();
                    assert_eq!(date.year(), year, "年份不匹配: {}", filename);
                    assert_eq!(date.month(), month, "月份不匹配: {}", filename);
                    assert_eq!(date.day(), day, "日期不匹配: {}", filename);
                }
                None => {
                    assert!(result.is_none(), "不应该能解析日期: {}", filename);
                }
            }
        }
    }

    /// 简化版本的日期解析函数用于测试
    fn parse_date_from_filename(filename: &str) -> Option<chrono::NaiveDate> {
        let name_without_ext = filename.replace(".jsonl", "").replace(".zst", "");
        let parts: Vec<&str> = name_without_ext.split('-').collect();

        if parts.len() >= 4 {
            let year_str = parts[parts.len() - 3];
            let month_str = parts[parts.len() - 2];
            let day_str = parts[parts.len() - 1];

            if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                if let (Ok(year), Ok(month), Ok(day)) = (
                    year_str.parse::<i32>(),
                    month_str.parse::<u32>(),
                    day_str.parse::<u32>(),
                ) {
                    return chrono::NaiveDate::from_ymd_opt(year, month, day);
                }
            }
        }

        None
    }

    #[test]
    fn test_file_cleanup_logic() {
        let ctx = RotationTestContext::new();

        // 创建一些模拟的日志文件
        let dates = ["2024-01-01", "2024-01-02", "2024-01-03"];

        for date in &dates {
            let file_path = ctx.temp_dir.path().join(format!("core-{}.jsonl", date));
            std::fs::write(&file_path, "test content").unwrap();
        }

        // 验证文件创建成功
        let initial_count = ctx.count_files();
        assert_eq!(initial_count, 3, "应该有3个测试文件");

        // 模拟清理逻辑（这里只是验证文件存在性）
        for date in &dates {
            let file_path = ctx.temp_dir.path().join(format!("core-{}.jsonl", date));
            assert!(file_path.exists(), "文件应该存在: {}", date);
        }

        println!("文件清理逻辑测试完成");
    }
}
