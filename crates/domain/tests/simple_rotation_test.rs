//! 简化的日志轮转测试

use std::sync::Arc;
use tempfile::TempDir;
use uuid::Uuid;

use ::domain::logging::{
    LogLevel, StructuredLogEntry, file::FileLayer, source::LogSource,
    forward::LogWriter,
};
use shared::lru_cache::LruCache;

/// 简单的轮转测试上下文
struct SimpleTestContext {
    _temp_dir: TempDir,
    file_layer: FileLayer,
}

impl SimpleTestContext {
    fn new() -> Self {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let debug_cache = Arc::new(LruCache::new(100));
        let file_layer = FileLayer::new(temp_dir.path().to_path_buf(), debug_cache.clone());

        Self {
            _temp_dir: temp_dir,
            file_layer,
        }
    }

    fn create_small_entry(&self) -> StructuredLogEntry {
        StructuredLogEntry::new(
            LogLevel::Info,
            "test",
            "test_target",
            "small test message",
            Some(Uuid::new_v4()),
        )
        .with_source("core")
    }
}

#[test]
fn test_basic_performance_stats() {
    let ctx = SimpleTestContext::new();
    
    // 写入一些小消息
    for _i in 0..5 {
        let entry = ctx.create_small_entry();
        ctx.file_layer.write_log(entry).unwrap();
    }
    
    // 获取性能统计
    if let Some(stats) = ctx.file_layer.get_performance_stats(LogSource::Core) {
        println!("基础性能统计:");
        println!("  Source: {}", stats.source);
        println!("  写入次数: {}", stats.write_count);
        println!("  轮转次数: {}", stats.rotation_count);
        println!("  平均轮转时间: {}ms", stats.avg_rotation_time_ms);
        
        // 验证基本数据
        assert!(stats.write_count > 0, "应该有写入记录");
        assert_eq!(stats.source, LogSource::Core, "Source 应该是 Core");
        
        println!("基础性能统计测试通过");
    } else {
        panic!("应该能获取到性能统计信息");
    }
}

#[test]
fn test_rotation_optimization() {
    let ctx = SimpleTestContext::new();
    
    // 写入消息但不触发轮转
    for i in 0..10 {
        let entry = StructuredLogEntry::new(
            LogLevel::Info,
            "test",
            "test_target",
            &format!("优化测试消息 {}", i),
            Some(Uuid::new_v4()),
        )
        .with_source("core");
        
        ctx.file_layer.write_log(entry).unwrap();
    }
    
    if let Some(stats) = ctx.file_layer.get_performance_stats(LogSource::Core) {
        println!("轮转优化测试结果:");
        println!("  写入次数: {}", stats.write_count);
        println!("  轮转次数: {}", stats.rotation_count);
        
        // 验证写入计数
        assert!(stats.write_count >= 10, "写入次数应该至少是10");
        
        println!("轮转优化测试通过");
    }
}

#[test]
fn test_multiple_sources() {
    let ctx = SimpleTestContext::new();
    
    // 向不同 source 写入消息
    let sources = ["core", "daemon", "desktop"];
    
    for &source in &sources {
        let entry = StructuredLogEntry::new(
            LogLevel::Info,
            "test",
            "test_target",
            &format!("多源测试消息 from {}", source),
            Some(Uuid::new_v4()),
        )
        .with_source(source);
        
        ctx.file_layer.write_log(entry).unwrap();
    }
    
    // 获取所有统计信息
    let all_stats = ctx.file_layer.get_all_performance_stats();
    
    // 应该至少有一个 source 的统计信息
    assert!(!all_stats.is_empty(), "应该有统计信息");
    
    for stats in all_stats {
        println!("Source {} 统计: 写入={}, 轮转={}", 
            stats.source, stats.write_count, stats.rotation_count);
        assert!(stats.write_count > 0, "每个source都应该有写入");
    }
    
    println!("多源测试通过");
}

#[test]
fn test_performance_stats_reset() {
    let ctx = SimpleTestContext::new();
    
    // 写入一些消息
    for _i in 0..3 {
        let entry = ctx.create_small_entry();
        ctx.file_layer.write_log(entry).unwrap();
    }
    
    // 重置统计
    ctx.file_layer.reset_performance_stats(LogSource::Core);
    
    // 检查是否被重置
    if let Some(stats) = ctx.file_layer.get_performance_stats(LogSource::Core) {
        assert_eq!(stats.rotation_count, 0, "轮转计数应该被重置");
        assert_eq!(stats.total_rotation_time_ms, 0, "轮转时间应该被重置");
        
        // 写入计数不应该被重置（这是写入计数器，不是轮转计数器）
        // assert_eq!(stats.write_count, 0, "写入计数应该被重置");
        
        println!("统计重置测试通过");
    }
}