use super::{Adapter<PERSON>onfig, Ada<PERSON>er<PERSON>rror, RetryConfig};
use anyhow::Result;
use futures_util::StreamExt;
use reqwest::{Client, Response};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::future::Future;
use std::path::Path;
use std::pin::Pin;
use std::sync::Arc;
use std::sync::OnceLock;
use std::time::Duration;
use std::time::Instant;
use tokio::fs::File;
use tokio::io::AsyncWriteExt;
use tracing::instrument;
use tracing::{debug, error, info, trace, warn};

pub trait Interceptor: Send + Sync {
    /// 获取当前的认证 token
    fn get_token(&self) -> Pin<Box<dyn Future<Output = Option<String>> + Send + '_>>;

    /// 处理登录失效事件
    fn handle_login_expired(
        &self,
        error_message: &str,
    ) -> Pin<Box<dyn Future<Output = ()> + Send + '_>>;
}

/// 下载进度信息
#[derive(Debug, Clone)]
pub struct DownloadProgress {
    /// 文件总大小（字节）
    pub total_bytes: u64,
    /// 已下载大小（字节）
    pub downloaded_bytes: u64,
    /// 下载百分比（0-100）
    pub percentage: f64,
    /// 下载速度（字节/秒）
    pub speed: f64,
    /// 开始时间
    pub start_time: Instant,
    /// 是否已完成
    pub completed: bool,
}

impl DownloadProgress {
    pub fn new(total_bytes: u64) -> Self {
        Self {
            total_bytes,
            downloaded_bytes: 0,
            percentage: 0.0,
            speed: 0.0,
            start_time: Instant::now(),
            completed: false,
        }
    }

    pub fn update(&mut self, downloaded_bytes: u64) {
        self.downloaded_bytes = downloaded_bytes;
        self.percentage = if self.total_bytes > 0 {
            (downloaded_bytes as f64 / self.total_bytes as f64) * 100.0
        } else {
            0.0
        };

        let elapsed = self.start_time.elapsed().as_secs_f64();
        self.speed = if elapsed > 0.0 {
            downloaded_bytes as f64 / elapsed
        } else {
            0.0
        };

        self.completed = downloaded_bytes >= self.total_bytes;
    }
}

/// 进度回调 trait
pub trait ProgressCallback: Send + Sync {
    fn on_progress(&self, progress: &DownloadProgress);
    fn on_error(&self, error: &AdapterError);
    fn on_completed(&self, progress: &DownloadProgress);
}

/// 通用业务响应结构（用于检查业务错误码）
#[derive(Debug, Deserialize)]
struct BusinessResponse {
    #[serde(default)]
    err_code: Option<u32>,
    #[serde(default)]
    err_message: Option<String>,
}

/// 下载选项配置
#[derive(Debug, Clone)]
pub struct DownloadOptions {
    /// 是否启用断点续传
    pub resume: bool,
    /// 缓冲区大小
    pub buffer_size: usize,
    /// 重试次数
    pub retry_count: u32,
    /// 重试间隔（毫秒）
    pub retry_delay_ms: u64,
}

impl Default for DownloadOptions {
    fn default() -> Self {
        Self {
            resume: true,
            buffer_size: 8192,
            retry_count: 3,
            retry_delay_ms: 1000,
        }
    }
}

/// HTTP 适配器配置
#[derive(Debug, Clone)]
pub struct HttpConfig {
    pub base_url: String,
    pub timeout_ms: u64,
    pub user_agent: String,
    pub default_headers: HashMap<String, String>,
    pub retry_config: RetryConfig,
}

impl Default for HttpConfig {
    fn default() -> Self {
        let mut default_headers = HashMap::new();
        default_headers.insert("Content-Type".to_string(), "application/json".to_string());
        default_headers.insert("Accept".to_string(), "application/json".to_string());

        Self {
            base_url: "https://api.echowave.cn".to_string(),
            timeout_ms: 30000,
            user_agent: "EchoWave-Client/1.0".to_string(),
            default_headers,
            retry_config: RetryConfig::default(),
        }
    }
}

impl AdapterConfig for HttpConfig {
    fn validate(&self) -> Result<()> {
        if self.base_url.is_empty() {
            anyhow::bail!("Base URL cannot be empty");
        }

        // 验证 URL 格式
        url::Url::parse(&self.base_url).map_err(|e| anyhow::anyhow!("Invalid base URL: {}", e))?;

        if self.timeout_ms == 0 {
            anyhow::bail!("Timeout must be greater than 0");
        }

        Ok(())
    }
}

/// HTTP 适配器，负责与服务端 API 通信
pub struct HttpAdapter {
    config: HttpConfig,
    client: Client,
    interceptor: OnceLock<Arc<dyn Interceptor>>,
}

impl HttpAdapter {
    /// 创建新的 HTTP 适配器
    pub fn new(config: HttpConfig) -> Result<Self> {
        debug!("创建 HTTP 适配器，配置：{:?}", "config");
        trace!("验证 HTTP 配置");
        config.validate()?;

        let mut headers = reqwest::header::HeaderMap::new();
        debug!("构建默认请求头，共 {} 个", config.default_headers.len());
        for (key, value) in &config.default_headers {
            let header_name =
                reqwest::header::HeaderName::from_bytes(key.as_bytes()).map_err(|e| {
                    error!("无效的请求头名称 '{}': {}", key, e);
                    anyhow::anyhow!("Invalid header name '{}': {}", key, e)
                })?;
            let header_value = reqwest::header::HeaderValue::from_str(value).map_err(|e| {
                error!("无效的请求头值 '{}': {}", value, e);
                anyhow::anyhow!("Invalid header value '{}': {}", value, e)
            })?;
            headers.insert(header_name, header_value);
        }

        debug!("构建 HTTP 客户端，超时时间：{}ms", config.timeout_ms);
        let client = Client::builder()
            .timeout(Duration::from_millis(config.timeout_ms))
            .user_agent(&config.user_agent)
            .default_headers(headers)
            .build()
            .map_err(|e| {
                error!("创建 HTTP 客户端失败: {}", e);
                anyhow::anyhow!("Failed to create HTTP client: {}", e)
            })?;

        info!("HTTP 适配器创建成功，基础 URL：{}", config.base_url);
        Ok(Self {
            config,
            client,
            interceptor: OnceLock::new(),
        })
    }

    pub fn set_interceptor(&self, interceptor: Arc<dyn Interceptor>) {
        debug!("设置 HTTP 拦截器");
        if let Err(_) = self.interceptor.set(interceptor) {
            warn!("HTTP 拦截器已存在，无法重复设置");
        } else {
            debug!("HTTP 拦截器设置成功");
        }
    }

    /// 构建完整的 URL
    fn build_url(&self, path: &str) -> String {
        let url = if path.starts_with('/') {
            format!("{}{}", self.config.base_url.trim_end_matches('/'), path)
        } else {
            format!("{}/{}", self.config.base_url.trim_end_matches('/'), path)
        };
        trace!("构建 URL：{} -> {}", path, url);
        url
    }

    /// 添加 trace_id 和认证头到请求头
    async fn build_request_headers(&self) -> reqwest::header::HeaderMap {
        trace!("构建 HTTP 请求头");
        let mut headers = reqwest::header::HeaderMap::new();

        // 尝试从当前 span 获取 trace_id，如果没有则创建新的
        let trace_id = crate::logging::current_trace_id();
        // 如果以 00000000 开头则视为不存在
        if trace_id.as_fields().0 != 0 {
            debug!("添加 trace ID 到请求头：{}", trace_id);
            headers.insert(
                "X-Trace-ID",
                reqwest::header::HeaderValue::from_str(&trace_id.to_string()).unwrap(),
            );
        } else {
            trace!("没有有效的 trace ID，跳过添加");
        }

        // 添加认证头
        if let Some(ref provider) = self.interceptor.get() {
            trace!("尝试获取认证 token");
            if let Some(token) = provider.get_token().await {
                // let auth_value = format!("Bearer {}", token);
                let auth_value = token;
                if let Ok(header_value) = reqwest::header::HeaderValue::from_str(&auth_value) {
                    debug!("添加 Authorization 头到请求");
                    headers.insert("Authorization", header_value);
                } else {
                    warn!("无效的 token 格式，跳过认证头");
                }
            } else {
                trace!("没有可用的 token");
            }
        } else {
            trace!("没有配置认证拦截器");
        }

        debug!("构建完成请求头，共 {} 个", headers.len());
        headers
    }

    /// 处理 HTTP 响应
    async fn handle_response<T>(&self, response: Response) -> Result<T, AdapterError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let status = response.status();
        let url = response.url().clone();

        debug!("HTTP 响应：{} {}", status, url);

        if status.is_success() {
            trace!("响应成功，开始读取响应体");
            let text = response.text().await.map_err(|e| {
                error!("读取响应体失败：{}", e);
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

            debug!("响应体长度：{} 字符", text.len());

            // 先检查业务错误码
            if let Ok(business_response) = serde_json::from_str::<BusinessResponse>(&text) {
                if let Some(err_code) = business_response.err_code
                    && err_code != 0
                {
                    warn!("检测到业务错误码：{}", err_code);
                    // 检查是否是登录失效错误码 901
                    if err_code == 901 {
                        let error_msg = business_response
                            .err_message
                            .unwrap_or_else(|| "Authentication failed".to_string());

                        info!("认证失效：{}", error_msg);

                        // 调用登录失效处理器
                        if let Some(ref handler) = self.interceptor.get() {
                            debug!("调用登录失效处理器");
                            handler.handle_login_expired(&error_msg).await;
                        }

                        // 返回错误
                        return Err(AdapterError::Other(anyhow::anyhow!(
                            "Authentication expired, {}",
                            error_msg
                        )));
                    }
                }
            }

            // 正常解析响应
            trace!("开始解析 JSON 响应");
            serde_json::from_str(&text).map_err(|e| {
                error!("JSON 解析失败：{}", e);
                AdapterError::InvalidResponse(format!("Failed to parse JSON: {}", e))
            })
        } else {
            debug!("HTTP 请求失败，状态码：{}", status);
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            error!("HTTP 请求失败：{} {} - {}", status, url, error_text);
            Err(AdapterError::Other(anyhow::anyhow!(
                "HTTP request failed: {} {} - {}",
                status,
                url,
                error_text
            )))
        }
    }

    /// 发送 GET 请求
    #[instrument(skip(self), fields(url = path))]
    pub async fn get<T>(&self, path: &str) -> Result<T, AdapterError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let url = self.build_url(path);
        debug!("发送 GET 请求：{}", url);

        let headers = self.build_request_headers().await;

        let response = self
            .client
            .get(&url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| {
                let error_details = format!(
                    "请求失败 - URL: {}, 错误类型: {}, 详细信息: {:?}",
                    url,
                    if e.is_timeout() {
                        "超时"
                    } else if e.is_connect() {
                        "连接失败"
                    } else if e.is_decode() {
                        "解码失败"
                    } else if e.is_redirect() {
                        "重定向失败"
                    } else if e.is_request() {
                        "请求错误"
                    } else if e.is_body() {
                        "请求体错误"
                    } else {
                        "其他错误"
                    },
                    e
                );
                error!("GET 请求发送失败：{} {}", e, error_details);
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

        trace!("GET 请求发送成功，处理响应");
        self.handle_response(response).await
    }

    /// 发送 GET 请求并返回二进制数据
    pub async fn get_binary(&self, path: &str) -> Result<Vec<u8>, AdapterError> {
        let url = self.build_url(path);
        debug!("发送 GET 二进制请求：{}", url);

        let headers = self.build_request_headers().await;
        let response = self
            .client
            .get(&url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| {
                error!("GET 二进制请求发送失败：{}", e);
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

        let status = response.status();
        if status.is_success() {
            trace!("GET 二进制请求成功，读取响应体");
            response
                .bytes()
                .await
                .map(|bytes| {
                    debug!("GET 二进制响应读取完成，大小：{} 字节", bytes.len());
                    bytes.to_vec()
                })
                .map_err(|e| {
                    error!("读取二进制响应体失败：{}", e);
                    AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
                })
        } else {
            debug!("GET 二进制请求失败，状态码：{}", status);
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            error!("GET 二进制请求失败：{} {} - {}", status, url, error_text);
            Err(AdapterError::Other(anyhow::anyhow!(
                "HTTP request failed: {} {} - {}",
                status,
                url,
                error_text
            )))
        }
    }

    /// 流式下载文件到指定目录，支持断点续传和进度监听
    pub async fn download_file_stream<P: AsRef<Path>>(
        &self,
        path: &str,
        file_path: P,
        options: DownloadOptions,
        callback: Option<Arc<dyn ProgressCallback>>,
    ) -> Result<(), AdapterError> {
        let file_path = file_path.as_ref();
        info!("开始下载文件：{} -> {:?}", path, file_path);
        debug!("下载选项：{:?}", options);

        // 创建目标目录
        if let Some(parent) = file_path.parent() {
            debug!("创建目标目录：{:?}", parent);
            tokio::fs::create_dir_all(parent).await.map_err(|e| {
                error!("创建目标目录失败：{:?}, 错误：{}", parent, e);
                AdapterError::IoError(e)
            })?;
        }

        // 检查是否需要断点续传
        let mut start_byte = 0u64;
        if options.resume && file_path.exists() {
            let metadata = tokio::fs::metadata(&file_path).await.map_err(|e| {
                error!("获取文件元数据失败：{:?}, 错误：{}", file_path, e);
                AdapterError::IoError(e)
            })?;
            start_byte = metadata.len();
            debug!("启用断点续传，起始字节：{}", start_byte);
        }

        let mut retry_count = 0;
        while retry_count <= options.retry_count {
            match self
                .download_file_internal(path, file_path, start_byte, &options, callback.as_deref())
                .await
            {
                Ok(()) => {
                    info!("文件下载成功：{:?}", file_path);
                    return Ok(());
                }
                Err(e) => {
                    if retry_count < options.retry_count {
                        warn!(
                            "下载失败，重试 {}/{}: {}",
                            retry_count + 1,
                            options.retry_count,
                            e
                        );
                        tokio::time::sleep(Duration::from_millis(options.retry_delay_ms)).await;
                        retry_count += 1;
                    } else {
                        error!("下载失败，已达到最大重试次数：{}", e);
                        if let Some(callback) = &callback {
                            callback.on_error(&e);
                        }
                        return Err(e);
                    }
                }
            }
        }

        Err(AdapterError::Other(anyhow::anyhow!(
            "Download failed after {} retries",
            options.retry_count
        )))
    }

    /// 内部下载实现
    async fn download_file_internal<P: AsRef<Path>>(
        &self,
        path: &str,
        file_path: P,
        start_byte: u64,
        options: &DownloadOptions,
        callback: Option<&dyn ProgressCallback>,
    ) -> Result<(), AdapterError> {
        let file_path = file_path.as_ref();
        let url = self.build_url(path);

        debug!(
            "HTTP 下载开始: {} -> {:?}, 起始字节: {}",
            url, file_path, start_byte
        );

        let mut headers = self.build_request_headers().await;

        // 添加 Range 头支持断点续传
        if start_byte > 0 {
            debug!("添加 Range 头支持断点续传: bytes={}-", start_byte);
            headers.insert(
                "Range",
                reqwest::header::HeaderValue::from_str(&format!("bytes={}-", start_byte)).unwrap(),
            );
        }

        let response = self
            .client
            .get(&url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| {
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

        let status = response.status();
        if !status.is_success() && status != reqwest::StatusCode::PARTIAL_CONTENT {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(AdapterError::Other(anyhow::anyhow!(
                "HTTP request failed: {} {} - {}",
                status,
                url,
                error_text
            )));
        }

        // 获取文件总大小
        let content_length = response
            .headers()
            .get("content-length")
            .and_then(|v| v.to_str().ok())
            .and_then(|v| v.parse::<u64>().ok())
            .unwrap_or(0);

        let total_size = if start_byte > 0 {
            start_byte + content_length
        } else {
            content_length
        };

        debug!(
            "文件总大小：{} 字节，内容长度：{} 字节",
            total_size, content_length
        );

        // 创建进度跟踪器
        let mut progress = DownloadProgress::new(total_size);
        progress.update(start_byte);
        trace!("初始化下载进度：{:.2}%", progress.percentage);

        // 打开文件进行写入
        let mut file = if start_byte > 0 {
            debug!("以追加模式打开文件进行断点续传");
            tokio::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(file_path)
                .await
                .map_err(|e| {
                    error!("以追加模式打开文件失败：{:?}, 错误：{}", file_path, e);
                    AdapterError::IoError(e)
                })?
        } else {
            debug!("创建新文件进行下载");
            File::create(file_path).await.map_err(|e| {
                error!("创建文件失败：{:?}, 错误：{}", file_path, e);
                AdapterError::IoError(e)
            })?
        };

        // 使用流式方式下载
        debug!("开始流式下载，缓冲区大小：{} 字节", options.buffer_size);
        let mut stream = response.bytes_stream();
        let mut downloaded = start_byte;
        let mut buffer = Vec::with_capacity(options.buffer_size);

        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| {
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

            // 将数据添加到缓冲区
            buffer.extend_from_slice(&chunk);

            // 当缓冲区达到指定大小时，批量写入文件
            if buffer.len() >= options.buffer_size {
                file.write_all(&buffer)
                    .await
                    .map_err(|e| AdapterError::IoError(e))?;

                // 更新进度
                downloaded += buffer.len() as u64;
                progress.update(downloaded);

                // 调用进度回调
                if let Some(callback) = callback {
                    callback.on_progress(&progress);
                }

                buffer.clear();
            }
        }

        // 写入剩余的缓冲区数据
        if !buffer.is_empty() {
            file.write_all(&buffer)
                .await
                .map_err(|e| AdapterError::IoError(e))?;

            // 更新进度
            downloaded += buffer.len() as u64;
            progress.update(downloaded);

            // 调用进度回调
            if let Some(callback) = callback {
                callback.on_progress(&progress);
            }
        }

        // 确保数据写入磁盘
        trace!("刷新文件缓冲区到磁盘");
        file.flush().await.map_err(|e| {
            error!("刷新文件缓冲区失败：{}", e);
            AdapterError::IoError(e)
        })?;

        // 标记完成
        progress.completed = true;
        if let Some(callback) = callback {
            debug!("调用下载完成回调");
            callback.on_completed(&progress);
        }

        info!("下载完成: {:?}, 总字节数: {}", file_path, downloaded);
        Ok(())
    }

    /// 发送 POST 请求
    #[instrument(skip(self, body), fields(url = path))]
    pub async fn post<T, R>(&self, path: &str, body: &T) -> Result<R, AdapterError>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de>,
    {
        let url = self.build_url(path);
        debug!("发送 POST 请求：{}", url);

        let headers = self.build_request_headers().await;
        let body_json = serde_json::to_string(body).map_err(|e| {
            error!("POST 请求体序列化失败：{}", e);
            AdapterError::SerializationError(e)
        })?;

        debug!("POST 请求体长度：{} 字符", body_json.len());

        let response = self
            .client
            .post(&url)
            .headers(headers)
            .body(body_json)
            .send()
            .await
            .map_err(|e| {
                let error_details = format!(
                    "请求失败 - URL: {}, 错误类型: {}, 详细信息: {:?}",
                    url,
                    if e.is_timeout() {
                        "超时"
                    } else if e.is_connect() {
                        "连接失败"
                    } else if e.is_decode() {
                        "解码失败"
                    } else if e.is_redirect() {
                        "重定向失败"
                    } else if e.is_request() {
                        "请求错误"
                    } else if e.is_body() {
                        "请求体错误"
                    } else {
                        "其他错误"
                    },
                    e
                );
                error!("POST 请求发送失败：{} {}", e, error_details);
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

        trace!("POST 请求发送成功，处理响应");
        self.handle_response(response).await
    }

    /// 发送 PUT 请求
    #[instrument(skip(self, body), fields(url = path))]
    pub async fn put<T, R>(&self, path: &str, body: &T) -> Result<R, AdapterError>
    where
        T: Serialize,
        R: for<'de> Deserialize<'de>,
    {
        let url = self.build_url(path);
        debug!("发送 PUT 请求：{}", url);

        let headers = self.build_request_headers().await;
        let body_json = serde_json::to_string(body).map_err(|e| {
            error!("PUT 请求体序列化失败：{}", e);
            AdapterError::SerializationError(e)
        })?;

        debug!("PUT 请求体长度：{} 字符", body_json.len());

        let response = self
            .client
            .put(&url)
            .headers(headers)
            .body(body_json)
            .send()
            .await
            .map_err(|e| {
                error!("PUT 请求发送失败：{}", e);
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

        trace!("PUT 请求发送成功，处理响应");
        self.handle_response(response).await
    }

    /// 发送 DELETE 请求
    #[instrument(skip(self), fields(url = path))]
    pub async fn delete<T>(&self, path: &str) -> Result<T, AdapterError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let url = self.build_url(path);
        debug!("发送 DELETE 请求：{}", url);

        let headers = self.build_request_headers().await;
        let response = self
            .client
            .delete(&url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| {
                error!("DELETE 请求发送失败：{}", e);
                AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e))
            })?;

        trace!("DELETE 请求发送成功，处理响应");
        self.handle_response(response).await
    }

    /// 检查连接状态（发送健康检查请求）
    pub async fn health_check(&self) -> anyhow::Result<bool> {
        Ok(true)
        // debug!("开始 HTTP 健康检查");
        // match self.get::<serde_json::Value>("/health").await {
        //     Ok(_) => {
        //         info!("HTTP 健康检查通过");
        //         true
        //     }
        //     Err(e) => {
        //         warn!("HTTP 健康检查失败: {}", e);
        //         false
        //     }
        // }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;

    #[test]
    fn test_http_config_validation() {
        let config = HttpConfig::default();
        assert!(config.validate().is_ok());

        let invalid_config = HttpConfig {
            base_url: "invalid-url".to_string(),
            ..Default::default()
        };
        assert!(invalid_config.validate().is_err());

        let empty_url_config = HttpConfig {
            base_url: "".to_string(),
            ..Default::default()
        };
        assert!(empty_url_config.validate().is_err());
    }

    #[test]
    fn test_url_building() {
        let config = HttpConfig {
            base_url: "https://api.example.com".to_string(),
            ..Default::default()
        };
        let adapter = HttpAdapter::new(config).unwrap();

        assert_eq!(adapter.build_url("/test"), "https://api.example.com/test");
        assert_eq!(adapter.build_url("test"), "https://api.example.com/test");

        let config_with_slash = HttpConfig {
            base_url: "https://api.example.com/".to_string(),
            ..Default::default()
        };
        let adapter_with_slash = HttpAdapter::new(config_with_slash).unwrap();
        assert_eq!(
            adapter_with_slash.build_url("/test"),
            "https://api.example.com/test"
        );
    }

    #[tokio::test]
    async fn test_trace_header() {
        let config = HttpConfig::default();
        let adapter = HttpAdapter::new(config).unwrap();

        let headers = adapter.build_request_headers().await;
        // 在测试环境中，如果没有活动的 span，可能不会有 X-Trace-ID 头
        // 这是正常的行为，所以我们检查如果存在则格式是否正确
        if let Some(trace_id_header) = headers.get("X-Trace-ID") {
            // 验证是有效的 UUID 格式
            assert!(Uuid::parse_str(trace_id_header.to_str().unwrap()).is_ok());
        }
    }

    #[tokio::test]
    async fn test_adapter_creation() {
        let config = HttpConfig::default();
        let adapter = HttpAdapter::new(config);

        assert!(adapter.is_ok());
    }

    #[test]
    fn test_download_progress() {
        let mut progress = DownloadProgress::new(1000);
        assert_eq!(progress.total_bytes, 1000);
        assert_eq!(progress.downloaded_bytes, 0);
        assert_eq!(progress.percentage, 0.0);
        assert!(!progress.completed);

        progress.update(500);
        assert_eq!(progress.downloaded_bytes, 500);
        assert_eq!(progress.percentage, 50.0);
        assert!(!progress.completed);

        progress.update(1000);
        assert_eq!(progress.downloaded_bytes, 1000);
        assert_eq!(progress.percentage, 100.0);
        assert!(progress.completed);
    }

    #[test]
    fn test_download_options_default() {
        let options = DownloadOptions::default();
        assert!(options.resume);
        assert_eq!(options.buffer_size, 8192);
        assert_eq!(options.retry_count, 3);
        assert_eq!(options.retry_delay_ms, 1000);
    }

    // 测试回调实现
    struct TestCallback {
        progress_called: std::sync::Arc<std::sync::atomic::AtomicBool>,
        error_called: std::sync::Arc<std::sync::atomic::AtomicBool>,
        completed_called: std::sync::Arc<std::sync::atomic::AtomicBool>,
    }

    impl TestCallback {
        fn new() -> Self {
            Self {
                progress_called: std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false)),
                error_called: std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false)),
                completed_called: std::sync::Arc::new(std::sync::atomic::AtomicBool::new(false)),
            }
        }
    }

    impl ProgressCallback for TestCallback {
        fn on_progress(&self, _progress: &DownloadProgress) {
            self.progress_called
                .store(true, std::sync::atomic::Ordering::Relaxed);
        }

        fn on_error(&self, _error: &AdapterError) {
            self.error_called
                .store(true, std::sync::atomic::Ordering::Relaxed);
        }

        fn on_completed(&self, _progress: &DownloadProgress) {
            self.completed_called
                .store(true, std::sync::atomic::Ordering::Relaxed);
        }
    }

    #[test]
    fn test_progress_callback() {
        let callback = TestCallback::new();
        let progress = DownloadProgress::new(100);
        let error = AdapterError::Other(anyhow::anyhow!("test error"));

        callback.on_progress(&progress);
        assert!(
            callback
                .progress_called
                .load(std::sync::atomic::Ordering::Relaxed)
        );

        callback.on_error(&error);
        assert!(
            callback
                .error_called
                .load(std::sync::atomic::Ordering::Relaxed)
        );

        callback.on_completed(&progress);
        assert!(
            callback
                .completed_called
                .load(std::sync::atomic::Ordering::Relaxed)
        );
    }
}
