use anyhow::Result;

pub mod agent;
pub mod file;
pub mod helper;
pub mod http;

/// 统一的适配器接口
pub trait ModuleAdapter {
    type Event;
    type Response;
    type Error: std::error::Error + Send + Sync + 'static;

    /// 发送事件到目标模块
    async fn send_event(&self, event: Self::Event) -> Result<Self::Response, Self::Error>;

    /// 检查连接状态
    async fn is_connected(&self) -> bool;

    /// 重新连接
    async fn reconnect(&self) -> Result<(), Self::Error>;

    /// 获取适配器名称
    fn name(&self) -> &str;
}

/// 适配器配置基础 trait
pub trait AdapterConfig {
    fn validate(&self) -> Result<()>;
}

/// 适配器错误类型
#[derive(Debug, thiserror::Error)]
pub enum AdapterError {
    #[error("Connection failed: {0}")]
    ConnectionFailed(String),

    #[error("Request failed")]
    RequestFailed(String),

    #[error("Communication timeout")]
    Timeout,

    #[error("Invalid response: {0}")]
    InvalidResponse(String),

    #[error("Module not available: {0}")]
    ModuleNotAvailable(String),

    #[error("Serialization error: {0}")]
    SerializationError(#[from] serde_json::Error),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("{0}")]
    Other(#[from] anyhow::Error),
}

impl AdapterError {
    /// 判断错误是否可恢复
    pub fn is_recoverable(&self) -> bool {
        match self {
            AdapterError::ConnectionFailed(_) => true,
            AdapterError::Timeout => true,
            AdapterError::ModuleNotAvailable(_) => false,
            AdapterError::InvalidResponse(_) => false,
            AdapterError::SerializationError(_) => false,
            AdapterError::IoError(_) => true,
            AdapterError::Other(_) => false,
            AdapterError::RequestFailed(_) => true,
        }
    }

    /// 获取错误码
    pub fn error_code(&self) -> u32 {
        match self {
            AdapterError::ConnectionFailed(_) => 1001,
            AdapterError::Timeout => 1002,
            AdapterError::InvalidResponse(_) => 1003,
            AdapterError::ModuleNotAvailable(_) => 1004,
            AdapterError::SerializationError(_) => 1005,
            AdapterError::IoError(_) => 1006,
            AdapterError::RequestFailed(_) => 1007,
            AdapterError::Other(_) => 1999,
        }
    }
}

/// 重试配置
#[derive(Debug, Clone)]
pub struct RetryConfig {
    pub max_retries: u32,
    pub initial_delay_ms: u64,
    pub max_delay_ms: u64,
    pub backoff_multiplier: f64,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            initial_delay_ms: 100,
            max_delay_ms: 5000,
            backoff_multiplier: 2.0,
        }
    }
}

/// 带重试的适配器操作
pub async fn with_retry<F, Fut, T, E>(mut operation: F, config: &RetryConfig) -> Result<T, E>
where
    F: FnMut() -> Fut,
    Fut: Future<Output = Result<T, E>>,
    E: std::error::Error + Send + Sync + 'static,
{
    let mut delay = config.initial_delay_ms;

    for attempt in 0..=config.max_retries {
        match operation().await {
            Ok(result) => {
                if attempt > 0 {
                    tracing::debug!("Operation succeeded after {} retries", attempt);
                }
                return Ok(result);
            }
            Err(e) if attempt < config.max_retries => {
                tracing::warn!(
                    "Operation failed (attempt {}/{}): {}. Retrying in {}ms",
                    attempt + 1,
                    config.max_retries + 1,
                    e,
                    delay
                );

                tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
                delay = std::cmp::min(
                    (delay as f64 * config.backoff_multiplier) as u64,
                    config.max_delay_ms,
                );
            }
            Err(e) => {
                tracing::error!("Operation failed after all retries: {}", e);
                return Err(e);
            }
        }
    }

    unreachable!()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_adapter_error_properties() {
        let error = AdapterError::ConnectionFailed("test".to_string());
        assert!(error.is_recoverable());
        assert_eq!(error.error_code(), 1001);

        let error = AdapterError::ModuleNotAvailable("test".to_string());
        assert!(!error.is_recoverable());
        assert_eq!(error.error_code(), 1004);
    }

    #[test]
    fn test_retry_config_default() {
        let config = RetryConfig::default();
        assert_eq!(config.max_retries, 3);
        assert_eq!(config.initial_delay_ms, 100);
        assert_eq!(config.backoff_multiplier, 2.0);
    }

    #[tokio::test]
    async fn test_with_retry_success() {
        let config = RetryConfig::default();

        let mut call_count = 0;
        let operation = || {
            call_count += 1;
            Box::pin(async move {
                if call_count < 2 {
                    Err(AdapterError::ConnectionFailed("test".to_string()))
                } else {
                    Ok("success")
                }
            })
        };

        let result = with_retry(operation, &config).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "success");
    }

    #[tokio::test]
    async fn test_with_retry_failure() {
        let config = RetryConfig {
            max_retries: 1,
            ..Default::default()
        };

        let operation = || {
            Box::pin(
                async move { Err::<&str, _>(AdapterError::ConnectionFailed("test".to_string())) },
            )
        };

        let result = with_retry(operation, &config).await;
        assert!(result.is_err());
    }
}
