use shared::command::{WaitForChildResult, create_command, wait_for_child};
use shared::priority_channel;
use shared::version::Version;

use super::{AdapterConfig, Ada<PERSON>erError, ModuleAdapter, RetryConfig};
use crate::file_sender::FileSender;
use anyhow::Result;
use dashmap::DashMap;
use protocol::events::transfer::chunk_sizes::ChunkSize;
use protocol::events::{
    AgentEvent, AgentResponse, DockerAuth, LatencyInfo, NodeInfo, SecretString, TailscaleAuth,
    TaskInfo, TransferEvent,
};
use protocol::frame::{EventCode, MessageFrame, read_message_frame, send_message_frame};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use std::process::Stdio;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU32, Ordering};
use std::time::Duration;
use tokio::io::BufReader;
use tokio::process::Child;
use tokio::sync::oneshot;
use tokio::sync::{self, Mutex};
use tokio::task::JoinHandle;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, instrument, warn};

/// Agent 适配器配置
#[derive(Debug, Clone)]
pub struct AgentConfig {
    pub agent_binary_path: String,
    pub startup_timeout_ms: u64,
    pub communication_timeout_ms: u64,
    pub retry_config: RetryConfig,
    pub docker_auth: Option<DockerAuth>,
    pub tailscale_auth: Option<TailscaleAuth>,
}

impl Default for AgentConfig {
    fn default() -> Self {
        Self {
            agent_binary_path: "/opt/agent/agent".to_string(),
            startup_timeout_ms: 10000,
            communication_timeout_ms: 60_000,
            retry_config: RetryConfig::default(),
            docker_auth: None,
            tailscale_auth: Some(TailscaleAuth {
                auth_key: SecretString::from(
                    "b9bf620ab4c6f0189c8586d231b1ebe8e9e0f07fd78fd6b4".to_string(),
                ),
                login_server: "https://headscale.echowave.cn".to_string(),
            }),
        }
    }
}

impl AdapterConfig for AgentConfig {
    fn validate(&self) -> Result<()> {
        if self.agent_binary_path.is_empty() {
            anyhow::bail!("Agent binary path cannot be empty");
        }
        if self.startup_timeout_ms == 0 {
            anyhow::bail!("Startup timeout must be greater than 0");
        }
        if self.communication_timeout_ms == 0 {
            anyhow::bail!("Communication timeout must be greater than 0");
        }
        Ok(())
    }
}

type ResponseSender = oneshot::Sender<AgentResponse>;

/// 内部事件类型结构，包含操作ID和可选的响应
/// 或者是原始的MessageFrame（用于二进制数据传输）
enum EventEnvelope {
    /// 标准的AgentEvent
    Event(u32, AgentEvent, Option<ResponseSender>),
    /// 原始的MessageFrame（用于二进制数据）
    Frame(MessageFrame),
}

/// Agent 适配器的内部上下文
struct AgentContext {
    handler: JoinHandle<anyhow::Result<()>>,
    event_sender: priority_channel::Sender<EventEnvelope>,
    resp_pools: Arc<DashMap<u32, ResponseSender>>,
    process: Child,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum AgentState {
    Disconnected,
    Connecting,
    Connected,
    Reconnecting {
        attempt: u32,
        max_attempts: u32,
        next_retry_in_ms: Option<u64>,
    },
    RestartingWslMirror {
        progress_message: String,
        estimated_time_ms: Option<u64>,
    },
    Error(String),
}

impl AgentState {
    pub fn is_connected(&self) -> bool {
        matches!(self, AgentState::Connected)
    }
    pub fn is_disconnected(&self) -> bool {
        matches!(self, AgentState::Disconnected)
    }
    pub fn is_reconnecting(&self) -> bool {
        matches!(self, AgentState::Reconnecting { .. })
    }
    pub fn is_error(&self) -> bool {
        matches!(self, AgentState::Error(_))
    }
    pub fn description(&self) -> &'static str {
        match self {
            AgentState::Disconnected => "未连接",
            AgentState::Connecting => "正在连接",
            AgentState::Connected => "已连接",
            AgentState::Reconnecting { .. } => "正在重新连接",
            AgentState::Error(_) => "连接错误",
            AgentState::RestartingWslMirror { .. } => "正在重启WSL镜像",
        }
    }
    pub fn user_friendly_message(&self) -> String {
        match self {
            AgentState::Disconnected => "任务引擎暂未连接".to_string(),
            AgentState::Connecting => "正在连接任务引擎...".to_string(),
            AgentState::Connected => "任务引擎已就绪".to_string(),
            AgentState::Reconnecting {
                attempt,
                max_attempts,
                next_retry_in_ms,
            } => {
                if let Some(delay_ms) = next_retry_in_ms {
                    format!(
                        "任务引擎连接中断，正在尝试自动恢复（第 {}/{} 次）- {}秒后重试",
                        attempt,
                        max_attempts,
                        delay_ms / 1000
                    )
                } else {
                    format!(
                        "任务引擎连接中断，正在尝试自动恢复（第 {}/{} 次）",
                        attempt, max_attempts
                    )
                }
            }
            AgentState::Error(msg) => format!("任务引擎连接异常: {}", msg),
            AgentState::RestartingWslMirror {
                progress_message, ..
            } => {
                format!("关键服务无响应，正在尝试重启核心组件: {}", progress_message)
            }
        }
    }
}

pub struct AgentAdapter {
    distro_name: String,
    config: AgentConfig,
    context: Arc<Mutex<Option<AgentContext>>>,
    operation_counter: AtomicU32,
    connection_state: sync::watch::Sender<AgentState>,
    reconnect_attempts: Arc<std::sync::atomic::AtomicU32>,
    last_error: Arc<Mutex<Option<String>>>,
    shutting_down: AtomicBool,
}

impl AgentAdapter {
    /// 设置连接状态并记录状态变化
    async fn set_state(&self, new_state: AgentState) {
        let old_state = self.connection_state.borrow().clone();

        if old_state != new_state {
            info!(
                "Agent 连接状态变化: {} -> {} ({})",
                old_state.description(),
                new_state.description(),
                new_state.user_friendly_message()
            );

            // 记录状态变化到错误记录（如果是错误状态）
            if let AgentState::Error(ref msg) = new_state {
                let mut last_error = self.last_error.lock().await;
                *last_error = Some(msg.clone());
            }

            self.connection_state.send_replace(new_state);
        }
    }

    /// 获取当前连接状态
    pub fn get_connection_state(&self) -> AgentState {
        self.connection_state.borrow().clone()
    }

    /// 获取连接状态订阅者
    pub fn state_receiver(&self) -> sync::watch::Receiver<AgentState> {
        self.connection_state.subscribe()
    }

    /// 获取重连尝试次数
    pub fn get_reconnect_attempts(&self) -> u32 {
        self.reconnect_attempts
            .load(std::sync::atomic::Ordering::Relaxed)
    }

    /// 获取最后一次错误信息
    pub async fn get_last_error(&self) -> Option<String> {
        let last_error = self.last_error.lock().await;
        last_error.clone()
    }

    /// 重置重连计数器
    pub fn reset_reconnect_attempts(&self) {
        self.reconnect_attempts
            .store(0, std::sync::atomic::Ordering::Relaxed);
    }

    /// 增加重连尝试次数
    pub fn increment_reconnect_attempts(&self) -> u32 {
        self.reconnect_attempts
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed)
            + 1
    }

    /// 计算指数退避延迟时间（毫秒）
    pub fn calculate_backoff_delay(&self, attempt: u32) -> u64 {
        // 基础延迟1秒，每次重连翻倍，最大60秒
        let base_delay = 1000u64;
        let max_delay = 60000u64;
        let delay = base_delay * (2u64.pow(attempt.saturating_sub(1)));
        delay.min(max_delay)
    }

    /// 启动 Agent 并启用自动重连机制
    #[instrument(skip(self))]
    pub async fn start_with_auto_reconnect(&self) -> Result<(), AdapterError> {
        const MAX_RECONNECT_ATTEMPTS: u32 = 3;

        // 首次连接尝试
        let mut result = self.start().await;

        // 如果首次连接失败，开始重连流程
        if result.is_err() {
            info!("首次连接失败，开始自动重连流程");

            for attempt in 1..=MAX_RECONNECT_ATTEMPTS {
                // 计算延迟时间
                let delay_ms = self.calculate_backoff_delay(attempt);

                // 设置重连状态，包含详细进度信息
                self.set_state(AgentState::Reconnecting {
                    attempt,
                    max_attempts: MAX_RECONNECT_ATTEMPTS,
                    next_retry_in_ms: Some(delay_ms),
                })
                .await;

                self.increment_reconnect_attempts();

                info!(
                    "开始第 {}/{} 次重连尝试，延迟 {}ms",
                    attempt, MAX_RECONNECT_ATTEMPTS, delay_ms
                );

                // 等待指数退避延迟
                tokio::time::sleep(std::time::Duration::from_millis(delay_ms)).await;

                // 更新状态，移除延迟信息，开始实际重连
                self.set_state(AgentState::Reconnecting {
                    attempt,
                    max_attempts: MAX_RECONNECT_ATTEMPTS,
                    next_retry_in_ms: None,
                })
                .await;

                // 尝试重连
                result = self.start().await;

                match &result {
                    Ok(_) => {
                        info!("第 {} 次重连成功", attempt);
                        self.reset_reconnect_attempts();
                        break;
                    }
                    Err(e) => {
                        warn!("第 {} 次重连失败: {}", attempt, e);

                        // 保存错误信息
                        {
                            let mut last_error = self.last_error.lock().await;
                            *last_error = Some(e.to_string());
                        }

                        if attempt == MAX_RECONNECT_ATTEMPTS {
                            error!(
                                "已达到最大重连尝试次数 {}，重连失败",
                                MAX_RECONNECT_ATTEMPTS
                            );
                            self.set_state(AgentState::Error(format!(
                                "重连失败，已尝试 {} 次: {}",
                                MAX_RECONNECT_ATTEMPTS, e
                            )))
                            .await;
                        }
                    }
                }
            }
        }

        result
    }

    /// 判断是否应该进行自动重连
    async fn should_auto_reconnect(&self) -> bool {
        // 如果正在关闭，则不应重连
        if self
            .shutting_down
            .load(std::sync::atomic::Ordering::Relaxed)
        {
            debug!("检测到正在关闭，取消自动重连");
            return false;
        }

        // 检查重连尝试次数是否已达上限
        let attempts = self.get_reconnect_attempts();
        const MAX_RECONNECT_ATTEMPTS: u32 = 3;
        if attempts >= MAX_RECONNECT_ATTEMPTS {
            debug!("已达到最大重连尝试次数 ({})，不再自动重连", attempts);
            return false;
        }

        // 检查是否在正常运行状态下突然断开
        // 这里可以添加更多的判断逻辑
        true
    }

    /// 创建新的 Agent 适配器
    pub fn new(distro_name: String, config: AgentConfig) -> Result<Self> {
        config.validate().map_err(|e| {
            error!("Agent 配置验证失败: {}\n配置内容: {:?}", e, config);
            e
        })?;

        Ok(Self {
            distro_name,
            config,
            context: Arc::new(Mutex::new(None)),
            operation_counter: AtomicU32::new(1),
            connection_state: sync::watch::Sender::new(AgentState::Disconnected),
            reconnect_attempts: Arc::new(std::sync::atomic::AtomicU32::new(0)),
            last_error: Arc::new(Mutex::new(None)),
            shutting_down: AtomicBool::new(false),
        })
    }

    /// 启动 Agent 进程
    #[instrument(skip(self))]
    pub async fn start(&self) -> Result<(), AdapterError> {
        // 重置关闭标志，允许自动重连
        self.shutting_down
            .store(false, std::sync::atomic::Ordering::Relaxed);

        let start_time = std::time::Instant::now();
        // 检查当前状态，避免重复操作
        let current_state = self.connection_state.borrow().clone();
        if current_state.is_connected() {
            info!("Agent 已经连接，跳过启动操作");
            return Ok(());
        }

        // 设置连接中状态
        self.set_state(AgentState::Connecting).await;

        info!("正在启动 Agent 进程，发行版: {}", self.distro_name);

        // 清除之前的错误状态
        {
            let mut last_error = self.last_error.lock().await;
            *last_error = None;
        }
        self.reset_reconnect_attempts();

        let mut guard = self.context.lock().await;
        if guard.is_some() {
            warn!("Agent 进程已经启动，无需重复启动");
            return Ok(());
        }

        debug!(
            "创建 WSL 命令: wsl -d {} -e {} --join",
            self.distro_name, self.config.agent_binary_path
        );

        // 创建进程
        let mut child = create_command("wsl")
            .args([
                "-d",
                &self.distro_name,
                "-e",
                &self.config.agent_binary_path,
                "--join",
            ])
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .map_err(|e| {
                let error_msg = format!("创建 Agent 进程失败: {}", e);
                error!(
                    "创建 Agent 进程失败: {}\n发行版: {}\n二进制路径: {}\n可能原因: WSL 未安装或发行版不存在",
                    e, self.distro_name, self.config.agent_binary_path
                );
                // 异步设置错误状态，但这里不能等待
                let state_sender = self.connection_state.clone();
                let error_msg_clone = error_msg.clone();
                tokio::spawn(async move {
                    state_sender.send_replace(AgentState::Error(error_msg_clone));
                });
                AdapterError::ConnectionFailed(error_msg)
            })?;

        let stdin = child.stdin.take().ok_or_else(|| {
            error!("无法获取 Agent 进程的标准输入流");
            AdapterError::ConnectionFailed("无法获取 Agent 进程的标准输入流".to_string())
        })?;

        let stdout = child.stdout.take().ok_or_else(|| {
            error!("无法获取 Agent 进程的标准输出流");
            AdapterError::ConnectionFailed("无法获取 Agent 进程的标准输出流".to_string())
        })?;

        let stderr = child.stderr.take().ok_or_else(|| {
            error!("无法获取 Agent 进程的标准错误流");
            AdapterError::ConnectionFailed("无法获取 Agent 进程的标准错误流".to_string())
        })?;

        info!("Agent 进程创建成功，正在初始化通信通道");

        let cancellation_token = CancellationToken::new();
        let (event_sender, mut event_receiver) = priority_channel::channel::<EventEnvelope>(64);
        let reps_pools = Arc::new(DashMap::<u32, oneshot::Sender<AgentResponse>>::new());

        info!("初始化通信通道，事件队列大小: 64");

        // Input task: 处理发送事件到 Agent
        debug!("启动输入任务：处理发送事件到 Agent");
        let input_task = {
            let mut stdin = stdin;
            let token = cancellation_token.clone();
            let reps_pools = reps_pools.clone();

            tokio::spawn(async move {
                loop {
                    tokio::select! {
                        biased;
                        it = event_receiver.recv() => {
                            let Some(envelope) = it else {
                                continue;
                            };
                            match envelope {
                                EventEnvelope::Event(operation_id, event, response_tx) => {
                                    if let Some(response_tx) = response_tx {
                                        reps_pools.insert(operation_id, response_tx);
                                    }
                                    if let AgentEvent::Shutdown { .. } = event {
                                        debug!("收到关闭事件，清理响应池");
                                        reps_pools.clear();
                                    }
                                    // 序列化事件
                                    let bytes = match serde_json::to_vec(&event) {
                                        Ok(bytes) => bytes,
                                        Err(e) => {
                                            error!("序列化事件失败: {}\n事件类型: {:?}\n操作 ID: {}", e, event.event_name(), operation_id);
                                            continue;
                                        }
                                    };

                                    if let Err(e) = send_message_frame(
                                        &mut stdin,
                                        EventCode::Data,
                                        operation_id,
                                        false,
                                        &bytes,
                                    )
                                    .await
                                    {
                                        // todo: 需要细化无法发送的错误类型，有一些错误或许是可恢复的
                                        error!("发送事件到 Agent 失败: {}\n操作 ID: {}\n事件类型: {:?}", e, operation_id, event.event_name());
                                        break;
                                    }
                                },
                                EventEnvelope::Frame(frame) => {
                                    if let Err(e) = send_message_frame(
                                        &mut stdin,
                                        EventCode::Binary,
                                        frame.operation_id,
                                        false,
                                        &frame.bytes,
                                    )
                                    .await
                                    {
                                        error!("发送二进制数据到 Agent 失败: {}\n操作 ID: {}", e, frame.operation_id);
                                        break;
                                    }
                                }
                            }
                        }
                        _ = token.cancelled() => {
                            break;
                        }
                    }
                }
                Ok(()) as anyhow::Result<()>
            })
        };

        // Output task: 处理从 Agent 接收响应
        debug!("启动输出任务：处理从 Agent 接收响应");
        let output_task = {
            let mut stdout = BufReader::new(stdout);
            let token = cancellation_token.clone();
            let reps_pools = reps_pools.clone();

            tokio::spawn(async move {
                loop {
                    tokio::select! {
                        biased;
                        it = read_message_frame(&mut stdout) => {
                            let it = it?;
                            let Some(frame) = it else {
                                continue;
                            };
                            if frame.is_response {
                                if let Some((_, tx)) = reps_pools.remove(&frame.operation_id) {
                                    let response =
                                        serde_json::from_slice::<AgentResponse>(&frame.bytes);
                                    let response = match response {
                                        Ok(response) => response,
                                        Err(e) => {
                                            error!("反序列化 Agent 响应失败: {}\n操作 ID: {}\n响应数据长度: {} 字节", e, frame.operation_id, frame.bytes.len());
                                            continue;
                                        }
                                    };
                                    if tx.is_closed() {
                                        error!(
                                            "响应通道已关闭，操作 ID: {}",
                                            frame.operation_id
                                        );
                                        continue;
                                    }
                                    if let Err(_) = tx.send(response) {
                                        error!(
                                            "发送响应到通道失败，操作 ID: {}",
                                            frame.operation_id
                                        );
                                    } else {
                                        debug!("响应成功发送到通道，操作 ID: {}", frame.operation_id);
                                    }
                                } else {
                                    warn!(
                                        "未找到操作 ID 对应的响应通道: {}\n可能是因为超时或其他原因导致通道已被清理",
                                        frame.operation_id
                                    );
                                }
                            } else {
                                Self::dispatch(frame)?;
                            }
                        }
                        _ = token.cancelled() => {
                            break
                        }
                    }
                }
                Ok(()) as anyhow::Result<()>
            })
        };

        // Error task: 处理 Agent 的错误输出并推送到 tracing
        debug!("启动错误处理任务：处理 Agent 错误输出");
        let error_task = {
            let mut stderr = BufReader::new(stderr);
            let token = cancellation_token.clone();

            tokio::spawn(async move {
                use tokio::io::AsyncBufReadExt;
                let mut line = String::new();

                loop {
                    tokio::select! {
                        biased;
                        it = stderr.read_line(&mut line) => {
                            match it {
                                Ok(0) => {
                                    debug!("Agent 错误输出流已结束");
                                    break;
                                }
                                Ok(_) => {
                                    let trimmed_line = line.trim();
                                    if !trimmed_line.is_empty() {
                                        warn!("Agent 错误输出: {}", trimmed_line);
                                    }
                                    line.clear();
                                }
                                Err(e) => {
                                    error!("读取 Agent 错误输出失败: {}", e);
                                    break;
                                }
                            }
                        }
                        _ = token.cancelled() => {
                            break;
                        }
                    }
                }
                Ok(()) as anyhow::Result<()>
            })
        };

        let ctx = self.context.clone();
        let state_sender = self.connection_state.clone();
        let internal: JoinHandle<anyhow::Result<()>> = tokio::spawn(async move {
            #[allow(unused_assignments)]
            let mut who = "unknown";
            let token = cancellation_token.clone();
            tokio::select! {
                a = input_task => {
                    if let Err(e) = a {
                        error!("发送通道崩溃，原因: {:?}", e);
                    }
                    who = "input channel";
                }
                b = output_task => {
                    if let Err(e) = b {
                        error!("接收通道崩溃，原因: {:?}", e);
                    }
                    who = "output channel";
                }
            }
            token.cancel();

            // 检测到连接断开，设置状态为 Disconnected
            warn!("Agent 连接意外断开: {}", who);
            state_sender.send_replace(AgentState::Disconnected);
            // 需要从上下文看一下通道为什么关闭, 如果是进程崩溃上下文应该没有被锁，否则上下文应该处于锁定状态或者上下文已被释放
            if let Some(mut guard) = ctx.try_lock().ok().filter(|guard| guard.is_some()) {
                let mut ctx = guard.take().expect("Context already taken");
                match ctx.process.try_wait() {
                    Ok(exit_status) => {
                        info!(
                            "Agent 进程已终止，退出代码: {}",
                            exit_status.unwrap_or_default()
                        )
                    }
                    Err(err) => {
                        error!("检查 Agent 进程状态时发生错误: {}", err);
                    }
                }
            } else {
                info!("{} 已关闭", who);
            };
            let _ = error_task.await?;
            Ok(())
        });

        // 保存服务上下文，包含共享的进程句柄
        *guard = Some(AgentContext {
            handler: internal,
            event_sender,
            resp_pools: reps_pools,
            process: child,
        });

        self.set_state(AgentState::Connected).await;

        let elapsed = start_time.elapsed();
        info!("Agent 进程启动成功，通信通道已建立，耗时: {:?}", elapsed);
        Ok(())
    }

    /// 停止 Agent 进程
    #[instrument(skip(self))]
    pub async fn stop(&self) -> Result<(), AdapterError> {
        // 设置关闭标志，防止触发自动重连
        self.shutting_down
            .store(true, std::sync::atomic::Ordering::Relaxed);

        let start_time = std::time::Instant::now();
        if !self.connection_state.borrow().is_connected() {
            warn!("Agent 未连接，无需停止操作");
            return Ok(());
        }
        let mut guard = self.context.lock().await;

        let mut ctx = guard.take().ok_or_else(|| {
            error!("Agent 进程未运行，无法执行停止操作");
            AdapterError::ConnectionFailed("Agent 进程未运行".to_string())
        })?;

        info!("正在停止 Agent 进程，尝试优雅关闭");

        // Step 1: 发送优雅关闭信号
        let event = AgentEvent::Shutdown {
            trace_id: crate::logging::current_trace_id(),
        };

        // 尝试发送关闭事件，但不依赖于它的成功
        if let Err(e) = ctx
            .event_sender
            .send(
                EventEnvelope::Event(0, event, None),
                priority_channel::Priority::High,
            )
            .await
        {
            warn!("发送优雅关闭信号失败: {}，将继续进行强制终止", e);
        } else {
            debug!("优雅关闭信号已发送");
        }

        // Step 2: 等待进程退出
        let timeout = wait_for_child(
            &mut ctx.process,
            Duration::from_millis(self.config.startup_timeout_ms),
        )
        .await;
        match timeout {
            Ok(WaitForChildResult::Timeout) => {
                warn!("等待 Agent 进程优雅关闭超时，将强制终止");
                if let Err(e) = ctx.process.kill().await {
                    error!("强制终止 Agent 进程失败: {}", e);
                } else {
                    info!("Agent 进程已强制终止");
                }
            }
            Ok(WaitForChildResult::Exited(status)) => {
                info!("Agent 进程优雅退出，状态码: {}", status);
            }
            Err(err) => {
                error!("等待 Agent 进程失败: {}，尝试强制终止", err);
                if let Err(e) = ctx.process.kill().await {
                    error!("强制终止 Agent 进程失败: {}", e);
                } else {
                    info!("Agent 进程已强制终止");
                }
                self.set_state(AgentState::Disconnected).await;
                return Err(AdapterError::IoError(err));
            }
        }

        // Step 3: 等待所有任务退出
        let shutdown_timeout = Duration::from_millis(self.config.startup_timeout_ms);
        if !ctx.handler.is_finished() {
            debug!(
                "等待内部任务完成，超时时间: {}ms",
                self.config.startup_timeout_ms
            );
            tokio::select! {
                biased;
                it = &mut ctx.handler => {
                    match it {
                        Ok(Ok(())) => {
                            debug!("内部任务优雅完成");
                        }
                        Ok(Err(err)) => {
                            warn!("内部任务执行失败: {:?}", err);
                        }
                        Err(join_err) => {
                            warn!("内部任务崩溃或被取消: {:?}", join_err);
                        }
                    }
                }
                _ = tokio::time::sleep(shutdown_timeout) => {
                    warn!("等待内部任务完成超时，强制中止");
                    ctx.handler.abort();
                }
            }
        } else {
            debug!("内部任务已完成，无需等待");
        }

        let elapsed = start_time.elapsed();
        info!("Agent 进程已完全停止，资源已清理，耗时: {:?}", elapsed);
        self.set_state(AgentState::Disconnected).await;
        Ok(())
    }

    /// 获取下一个操作 ID
    fn next_operation_id(&self) -> u32 {
        self.operation_counter.fetch_add(1, Ordering::Relaxed)
    }

    /// 发送事件并等待响应
    #[instrument(skip(self, event), fields(event_name = %event.event_name()))]
    async fn send_event(&self, event: AgentEvent) -> Result<AgentResponse, AdapterError> {
        let start_time = std::time::Instant::now();
        if !self.connection_state.borrow().is_connected() {
            warn!("尝试发送事件但 Agent 未连接: {}", event.event_name());
            return Err(AdapterError::ModuleNotAvailable("Agent 未连接".to_string()));
        }

        let operation_id = self.next_operation_id();
        let event_name = event.event_name();
        info!(
            "发送事件到 Agent: event_name={} operation_id={} timeout={}ms",
            event_name, operation_id, self.config.communication_timeout_ms
        );

        // 创建 oneshot channel 用于接收响应
        let (response_tx, response_rx) = oneshot::channel();
        debug!("为操作 ID {} 创建响应通道", operation_id);

        // 发送事件到 writer task
        {
            let state = self.context.lock().await;
            if let Some(state) = state.as_ref() {
                state
                    .event_sender
                    .send(
                        EventEnvelope::Event(operation_id, event, Some(response_tx)),
                        priority_channel::Priority::High,
                    )
                    .await
                    .map_err(|_| {
                        error!("Agent 事件通道已关闭，无法发送事件: {}", event_name);
                        AdapterError::ConnectionFailed("事件通道已关闭".to_string())
                    })?;
            } else {
                error!("Agent 未启动，无法发送事件: {}", event_name);
                return Err(AdapterError::ConnectionFailed("Agent 未启动".to_string()));
            }
        }

        // 等待响应
        let timeout = Duration::from_millis(self.config.communication_timeout_ms);

        match tokio::time::timeout(timeout, response_rx).await {
            Ok(Ok(response)) => {
                let elapsed = start_time.elapsed();
                debug!(
                    "从 Agent 成功接收响应: event_name={} operation_id={} 耗时={:?}",
                    event_name, operation_id, elapsed
                );
                Ok(response)
            }
            Ok(Err(_)) => {
                let elapsed = start_time.elapsed();
                error!(
                    "响应通道已关闭: event_name={} operation_id={} 调用耗时={:?}",
                    event_name, operation_id, elapsed
                );
                Err(AdapterError::ConnectionFailed("响应通道已关闭".to_string()))
            }
            Err(_) => {
                let elapsed = start_time.elapsed();
                warn!(
                    "等待 Agent 响应超时: event_name={} operation_id={} 配置超时={}ms 实际耗时={:?}",
                    event_name, operation_id, self.config.communication_timeout_ms, elapsed
                );
                Err(AdapterError::Timeout)
            }
        }
    }

    /// 分发响应事件（这不应为异步，否则消息通道会被阻塞）
    fn dispatch(frame: MessageFrame) -> anyhow::Result<()> {
        // 这是日志或其他事件，推送到 tracing 系统
        match serde_json::from_slice::<AgentEvent>(&frame.bytes) {
            Ok(AgentEvent::LogForward(log_entry)) => {
                // Handle log forward event
                debug!("接收到单条日志转发事件");
                if let Err(err) = crate::logging::forward::forward_log(log_entry) {
                    error!("转发日志失败: {}", err);
                };
            }
            Ok(AgentEvent::LogBatchForward(log_entries)) => {
                // Handle batch log forwarding event
                debug!("接收到批量日志转发事件，数量: {}", log_entries.len());
                if let Err(err) = crate::logging::forward::forward_logs(log_entries) {
                    error!("批量转发日志失败: {}", err);
                };
            }
            Ok(event) => {
                debug!(
                    "从 Agent 接收到不支持的事件: {:?}, 操作 ID: {}",
                    event.event_name(),
                    frame.operation_id
                );
            }
            Err(e) => {
                warn!(
                    "从 Agent 反序列化事件失败: {}, 操作 ID: {}, 数据长度: {} 字节",
                    e,
                    frame.operation_id,
                    frame.bytes.len()
                );
            }
        }
        Ok(())
    }
}

impl ModuleAdapter for AgentAdapter {
    type Event = AgentEvent;
    type Response = AgentResponse;
    type Error = AdapterError;

    async fn send_event(&self, event: Self::Event) -> Result<Self::Response, Self::Error> {
        let mut attempt = 0;
        let max_attempts = self.config.retry_config.max_retries + 1;
        let mut delay = self.config.retry_config.initial_delay_ms;

        loop {
            match self.send_event(event.clone()).await {
                Ok(response) => {
                    if attempt > 0 {
                        info!("操作在 {} 次重试后成功", attempt);
                    }
                    return Ok(response);
                }
                Err(e) if attempt < max_attempts - 1 => {
                    warn!(
                        "操作失败 (第 {}/{} 次尝试): {}。将在 {}ms 后重试",
                        attempt + 1,
                        max_attempts,
                        e,
                        delay
                    );

                    tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
                    delay = std::cmp::min(
                        (delay as f64 * self.config.retry_config.backoff_multiplier) as u64,
                        self.config.retry_config.max_delay_ms,
                    );
                    attempt += 1;
                }
                Err(e) => {
                    error!("Agent 操作最终失败，已用尽所有重试机会: {}", e);
                    return Err(e);
                }
            }
        }
    }

    async fn is_connected(&self) -> bool {
        self.connection_state.borrow().is_connected()
    }

    async fn reconnect(&self) -> Result<(), Self::Error> {
        info!("开始 Agent 重连操作");
        let start_time = std::time::Instant::now();

        if self.connection_state.borrow().is_connected() {
            info!("Agent 当前已连接，先断开连接");
            self.stop().await?;
        }

        let result = self.start().await;
        let elapsed = start_time.elapsed();

        match &result {
            Ok(_) => info!("Agent 重连成功，耗时: {:?}", elapsed),
            Err(e) => error!("Agent 重连失败: {}，耗时: {:?}", e, elapsed),
        }

        result
    }

    fn name(&self) -> &str {
        "agent"
    }
}

impl AgentAdapter {
    /// 检查 Agent 健康状态
    #[instrument(skip(self))]
    pub async fn check_health(&self) -> Result<(), AdapterError> {
        let start_time = std::time::Instant::now();
        let event = AgentEvent::GetHealthStatus {
            trace_id: crate::logging::current_trace_id(),
        };

        let response = self.send_event(event).await?;
        if let AgentResponse::HealthStatus {
            trace_id: _trace_id,
            healthy,
            version,
        } = response
        {
            let elapsed = start_time.elapsed();
            if healthy {
                info!("Agent 健康检查通过，版本: {}，耗时: {:?}", version, elapsed);
                Ok(())
            } else {
                warn!("Agent 健康检查失败，版本: {}，耗时: {:?}", version, elapsed);
                Err(AdapterError::ModuleNotAvailable(
                    "Agent 健康状态异常".to_string(),
                ))
            }
        } else {
            error!("健康检查时收到无效响应: {:?}", response);
            Err(AdapterError::InvalidResponse(
                "从 Agent 收到无效的健康检查响应".to_string(),
            ))
        }
    }
    /// 获取 agent 版本
    #[instrument(skip(self))]
    pub async fn get_version(&self) -> anyhow::Result<Version> {
        debug!("正在获取 Agent 版本信息");
        let output = create_command("wsl")
            .args([
                "-d",
                &self.distro_name,
                "-e",
                &self.config.agent_binary_path,
                "--version",
            ])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to get agent version: {}", e))?;
        if !output.status.success() {
            error!(
                "获取 Agent 版本失败: 退出状态={}, 标准输出={} 字节, 标准错误={} 字节",
                output.status,
                output.stdout.len(),
                output.stderr.len()
            );
            let stderr = if output.stderr.len() > 0 {
                String::from_utf8(output.stderr).unwrap_or_default()
            } else {
                String::from_utf8(output.stdout).unwrap_or_default()
            };
            return Err(anyhow::anyhow!("获取 Agent 版本失败: {}", stderr));
        }
        let stdout = String::from_utf8(output.stdout).map_err(|e| {
            error!("解析 Agent 版本输出失败: {}", e);
            anyhow::anyhow!("解析 Agent 版本输出失败: {}", e)
        })?;
        debug!("Agent 版本原始输出: {}", stdout.trim());
        let version = Version::parse(&stdout)?;
        info!("Agent 版本: {}", version);
        Ok(version)
    }
    /// 启动 agent 守护进程
    #[instrument(skip(self))]
    pub async fn start_daemon(&self, trace_id: uuid::Uuid) -> Result<(), AdapterError> {
        info!("确保 Tailscale 准备就绪后再启动守护进程...");

        // 1. 等待 Tailscale 就绪
        let wait_event = AgentEvent::WaitForTailscaleReady {
            trace_id,
            tailscale_auth: self.config.tailscale_auth.clone(),
        };
        let wait_response = self.send_event(wait_event).await?;

        let tailscale_ip = match wait_response {
            AgentResponse::TailscaleReady { is_ready, ip, .. } => {
                if is_ready {
                    if let Some(ip) = ip {
                        info!("Tailscale 已就绪，分配 IP: {}", ip);
                        ip
                    } else {
                        error!("Tailscale 已就绪但缺少 IP 地址");
                        return Err(AdapterError::InvalidResponse(
                            "Tailscale 已就绪但缺少 IP 地址".to_string(),
                        ));
                    }
                } else {
                    error!("Tailscale 未能正常启动或准备就绪");
                    return Err(AdapterError::ConnectionFailed(
                        "Tailscale 未能正常启动或准备就绪".to_string(),
                    ));
                }
            }
            _ => {
                error!("等待 Tailscale 准备就绪时收到无效响应: {:?}", wait_response);
                return Err(AdapterError::InvalidResponse(
                    "等待 Tailscale 准备就绪时收到无效响应".to_string(),
                ));
            }
        };

        info!(
            "正在启动 Agent 守护进程，使用 Tailscale IP: {}",
            tailscale_ip
        );
        let event = AgentEvent::Start {
            trace_id,
            tailscale_ip,
            docker_auth: self.config.docker_auth.clone(),
        };
        let response = self.send_event(event).await?;
        match response {
            AgentResponse::Success { .. } => {
                info!("Agent 守护进程启动成功");
                Ok(())
            }
            AgentResponse::Error { code, message, .. } => {
                error!(
                    "Agent 守护进程启动失败，错误代码: {}，错误消息: {}",
                    code, message
                );
                Err(AdapterError::ConnectionFailed(format!(
                    "Agent 守护进程启动失败: code={}, message={}",
                    code, message
                )))
            }
            _ => {
                error!("Agent 守护进程启动失败，收到未知响应类型");
                Err(AdapterError::ConnectionFailed(
                    "Agent 守护进程启动失败".to_string(),
                ))
            }
        }
    }
    /// 停止 agent 守护进程
    #[instrument(skip(self))]
    pub async fn stop_daemon(&self, trace_id: uuid::Uuid) -> Result<(), AdapterError> {
        info!("正在停止 Agent 守护进程");
        let event = AgentEvent::Stop { trace_id };
        let response = self.send_event(event).await?;
        match response {
            AgentResponse::Success { .. } => {
                info!("Agent 守护进程停止成功");
                Ok(())
            }
            AgentResponse::Error { code, message, .. } => {
                error!(
                    "Agent 守护进程停止失败，错误代码: {}，错误消息: {}",
                    code, message
                );
                Err(AdapterError::ConnectionFailed(format!(
                    "Agent 守护进程停止失败: code={}, message={}",
                    code, message
                )))
            }
            _ => {
                error!("Agent 守护进程停止失败，收到未知响应类型");
                Err(AdapterError::ConnectionFailed(
                    "Agent 守护进程停止失败".to_string(),
                ))
            }
        }
    }
    /// 获取节点状态
    #[instrument(skip(self))]
    pub async fn get_node_status(
        &self,
        trace_id: uuid::Uuid,
    ) -> Result<(bool, NodeInfo, Vec<TaskInfo>), AdapterError> {
        debug!("正在获取节点状态");
        let event = AgentEvent::GetNodeStatus { trace_id };
        let response = self.send_event(event).await?;
        match response {
            AgentResponse::NodeStatus {
                trace_id: _trace_id,
                idle,
                tasks,
                node_info,
            } => Ok((idle, node_info, tasks)),
            event => {
                error!("获取节点状态时收到不支持的事件: {:?}", event);
                Err(AdapterError::ConnectionFailed(format!(
                    "从 Agent 收到不支持的事件: {:?}",
                    event
                )))
            }
        }
    }
    /// 获取网络延迟信息
    #[instrument(skip(self))]
    pub async fn get_network_latency(
        &self,
        trace_id: uuid::Uuid,
    ) -> Result<LatencyInfo, AdapterError> {
        debug!("正在获取网络延迟信息");
        let event = AgentEvent::GetNetworkLatency { trace_id };
        let response = self.send_event(event).await?;
        match response {
            AgentResponse::NetworkLatency {
                trace_id: _trace_id,
                latency_info,
            } => Ok(latency_info),
            response => {
                error!("获取网络延迟时收到意外响应: {:?}", response);
                Err(AdapterError::ConnectionFailed(
                    "获取网络延迟失败".to_string(),
                ))
            }
        }
    }

    /// 启动 Agent 自我更新
    ///
    /// 该方法会：
    /// 1. 使用 FileSender 将新的 Agent 可执行文件分块传输给 Agent
    /// 2. 发送 StartSelfUpdate 事件通知 Agent 进行文件替换
    /// 3. 等待 Agent 发送 SelfUpdateReady 事件确认更新状态
    #[instrument(skip(self, new_agent_path))]
    pub async fn start_self_update(
        &self,
        new_agent_path: &Path,
        version: String,
    ) -> Result<(), AdapterError> {
        let trace_id = crate::logging::current_trace_id();
        let transfer_id = self.next_operation_id();

        info!(
            trace_id = %trace_id,
            transfer_id = transfer_id,
            version = %version,
            agent_path = %new_agent_path.display(),
            "开始 Agent 自我更新流程"
        );

        // 第一步：创建 FileSender
        let file_sender = FileSender::new(
            transfer_id,
            trace_id,
            new_agent_path,
            Some(ChunkSize::Kb16), // 使用 16KB 分块大小
        )
        .await
        .map_err(|e| {
            error!(
                trace_id = %trace_id,
                error = %e,
                "创建 FileSender 失败"
            );
            AdapterError::InvalidResponse(format!("创建 FileSender 失败: {}", e))
        })?;

        // 第二步：获取文件传输参数并发送 StartTransfer 事件
        let transfer_event = TransferEvent::StartTransfer {
            trace_id,
            transfer_id,
            file_size: file_sender.file_size(),
            file_hash: file_sender.file_hash().to_string(),
            total_chunks: file_sender.total_chunks(),
            chunk_size: file_sender.chunk_size(),
        };

        // 发送 StartTransfer 事件
        let agent_event = AgentEvent::Transfer(transfer_event);
        let response = self.send_event(agent_event).await?;

        // 验证 StartTransfer 响应
        match response {
            AgentResponse::Success { .. } => {
                info!(
                    trace_id = %trace_id,
                    transfer_id = transfer_id,
                    "StartTransfer 事件发送成功"
                );
            }
            AgentResponse::Error { code, message, .. } => {
                error!(
                    trace_id = %trace_id,
                    code = code,
                    message = %message,
                    "StartTransfer 事件发送失败"
                );
                return Err(AdapterError::ConnectionFailed(format!(
                    "StartTransfer 失败: code={}, message={}",
                    code, message
                )));
            }
            _ => {
                error!(
                    trace_id = %trace_id,
                    "StartTransfer 收到意外响应"
                );
                return Err(AdapterError::InvalidResponse(
                    "StartTransfer 收到意外响应".to_string(),
                ));
            }
        }

        // 第三步：发送所有分块数据
        info!(
            trace_id = %trace_id,
            transfer_id = transfer_id,
            total_chunks = file_sender.total_chunks(),
            chunk_size = file_sender.chunk_size(),
            "开始发送文件分块"
        );

        // 实际发送所有分块数据
        self.send_file_chunks(&file_sender).await.map_err(|e| {
            error!(
                trace_id = %trace_id,
                transfer_id = transfer_id,
                error = %e,
                "发送文件分块失败"
            );
            AdapterError::ConnectionFailed(format!("发送文件分块失败: {}", e))
        })?;

        info!(
            trace_id = %trace_id,
            transfer_id = transfer_id,
            "所有分块数据发送完成"
        );

        // 第四步：发送 StartSelfUpdate 事件
        let update_event = AgentEvent::StartSelfUpdate {
            trace_id,
            transfer_id,
            version,
        };

        let response = self.send_event(update_event).await?;
        match response {
            AgentResponse::Success { .. } => {
                info!(
                    trace_id = %trace_id,
                    transfer_id = transfer_id,
                    "Agent 自我更新请求已发送成功，等待Agent重启"
                );

                // Agent更新后需要重新建立连接
                // 给Agent一些时间完成更新和重启，然后尝试重连
                match self.reconnect_after_self_update(trace_id, 30000).await {
                    Ok(()) => {
                        info!(
                            trace_id = %trace_id,
                            transfer_id = transfer_id,
                            "Agent自我更新完成，连接已恢复"
                        );
                        Ok(())
                    }
                    Err(e) => {
                        error!(
                            trace_id = %trace_id,
                            transfer_id = transfer_id,
                            error = %e,
                            "Agent自我更新后重连失败"
                        );
                        // 即使重连失败也返回成功，因为更新本身可能已经成功了
                        // 调用方可以稍后尝试手动重连
                        Ok(())
                    }
                }
            }
            AgentResponse::Error { code, message, .. } => {
                error!(
                    trace_id = %trace_id,
                    transfer_id = transfer_id,
                    code = code,
                    message = %message,
                    "Agent 自我更新请求失败"
                );
                Err(AdapterError::ConnectionFailed(format!(
                    "Agent 自我更新失败: code={}, message={}",
                    code, message
                )))
            }
            _ => {
                error!(
                    trace_id = %trace_id,
                    transfer_id = transfer_id,
                    "Agent 自我更新收到意外响应"
                );
                Err(AdapterError::InvalidResponse(
                    "Agent 自我更新收到意外响应".to_string(),
                ))
            }
        }
    }

    /// Agent 自我更新后重新连接
    ///
    /// 该方法在Agent自我更新完成后调用，会等待Agent重启并重新建立连接
    #[instrument(skip(self))]
    pub async fn reconnect_after_self_update(
        &self,
        trace_id: uuid::Uuid,
        timeout_ms: u64,
    ) -> Result<(), AdapterError> {
        info!(
            trace_id = %trace_id,
            timeout_ms = timeout_ms,
            "开始Agent自我更新后重连流程"
        );

        // 等待一段时间让Agent完成重启
        let restart_wait_time = std::cmp::min(timeout_ms / 4, 5000); // 最多等待5秒
        tokio::time::sleep(Duration::from_millis(restart_wait_time)).await;

        info!(
            trace_id = %trace_id,
            restart_wait_time = restart_wait_time,
            "Agent重启等待完成，开始尝试重新连接"
        );

        let start_time = std::time::Instant::now();
        let timeout = Duration::from_millis(timeout_ms);
        let mut attempts = 0;
        let max_attempts = 10;

        while start_time.elapsed() < timeout && attempts < max_attempts {
            attempts += 1;

            info!(
                trace_id = %trace_id,
                attempt = attempts,
                max_attempts = max_attempts,
                elapsed = ?start_time.elapsed(),
                "尝试重新连接Agent"
            );

            // 确保之前的连接已经完全关闭
            if self.connection_state.borrow().is_connected() {
                info!(
                    trace_id = %trace_id,
                    attempt = attempts,
                    "检测到旧连接，先关闭"
                );
                let _ = self.stop().await; // 忽略关闭错误
            }

            // 尝试重新启动连接
            match self.start().await {
                Ok(()) => {
                    // 连接成功，验证Agent是否正常工作
                    info!(
                        trace_id = %trace_id,
                        attempt = attempts,
                        elapsed = ?start_time.elapsed(),
                        "Agent连接建立成功，开始健康检查"
                    );

                    match self.check_health().await {
                        Ok(()) => {
                            info!(
                                trace_id = %trace_id,
                                attempt = attempts,
                                elapsed = ?start_time.elapsed(),
                                "Agent自我更新后重连成功"
                            );
                            return Ok(());
                        }
                        Err(e) => {
                            warn!(
                                trace_id = %trace_id,
                                attempt = attempts,
                                error = %e,
                                "Agent连接成功但健康检查失败，继续重试"
                            );
                        }
                    }
                }
                Err(e) => {
                    debug!(
                        trace_id = %trace_id,
                        attempt = attempts,
                        error = %e,
                        "Agent连接失败，继续重试"
                    );
                }
            }

            // 等待一段时间后再次尝试
            let retry_delay = std::cmp::min(1000 * attempts, 5000); // 最多等待5秒
            tokio::time::sleep(Duration::from_millis(retry_delay)).await;
        }

        let elapsed = start_time.elapsed();
        error!(
            trace_id = %trace_id,
            attempts = attempts,
            elapsed = ?elapsed,
            timeout_ms = timeout_ms,
            "Agent自我更新后重连失败，已用尽所有重试"
        );

        Err(AdapterError::ConnectionFailed(format!(
            "Agent自我更新后重连失败: 尝试{}次，耗时{:?}",
            attempts, elapsed
        )))
    }

    /// 发送文件分块数据
    ///
    /// 这个方法将FileSender中的所有分块数据通过EventCode::Binary发送给Agent
    #[instrument(skip(self, file_sender))]
    async fn send_file_chunks(&self, file_sender: &FileSender) -> anyhow::Result<()> {
        use protocol::events::transfer::ChunkData;

        let total_chunks = file_sender.total_chunks();
        let chunk_size = file_sender.chunk_size();
        let transfer_id = file_sender.transfer_id;

        info!(
            transfer_id = transfer_id,
            total_chunks = total_chunks,
            chunk_size = chunk_size,
            "开始发送文件分块数据"
        );

        // 读取文件内容
        let file_content = tokio::fs::read(&file_sender.file_path).await?;

        // 发送每一个分块
        for chunk_id in 0..total_chunks {
            let start_offset = (chunk_id as u64) * (chunk_size as u64);
            let end_offset = std::cmp::min(
                start_offset + (chunk_size as u64),
                file_content.len() as u64,
            );

            let chunk_data_slice = &file_content[start_offset as usize..end_offset as usize];

            let chunk_data = ChunkData {
                transfer_id,
                chunk_id,
                data: chunk_data_slice,
            };

            // 序列化ChunkData为二进制格式
            let binary_payload = chunk_data.to_bytes();

            // 通过现有的通信机制发送二进制数据
            self.send_binary_frame(transfer_id, binary_payload)
                .await
                .map_err(|e| {
                    error!(
                        transfer_id = transfer_id,
                        chunk_id = chunk_id,
                        error = %e,
                        "发送分块数据失败"
                    );
                    e
                })?;

            debug!(
                transfer_id = transfer_id,
                chunk_id = chunk_id,
                data_size = chunk_data_slice.len(),
                progress = format!("{}/{}", chunk_id + 1, total_chunks),
                "分块数据发送成功"
            );
        }

        info!(
            transfer_id = transfer_id,
            total_chunks = total_chunks,
            "所有文件分块数据发送完成"
        );

        Ok(())
    }

    /// 发送二进制消息帧
    ///
    /// 这个方法直接通过stdin发送EventCode::Binary类型的消息帧
    #[instrument(skip(self, payload))]
    async fn send_binary_frame(&self, operation_id: u32, payload: Vec<u8>) -> anyhow::Result<()> {
        // 获取AgentContext中的event_sender
        let context = self.context.lock().await;
        let context = context
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("Agent 未启动，无法发送二进制数据"))?;

        // 创建二进制MessageFrame
        use protocol::frame::MessageFrame;

        let bytes_len = payload.len();
        let frame = MessageFrame {
            event_code: EventCode::Binary,
            operation_id,
            is_response: false,
            bytes: payload,
        };

        // 通过event_sender发送原始MessageFrame
        context
            .event_sender
            .send(EventEnvelope::Frame(frame), priority_channel::Priority::Low)
            .await
            .map_err(|e| anyhow::anyhow!("发送二进制帧失败: {}", e))?;

        debug!(
            operation_id = operation_id,
            payload_size = bytes_len,
            "二进制消息帧发送成功"
        );

        Ok(())
    }
}

#[derive(Debug, Clone)]
pub struct DistributionInfo {
    pub id: String,
    pub version: Version,
    pub build_date: String,
    pub arch: String,
}

impl AgentAdapter {
    #[instrument(skip(self))]
    pub async fn get_distribution_info(&self) -> anyhow::Result<DistributionInfo> {
        debug!("正在获取发行版信息");
        let output = create_command("wsl")
            .args([
                "-d",
                &self.distro_name,
                "-e",
                &self.config.agent_binary_path,
                "--distribution-info",
            ])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .output()
            .await
            .map_err(|e| {
                error!("执行获取发行版信息命令失败: {}", e);
                AdapterError::Other(anyhow::anyhow!("获取发行版信息失败: {}", e))
            })?;
        if !output.status.success() {
            error!(
                "获取发行版信息失败: 退出状态={}, 标准输出={} 字节, 标准错误={} 字节",
                output.status,
                output.stdout.len(),
                output.stderr.len()
            );
            let stderr = if output.stderr.len() > 0 {
                String::from_utf8(output.stderr).unwrap_or_default()
            } else {
                String::from_utf8(output.stdout).unwrap_or_default()
            };
            return Err(anyhow::anyhow!("获取发行版信息失败: {}", stderr));
        }
        let stdout = String::from_utf8(output.stdout).map_err(|e| {
            error!("解析发行版信息输出失败: {}", e);
            AdapterError::Other(anyhow::anyhow!("解析发行版信息输出失败: {}", e))
        })?;
        debug!("发行版信息原始输出: {}", stdout.trim());
        let map = stdout
            .split("\n")
            .filter_map(|s| s.split_once("=").map(|(k, v)| (k.trim(), v.trim())))
            .collect::<HashMap<&str, &str>>();
        let id = *map.get("ID").ok_or(anyhow::anyhow!("ID not found"))?;
        if id != "echowave" {
            error!("不支持的发行版: {}，需要 echowave 发行版", id);
            return Err(anyhow::anyhow!("不支持的发行版: {}", id));
        }
        let version = *map
            .get("VERSION")
            .ok_or(anyhow::anyhow!("VERSION not found"))?;
        let version = Version::parse(version)?;
        let build_date = *map
            .get("BUILD_DATE")
            .ok_or(anyhow::anyhow!("BUILD_DATE not found"))?;
        let arch = *map.get("ARCH").ok_or(anyhow::anyhow!("ARCH not found"))?;

        let info = DistributionInfo {
            id: id.to_string(),
            version: version.clone(),
            build_date: build_date.to_string(),
            arch: arch.to_string(),
        };
        info!(
            "发行版信息: ID={}, 版本={}, 构建日期={}, 架构={}",
            id, version, build_date, arch
        );
        Ok(info)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_agent_config_validation() {
        let config = AgentConfig::default();
        assert!(config.validate().is_ok());

        let invalid_config = AgentConfig {
            agent_binary_path: "".to_string(),
            ..Default::default()
        };
        assert!(invalid_config.validate().is_err());
    }

    #[test]
    fn test_operation_id_increment() {
        let config = AgentConfig::default();
        let adapter = AgentAdapter::new("echowave-engine".to_string(), config).unwrap();

        let id1 = adapter.next_operation_id();
        let id2 = adapter.next_operation_id();

        assert_eq!(id2, id1 + 1);
    }

    #[tokio::test]
    async fn test_adapter_creation() {
        let config = AgentConfig::default();
        let adapter = AgentAdapter::new("echowave-engine".to_string(), config);

        assert!(adapter.is_ok());
        let adapter = adapter.unwrap();
        assert!(!adapter.is_connected().await);
        assert_eq!(adapter.name(), "agent");
    }
}
