use anyhow::Result;
use std::sync::atomic::{AtomicU32, Ordering};
use uuid::Uuid;

#[cfg(target_os = "windows")]
use tokio::net::windows::named_pipe::{ClientOptions, NamedPipeClient};
#[cfg(target_os = "windows")]
use windows::Win32::Foundation::ERROR_PIPE_BUSY;

use protocol::events::{HelperSvcEvent, HelperSvcResponse, FeatureStatus, VirtualizationDetails};
#[cfg(target_os = "windows")]
use protocol::frame::{EventCode, read_message_frame, send_message_frame};

use super::{AdapterConfig, AdapterError, ModuleAdapter, RetryConfig};

/// Helper 服务适配器配置
#[derive(Debug, Clone)]
pub struct HelperConfig {
    pub pipe_name: String,
    pub connection_timeout_ms: u64,
    pub communication_timeout_ms: u64,
    pub retry_config: RetryConfig,
}

impl Default for HelperConfig {
    fn default() -> Self {
        Self {
            pipe_name: r"\\.\pipe\echowave_helper".to_string(),
            connection_timeout_ms: 5000,
            communication_timeout_ms: 10000,
            retry_config: RetryConfig::default(),
        }
    }
}

impl AdapterConfig for HelperConfig {
    fn validate(&self) -> Result<()> {
        if self.pipe_name.is_empty() {
            anyhow::bail!("Pipe name cannot be empty");
        }
        if self.connection_timeout_ms == 0 {
            anyhow::bail!("Connection timeout must be greater than 0");
        }
        if self.communication_timeout_ms == 0 {
            anyhow::bail!("Communication timeout must be greater than 0");
        }
        Ok(())
    }
}

/// Helper 服务适配器，负责与 Windows 服务模块通信
pub struct HelperAdapter {
    config: HelperConfig,
    #[allow(dead_code)]
    operation_counter: AtomicU32,
}

impl HelperAdapter {
    /// 创建新的 Helper 适配器
    pub fn new(config: HelperConfig) -> Result<Self> {
        config.validate()?;

        Ok(Self {
            config,
            operation_counter: AtomicU32::new(1),
        })
    }

    /// 获取下一个操作 ID
    #[allow(dead_code)]
    fn next_operation_id(&self) -> u32 {
        self.operation_counter.fetch_add(1, Ordering::Relaxed)
    }

    /// 连接到 Named Pipe
    #[cfg(target_os = "windows")]
    async fn connect(&self) -> Result<NamedPipeClient, AdapterError> {
        tracing::debug!("Connecting to named pipe: {}", self.config.pipe_name);

        let timeout = tokio::time::Duration::from_millis(self.config.connection_timeout_ms);

        let client = tokio::time::timeout(timeout, async {
            use std::time::Duration;

            let client = loop {
                match ClientOptions::new().open(&self.config.pipe_name) {
                    Ok(client) => {
                        tracing::debug!("Successfully connected to named pipe");
                        break Ok(client);
                    }
                    Err(e) if e.raw_os_error() == Some(ERROR_PIPE_BUSY.0 as i32) => {
                        tracing::debug!("Named pipe is busy, retrying...");
                    }
                    Err(e) => {
                        // todo: 检查 Windows 是否存在 echowave_helper 服务， 如果存在，则启动服务，否则报错
                        tracing::error!("Failed to connect to named pipe: {}", e);
                        break Err(AdapterError::ConnectionFailed(format!("Failed to connect to named pipe: {}", e)))
                    }
                }
                tokio::time::sleep(Duration::from_millis(50)).await;
            };
            client
        })
        .await
        .map_err(|_| AdapterError::Timeout)?
        .map_err(|e| {
            AdapterError::ConnectionFailed(format!("Failed to connect to named pipe: {}", e))
        })?;

        tracing::debug!("Successfully connected to named pipe");
        Ok(client)
    }

    /// 在非 Windows 平台上的占位实现
    #[cfg(not(target_os = "windows"))]
    #[allow(dead_code)]
    async fn connect(&self) -> Result<(), AdapterError> {
        Err(AdapterError::ModuleNotAvailable(
            "Helper service only available on Windows".to_string(),
        ))
    }

    /// 发送事件并等待响应
    #[cfg(target_os = "windows")]
    async fn send_event_internal(
        &self,
        event: HelperSvcEvent,
    ) -> Result<HelperSvcResponse, AdapterError> {
        // Use enhanced version with better error handling
        self.send_event_internal_enhanced(event).await
    }

    /// 在非 Windows 平台上的占位实现
    #[cfg(not(target_os = "windows"))]
    async fn send_event_internal(
        &self,
        _event: HelperSvcEvent,
    ) -> Result<HelperSvcResponse, AdapterError> {
        Err(AdapterError::ModuleNotAvailable(
            "Helper service only available on Windows".to_string(),
        ))
    }

    /// 检查 Windows 服务是否可用
    #[cfg(target_os = "windows")]
    pub async fn check_service_availability(&self) -> bool {
        match self.connect().await {
            Ok(_) => {
                tracing::debug!("Helper service is available");
                true
            }
            Err(e) => {
                tracing::debug!("Helper service not available: {}", e);
                false
            }
        }
    }

    /// 在非 Windows 平台上总是返回 false
    #[cfg(not(target_os = "windows"))]
    pub async fn check_service_availability(&self) -> bool {
        false
    }
}

impl ModuleAdapter for HelperAdapter {
    type Event = HelperSvcEvent;
    type Response = HelperSvcResponse;
    type Error = AdapterError;

    async fn send_event(&self, event: Self::Event) -> Result<Self::Response, Self::Error> {
        let mut attempt = 0;
        let max_attempts = self.config.retry_config.max_retries + 1;
        let mut delay = self.config.retry_config.initial_delay_ms;

        loop {
            match self.send_event_internal(event.clone()).await {
                Ok(response) => {
                    if attempt > 0 {
                        tracing::debug!("Operation succeeded after {} retries", attempt);
                    }
                    return Ok(response);
                }
                Err(e) if attempt < max_attempts - 1 => {
                    tracing::warn!(
                        "Operation failed (attempt {}/{}): {}. Retrying in {}ms",
                        attempt + 1,
                        max_attempts,
                        e,
                        delay
                    );

                    tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
                    delay = std::cmp::min(
                        (delay as f64 * self.config.retry_config.backoff_multiplier) as u64,
                        self.config.retry_config.max_delay_ms,
                    );
                    attempt += 1;
                }
                Err(e) => {
                    tracing::error!("Helper operation failed: {}", e);
                    return Err(e);
                }
            }
        }
    }

    async fn is_connected(&self) -> bool {
        // 对于 Named Pipe，我们每次都重新连接，所以这里检查服务可用性
        self.check_service_availability().await
    }

    async fn reconnect(&self) -> Result<(), Self::Error> {
        // Named Pipe 不需要持久连接，每次请求都会重新连接
        // 这里只是检查服务是否可用
        if self.check_service_availability().await {
            Ok(())
        } else {
            Err(AdapterError::ModuleNotAvailable(
                "Helper service not available".to_string(),
            ))
        }
    }

    fn name(&self) -> &str {
        "helper"
    }
}

impl HelperAdapter {
    /// Create adapter with default configuration
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// let adapter = HelperAdapter::create_default().unwrap();
    /// ```
    pub fn create_default() -> Result<Self> {
        Self::new(HelperConfig::default())
    }

    /// Create adapter with custom pipe name
    /// 
    /// # Arguments
    /// * `pipe_name` - Custom named pipe path
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// let adapter = HelperAdapter::with_custom_pipe(r"\\.\pipe\my_helper").unwrap();
    /// ```
    pub fn with_custom_pipe(pipe_name: &str) -> Result<Self> {
        let config = HelperConfig {
            pipe_name: pipe_name.to_string(),
            ..Default::default()
        };
        Self::new(config)
    }

    /// Perform comprehensive health check of the helper service
    /// 
    /// This method checks:
    /// - Service availability
    /// - Basic communication
    /// - Response time
    /// 
    /// # Returns
    /// `Ok(())` if all checks pass, `Err` with details if any check fails
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let adapter = HelperAdapter::create_default()?;
    /// adapter.health_check().await?;
    /// println!("Helper service is healthy");
    /// # Ok(())
    /// # }
    /// ```
    pub async fn health_check(&self) -> Result<(), AdapterError> {
        tracing::info!("Starting helper service health check");
        
        // Check basic connectivity
        if !self.check_service_availability().await {
            return Err(AdapterError::ModuleNotAvailable(
                "Helper service is not available".to_string()
            ));
        }

        // Test basic communication with a simple request
        let trace_id = Uuid::new_v4();
        let test_event = HelperSvcEvent::CheckVirtualization { trace_id };
        
        let start_time = std::time::Instant::now();
        let response = self.send_event_internal(test_event).await?;
        let response_time = start_time.elapsed();

        // Validate response format
        match response {
            HelperSvcResponse::VirtualizationInfo { trace_id: resp_trace_id, .. } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, .. } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                // Error response is still a valid response format
            }
            _ => {
                return Err(AdapterError::InvalidResponse(
                    "Unexpected response type for virtualization check".to_string()
                ));
            }
        }

        tracing::info!(
            "Helper service health check passed (response time: {:?})",
            response_time
        );

        Ok(())
    }

    /// Check if a specific Windows feature is enabled
    /// 
    /// # Arguments
    /// * `feature_name` - Name of the Windows feature to check
    /// 
    /// # Returns
    /// `FeatureStatus` indicating the current state of the feature
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Feature name is invalid
    /// - Communication with helper service fails
    /// - Service returns an error
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// use protocol::events::FeatureStatus;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let status = adapter.check_windows_feature("Microsoft-Windows-Subsystem-Linux").await?;
    /// 
    /// match status {
    ///     FeatureStatus::Enabled => println!("WSL is enabled"),
    ///     FeatureStatus::Disabled => println!("WSL is disabled"),
    ///     FeatureStatus::Unknown => println!("WSL status is unknown"),
    ///     FeatureStatus::NotAvailable => println!("WSL is not available"),
    /// }
    /// # Ok(())
    /// # }
    /// ```
    pub async fn check_windows_feature(&self, feature_name: &str) -> Result<FeatureStatus, AdapterError> {
        // Validate feature name
        if feature_name.is_empty() {
            return Err(AdapterError::InvalidResponse(
                "Feature name cannot be empty".to_string()
            ));
        }

        if feature_name.len() > 256 {
            return Err(AdapterError::InvalidResponse(
                "Feature name too long (max 256 characters)".to_string()
            ));
        }

        tracing::debug!("Checking Windows feature: {}", feature_name);

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::CheckWindowsFeature {
            trace_id,
            feature_name: feature_name.to_string(),
        };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::WindowsFeatureStatus { trace_id: resp_trace_id, status, .. } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
                tracing::debug!("Windows feature '{}' status: {:?}", feature_name, status);
                Ok(status)
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to check Windows feature '{}': {} (code: {})", feature_name, message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for Windows feature check".to_string()
                ))
            }
        }
    }

    /// Enable a specific Windows feature
    /// 
    /// # Arguments
    /// * `feature_name` - Name of the Windows feature to enable
    /// 
    /// # Returns
    /// `(FeatureStatus, bool)` - The new status and whether a restart is required
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Feature name is invalid
    /// - Communication with helper service fails
    /// - Service returns an error
    /// - Insufficient privileges to enable the feature
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// use protocol::events::FeatureStatus;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let (status, restart_required) = adapter.enable_windows_feature("Microsoft-Windows-Subsystem-Linux").await?;
    /// 
    /// match status {
    ///     FeatureStatus::Enabled => {
    ///         if restart_required {
    ///             println!("WSL enabled successfully. Restart required.");
    ///         } else {
    ///             println!("WSL enabled successfully. No restart required.");
    ///         }
    ///     }
    ///     _ => println!("Failed to enable WSL: {:?}", status),
    /// }
    /// # Ok(())
    /// # }
    /// ```
    pub async fn enable_windows_feature(&self, feature_name: &str) -> Result<(FeatureStatus, bool), AdapterError> {
        // Validate feature name
        if feature_name.is_empty() {
            return Err(AdapterError::InvalidResponse(
                "Feature name cannot be empty".to_string()
            ));
        }

        if feature_name.len() > 256 {
            return Err(AdapterError::InvalidResponse(
                "Feature name too long (max 256 characters)".to_string()
            ));
        }

        tracing::info!("Enabling Windows feature: {}", feature_name);

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::EnableWindowsFeature {
            trace_id,
            feature_name: feature_name.to_string(),
        };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::WindowsFeatureStatus { trace_id: resp_trace_id, status, restart_required, .. } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
                tracing::info!(
                    "Windows feature '{}' enable result: {:?}, restart_required: {}",
                    feature_name, status, restart_required
                );
                Ok((status, restart_required))
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to enable Windows feature '{}': {} (code: {})", feature_name, message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for Windows feature enable".to_string()
                ))
            }
        }
    }

    /// Get list of all available Windows features
    /// 
    /// # Returns
    /// `Vec<String>` containing names of all available Windows features
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Communication with helper service fails
    /// - Service returns an error
    /// - Response format is invalid
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let features = adapter.get_all_windows_features().await?;
    /// 
    /// println!("Available Windows features:");
    /// for feature in features {
    ///     println!("  - {}", feature);
    /// }
    /// # Ok(())
    /// # }
    /// ```
    pub async fn get_all_windows_features(&self) -> Result<Vec<String>, AdapterError> {
        tracing::debug!("Getting all Windows features");

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::GetAllWindowsFeatures { trace_id };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::Success { trace_id: resp_trace_id, data } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }

                match data {
                    Some(data) => {
                        let features: Vec<String> = serde_json::from_value(data)
                            .map_err(|e| AdapterError::InvalidResponse(
                                format!("Failed to parse features list: {}", e)
                            ))?;
                        
                        tracing::debug!("Retrieved {} Windows features", features.len());
                        Ok(features)
                    }
                    None => {
                        tracing::warn!("No data in success response for get all Windows features");
                        Ok(Vec::new())
                    }
                }
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to get Windows features: {} (code: {})", message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for get all Windows features".to_string()
                ))
            }
        }
    }

    /// Check virtualization support and status
    /// 
    /// # Returns
    /// `(bool, bool, VirtualizationDetails)` - (supported, enabled, details)
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Communication with helper service fails
    /// - Service returns an error
    /// - Response format is invalid
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let (supported, enabled, details) = adapter.check_virtualization().await?;
    /// 
    /// println!("Virtualization supported: {}", supported);
    /// println!("Virtualization enabled: {}", enabled);
    /// println!("Hyper-V available: {}", details.hyper_v_available);
    /// println!("WSL available: {}", details.wsl_available);
    /// # Ok(())
    /// # }
    /// ```
    pub async fn check_virtualization(&self) -> Result<(bool, bool, VirtualizationDetails), AdapterError> {
        tracing::debug!("Checking virtualization support and status");

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::CheckVirtualization { trace_id };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::VirtualizationInfo { trace_id: resp_trace_id, supported, enabled, details } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
                
                tracing::debug!(
                    "Virtualization check result: supported={}, enabled={}, hyper_v={}, wsl={}",
                    supported, enabled, details.hyper_v_available, details.wsl_available
                );
                
                Ok((supported, enabled, details))
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to check virtualization: {} (code: {})", message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for virtualization check".to_string()
                ))
            }
        }
    }

    /// Install system update from specified path
    /// 
    /// # Arguments
    /// * `update_path` - Path to the update file or package
    /// 
    /// # Returns
    /// `(bool, String)` - (success, message)
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Update path is invalid
    /// - Communication with helper service fails
    /// - Service returns an error
    /// - Insufficient privileges to install updates
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let (success, message) = adapter.install_update("C:\\Updates\\update.msu").await?;
    /// 
    /// if success {
    ///     println!("Update installed successfully: {}", message);
    /// } else {
    ///     println!("Update installation failed: {}", message);
    /// }
    /// # Ok(())
    /// # }
    /// ```
    pub async fn install_update(&self, update_path: &str) -> Result<(bool, String), AdapterError> {
        // Validate update path
        if update_path.is_empty() {
            return Err(AdapterError::InvalidResponse(
                "Update path cannot be empty".to_string()
            ));
        }

        if update_path.len() > 1024 {
            return Err(AdapterError::InvalidResponse(
                "Update path too long (max 1024 characters)".to_string()
            ));
        }

        tracing::info!("Installing update from: {}", update_path);

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::InstallUpdate {
            trace_id,
            update_path: update_path.to_string(),
        };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::UpdateInstallResult { trace_id: resp_trace_id, success, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
                
                if success {
                    tracing::info!("Update installation succeeded: {}", message);
                } else {
                    tracing::warn!("Update installation failed: {}", message);
                }
                
                Ok((success, message))
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to install update: {} (code: {})", message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for update installation".to_string()
                ))
            }
        }
    }

    /// Rollback a system update from backup
    /// 
    /// # Arguments
    /// * `backup_path` - Path to the backup file or directory
    /// 
    /// # Returns
    /// `(bool, String)` - (success, message)
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Backup path is invalid
    /// - Communication with helper service fails
    /// - Service returns an error
    /// - Insufficient privileges to rollback updates
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let (success, message) = adapter.rollback_update("C:\\Backups\\system_backup").await?;
    /// 
    /// if success {
    ///     println!("Update rollback successful: {}", message);
    /// } else {
    ///     println!("Update rollback failed: {}", message);
    /// }
    /// # Ok(())
    /// # }
    /// ```
    pub async fn rollback_update(&self, backup_path: &str) -> Result<(bool, String), AdapterError> {
        // Validate backup path
        if backup_path.is_empty() {
            return Err(AdapterError::InvalidResponse(
                "Backup path cannot be empty".to_string()
            ));
        }

        if backup_path.len() > 1024 {
            return Err(AdapterError::InvalidResponse(
                "Backup path too long (max 1024 characters)".to_string()
            ));
        }

        tracing::info!("Rolling back update from backup: {}", backup_path);

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::RollbackUpdate {
            trace_id,
            backup_path: backup_path.to_string(),
        };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::UpdateInstallResult { trace_id: resp_trace_id, success, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
                
                if success {
                    tracing::info!("Update rollback succeeded: {}", message);
                } else {
                    tracing::warn!("Update rollback failed: {}", message);
                }
                
                Ok((success, message))
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to rollback update: {} (code: {})", message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for update rollback".to_string()
                ))
            }
        }
    }


    /// Check multiple Windows features at once for efficiency
    /// 
    /// # Arguments
    /// * `feature_names` - List of Windows feature names to check
    /// 
    /// # Returns
    /// `Vec<(String, Result<FeatureStatus, String>)>` - Results for each feature
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// let features = vec![
    ///     "Microsoft-Windows-Subsystem-Linux",
    ///     "VirtualMachinePlatform",
    ///     "Containers"
    /// ];
    /// 
    /// let results = adapter.check_multiple_windows_features(&features).await?;
    /// for (feature, result) in results {
    ///     match result {
    ///         Ok(status) => println!("{}: {:?}", feature, status),
    ///         Err(error) => println!("{}: Error - {}", feature, error),
    ///     }
    /// }
    /// # Ok(())
    /// # }
    /// ```
    pub async fn check_multiple_windows_features(
        &self, 
        feature_names: &[&str]
    ) -> Result<Vec<(String, Result<FeatureStatus, String>)>, AdapterError> {
        tracing::debug!("Checking {} Windows features", feature_names.len());

        let mut results = Vec::with_capacity(feature_names.len());
        
        for &feature_name in feature_names {
            let result = match self.check_windows_feature(feature_name).await {
                Ok(status) => Ok(status),
                Err(e) => Err(e.to_string()),
            };
            results.push((feature_name.to_string(), result));
        }

        tracing::debug!("Completed checking {} Windows features", feature_names.len());
        Ok(results)
    }

    /// Process log entry received from helper service and output to tracing system
    /// 
    /// This function handles log entries forwarded from helper-svc and routes them
    /// to the appropriate tracing level with structured fields.
    /// 
    /// # Arguments
    /// * `log_entry` - Log entry received from helper service
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// use protocol::events::{LogEntry, LogLevel};
    /// 
    /// let adapter = HelperAdapter::create_default().unwrap();
    /// 
    /// // This would typically be called when receiving log data from helper-svc
    /// let log_entry = LogEntry {
    ///     source: "helper-svc".to_string(),
    ///     level: LogLevel::Info,
    ///     message: "System operation completed".to_string(),
    ///     timestamp: chrono::Utc::now().timestamp_millis() as u64,
    ///     trace_id: Some(uuid::Uuid::new_v4()),
    ///     module: "dism_manager".to_string(),
    ///     target: Some("helper_svc::managers::dism".to_string()),
    ///     file: Some("dism_manager.rs".to_string()),
    ///     line: Some(123),
    /// };
    /// 
    /// adapter.process_helper_log(log_entry);
    /// ```
    pub fn process_helper_log(&self, log_entry: protocol::events::StructuredLogEntry) {
        // Route to appropriate tracing level with structured fields
        match log_entry.level {
            protocol::events::LogLevel::Error => {
                tracing::error!(
                    target: "helper-svc",
                    source = %log_entry.source,
                    module = %log_entry.module,
                    timestamp = log_entry.timestamp,
                    trace_id = ?log_entry.trace_id,
                    file = ?log_entry.file,
                    line = ?log_entry.line,
                    "{}",
                    log_entry.message
                );
            }
            protocol::events::LogLevel::Warn => {
                tracing::warn!(
                    target: "helper-svc",
                    source = %log_entry.source,
                    module = %log_entry.module,
                    timestamp = log_entry.timestamp,
                    trace_id = ?log_entry.trace_id,
                    file = ?log_entry.file,
                    line = ?log_entry.line,
                    "{}",
                    log_entry.message
                );
            }
            protocol::events::LogLevel::Info => {
                tracing::info!(
                    target: "helper-svc",
                    source = %log_entry.source,
                    module = %log_entry.module,
                    timestamp = log_entry.timestamp,
                    trace_id = ?log_entry.trace_id,
                    file = ?log_entry.file,
                    line = ?log_entry.line,
                    "{}",
                    log_entry.message
                );
            }
            protocol::events::LogLevel::Debug => {
                tracing::debug!(
                    target: "helper-svc",
                    source = %log_entry.source,
                    module = %log_entry.module,
                    timestamp = log_entry.timestamp,
                    trace_id = ?log_entry.trace_id,
                    file = ?log_entry.file,
                    line = ?log_entry.line,
                    "{}",
                    log_entry.message
                );
            }
            protocol::events::LogLevel::Trace => {
                tracing::trace!(
                    target: "helper-svc",
                    source = %log_entry.source,
                    module = %log_entry.module,
                    timestamp = log_entry.timestamp,
                    trace_id = ?log_entry.trace_id,
                    file = ?log_entry.file,
                    line = ?log_entry.line,
                    "{}",
                    log_entry.message
                );
            }
        }
    }

    /// Get service statistics and diagnostics
    /// 
    /// # Returns
    /// JSON object containing service statistics and health information
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let adapter = HelperAdapter::create_default()?;
    /// let stats = adapter.get_service_stats().await?;
    /// 
    /// println!("Service statistics: {}", stats);
    /// # Ok(())
    /// # }
    /// ```
    pub async fn get_service_stats(&self) -> Result<serde_json::Value, AdapterError> {
        if !self.check_service_availability().await {
            return Err(AdapterError::ModuleNotAvailable(
                "Helper service is not available".to_string()
            ));
        }

        // Collect basic metrics
        let connection_test_start = std::time::Instant::now();
        let connection_result = self.health_check().await;
        let connection_time = connection_test_start.elapsed();

        let stats = serde_json::json!({
            "service_available": connection_result.is_ok(),
            "connection_time_ms": connection_time.as_millis(),
            "pipe_name": self.config.pipe_name,
            "connection_timeout_ms": self.config.connection_timeout_ms,
            "communication_timeout_ms": self.config.communication_timeout_ms,
            "retry_config": {
                "max_retries": self.config.retry_config.max_retries,
                "initial_delay_ms": self.config.retry_config.initial_delay_ms,
                "max_delay_ms": self.config.retry_config.max_delay_ms,
                "backoff_multiplier": self.config.retry_config.backoff_multiplier
            },
            "last_health_check": connection_result.map(|_| "healthy").unwrap_or_else(|e| {
                tracing::warn!("Health check failed: {}", e);
                "unhealthy"
            })
        });

        Ok(stats)
    }

    /// Restart the system with specified delay
    /// 
    /// # Arguments
    /// * `delay_seconds` - Delay in seconds before restart (0-3600)
    /// 
    /// # Errors
    /// Returns `AdapterError` if:
    /// - Delay is out of valid range
    /// - Communication with helper service fails
    /// - Service returns an error
    /// - Insufficient privileges to restart system
    /// 
    /// # Example
    /// ```rust
    /// use domain::adapters::helper::HelperAdapter;
    /// 
    /// # async fn example() -> anyhow::Result<()> {
    /// let mut adapter = HelperAdapter::create_default()?;
    /// 
    /// // Restart in 30 seconds
    /// adapter.restart_system(30).await?;
    /// println!("System restart scheduled in 30 seconds");
    /// # Ok(())
    /// # }
    /// ```
    pub async fn restart_system(&self, delay_seconds: u32) -> Result<(), AdapterError> {
        // Validate delay
        if delay_seconds > 3600 {
            return Err(AdapterError::InvalidResponse(
                "Restart delay cannot exceed 3600 seconds (1 hour)".to_string()
            ));
        }

        tracing::warn!("Scheduling system restart in {} seconds", delay_seconds);

        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::RestartSystem {
            trace_id,
            delay_seconds,
        };

        let response = self.send_event(event).await?;

        match response {
            HelperSvcResponse::Success { trace_id: resp_trace_id, .. } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Response trace ID mismatch".to_string()
                    ));
                }
                
                tracing::warn!("System restart scheduled successfully");
                Ok(())
            }
            HelperSvcResponse::Error { trace_id: resp_trace_id, code, message } => {
                if resp_trace_id != trace_id {
                    return Err(AdapterError::InvalidResponse(
                        "Error response trace ID mismatch".to_string()
                    ));
                }
                tracing::error!("Failed to schedule system restart: {} (code: {})", message, code);
                Err(AdapterError::Other(anyhow::anyhow!(
                    "Helper service error: {} (code: {})", message, code
                )))
            }
            _ => {
                Err(AdapterError::InvalidResponse(
                    "Unexpected response type for system restart".to_string()
                ))
            }
        }
    }

    /// Enhanced connection method with better error diagnostics
    #[cfg(target_os = "windows")]
    async fn connect_with_diagnostics(&self) -> Result<NamedPipeClient, AdapterError> {
        tracing::debug!("Connecting to named pipe with diagnostics: {}", self.config.pipe_name);

        let timeout = tokio::time::Duration::from_millis(self.config.connection_timeout_ms);

        let client = tokio::time::timeout(timeout, async {
            use std::time::Duration;
            let mut attempts = 0;
            const MAX_ATTEMPTS: u32 = 50; // 50 * 50ms = 2.5 seconds max wait for pipe

            let client = loop {
                attempts += 1;
                
                match ClientOptions::new().open(&self.config.pipe_name) {
                    Ok(client) => {
                        tracing::debug!("Successfully connected to named pipe after {} attempts", attempts);
                        break Ok(client);
                    }
                    Err(e) if e.raw_os_error() == Some(ERROR_PIPE_BUSY.0 as i32) => {
                        if attempts >= MAX_ATTEMPTS {
                            tracing::error!("Named pipe busy after {} attempts, giving up", attempts);
                            break Err(AdapterError::ConnectionFailed(
                                format!("Named pipe busy after {} attempts: {}", attempts, e)
                            ));
                        }
                        tracing::debug!("Named pipe is busy (attempt {}/{}), retrying...", attempts, MAX_ATTEMPTS);
                        tokio::time::sleep(Duration::from_millis(50)).await;
                    }
                    Err(e) if e.raw_os_error() == Some(109) => { // ERROR_BROKEN_PIPE
                        tracing::warn!("Named pipe is broken, helper service may have crashed");
                        break Err(AdapterError::ConnectionFailed(
                            "Named pipe is broken - helper service may have crashed".to_string()
                        ));
                    }
                    Err(e) if e.raw_os_error() == Some(2) => { // ERROR_FILE_NOT_FOUND
                        tracing::error!("Named pipe not found: {}. Helper service may not be running.", self.config.pipe_name);
                        break Err(AdapterError::ModuleNotAvailable(
                            "Helper service not running or pipe not found".to_string()
                        ));
                    }
                    Err(e) if e.raw_os_error() == Some(5) => { // ERROR_ACCESS_DENIED
                        tracing::error!("Access denied to named pipe: {}. Insufficient privileges.", self.config.pipe_name);
                        break Err(AdapterError::ConnectionFailed(
                            "Access denied - insufficient privileges".to_string()
                        ));
                    }
                    Err(e) => {
                        tracing::error!("Failed to connect to named pipe: {} (OS error: {:?})", e, e.raw_os_error());
                        break Err(AdapterError::ConnectionFailed(
                            format!("Failed to connect to named pipe: {} (OS error: {:?})", e, e.raw_os_error())
                        ));
                    }
                }
            };
            client
        })
        .await
        .map_err(|_| {
            tracing::error!("Connection timeout after {}ms", self.config.connection_timeout_ms);
            AdapterError::Timeout
        })?;

        client
    }

    /// Enhanced send_event_internal with better error handling and validation
    #[cfg(target_os = "windows")]
    async fn send_event_internal_enhanced(
        &self,
        event: HelperSvcEvent,
    ) -> Result<HelperSvcResponse, AdapterError> {
        let operation_id = self.next_operation_id();

        tracing::debug!(
            "Sending event to helper service: operation_id={}, event_type={:?}",
            operation_id,
            std::mem::discriminant(&event)
        );

        // Connect to Named Pipe with enhanced diagnostics
        let mut client = self.connect_with_diagnostics().await?;

        // Serialize event with size validation
        let event_bytes = serde_json::to_vec(&event).map_err(AdapterError::SerializationError)?;
        
        if event_bytes.len() > 1024 * 1024 { // 1MB limit
            return Err(AdapterError::InvalidResponse(
                "Event payload too large (max 1MB)".to_string()
            ));
        }

        tracing::trace!("Event serialized to {} bytes", event_bytes.len());

        // Send message frame
        send_message_frame(
            &mut client,
            EventCode::Data,
            operation_id,
            false,
            &event_bytes,
        )
        .await
        .map_err(|e| {
            tracing::error!("Failed to send message frame: {}", e);
            AdapterError::IoError(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
        })?;

        tracing::trace!("Message frame sent successfully");

        // Wait for response with enhanced timeout handling
        let timeout = tokio::time::Duration::from_millis(self.config.communication_timeout_ms);

        let response = tokio::time::timeout(timeout, async {
            let mut buf_reader = tokio::io::BufReader::new(&mut client);
            let mut text_output_count = 0;
            const MAX_TEXT_OUTPUTS: u32 = 100; // Prevent infinite loop on text output

            loop {
                match read_message_frame(&mut buf_reader).await {
                    Ok(Some(frame)) => {
                        tracing::trace!(
                            "Received frame: operation_id={}, is_response={}, size={}",
                            frame.operation_id, frame.is_response, frame.bytes.len()
                        );

                        if frame.is_response && frame.operation_id == operation_id {
                            let response: HelperSvcResponse = serde_json::from_slice(&frame.bytes)
                                .map_err(|e| {
                                    tracing::error!("Failed to deserialize response: {}", e);
                                    AdapterError::InvalidResponse(
                                        format!("Failed to deserialize response: {}", e)
                                    )
                                })?;
                            
                            tracing::trace!("Response deserialized successfully");
                            return Ok(response);
                        } else {
                            tracing::debug!(
                                "Ignoring frame: operation_id={}, is_response={} (expected operation_id={})",
                                frame.operation_id, frame.is_response, operation_id
                            );
                        }
                    }
                    Ok(None) => {
                        // Received text output, continue waiting but with limit
                        text_output_count += 1;
                        if text_output_count > MAX_TEXT_OUTPUTS {
                            tracing::warn!("Too many text outputs received, giving up");
                            return Err(AdapterError::InvalidResponse(
                                "Too many text outputs, no valid response received".to_string()
                            ));
                        }
                        tracing::trace!("Received text output ({}), continuing to wait", text_output_count);
                        continue;
                    }
                    Err(e) => {
                        // 区分不同类型的读取错误
                        match e.kind() {
                            std::io::ErrorKind::UnexpectedEof => {
                                tracing::debug!("Helper service disconnected (EOF)");
                                return Err(AdapterError::ConnectionFailed(
                                    "Helper service disconnected unexpectedly".to_string()
                                ));
                            }
                            std::io::ErrorKind::BrokenPipe => {
                                tracing::debug!("Helper service pipe broken");
                                return Err(AdapterError::ConnectionFailed(
                                    "Helper service pipe was broken".to_string()
                                ));
                            }
                            _ => {
                                tracing::error!("Failed to read message frame: {}", e);
                                return Err(AdapterError::IoError(e));
                            }
                        }
                    }
                }
            }
        })
        .await;

        match response {
            Ok(result) => {
                tracing::debug!(
                    "Received response from helper service: operation_id={}",
                    operation_id
                );
                result
            }
            Err(_) => {
                tracing::warn!(
                    "Timeout waiting for helper service response: operation_id={}, timeout={}ms",
                    operation_id, self.config.communication_timeout_ms
                );
                Err(AdapterError::Timeout)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_helper_config_validation() {
        let config = HelperConfig::default();
        assert!(config.validate().is_ok());

        let invalid_config = HelperConfig {
            pipe_name: "".to_string(),
            ..Default::default()
        };
        assert!(invalid_config.validate().is_err());
    }

    #[test]
    fn test_operation_id_increment() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config).unwrap();

        let id1 = adapter.next_operation_id();
        let id2 = adapter.next_operation_id();

        assert_eq!(id2, id1 + 1);
    }

    #[tokio::test]
    async fn test_adapter_creation() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config);

        assert!(adapter.is_ok());
        let adapter = adapter.unwrap();
        assert_eq!(adapter.name(), "helper");
    }

    #[cfg(not(target_os = "windows"))]
    #[tokio::test]
    async fn test_non_windows_behavior() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config).unwrap();
        // 在非 Windows 平台上应该返回不可用
        assert!(!adapter.check_service_availability().await);
    }

    #[test]
    fn test_create_default() {
        let adapter = HelperAdapter::create_default();
        assert!(adapter.is_ok());
        assert_eq!(adapter.unwrap().name(), "helper");
    }

    #[test]
    fn test_with_custom_pipe() {
        let custom_pipe = r"\\.\pipe\test_helper";
        let adapter = HelperAdapter::with_custom_pipe(custom_pipe);
        assert!(adapter.is_ok());
        
        let adapter = adapter.unwrap();
        assert_eq!(adapter.config.pipe_name, custom_pipe);
    }

    #[test]
    fn test_feature_name_validation() {
        // Test empty feature name
        let result = std::panic::catch_unwind(|| {
            // This would be tested in an async context normally
            let empty_name = "";
            assert!(empty_name.is_empty());
        });
        assert!(result.is_ok());

        // Test long feature name
        let long_name = "a".repeat(300);
        assert!(long_name.len() > 256);
    }

    #[test]
    fn test_update_path_validation() {
        // Test empty path
        let empty_path = "";
        assert!(empty_path.is_empty());

        // Test long path
        let long_path = "a".repeat(1100);
        assert!(long_path.len() > 1024);
    }

    #[test]
    fn test_restart_delay_validation() {
        // Test valid delay
        let valid_delay = 300u32;
        assert!(valid_delay <= 3600);

        // Test invalid delay
        let invalid_delay = 4000u32;
        assert!(invalid_delay > 3600);
    }

    #[tokio::test]
    async fn test_health_check_service_unavailable() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config).unwrap();
        
        // On non-Windows or when service is not available, health check should fail
        #[cfg(not(target_os = "windows"))]
        {
            let result = adapter.health_check().await;
            assert!(result.is_err());
            match result.unwrap_err() {
                AdapterError::ModuleNotAvailable(_) => (),
                other => panic!("Expected ModuleNotAvailable, got {:?}", other),
            }
        }
    }


    #[tokio::test]
    async fn test_multiple_features_check() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config).unwrap();

        let features = vec!["WSL", "Hyper-V", "VirtualMachinePlatform"];
        
        // This should return results for all features (even if they fail on non-Windows)
        let results = adapter.check_multiple_windows_features(&features).await;
        
        #[cfg(not(target_os = "windows"))]
        {
            // On non-Windows, all should fail
            assert!(results.is_ok());
            let results = results.unwrap();
            assert_eq!(results.len(), 3);
            
            for (feature_name, result) in results {
                assert!(features.contains(&feature_name.as_str()));
                assert!(result.is_err()); // Should fail on non-Windows
            }
        }
    }

    #[tokio::test]
    async fn test_service_stats() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config).unwrap();

        let stats_result = adapter.get_service_stats().await;
        
        #[cfg(not(target_os = "windows"))]
        {
            // On non-Windows, should fail
            assert!(stats_result.is_err());
            match stats_result.unwrap_err() {
                AdapterError::ModuleNotAvailable(_) => (),
                other => panic!("Expected ModuleNotAvailable, got {:?}", other),
            }
        }
    }

    #[test]
    fn test_backup_path_validation() {
        // Test empty backup path
        let empty_path = "";
        assert!(empty_path.is_empty());

        // Test long backup path
        let long_path = "a".repeat(1100);
        assert!(long_path.len() > 1024);
    }
    #[tokio::test]
    async fn test_rollback_update_validation() {
        let config = HelperConfig::default();
        let adapter = HelperAdapter::new(config).unwrap();

        // Test empty backup path
        let result = adapter.rollback_update("").await;
        assert!(result.is_err());
        match result.unwrap_err() {
            AdapterError::InvalidResponse(msg) => {
                assert!(msg.contains("empty"));
            }
            other => panic!("Expected InvalidResponse, got {:?}", other),
        }

        // Test long backup path
        let long_path = "a".repeat(1100);
        let result = adapter.rollback_update(&long_path).await;
        assert!(result.is_err());
        match result.unwrap_err() {
            AdapterError::InvalidResponse(msg) => {
                assert!(msg.contains("too long"));
            }
            other => panic!("Expected InvalidResponse, got {:?}", other),
        }
    }
}
