use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use tokio::fs;
// TODO: These will be used when implementing actual file operations
// use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tracing::{debug, error, info, trace, warn};
use super::{AdapterConfig, AdapterError};

/// 文件适配器配置
#[derive(Debug, Clone)]
pub struct FileConfig {
    pub base_path: PathBuf,
    pub create_dirs: bool,
    pub backup_enabled: bool,
    pub backup_suffix: String,
}

impl Default for FileConfig {
    fn default() -> Self {
        Self {
            base_path: Self::get_app_data_path(),
            create_dirs: true,
            backup_enabled: true,
            backup_suffix: ".bak".to_string(),
        }
    }
}

impl FileConfig {
    /// 获取应用程序数据目录路径 (%APPDATA%/echowave_client)
    pub fn get_app_data_path() -> PathBuf {
        dirs::data_dir()
            .unwrap_or_else(|| std::env::current_dir().unwrap_or_else(|_| PathBuf::from(".")))
            .join("echowave_client")
    }
}

impl AdapterConfig for FileConfig {
    fn validate(&self) -> Result<()> {
        if !self.base_path.exists() && !self.create_dirs {
            anyhow::bail!("Base path does not exist and create_dirs is false");
        }
        
        if self.backup_suffix.is_empty() {
            anyhow::bail!("Backup suffix cannot be empty");
        }
        
        Ok(())
    }
}

/// 文件适配器，负责本地文件操作
pub struct FileAdapter {
    config: FileConfig,
}

impl FileAdapter {
    /// 创建新的文件适配器
    pub fn new(config: FileConfig) -> Result<Self> {
        debug!("创建文件适配器，基础路径：{:?}", config.base_path);
        config.validate()?;
        info!("文件适配器初始化完成");
        Ok(Self { config })
    }

    /// 验证路径安全性（防止路径遍历攻击）
    fn validate_path(&self, path: &Path) -> Result<PathBuf, AdapterError> {
        trace!("验证路径：{:?}", path);
        
        // 检查路径遍历模式
        let path_str = path.to_string_lossy();
        if path_str.contains("..") {
            warn!("路径遍历攻击检测：{:?}", path);
            return Err(AdapterError::Other(anyhow::anyhow!(
                "Path traversal detected: {:?}",
                path
            )));
        }

        // 构建绝对路径
        let absolute_path = if path.is_absolute() {
            debug!("绝对路径：{:?}", path);
            path.to_path_buf()
        } else {
            debug!("相对路径，基于：{:?}", self.config.base_path);
            self.config.base_path.join(path)
        };

        // 规范化并验证路径范围
        let canonical_base = self.config.base_path.canonicalize()
            .map_err(|e| {
                error!("基础路径规范化失败 {:?}: {}", self.config.base_path, e);
                AdapterError::IoError(e)
            })?;

        if let Ok(canonical_path) = absolute_path.canonicalize() {
            if !canonical_path.starts_with(&canonical_base) {
                warn!("路径超出范围：{:?}", path);
                return Err(AdapterError::Other(anyhow::anyhow!(
                    "Path traversal detected: {:?}",
                    path
                )));
            }
            trace!("路径验证通过：{:?}", canonical_path);
            Ok(canonical_path)
        } else {
            // 文件不存在时检查父目录
            if let Some(parent) = absolute_path.parent() {
                if parent.exists() {
                    let canonical_parent = parent.canonicalize()
                        .map_err(|e| {
                            error!("父目录规范化失败 {:?}: {}", parent, e);
                            AdapterError::IoError(e)
                        })?;
                    if !canonical_parent.starts_with(&canonical_base) {
                        warn!("父目录超出范围：{:?}", path);
                        return Err(AdapterError::Other(anyhow::anyhow!(
                            "Path traversal detected: {:?}",
                            path
                        )));
                    }
                }
            }
            trace!("路径验证通过（新文件）：{:?}", absolute_path);
            Ok(absolute_path)
        }
    }

    /// 创建备份文件
    async fn create_backup(&self, path: &Path, ) -> Result<(), AdapterError> {
        if !self.config.backup_enabled {
            trace!("备份已禁用");
            return Ok(());
        }

        if !path.exists() {
            trace!("文件不存在，跳过备份：{:?}", path);
            return Ok(());
        }

        let backup_path = path.with_extension(
            format!("{}{}", 
                path.extension().and_then(|s| s.to_str()).unwrap_or(""),
                self.config.backup_suffix
            )
        );

        debug!("创建备份：{:?}", backup_path);
        match fs::copy(path, &backup_path).await {
            Ok(bytes_copied) => {
                trace!("备份完成，复制 {} 字节", bytes_copied);
                Ok(())
            }
            Err(e) => {
                error!("备份失败：{:?}，错误：{}", path, e);
                Err(AdapterError::IoError(e))
            }
        }
    }

    /// 读取 JSON 文件
    pub async fn read_json<T>(&self, path: &Path, ) -> Result<T, AdapterError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let safe_path = self.validate_path(path)?;
        debug!("读取JSON：{:?}", safe_path);

        let content = fs::read_to_string(&safe_path).await
            .map_err(|e| {
                error!("读取文件失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })?;

        serde_json::from_str(&content)
            .map_err(|e| {
                error!("JSON解析失败：{:?}，错误：{}", safe_path, e);
                AdapterError::SerializationError(e)
            })
    }

    /// 写入 JSON 文件
    pub async fn write_json<T>(&self, path: &Path, data: &T, ) -> Result<(), AdapterError>
    where
        T: Serialize,
    {
        let safe_path = self.validate_path(path)?;
        debug!("写入JSON：{:?}", safe_path);

        // 创建父目录
        if let Some(parent) = safe_path.parent() {
            if self.config.create_dirs {
                trace!("创建目录：{:?}", parent);
                fs::create_dir_all(parent).await
                    .map_err(|e| {
                        error!("创建目录失败：{:?}，错误：{}", parent, e);
                        AdapterError::IoError(e)
                    })?;
            }
        }

        // 创建备份
        self.create_backup(&safe_path).await?;

        // 序列化数据
        let json_content = serde_json::to_string_pretty(data)
            .map_err(|e| {
                error!("JSON序列化失败：{:?}，错误：{}", safe_path, e);
                AdapterError::SerializationError(e)
            })?;

        // 原子写入
        let temp_path = safe_path.with_extension("tmp");
        trace!("原子写入：{:?} -> {:?}", temp_path, safe_path);
        
        fs::write(&temp_path, json_content).await
            .map_err(|e| {
                error!("写入临时文件失败：{:?}，错误：{}", temp_path, e);
                AdapterError::IoError(e)
            })?;

        fs::rename(&temp_path, &safe_path).await
            .map_err(|e| {
                error!("文件重命名失败：{:?}，错误：{}", temp_path, e);
                AdapterError::IoError(e)
            })?;

        info!("JSON写入完成：{:?}", safe_path);
        Ok(())
    }

    /// 读取文本文件
    pub async fn read_text(&self, path: &Path, ) -> Result<String, AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("读取文本：{:?}", safe_path);

        fs::read_to_string(&safe_path).await
            .map_err(|e| {
                error!("读取文本失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })
    }

    /// 写入文本文件
    pub async fn write_text(&self, path: &Path, content: &str, ) -> Result<(), AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("写入文本：{:?}，长度：{}", safe_path, content.len());

        // 创建父目录
        if let Some(parent) = safe_path.parent() {
            if self.config.create_dirs {
                trace!("创建目录：{:?}", parent);
                fs::create_dir_all(parent).await
                    .map_err(|e| {
                        error!("创建目录失败：{:?}，错误：{}", parent, e);
                        AdapterError::IoError(e)
                    })?;
            }
        }

        // 创建备份
        self.create_backup(&safe_path).await?;

        // 原子写入
        let temp_path = safe_path.with_extension("tmp");
        trace!("原子写入：{:?}", temp_path);
        
        fs::write(&temp_path, content).await
            .map_err(|e| {
                error!("写入临时文件失败：{:?}，错误：{}", temp_path, e);
                AdapterError::IoError(e)
            })?;

        fs::rename(&temp_path, &safe_path).await
            .map_err(|e| {
                error!("文件重命名失败：{:?}，错误：{}", temp_path, e);
                AdapterError::IoError(e)
            })?;

        info!("文本写入完成：{:?}", safe_path);
        Ok(())
    }

    /// 读取二进制文件
    pub async fn read_bytes(&self, path: &Path, ) -> Result<Vec<u8>, AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("读取二进制：{:?}", safe_path);

        fs::read(&safe_path).await
            .map_err(|e| {
                error!("读取二进制失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })
    }

    /// 写入二进制文件
    pub async fn write_bytes(&self, path: &Path, data: &[u8], ) -> Result<(), AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("写入二进制：{:?}，大小：{}字节", safe_path, data.len());

        // 创建父目录
        if let Some(parent) = safe_path.parent() {
            if self.config.create_dirs {
                trace!("创建目录：{:?}", parent);
                fs::create_dir_all(parent).await
                    .map_err(|e| {
                        error!("创建目录失败：{:?}，错误：{}", parent, e);
                        AdapterError::IoError(e)
                    })?;
            }
        }

        // 创建备份
        self.create_backup(&safe_path).await?;

        // 原子写入
        let temp_path = safe_path.with_extension("tmp");
        trace!("原子写入：{:?}", temp_path);
        
        fs::write(&temp_path, data).await
            .map_err(|e| {
                error!("写入临时文件失败：{:?}，错误：{}", temp_path, e);
                AdapterError::IoError(e)
            })?;

        fs::rename(&temp_path, &safe_path).await
            .map_err(|e| {
                error!("文件重命名失败：{:?}，错误：{}", temp_path, e);
                AdapterError::IoError(e)
            })?;

        info!("二进制写入完成：{:?}", safe_path);
        Ok(())
    }

    /// 检查文件是否存在
    pub async fn exists(&self, path: &Path) -> bool {
        if let Ok(safe_path) = self.validate_path(path) {
            let exists = safe_path.exists();
            trace!("文件存在检查：{:?} -> {}", safe_path, exists);
            exists
        } else {
            warn!("路径验证失败：{:?}", path);
            false
        }
    }

    /// 删除文件
    pub async fn remove_file(&self, path: &Path, ) -> Result<(), AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("删除文件：{:?}", safe_path);

        // 创建备份
        self.create_backup(&safe_path).await?;

        fs::remove_file(&safe_path).await
            .map_err(|e| {
                error!("删除失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })?;

        info!("文件已删除：{:?}", safe_path);
        Ok(())
    }

    /// 创建目录
    pub async fn create_dir_all(&self, path: &Path, ) -> Result<(), AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("创建目录：{:?}", safe_path);

        fs::create_dir_all(&safe_path).await
            .map_err(|e| {
                error!("目录创建失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })?;

        info!("目录已创建：{:?}", safe_path);
        Ok(())
    }

    /// 创建目录（别名方法）
    pub async fn create_directory(&self, path: &Path, ) -> Result<(), AdapterError> {
        self.create_dir_all(path).await
    }

    /// 写入二进制文件（别名方法）
    pub async fn write_binary(&self, path: &Path, data: &[u8], ) -> Result<(), AdapterError> {
        self.write_bytes(path, data).await
    }

    /// 列出目录内容
    pub async fn list_dir(&self, path: &Path, ) -> Result<Vec<PathBuf>, AdapterError> {
        let safe_path = self.validate_path(path)?;
        debug!("列出目录：{:?}", safe_path);

        let mut entries = Vec::new();
        let mut dir = fs::read_dir(&safe_path).await
            .map_err(|e| {
                error!("打开目录失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })?;

        while let Some(entry) = dir.next_entry().await
            .map_err(|e| {
                error!("读取目录项失败：{:?}，错误：{}", safe_path, e);
                AdapterError::IoError(e)
            })? {
            entries.push(entry.path());
        }

        trace!("目录 {:?} 包含 {} 个项目", safe_path, entries.len());
        Ok(entries)
    }

    /// 根据文件名读取 JSON 文件（相对于 base_path）
    pub async fn read_json_by_name<T>(&self, filename: &str) -> Result<T, AdapterError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let path = Path::new(filename);
        self.read_json(path).await
    }

    /// 根据文件名写入 JSON 文件（相对于 base_path）
    pub async fn write_json_by_name<T>(&self, filename: &str, data: &T) -> Result<(), AdapterError>
    where
        T: Serialize,
    {
        let path = Path::new(filename);
        self.write_json(path, data).await
    }

    /// 根据文件名读取文本文件（相对于 base_path）
    pub async fn read_text_by_name(&self, filename: &str) -> Result<String, AdapterError> {
        let path = Path::new(filename);
        self.read_text(path).await
    }

    /// 根据文件名写入文本文件（相对于 base_path）
    pub async fn write_text_by_name(&self, filename: &str, content: &str) -> Result<(), AdapterError> {
        let path = Path::new(filename);
        self.write_text(path, content).await
    }

    /// 根据文件名检查文件是否存在（相对于 base_path）
    pub async fn exists_by_name(&self, filename: &str) -> bool {
        let path = Path::new(filename);
        self.exists(path).await
    }

    /// 根据文件名删除文件（相对于 base_path）
    pub async fn remove_file_by_name(&self, filename: &str) -> Result<(), AdapterError> {
        let path = Path::new(filename);
        self.remove_file(path).await
    }

    /// 获取应用程序数据目录路径 (%APPDATA%/echowave_client)
    pub fn get_app_data_path(&self) -> &PathBuf {
        &self.config.base_path
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use serde_json::json;

    #[tokio::test]
    async fn test_file_adapter_creation() {
        let temp_dir = TempDir::new().unwrap();
        let config = FileConfig {
            base_path: temp_dir.path().to_path_buf(),
            ..Default::default()
        };

        let adapter = FileAdapter::new(config);
        assert!(adapter.is_ok());
    }

    #[tokio::test]
    async fn test_json_read_write() {
        let temp_dir = TempDir::new().unwrap();
        let config = FileConfig {
            base_path: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        let adapter = FileAdapter::new(config).unwrap();

        let test_data = json!({
            "name": "test",
            "value": 42
        });

        let file_path = Path::new("test.json");
        
        // 写入 JSON
        adapter.write_json(file_path, &test_data, ).await.unwrap();
        
        // 读取 JSON
        let read_data: serde_json::Value = adapter.read_json(file_path, ).await.unwrap();
        
        assert_eq!(test_data, read_data);
    }

    #[tokio::test]
    async fn test_text_read_write() {
        let temp_dir = TempDir::new().unwrap();
        let config = FileConfig {
            base_path: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        let adapter = FileAdapter::new(config).unwrap();

        let test_content = "Hello, World!";
        let file_path = Path::new("test.txt");
        
        // 写入文本
        adapter.write_text(file_path, test_content, ).await.unwrap();
        
        // 读取文本
        let read_content = adapter.read_text(file_path, ).await.unwrap();
        
        assert_eq!(test_content, read_content);
    }

    #[tokio::test]
    async fn test_path_validation() {
        let temp_dir = TempDir::new().unwrap();
        let config = FileConfig {
            base_path: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        let adapter = FileAdapter::new(config).unwrap();

        // 正常路径应该通过
        let normal_path = Path::new("test.txt");
        assert!(adapter.validate_path(normal_path).is_ok());

        // 路径遍历应该被阻止
        let traversal_path = Path::new("../../../etc/passwd");
        assert!(adapter.validate_path(traversal_path).is_err());
    }

    #[tokio::test]
    async fn test_file_exists() {
        let temp_dir = TempDir::new().unwrap();
        let config = FileConfig {
            base_path: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        let adapter = FileAdapter::new(config).unwrap();

        let file_path = Path::new("test.txt");
        
        // 文件不存在
        assert!(!adapter.exists(file_path).await);
        
        // 创建文件
        adapter.write_text(file_path, "test", ).await.unwrap();
        
        // 文件存在
        assert!(adapter.exists(file_path).await);
    }

    #[tokio::test]
    async fn test_file_operations_by_name() {
        let temp_dir = TempDir::new().unwrap();
        let config = FileConfig {
            base_path: temp_dir.path().to_path_buf(),
            ..Default::default()
        };
        let adapter = FileAdapter::new(config).unwrap();

        let test_data = json!({
            "name": "test",
            "value": 42
        });

        // 测试按文件名写入和读取 JSON
        adapter.write_json_by_name("test-config.json", &test_data).await.unwrap();
        let read_data: serde_json::Value = adapter.read_json_by_name("test-config.json").await.unwrap();
        assert_eq!(test_data, read_data);

        // 测试按文件名检查文件存在性
        assert!(adapter.exists_by_name("test-config.json").await);
        assert!(!adapter.exists_by_name("non-existent.json").await);

        // 测试按文件名文本操作
        adapter.write_text_by_name("test.txt", "Hello, World!").await.unwrap();
        let content = adapter.read_text_by_name("test.txt").await.unwrap();
        assert_eq!(content, "Hello, World!");
    }

    #[tokio::test]
    async fn test_app_data_path() {
        let config = FileConfig::default();
        let adapter = FileAdapter::new(config).unwrap();
        
        let path = adapter.get_app_data_path();
        assert!(path.ends_with("echowave_client"));
    }
}
