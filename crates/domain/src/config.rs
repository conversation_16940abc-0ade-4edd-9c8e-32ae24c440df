use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 构建模式
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum AppMode {
    Development,
    Production,
    Testing,
}

impl AppMode {
    pub fn as_str(&self) -> &'static str {
        match self {
            AppMode::Development => "development",
            AppMode::Production => "production",
            AppMode::Testing => "testing",
        }
    }
}

impl AppMode {
    #[inline]
    pub fn is_dev(&self) -> bool {
        matches!(self, AppMode::Development)
    }
    #[inline]
    pub fn is_prod(&self) -> bool {
        matches!(self, AppMode::Production)
    }
    #[inline]
    pub fn is_test(&self) -> bool {
        matches!(self, AppMode::Testing)
    }
}

#[derive(Debug, Clone)]
pub struct ContextConfig {
    pub app_mode: AppMode,
    pub version: String,

    pub userdata_dir: PathBuf,
    pub distro_name: String,
    pub product_name: String,

    pub min_disk_space: u64,

    pub environment: EnvironmentConfig,
}

impl Default for ContextConfig {
    fn default() -> Self {
        Self {
            app_mode: AppMode::Production,
            version: env!("CARGO_PKG_VERSION").to_string(),
            userdata_dir: dirs::data_dir()
                .unwrap_or_else(|| std::path::PathBuf::from("."))
                .join("echowave_client"),
            distro_name: "noble-rootfs".to_string(),
            product_name: "EchoWave".to_string(),
            min_disk_space: 1024 * 1024 * 1024 * 50, // 50GB
            environment: EnvironmentConfig::production(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct EnvironmentConfig {
    pub update_server: String,
    pub api_url: String,
    pub portal_url: String,
    pub client_name: String,
}

impl Default for EnvironmentConfig {
    fn default() -> Self {
        Self::production()
    }
}

impl EnvironmentConfig {
    pub fn development() -> Self {
        Self {
            update_server: "http://192.168.1.58:8000/releases".to_string(),
            api_url: "http://api.echowave.cn:8090".to_string(),
            portal_url: "https://www.echowave.cn".to_string(),
            client_name: "EchoWave 客户端(开发版)".to_string(),
        }
    }
    pub fn production() -> Self {
        Self {
            update_server: "http://cdn.echowave.cn/releases".to_string(),
            api_url: "http://api.echowave.cn:8090".to_string(),
            portal_url: "https://www.echowave.cn".to_string(),
            client_name: "EchoWave 客户端".to_string(),
        }
    }
    pub fn testing() -> Self {
        Self {
            update_server: "http://192.168.2.10/releases".to_string(),
            api_url: "http://api.echowave.cn:8090".to_string(),
            portal_url: "https://www.echowave.cn".to_string(),
            client_name: "EchoWave 客户端(测试版)".to_string(),
        }
    }
}
