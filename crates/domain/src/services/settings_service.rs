//! # 用户设置管理服务
//!
//! 负责管理 EchoWave 客户端的用户设置，包括文件存储和系统级配置。
//!
//! ## 功能概述
//!
//! - **文件设置管理**: 管理存储在 `%APPDATA%\echowave_client\user-settings.json` 的设置
//! - **开机自启动管理**: 通过注册表和启动目录管理开机自启动
//! - **设置同步**: 提供设置变更通知和事件机制
//! - **错误恢复**: 文件损坏时自动恢复默认设置
//!
//! ## 数据来源
//!
//! ### 1. 文件存储设置
//!
//! - **路径**: `%APPDATA%\echowave_client\user-settings.json`
//! - **示例**: `C:\Users\<USER>\AppData\Roaming\echowave_client\user-settings.json`
//! - **内容**: 仅包含需要持久化的设置（如 auto_accept_tasks）
//! - **格式**: JSON 格式，自动创建目录和文件
//!
//! ### 2. 系统级设置
//!
//! - **开机自启动**: 从 Windows 注册表和启动目录读取/写入
//!   - 注册表路径: `HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run`
//!   - 启动目录: `%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup`
//!   - 优先使用注册表，失败时降级到启动目录
//!
//! ## 设置项说明
//!
//! - **auto_accept_tasks**: 自动接单开关，存储在文件中
//! - **auto_start_on_boot**: 开机自启动，从系统注册表/启动目录读取，不存储在文件中
//!
//! ## 错误处理
//!
//! - 文件不存在时使用默认设置
//! - 文件损坏时记录错误并恢复默认设置  
//! - 注册表操作失败时尝试启动目录方式
//! - 权限不足时记录错误但不影响其他功能
//!
//! ## 使用示例
//!
//! ```rust
//! use crate::services::settings_service::*;
//! use tokio::sync::mpsc;
//!
//! // 创建设置服务
//! let (tx, _rx) = mpsc::unbounded_channel();
//! let config = SettingsServiceConfig::default();
//! let service = SettingsService::new(config, tx).await?;
//!
//! // 启动服务（自动加载设置）
//! service.start().await?;
//!
//! // 切换设置
//! let trace_id = uuid::Uuid::new_v4();
//! service.toggle_setting("auto_accept_tasks", trace_id).await?;
//!
//! // 获取当前设置
//! let settings = service.get_current_settings().await;
//! ```

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::sync::watch;
use tracing::{error, info, instrument};
use uuid::Uuid;

use super::{CoreService, ServiceFuture, ServiceStatus};
use crate::CoreResult;
use crate::adapters::file::FileAdapter;

#[cfg(target_os = "windows")]
use winreg::RegKey;
#[cfg(target_os = "windows")]
use winreg::enums::*;

/// 设置状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserSettingsState {
    /// 开启自动接单
    pub auto_accept_tasks: bool,
    /// 开机自启动（运行时状态，不存储在文件中）
    pub auto_start_on_boot: bool,
    /// 数据根目录(包含镜像数据、下载缓存、其他依赖文件)
    pub data_root_path: PathBuf,
}

/// 文件存储的设置结构（不包含开机自启动）
#[derive(Debug, Clone, Serialize, Deserialize)]
struct FileSettings {
    /// 开启自动接单
    pub auto_accept_tasks: bool,
    /// 数据根目录(包含镜像数据、下载缓存、其他依赖文件)
    pub data_root_path: PathBuf,
}

impl Default for UserSettingsState {
    fn default() -> Self {
        let default_storage_path = dirs::data_dir()
            .unwrap_or_else(|| std::path::PathBuf::from("."))
            .join("echowave_client");

        Self {
            auto_accept_tasks: true,
            auto_start_on_boot: false,
            data_root_path: default_storage_path,
        }
    }
}

impl UserSettingsState {
    pub fn get_distro_install_path(&self) -> PathBuf {
        self.data_root_path.join("distro")
    }
    pub fn get_cache_path(&self) -> PathBuf {
        self.data_root_path.join("cache")
    }
}

impl Default for FileSettings {
    fn default() -> Self {
        let default_storage_path = dirs::data_dir()
            .unwrap_or_else(|| std::path::PathBuf::from("."))
            .join("echowave_client");

        Self {
            auto_accept_tasks: true,
            data_root_path: default_storage_path,
        }
    }
}

/// 设置服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SettingsServiceConfig {
    pub auto_save: bool,
    pub backup_enabled: bool,
    pub validation_enabled: bool,
}

impl Default for SettingsServiceConfig {
    fn default() -> Self {
        Self {
            auto_save: true,
            backup_enabled: true,
            validation_enabled: true,
        }
    }
}

/// 简化版设置服务 - 解决当前架构问题
pub struct SettingsService {
    config: SettingsServiceConfig,
    state: watch::Sender<UserSettingsState>,
    status: watch::Sender<ServiceStatus>,
    is_dirty: AtomicBool,
    file_adapter: Arc<FileAdapter>,
}

impl SettingsService {
    /// 获取设置文件路径（用于测试）
    #[cfg(test)]
    fn get_settings_file_path() -> Result<PathBuf> {
        let app_data_dir =
            dirs::data_dir().ok_or_else(|| anyhow::anyhow!("Failed to get app data directory"))?;

        let settings_dir = app_data_dir.join("echowave_client");
        let settings_file = settings_dir.join("user-settings.json");

        Ok(settings_file)
    }

    /// 确保设置目录存在（用于测试）
    #[cfg(test)]
    async fn ensure_settings_dir() -> Result<PathBuf> {
        let settings_file = Self::get_settings_file_path()?;
        let settings_dir = settings_file
            .parent()
            .ok_or_else(|| anyhow::anyhow!("Failed to get settings directory"))?;

        if !settings_dir.exists() {
            tokio::fs::create_dir_all(settings_dir).await?;
        }

        Ok(settings_file)
    }

    /// 从文件加载设置
    async fn load_settings_from_file(&self) -> Result<FileSettings> {
        if !self.file_adapter.exists_by_name("user-settings.json").await {
            info!("User settings file does not exist, using defaults");
            return Ok(FileSettings::default());
        }

        match self
            .file_adapter
            .read_json_by_name::<FileSettings>("user-settings.json")
            .await
        {
            Ok(settings) => Ok(settings),
            Err(e) => {
                // 如果文件不存在或读取失败，返回默认设置
                info!("Failed to load settings file ({}), using defaults", e);
                Ok(FileSettings::default())
            }
        }
    }

    /// 保存设置到文件
    async fn save_settings_to_file(&self, settings: &FileSettings) -> Result<()> {
        self.file_adapter
            .write_json_by_name("user-settings.json", settings)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to save settings: {}", e))?;
        Ok(())
    }

    /// 检查开机自启动状态
    async fn get_auto_start_status() -> Result<bool> {
        #[cfg(target_os = "windows")]
        {
            // 首先检查注册表
            if let Ok(status) = Self::check_registry_auto_start() {
                info!("Check registry auto start status: {}", status);
                return Ok(status);
            }

            // 如果注册表检查失败，检查启动目录
            Self::check_startup_folder_auto_start().await
        }

        #[cfg(not(target_os = "windows"))]
        {
            info!("Auto start status: false, not supported non windows platform");
            // 非Windows系统暂时返回false
            Ok(false)
        }
    }

    /// 设置开机自启动状态
    async fn set_auto_start_status(enable: bool) -> Result<()> {
        #[cfg(target_os = "windows")]
        {
            // 首先尝试注册表方式
            if let Ok(()) = Self::set_registry_auto_start(enable) {
                return Ok(());
            }

            // 如果注册表失败，尝试启动目录方式
            Self::set_startup_folder_auto_start(enable).await
        }

        #[cfg(not(target_os = "windows"))]
        {
            // 非Windows系统暂时不支持
            let _ = enable; // 避免 unused variable 警告
            Err(anyhow::anyhow!(
                "Auto start is not supported on this platform"
            ))
        }
    }

    #[cfg(target_os = "windows")]
    /// 检查注册表中的自启动状态
    fn check_registry_auto_start() -> Result<bool> {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let run_key = hkcu.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run")?;

        match run_key.get_value::<String, _>("EchoWave") {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    #[cfg(target_os = "windows")]
    /// 设置注册表中的自启动状态
    fn set_registry_auto_start(enable: bool) -> Result<()> {
        let hkcu = RegKey::predef(HKEY_CURRENT_USER);
        let run_key = hkcu.open_subkey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run")?;

        if enable {
            let exe_path = std::env::current_exe()?;
            let exe_path_str = exe_path.to_string_lossy();
            run_key.set_value("EchoWave", &exe_path_str.as_ref())?;
        } else {
            let _ = run_key.delete_value("EchoWave"); // 忽略删除失败的错误
        }

        Ok(())
    }

    #[cfg(target_os = "windows")]
    /// 检查启动目录中的自启动状态
    async fn check_startup_folder_auto_start() -> Result<bool> {
        let startup_dir = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Failed to get config directory"))?
            .join("Microsoft")
            .join("Windows")
            .join("Start Menu")
            .join("Programs")
            .join("Startup");

        let shortcut_path = startup_dir.join("EchoWave.lnk");
        info!(
            "Check startup folder auto start status: {}",
            shortcut_path.exists()
        );
        Ok(shortcut_path.exists())
    }

    #[cfg(target_os = "windows")]
    /// 设置启动目录中的自启动状态
    async fn set_startup_folder_auto_start(enable: bool) -> Result<()> {
        let startup_dir = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Failed to get config directory"))?
            .join("Microsoft")
            .join("Windows")
            .join("Start Menu")
            .join("Programs")
            .join("Startup");

        let shortcut_path = startup_dir.join("EchoWave.lnk");

        if enable {
            // 创建快捷方式（这里简化实现，实际应该使用Windows API）
            // 由于创建快捷方式比较复杂，这里只是占位实现
            return Err(anyhow::anyhow!(
                "Startup folder method not fully implemented"
            ));
        } else {
            // 删除快捷方式
            if shortcut_path.exists() {
                tokio::fs::remove_file(shortcut_path).await?;
            }
        }

        Ok(())
    }

    /// 创建新的设置服务
    pub fn new(config: SettingsServiceConfig, file_adapter: Arc<FileAdapter>) -> Result<Self> {
        Ok(Self {
            config,
            state: watch::Sender::new(UserSettingsState::default()),
            status: watch::Sender::new(ServiceStatus::Stopped),
            is_dirty: AtomicBool::new(false),
            file_adapter,
        })
    }

    /// 更新设置
    #[instrument(skip(self))]
    pub async fn update_settings(&self, settings: UserSettingsState, trace_id: Uuid) -> Result<()> {
        info!("Updating settings");

        // 更新当前设置
        self.state.send_replace(settings);
        self.is_dirty.store(true, Ordering::SeqCst);

        // 如果启用自动保存，立即保存
        if self.config.auto_save {
            self.save_settings().await?;
        }

        info!("Settings updated successfully");
        Ok(())
    }

    /// 保存设置
    #[instrument(skip(self))]
    pub async fn save_settings(&self) -> Result<()> {
        if !self.is_dirty.load(Ordering::SeqCst) {
            return Ok(()); // 没有变更，无需保存
        }

        info!("Saving settings");

        // 实际的设置保存逻辑
        let file_settings = FileSettings {
            auto_accept_tasks: self.state.borrow().auto_accept_tasks,
            data_root_path: self.state.borrow().data_root_path.clone(),
        };
        self.save_settings_to_file(&file_settings).await?;

        // 清除脏标志
        self.is_dirty.store(false, Ordering::SeqCst);

        info!("Settings saved successfully");
        Ok(())
    }

    /// 获取当前设置
    pub fn get_current_settings(&self) -> UserSettingsState {
        self.state.borrow().clone()
    }

    pub fn get_distro_install_path(&self) -> PathBuf {
        self.state.borrow().get_distro_install_path()
    }
    pub fn get_cache_path(&self) -> PathBuf {
        self.state.borrow().get_cache_path()
    }

    /// 检查是否有未保存的变更
    pub fn has_unsaved_changes(&self) -> bool {
        self.is_dirty.load(Ordering::SeqCst)
    }

    /// 更新镜像安装路径
    #[instrument(skip(self))]
    pub async fn update_storage_path(&self, new_path: PathBuf, trace_id: Uuid) -> Result<()> {
        info!("Updating storage path to: {}", new_path.display());

        // 更新设置
        self.state.send_modify(|current| {
            current.data_root_path = new_path;
        });

        // 标记为脏
        self.is_dirty.store(true, Ordering::SeqCst);

        // 如果启用自动保存，立即保存
        if self.config.auto_save {
            self.save_settings().await?;
        }

        info!("Distro install path updated successfully");
        Ok(())
    }

    /// 迁移数据目录
    #[instrument(skip(self))]
    pub async fn set_data_root_path(&self, new_path: PathBuf, trace_id: Uuid) -> Result<()> {
        info!("Setting storage path to: {}", new_path.display());
        // todo: 检查目录权限
        // todo: 复制旧目录数据到新目录
        // todo: 删除旧目录
        // todo: 如果存在 vhdx 文件，则注销 WSL 镜像，复制 vhdx 到新目录后再直接导入 vhdx
        self.state.send_modify(|current| {
            current.data_root_path = new_path;
        });
        self.is_dirty.store(true, Ordering::SeqCst);
        if self.config.auto_save {
            self.save_settings().await?;
        }
        Ok(())
    }

    /// 检查目录权限
    async fn check_directory_permissions(&self, path: &PathBuf) -> Result<()> {
        // 检查父目录的写权限
        let parent = if path.exists() {
            path.clone()
        } else {
            path.parent()
                .ok_or_else(|| anyhow::anyhow!("无法获取父目录"))?
                .to_path_buf()
        };

        // 尝试创建测试文件来验证写权限
        let test_file = parent.join(".echowave_permission_test");

        match tokio::fs::write(&test_file, "test").await {
            Ok(()) => {
                // 清理测试文件
                let _ = tokio::fs::remove_file(&test_file).await;
                Ok(())
            }
            Err(e) => Err(anyhow::anyhow!("目录无写权限: {}", e)),
        }
    }

    /// 切换布尔设置
    #[instrument(skip(self))]
    pub async fn toggle_setting(&self, key: &str, trace_id: Uuid) -> Result<bool> {
        let new_value = {
            match key {
                "auto_accept_tasks" => {
                    self.state.send_modify(|current| {
                        current.auto_accept_tasks = !current.auto_accept_tasks;
                    });
                    self.state.borrow().auto_accept_tasks
                }
                "auto_start_on_boot" => {
                    let new_auto_start = !self.state.borrow().auto_start_on_boot;

                    // 尝试设置系统的开机自启动状态
                    match Self::set_auto_start_status(new_auto_start).await {
                        Ok(()) => {
                            self.state.send_modify(|current| {
                                current.auto_start_on_boot = new_auto_start;
                            });
                            info!("Auto start status updated to {}", new_auto_start);
                        }
                        Err(e) => {
                            error!("Failed to update auto start status: {}", e);
                            return Err(e);
                        }
                    }

                    self.state.borrow().auto_start_on_boot
                }
                _ => {
                    return Err(anyhow::anyhow!(
                        "Unknown or non-boolean setting key: {}",
                        key
                    ));
                }
            }
        };

        // 只有非开机自启动设置才标记为脏
        if key != "auto_start_on_boot" {
            self.is_dirty.store(true, Ordering::SeqCst);

            // 如果启用自动保存，立即保存
            if self.config.auto_save {
                self.save_settings().await?;
            }
        }

        info!("Setting {} toggled to {}", key, new_value);
        Ok(new_value)
    }

    pub fn state_receiver(&self) -> watch::Receiver<UserSettingsState> {
        self.state.subscribe()
    }
}

impl CoreService for SettingsService {
    fn name(&self) -> &str {
        "settings_service"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(self.health_check_impl())
    }
}

impl SettingsService {
    async fn start_impl(&self) -> CoreResult<()> {
        info!("Starting settings service");
        self.status.send_replace(ServiceStatus::Starting);
        // 加载设置
        match self.load_settings_from_file().await {
            Ok(file_settings) => {
                // 加载开机自启动状态
                let auto_start_status = Self::get_auto_start_status().await.unwrap_or(false);

                self.state.send_modify(|current| {
                    current.auto_accept_tasks = file_settings.auto_accept_tasks;
                    current.auto_start_on_boot = auto_start_status;
                    current.data_root_path = file_settings.data_root_path;
                });

                info!("Settings loaded successfully");
            }
            Err(e) => {
                error!("Failed to load settings: {}, using defaults", e);
            }
        }
        self.status.send_replace(ServiceStatus::Running);
        info!("Settings service started successfully");
        Ok(())
    }

    async fn stop_impl(&self) -> CoreResult<()> {
        info!("Stopping settings service");
        // 如果有未保存的变更，保存它们
        if let Err(err) = self.save_settings().await {
            error!("Failed to save settings: {}", err);
        }
        self.status.send_replace(ServiceStatus::Stopped);
        info!("Settings service stopped successfully");
        Ok(())
    }

    async fn health_check_impl(&self) -> CoreResult<bool> {
        // 检查服务健康状态
        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::adapters::file::FileConfig;

    #[tokio::test]
    async fn test_settings_service_creation() {
        let config = SettingsServiceConfig::default();
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        assert_eq!(service.name(), "settings_service");
        assert!(!service.has_unsaved_changes());

        let settings = service.get_current_settings();
        assert!(!settings.auto_accept_tasks);
        assert!(!settings.auto_start_on_boot);
        assert!(settings.data_root_path.is_dir());
    }

    #[tokio::test]
    async fn test_settings_update() {
        let config = SettingsServiceConfig::default();
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        let trace_id = Uuid::new_v4();

        let new_settings = UserSettingsState {
            auto_accept_tasks: true,
            auto_start_on_boot: true,
            data_root_path: PathBuf::from("/custom/path"),
        };

        service
            .update_settings(new_settings.clone(), trace_id)
            .await
            .unwrap();

        let current_settings = service.get_current_settings();
        assert_eq!(
            current_settings.auto_accept_tasks,
            new_settings.auto_accept_tasks
        );
        assert_eq!(
            current_settings.auto_start_on_boot,
            new_settings.auto_start_on_boot
        );
    }

    #[tokio::test]
    async fn test_setting_toggle() {
        let config = SettingsServiceConfig::default();
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        let trace_id = Uuid::new_v4();

        // 切换auto_accept_tasks
        let new_value = service
            .toggle_setting("auto_accept_tasks", trace_id)
            .await
            .unwrap();
        assert!(new_value);
        assert!(service.get_current_settings().auto_accept_tasks);

        // 再次切换
        let new_value = service
            .toggle_setting("auto_accept_tasks", trace_id)
            .await
            .unwrap();
        assert!(!new_value);
        assert!(!service.get_current_settings().auto_accept_tasks);
    }

    #[tokio::test]
    async fn test_file_settings_persistence() {
        let config = SettingsServiceConfig::default();
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        // 设置 auto_accept_tasks
        let trace_id = Uuid::new_v4();
        service
            .toggle_setting("auto_accept_tasks", trace_id)
            .await
            .unwrap();

        // 保存设置
        service.save_settings().await.unwrap();

        // 验证设置已保存
        // 注意：此测试不再直接验证文件，而是验证保存操作成功
        assert!(!service.has_unsaved_changes());
    }

    #[tokio::test]
    async fn test_service_lifecycle() {
        let config = SettingsServiceConfig::default();
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        // 检查初始状态
        assert_eq!(service.status(), ServiceStatus::Stopped);

        // 启动服务
        service.start().await.unwrap();
        assert_eq!(service.status(), ServiceStatus::Running);

        // 健康检查
        assert!(service.health_check().await.unwrap());

        // 停止服务
        service.stop().await.unwrap();
        assert_eq!(service.status(), ServiceStatus::Stopped);
    }

    #[tokio::test]
    async fn test_default_settings() {
        let default_settings = UserSettingsState::default();
        assert!(!default_settings.auto_accept_tasks);
        assert!(!default_settings.auto_start_on_boot);
        assert!(default_settings.data_root_path.is_dir());

        let default_file_settings = FileSettings::default();
        assert!(!default_file_settings.auto_accept_tasks);
        assert!(default_file_settings.data_root_path.is_dir());
    }

    #[tokio::test]
    async fn test_invalid_setting_key() {
        let config = SettingsServiceConfig::default();
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        let trace_id = Uuid::new_v4();
        let result = service.toggle_setting("invalid_key", trace_id).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_settings_file_path() {
        let path = SettingsService::get_settings_file_path().unwrap();
        assert!(path.to_string_lossy().contains("echowave_client"));
        assert!(path.to_string_lossy().contains("user-settings.json"));
    }

    #[tokio::test]
    async fn test_ensure_settings_dir() {
        let file_path = SettingsService::ensure_settings_dir().await.unwrap();
        let dir_path = file_path.parent().unwrap();
        assert!(dir_path.exists());
    }

    #[tokio::test]
    async fn test_update_distro_install_path() {
        let mut config = SettingsServiceConfig::default();
        config.auto_save = false; // 禁用自动保存以测试脏标记
        let file_adapter = Arc::new(FileAdapter::new(FileConfig::default()).unwrap());
        let service = SettingsService::new(config, file_adapter).unwrap();

        let trace_id = Uuid::new_v4();
        let new_path = PathBuf::from("/custom/distro/path");

        // 更新镜像安装路径
        service
            .update_storage_path(new_path.clone(), trace_id)
            .await
            .unwrap();

        // 验证设置已更新
        let settings = service.get_current_settings();
        assert_eq!(settings.data_root_path, new_path);

        // 验证设置被标记为脏
        assert!(service.has_unsaved_changes());
    }
}
