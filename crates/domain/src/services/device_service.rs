//! 设备服务
//!
//! 负责设备相关功能，包括设备识别、硬件信息、机器ID管理等
//!
//! 机器ID管理策略（参考 legacy/BackL0/Operations/GetUID.mjs）：
//! 1. 尝试生成硬件机器ID，失败时使用后备方案
//! 2. 从本地文件读取之前保存的机器ID
//! 3. 比较新旧机器ID，如果不同则标记为"新设备需要重新注册节点"
//! 4. 提供写入机器ID到文件的功能

use super::{CoreService, ServiceFuture, ServiceStatus};
use crate::utils::limited_wsl;
use crate::{CoreError, CoreResult, adapters::file::FileAdapter};
use serde::{Deserialize, Serialize};
use shared::system_gpu_ext::SystemGpuExt;
use shared::{machine_id, version::Version};
use std::{collections::HashMap, sync::Arc, time::Duration};
use sysinfo::System;
use tokio::{sync::watch, time::timeout};
use tokio_util::sync::CancellationToken;
use tracing::{error, info, warn};

/// 设备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    /// 机器ID
    pub machine_id: String,
    /// 设备名称
    pub device_name: Option<String>,
    /// 平台
    pub platform: String,
    /// 架构
    pub arch: String,
    /// 版本
    pub version: Version,
    /// WSL镜像版本
    pub distro_version: Version,
    /// Agent版本
    pub agent_version: Version,
    /// WSL版本
    pub wsl_version: Version,
    /// 是否为新设备, 使用 Option 类型，因为初始化时并不知道是否为新设备
    pub is_new_device: Option<bool>,
    /// 标签
    #[serde(skip)]
    pub tags: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GpuInfo {
    pub name: String,
    pub memory: u64,
}

impl Default for DeviceInfo {
    fn default() -> Self {
        Self {
            machine_id: "".to_string(), // 将在启动时生成
            device_name: None,
            platform: std::env::consts::OS.to_string(),
            arch: std::env::consts::ARCH.to_string(),
            version: Version::default(),
            distro_version: Version::default(),
            agent_version: Version::default(),
            wsl_version: Version::default(),
            is_new_device: None, // 将在检查时确定
            tags: HashMap::new(),
        }
    }
}

impl DeviceInfo {
    /// 生成机器ID，失败时使用后备方案
    pub async fn generate_machine_id() -> String {
        // 尝试使用硬件因子生成机器ID
        match machine_id::get_machine_id_with_factors(vec![
            machine_id::MachineIdFactor::Baseboard,
            machine_id::MachineIdFactor::Processor,
            machine_id::MachineIdFactor::DiskDrives,
        ]) {
            Ok((id, factors)) => {
                info!("machine_id: {}", id);
                info!("Successfully generated machine ID using hardware factors");
                info!("Machine ID factors: {:?}", factors);
                id
            }
            Err(e) => {
                warn!(
                    "Failed to generate machine ID using hardware factors: {:?}",
                    e
                );
                Self::fallback_machine_id().await
            }
        }
    }

    /// 后备方案生成机器ID（参考 legacy 项目的 fallback 和 _f_mock 方法）
    async fn fallback_machine_id() -> String {
        info!("Using fallback method to generate machine ID");

        #[cfg(target_os = "windows")]
        {
            // Windows 后备方案：使用 UUID
            match Self::get_windows_uuid().await {
                Ok(uuid) => {
                    use sha2::{Digest, Sha256};
                    let mut hasher = Sha256::new();
                    hasher.update(uuid.as_bytes());
                    let result = hasher.finalize();
                    format!("{:x}", result)
                }
                Err(e) => {
                    warn!("Windows UUID fallback failed: {:?}", e);
                    Self::simple_fallback()
                }
            }
        }

        #[cfg(not(target_os = "windows"))]
        {
            // 非 Windows 平台使用简单后备方案
            Self::simple_fallback()
        }
    }

    #[cfg(target_os = "windows")]
    async fn get_windows_uuid() -> Result<String, std::io::Error> {
        use shared::command::create_command;

        let output = create_command("powershell")
            .args(&[
                "-Command",
                "Get-CimInstance -Class Win32_ComputerSystemProduct | Select-Object -ExpandProperty UUID"
            ])
            .output()
            .await?;

        if output.status.success() {
            let uuid = String::from_utf8_lossy(&output.stdout).trim().to_string();
            if !uuid.is_empty() && uuid != "03000200-0400-0500-0006-000700080009" {
                // 避免默认UUID
                return Ok(uuid);
            }
        }

        Err(std::io::Error::new(
            std::io::ErrorKind::Other,
            "Failed to get UUID",
        ))
    }

    /// 简单后备方案（基于系统信息生成）
    fn simple_fallback() -> String {
        use sha2::{Digest, Sha256};

        let mut info_parts = Vec::new();
        info_parts.push(std::env::consts::OS.to_string());
        info_parts.push(std::env::consts::ARCH.to_string());

        if let Ok(hostname) = std::env::var("COMPUTERNAME").or_else(|_| std::env::var("HOSTNAME")) {
            info_parts.push(hostname);
        }

        if let Ok(username) = std::env::var("USERNAME").or_else(|_| std::env::var("USER")) {
            info_parts.push(username);
        }

        let info_string = info_parts.join(":");
        let mut hasher = Sha256::new();
        hasher.update(info_string.as_bytes());
        let result = hasher.finalize();

        // 返回前32个字符
        format!("{:x}", result)[..32].to_string()
    }

    /// 获取设备唯一标识
    pub fn get_machine_id(&self) -> &str {
        &self.machine_id
    }

    /// 检查是否为新设备
    pub fn is_new_device(&self) -> Option<bool> {
        self.is_new_device
    }
}

/// 基于 U8 字节标记的采集状态
/// - 0b0000_0000: 未开始采集
/// - 0b0000_0001: 机器 ID 已就绪
/// - 0b0000_0010: Client 版本就绪
/// - 0b0000_0100: WSL 镜像版本就绪
/// - 0b0000_1000: Agent 版本就绪
/// - 0b0001_0000: WSL 版本就绪（WSL 属于外部依赖，通常不会更新，但还是应该记录）
/// - 0b0001_1111: 所有状态就绪
pub struct CollectStatus(u8);

impl CollectStatus {
    pub fn is_ready(&self) -> bool {
        self.0 == 0b0001_1111
    }
    pub fn is_machine_id_ready(&self) -> bool {
        self.0 & 0b0000_0001 == 0b0000_0001
    }
    pub fn is_client_version_ready(&self) -> bool {
        self.0 & 0b0000_0010 == 0b0000_0010
    }
    pub fn is_mirror_version_ready(&self) -> bool {
        self.0 & 0b0000_0100 == 0b0000_0100
    }
    pub fn is_agent_version_ready(&self) -> bool {
        self.0 & 0b0000_1000 == 0b0000_1000
    }
    // 额外拓展
    pub fn is_wsl_version_ready(&self) -> bool {
        self.0 & 0b0001_0000 == 0b0001_0000
    }
    pub fn mark_machine_id_ready(&self) -> Self {
        Self(self.0 | 0b0000_0001)
    }
    pub fn mark_client_version_ready(&self) -> Self {
        Self(self.0 | 0b0000_0010)
    }
    pub fn mark_distro_version_ready(&self) -> Self {
        Self(self.0 | 0b0000_0100)
    }
    pub fn mark_agent_version_ready(&self) -> Self {
        Self(self.0 | 0b00001000)
    }
    pub fn mark_wsl_version_ready(&self) -> Self {
        Self(self.0 | 0b0001_0000)
    }
}

/// 设备服务
pub struct DeviceService {
    /// 文件适配器
    file_adapter: Arc<FileAdapter>,
    /// 设备信息
    device_info: watch::Sender<DeviceInfo>,
    /// 服务状态
    status: watch::Sender<ServiceStatus>,
    /// 终止信号
    signal: CancellationToken,
    /// 采集状态
    collect_status: watch::Sender<CollectStatus>,
}

impl DeviceService {
    /// 机器ID文件名
    const MACHINE_ID_FILE: &'static str = "machineid";

    /// 创建新的设备服务
    pub fn new(file_adapter: Arc<FileAdapter>) -> CoreResult<Self> {
        Ok(Self {
            file_adapter,
            device_info: watch::Sender::new(DeviceInfo::default()),
            status: watch::Sender::new(ServiceStatus::Stopped),
            signal: CancellationToken::new(),
            collect_status: watch::Sender::new(CollectStatus(0)),
        })
    }

    /// 获取当前设备信息
    pub fn get_device_info(&self) -> DeviceInfo {
        self.device_info.borrow().clone()
    }
    /// 获取客户端版本
    pub fn get_client_version(&self) -> Version {
        self.device_info.borrow().version.clone()
    }
    /// 设置客户端版本
    pub fn set_client_version(&self, version: Version) {
        self.device_info.send_modify(|info| {
            info.version = version;
        });
        self.collect_status.send_modify(|status| {
            *status = status.mark_client_version_ready();
        });
    }
    /// 获取WSL镜像版本
    pub fn get_distro_version(&self) -> Version {
        self.device_info.borrow().distro_version.clone()
    }
    /// 设置WSL镜像版本
    pub fn set_distro_version(&self, version: Version) {
        self.device_info.send_modify(|info| {
            info.distro_version = version;
        });
        self.collect_status.send_modify(|status| {
            *status = status.mark_distro_version_ready();
        });
    }
    /// 获取Agent版本
    pub fn get_agent_version(&self) -> Version {
        self.device_info.borrow().agent_version.clone()
    }
    /// 设置Agent版本
    pub fn set_agent_version(&self, version: Version) {
        self.device_info.send_modify(|info| {
            info.agent_version = version;
        });
        self.collect_status.send_modify(|status| {
            *status = status.mark_agent_version_ready();
        });
    }
    pub fn set_wsl_version(&self, version: Version) {
        self.device_info.send_modify(|info| {
            info.wsl_version = version;
        });
        self.collect_status.send_modify(|status| {
            *status = status.mark_wsl_version_ready();
        });
    }
    pub fn get_wsl_version(&self) -> Version {
        self.device_info.borrow().wsl_version.clone()
    }
    /// 获取标签
    pub fn get_tags(&self) -> HashMap<String, String> {
        self.device_info.borrow().tags.clone()
    }

    /// 等待采集状态就绪
    pub async fn wait_ready(&self) -> anyhow::Result<()> {
        let mut status = self.collect_status.subscribe();
        let lop = async move {
            while let Ok(()) = status.changed().await {
                let status = status.borrow();
                if status.is_ready() {
                    break;
                }
            }
        };
        match timeout(Duration::from_secs(10), lop).await {
            Ok(()) => Ok(()),
            Err(e) => Err(anyhow::anyhow!("wait_ready timeout in 10 seconds: {}", e)),
        }
    }

    /// 获取设备信息接收器
    pub fn state_receiver(&self) -> watch::Receiver<DeviceInfo> {
        self.device_info.subscribe()
    }

    /// 获取机器ID
    pub fn get_machine_id(&self) -> String {
        self.device_info.borrow().machine_id.clone()
    }

    /// 获取设备名称
    pub fn get_device_name(&self) -> Option<String> {
        self.device_info.borrow().device_name.clone()
    }
    /// 获取系统名称
    pub fn get_os_name(&self) -> Option<String> {
        self.device_info.borrow().tags.get("os_name").cloned()
    }
    /// 获取系统版本
    pub fn get_os_version(&self) -> Option<String> {
        self.device_info.borrow().tags.get("os_version").cloned()
    }
    /// 获取内核版本
    pub fn get_kernel_version(&self) -> Option<String> {
        self.device_info
            .borrow()
            .tags
            .get("kernel_version")
            .cloned()
    }
    /// 获取CPU品牌
    pub fn get_cpu_brand(&self) -> Option<String> {
        self.device_info.borrow().tags.get("cpu_brand").cloned()
    }
    /// 获取CPU架构
    pub fn get_cpu_arch(&self) -> Option<String> {
        self.device_info.borrow().tags.get("cpu_arch").cloned()
    }
    /// 获取CPU核心数
    pub fn get_cpu_core_count(&self) -> Option<String> {
        self.device_info
            .borrow()
            .tags
            .get("cpu_core_count")
            .cloned()
    }
    /// 获取内存大小
    pub fn get_total_memory_gb(&self) -> Option<String> {
        self.device_info
            .borrow()
            .tags
            .get("total_memory_gb")
            .cloned()
    }
    /// 获取内存大小
    pub fn get_memory_tier(&self) -> Option<String> {
        self.device_info.borrow().tags.get("memory_tier").cloned()
    }
    /// 获取GPU名称
    pub fn get_gpu_name(&self) -> Option<String> {
        self.device_info.borrow().tags.get("gpu_name").cloned()
    }
    /// 获取GPU品牌
    pub fn get_gpu_brand(&self) -> Option<String> {
        self.device_info.borrow().tags.get("gpu_brand").cloned()
    }
    /// 获取主机名
    pub fn get_hostname(&self) -> Option<String> {
        self.device_info.borrow().tags.get("hostname").cloned()
    }

    /// 从文件加载机器ID
    async fn load_machine_id_from_file(&self) -> Option<String> {
        info!("Loading machine ID from file");

        if self
            .file_adapter
            .exists_by_name(Self::MACHINE_ID_FILE)
            .await
        {
            info!("Machine ID file exists");
        } else {
            info!("Machine ID file does not exist");
            return None;
        }

        match self
            .file_adapter
            .read_text_by_name(Self::MACHINE_ID_FILE)
            .await
        {
            Ok(content) => {
                let machine_id = content.trim().to_string();
                if !machine_id.is_empty() {
                    info!("Successfully loaded machine ID from file: {}", machine_id);
                    Some(machine_id)
                } else {
                    info!("Machine ID file is empty");
                    None
                }
            }
            Err(e) => {
                info!("Failed to load machine ID from file: {:?}", e);
                None
            }
        }
    }

    /// 将机器ID写入文件
    pub async fn write_machine_id_to_file(&self) -> CoreResult<()> {
        info!("Writing machine ID to file");

        let machine_id = self.get_machine_id();
        self.file_adapter
            .write_text_by_name(Self::MACHINE_ID_FILE, &machine_id)
            .await
            .map_err(|e| {
                error!("Failed to write machine ID to file: {:?}", e);
                CoreError::FileError(e)
            })?;

        info!("Successfully wrote machine ID to file");
        Ok(())
    }

    /// 清除新设备标记
    pub fn clear_new_device_flag(&self) {
        self.device_info.send_modify(|info| {
            info.is_new_device = Some(false);
        });
    }

    /// 初始化设备信息和检查是否为新设备
    pub async fn initialize_device_info(&self) -> CoreResult<()> {
        info!("Initializing device information");

        // 生成当前机器ID
        let current_machine_id = DeviceInfo::generate_machine_id().await;

        // 从文件加载之前保存的机器ID
        let saved_machine_id = self.load_machine_id_from_file().await;

        // 检查是否为新设备
        let is_new_device = match &saved_machine_id {
            Some(saved_id) => {
                let is_new = saved_id != &current_machine_id;
                if is_new {
                    info!(
                        "Machine ID mismatch, marking as new device: '{}' => '{}'",
                        saved_id, current_machine_id
                    );
                } else {
                    info!("Machine ID matches, existing device");
                }
                is_new
            }
            None => {
                info!("No saved machine ID found, marking as new device");
                true
            }
        };

        // 更新设备信息
        self.device_info.send_modify(|info| {
            info.machine_id = current_machine_id;
            info.version = Version::parse(env!("CARGO_PKG_VERSION")).unwrap();
            info.is_new_device = Some(is_new_device);
        });
        // 更新采集状态
        self.collect_status.send_modify(|status| {
            *status = status.mark_machine_id_ready().mark_client_version_ready();
        });
        // 采集标签信息

        let mut sys = System::new_all();
        sys.refresh_all(); // 刷新所有信息

        let mut tags = HashMap::new();

        // 1. 操作系统信息
        if let Some(name) = System::name() {
            tags.insert("os_name".to_string(), name);
        }
        if let Some(version) = System::os_version() {
            tags.insert("os_version".to_string(), version);
        }
        if let Some(kernel) = System::kernel_version() {
            tags.insert("kernel_version".to_string(), kernel);
        }

        // 2. CPU 信息
        if let Some(cpu) = sys.cpus().first() {
            // 通常所有核心的品牌和架构都一样，取第一个即可
            tags.insert("cpu_brand".to_string(), cpu.brand().to_string());
        }
        tags.insert("cpu_arch".to_string(), std::env::consts::ARCH.to_string());
        tags.insert("cpu_core_count".to_string(), sys.cpus().len().to_string());

        // 3. 内存信息
        // 为了方便灰度控制，可以将内存大小分级
        let total_memory_gb = sys.total_memory() as f64 / 1024.0 / 1024.0 / 1024.0;
        let memory_tier = if total_memory_gb >= 31.0 {
            "32GB+"
        } else if total_memory_gb >= 15.0 {
            "16GB"
        } else if total_memory_gb >= 7.0 {
            "8GB"
        } else if total_memory_gb >= 3.0 {
            "4GB"
        } else {
            "<4GB"
        };
        tags.insert(
            "total_memory_gb".to_string(),
            format!("{:.2}", total_memory_gb),
        );
        tags.insert("memory_tier".to_string(), memory_tier.to_string());

        // 4. GPU 信息
        // 注意：一个系统可能有多个 GPU，这里我们简单地获取第一个 GPU 的信息
        // 如果你的应用对多 GPU 敏感，可以考虑更复杂的逻辑
        if let Some(gpu) = sys.gpus().first() {
            tags.insert("gpu_name".to_string(), gpu.name().to_string());
            tags.insert("gpu_brand".to_string(), gpu.brand().to_string());
        } else {
            tags.insert("gpu_name".to_string(), "N/A".to_string());
            tags.insert("gpu_brand".to_string(), "N/A".to_string());
        }

        // 5. 主机信息
        if let Some(hostname) = System::host_name() {
            tags.insert("hostname".to_string(), hostname);
        }
        self.device_info.send_modify(|info| {
            info.tags = tags;
        });

        info!("Device information initialized successfully");
        Ok(())
    }

    /// 异步启动实现
    async fn start_impl(&self) -> CoreResult<()> {
        info!("Starting device service");
        if let Err(err) = limited_wsl::set_limited_wsl_config().await {
            tracing::error!("Failed to set limited WSL config: {:?}", err);
        };

        self.status.send_modify(|status| {
            *status = ServiceStatus::Running;
        });

        // 初始化设备信息
        self.initialize_device_info().await?;

        info!("Device service started successfully");
        Ok(())
    }

    /// 异步停止实现
    async fn stop_impl(&self) -> CoreResult<()> {
        info!("Stopping device service");
        if let Err(err) = limited_wsl::unset_limited_wsl_config().await {
            tracing::error!("Failed to unset limited WSL config: {:?}", err);
        };

        self.status.send_modify(|status| {
            *status = ServiceStatus::Stopping;
        });

        self.signal.cancel();

        self.status.send_modify(|status| {
            *status = ServiceStatus::Stopped;
        });

        info!("Device service stopped successfully");
        Ok(())
    }
}

impl CoreService for DeviceService {
    fn name(&self) -> &str {
        "device_service"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(async { Ok(true) })
    }
}
