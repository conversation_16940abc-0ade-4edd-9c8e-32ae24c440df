//! 平台和操作系统版本检查模块
//!
//! 负责检查平台兼容性和操作系统版本要求

use super::super::types::{CheckStatus, ManualAction};
use crate::CoreResult;
use shared::os_release;

/// 平台检查结果
pub struct PlatformCheckResult {
    pub status: CheckStatus,
    pub message: String,
    pub manual_action: Option<ManualAction>,
}

/// 检查平台（本地检查）
///
/// ## 执行步骤
///
/// - 检查平台是否为 Windows x86_64，在开发模式，允许 MacOS Arm 通过
pub async fn check_platform() -> CoreResult<PlatformCheckResult> {
    tracing::info!("Checking platform");

    let platform = std::env::consts::OS;
    let arch = std::env::consts::ARCH;

    let (status, message, manual_action) = match (platform, arch) {
        ("windows", "x86_64") => (CheckStatus::Passed, "Windows x86_64".to_string(), None),
        ("windows", _) => (
            CheckStatus::AutoFixFailed,
            format!("Windows {arch}"),
            Some(ManualAction::ContactSupport),
        ),
        #[cfg(debug_assertions)]
        ("macos", "aarch64") => (CheckStatus::Passed, "MacOS Arm".to_string(), None),
        _ => (
            CheckStatus::AutoFixFailed,
            format!("{platform} {arch}"),
            Some(ManualAction::ContactSupport),
        ),
    };

    Ok(PlatformCheckResult {
        status,
        message,
        manual_action,
    })
}

/// 检查操作系统版本
pub async fn check_os_version() -> CoreResult<PlatformCheckResult> {
    tracing::info!("Checking OS version");

    match os_release::get_os_release().await {
        Ok(os_release) => {
            tracing::info!("OS release: {:?}", os_release);

            if os_release.platform == "windows" {
                // Windows平台检查构建号
                if let Some(build_str) = &os_release.build_number {
                    if let Some(build_number) = parse_windows_build(&build_str) {
                        if build_number >= 10240 {
                            Ok(PlatformCheckResult {
                                status: CheckStatus::Passed,
                                message: format!("Windows Build {}", build_number),
                                manual_action: None,
                            })
                        } else {
                            Ok(PlatformCheckResult {
                                status: CheckStatus::AutoFixFailed,
                                message: format!("Windows Build {} (需要 >= 10240)", build_number),
                                manual_action: Some(ManualAction::ContactSupport),
                            })
                        }
                    } else {
                        Ok(PlatformCheckResult {
                            status: CheckStatus::AutoFixFailed,
                            message: "无法解析Windows构建号".to_string(),
                            manual_action: None,
                        })
                    }
                } else {
                    Ok(PlatformCheckResult {
                        status: CheckStatus::AutoFixFailed,
                        message: "无法获取Windows构建号".to_string(),
                        manual_action: None,
                    })
                }
            } else {
                // 非Windows平台在开发模式下通过
                #[cfg(debug_assertions)]
                {
                    Ok(PlatformCheckResult {
                        status: CheckStatus::Passed,
                        message: format!(
                            "开发模式 - {} {}",
                            os_release.platform, os_release.version
                        ),
                        manual_action: None,
                    })
                }
                #[cfg(not(debug_assertions))]
                {
                    Ok(PlatformCheckResult {
                        status: CheckStatus::AutoFixFailed,
                        message: format!("不支持的平台: {}", os_release.platform),
                        manual_action: Some(ManualAction::ContactSupport),
                    })
                }
            }
        }
        Err(e) => {
            tracing::warn!("Failed to get OS release: {:?}", e);
            Ok(PlatformCheckResult {
                status: CheckStatus::AutoFixFailed,
                message: format!("获取系统信息失败: {}", e),
                manual_action: None,
            })
        }
    }
}

/// 解析Windows构建号
fn parse_windows_build(build_str: &str) -> Option<u32> {
    // build_str 格式为 "19045.1234" 或 "19045"
    build_str.split('.').next()?.parse().ok()
}
