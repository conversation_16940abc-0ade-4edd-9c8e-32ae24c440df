//! 虚拟化检查模块
//!
//! 负责检查CPU虚拟化支持和操作系统虚拟化启用状态

use crate::utils::virtualization;
use crate::CoreResult;
use super::super::types::{CheckStatus, ManualAction};

/// 虚拟化检查结果
pub struct VirtualizationCheckResult {
    pub status: CheckStatus,
    pub message: String,
    pub manual_action: Option<ManualAction>,
    pub details: Option<serde_json::Value>,
}

/// 检查虚拟化支持
///
/// ## 检查步骤
/// 1. 优先使用helper adapter检查虚拟化支持
/// 2. 如果helper不可用，回退到本地检查
/// 3. 检查CPU是否支持虚拟化技术(VT-x/AMD-V)
/// 4. 检查操作系统是否报告虚拟化已启用
/// 5. 综合判断虚拟化是否可用
///
/// ## 错误处理
/// - 如果CPU不支持虚拟化，提示用户升级硬件
/// - 如果CPU支持但OS未启用，引导用户到FAQ页面查看BIOS设置方法
pub async fn check_virtualization() -> CoreResult<VirtualizationCheckResult> {
    tracing::info!("Checking virtualization support");
    
    // 获取虚拟化信息
    let virt_info = virtualization::get_virtualization();
    tracing::info!(
        "Local virtualization info: CPU支持={}, OS启用={}, 详情: {}",
        virt_info.cpu_supported,
        virt_info.os_reported_enabled,
        virt_info.overall_status_message
    );

    // 将虚拟化详情存储到检查结果中
    let details = serde_json::json!({
        "cpu_supported": virt_info.cpu_supported,
        "cpu_feature_name": virt_info.cpu_feature_name,
        "os_reported_enabled": virt_info.os_reported_enabled,
        "os_check_details": virt_info.os_check_details,
        "arch": virt_info.arch,
        "os": virt_info.os,
        "check_source": "local"
    });

    if virt_info.cpu_supported && virt_info.os_reported_enabled {
        // 虚拟化完全可用
        Ok(VirtualizationCheckResult {
            status: CheckStatus::Passed,
            message: format!("虚拟化可用 ({})", virt_info.cpu_feature_name),
            manual_action: None,
            details: Some(details),
        })
    } else if virt_info.cpu_supported && !virt_info.os_reported_enabled {
        // CPU支持但OS未启用 - 需要用户在BIOS中启用
        Ok(VirtualizationCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: format!("CPU支持{}但未在BIOS中启用", virt_info.cpu_feature_name),
            manual_action: Some(ManualAction::OpenFaqPage),
            details: Some(details),
        })
    } else if !virt_info.cpu_supported && virt_info.os_reported_enabled {
        // 不常见情况：CPU检测失败但OS报告支持（可能在虚拟机中）
        Ok(VirtualizationCheckResult {
            status: CheckStatus::Passed,
            message: "虚拟化可用 (可能运行在虚拟环境中)".to_string(),
            manual_action: None,
            details: Some(details),
        })
    } else {
        // CPU不支持虚拟化
        Ok(VirtualizationCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: format!("CPU不支持虚拟化技术 ({})", virt_info.cpu_feature_name),
            manual_action: Some(ManualAction::OpenFaqPage),
            details: Some(details),
        })
    }
} 