//! PowerShell检查模块
//!
//! 负责检查PowerShell的安装状态、版本和自动安装功能

use super::super::types::{CheckStatus, ManualAction, PowerShellStatus, ProgressInfo};
use crate::CoreError;
use crate::CoreResult;
use shared::bin_where;
use shared::command::create_command;
use tokio::sync::watch;

/// PowerShell检查结果
pub struct PowerShellCheckResult {
    pub status: CheckStatus,
    pub message: String,
    pub manual_action: Option<ManualAction>,
    pub progress: Option<ProgressInfo>,
    pub powershell_status: PowerShellStatus,
}

/// 检查 PowerShell
pub async fn check_powershell(
    auto_fix_enabled: bool,
    state_sender: &watch::Sender<super::super::types::SystemState>,
) -> CoreResult<PowerShellCheckResult> {
    tracing::info!("Checking PowerShell");

    // 只在开发模式下的macOS跳过PowerShell检查
    if std::env::consts::OS == "macos" {
        #[cfg(debug_assertions)]
        {
            return Ok(PowerShellCheckResult {
                status: CheckStatus::Passed,
                message: "开发模式 - 跳过PowerShell检查".to_string(),
                manual_action: None,
                progress: None,
                powershell_status: PowerShellStatus {
                    is_installed: true,
                    version: Some("跳过".to_string()),
                    installation_progress: 100,
                },
            });
        }
    }

    // 非Windows平台需要PowerShell但不支持自动安装
    if std::env::consts::OS != "windows" {
        return Ok(PowerShellCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: "非Windows平台不支持PowerShell自动安装".to_string(),
            manual_action: None,
            progress: None,
            powershell_status: PowerShellStatus::default(),
        });
    }

    // 检查PowerShell是否安装
    match bin_where::bin_where("powershell") {
        Some(path) => {
            tracing::info!("PowerShell found at: {:?}", path);

            // 检查PowerShell版本
            match get_powershell_version().await {
                Ok(version) => Ok(PowerShellCheckResult {
                    status: CheckStatus::Passed,
                    message: format!("PowerShell {}", version),
                    manual_action: None,
                    progress: None,
                    powershell_status: PowerShellStatus {
                        is_installed: true,
                        version: Some(version),
                        installation_progress: 100,
                    },
                }),
                Err(e) => {
                    tracing::warn!("Failed to get PowerShell version: {}", e);
                    Ok(PowerShellCheckResult {
                        status: CheckStatus::Passed,
                        message: "PowerShell已安装(版本未知)".to_string(),
                        manual_action: None,
                        progress: None,
                        powershell_status: PowerShellStatus {
                            is_installed: true,
                            version: None,
                            installation_progress: 100,
                        },
                    })
                }
            }
        }
        None => {
            tracing::info!("PowerShell not found, attempting auto-install");

            if auto_fix_enabled {
                // 开始自动安装流程
                let install_result = install_powershell_with_progress(state_sender).await;

                match install_result {
                    Ok(_) => Ok(PowerShellCheckResult {
                        status: CheckStatus::Passed,
                        message: "PowerShell安装成功".to_string(),
                        manual_action: None,
                        progress: None,
                        powershell_status: PowerShellStatus {
                            is_installed: true,
                            version: None,
                            installation_progress: 100,
                        },
                    }),
                    Err(e) => {
                        tracing::warn!("PowerShell auto-install failed: {}", e);
                        Ok(PowerShellCheckResult {
                            status: CheckStatus::AutoFixFailed,
                            message: format!("PowerShell自动安装失败: {}", e),
                            manual_action: None,
                            progress: None,
                            powershell_status: PowerShellStatus::default(),
                        })
                    }
                }
            } else {
                Ok(PowerShellCheckResult {
                    status: CheckStatus::AutoFixFailed,
                    message: "PowerShell未安装".to_string(),
                    manual_action: None,
                    progress: None,
                    powershell_status: PowerShellStatus::default(),
                })
            }
        }
    }
}

/// 获取PowerShell版本
async fn get_powershell_version() -> CoreResult<String> {
    let output = create_command("powershell")
        .args(["-Command", "$PSVersionTable.PSVersion.ToString()"])
        .output()
        .await
        .map_err(|e| CoreError::operation_failed(&format!("获取PowerShell版本失败: {}", e)))?;

    if output.status.success() {
        let version = String::from_utf8_lossy(&output.stdout).trim().to_string();
        Ok(version)
    } else {
        Err(CoreError::operation_failed("PowerShell版本查询失败"))
    }
}

/// 自动安装PowerShell（带进度更新）
async fn install_powershell_with_progress(
    state_sender: &watch::Sender<super::super::types::SystemState>,
) -> CoreResult<()> {
    // 更新初始进度
    state_sender.send_modify(|state| {
        state.powershell_status.installation_progress = 25;
        if let Some(check_result) = state.check_results.get_mut(2) {
            check_result.progress = Some(ProgressInfo {
                operation: "正在安装PowerShell".to_string(),
                percent: 25,
                current_step: "通过winget安装".to_string(),
            });
        }
    });

    let output = create_command("winget")
        .args([
            "install",
            "--id",
            "Microsoft.PowerShell",
            "--source",
            "winget",
            "--accept-package-agreements",
            "--accept-source-agreements",
        ])
        .output()
        .await
        .map_err(|e| CoreError::operation_failed(&format!("winget执行失败: {}", e)))?;

    // 更新进度
    state_sender.send_modify(|state| {
        state.powershell_status.installation_progress = 90;
        if let Some(check_result) = state.check_results.get_mut(2) {
            if let Some(progress) = &mut check_result.progress {
                progress.percent = 90;
                progress.current_step = "安装完成，验证中...".to_string();
            }
        }
    });

    if output.status.success() {
        let stdout = String::from_utf8_lossy(&output.stdout);
        if stdout.contains("Successfully installed") || stdout.contains("PowerShell") {
            tracing::info!("PowerShell installed successfully");

            // 完成进度
            state_sender.send_modify(|state| {
                state.powershell_status.installation_progress = 100;
            });

            Ok(())
        } else {
            Err(CoreError::operation_failed("PowerShell安装可能失败"))
        }
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr);
        Err(CoreError::operation_failed(&format!(
            "PowerShell安装失败: {}",
            stderr
        )))
    }
}
