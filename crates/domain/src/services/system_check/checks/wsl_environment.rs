//! WSL环境检查模块
//!
//! 负责检查WSL环境配置，包括Windows功能启用和WSL installer状态

use super::super::types::{CheckStatus, ManualAction, ProgressInfo, WSLManagerStatus};
#[cfg(target_os = "windows")]
use crate::CoreError;
use crate::CoreResult;
use crate::adapters::helper::HelperAdapter;
use crate::utils::wsl_manager;
use shared::version::Version;
use std::sync::Arc;
use tokio::sync::watch;

#[cfg(target_os = "windows")]
use crate::utils::windows_feature;
#[cfg(target_os = "windows")]
use protocol::events::FeatureStatus;

/// WSL环境检查结果
pub struct WslEnvironmentCheckResult {
    pub status: CheckStatus,
    pub message: String,
    pub manual_action: Option<ManualAction>,
    pub progress: Option<ProgressInfo>,
    pub details: Option<serde_json::Value>,
    pub wsl_manager_status: WSLManagerStatus,
    pub wsl_manager_version: Option<Version>,
}

/// 检查 WSL 环境（通过 Helper 和本地）
///
/// 1. 检查 HyperVisor 可选功能是否启用
/// 2. 检查 Windows Subsystem Linux 可选功能是否启用
/// 3. 检查 Virtual Machine Platform 可选功能是否启用
/// 4. 检查 Wsl 是否安装
///
/// ## 自动解决步骤
///
/// - 调用 helper-svc 启动相关的可选功能，完成后应该需要重启，前端给出按钮让用户点击重启电脑
/// - 如果 WSL installer 未安装，则自动下载并安装
#[cfg(target_os = "windows")]
pub async fn check_wsl_environment(
    helper_adapter: Option<Arc<HelperAdapter>>,
    auto_fix_enabled: bool,
    helper_fallback_enabled: bool,
    state_sender: &watch::Sender<super::super::types::SystemState>,
    context_config: &crate::config::ContextConfig,
) -> CoreResult<WslEnvironmentCheckResult> {
    tracing::info!("Checking WSL environment");
    // 定义WSL相关的Windows功能
    let wsl_features = vec![
        "Microsoft-Windows-Subsystem-Linux".to_string(),
        "VirtualMachinePlatform".to_string(),
        "Microsoft-Hyper-V".to_string(),
    ];

    // 尝试使用helper adapter进行WSL环境检查
    if let Some(helper) = helper_adapter {
        tracing::info!("Using helper adapter for WSL environment check");

        let feature_refs: Vec<&str> = wsl_features.iter().map(|s| s.as_str()).collect();
        match helper.check_multiple_windows_features(&feature_refs).await {
            Ok(feature_results) => {
                tracing::info!(
                    "Helper WSL environment check completed: {:?}",
                    feature_results
                );

                // 分析功能状态
                let mut missing_features = Vec::new();
                let mut all_enabled = true;

                for (feature, result) in &feature_results {
                    match result {
                        Ok(status) => {
                            if *status != FeatureStatus::Enabled {
                                all_enabled = false;
                                missing_features.push(feature.clone());
                            }
                        }
                        Err(e) => {
                            tracing::warn!("Failed to check feature {}: {}", feature, e);
                            all_enabled = false;
                            missing_features.push(feature.clone());
                        }
                    }
                }

                // 构建详细信息（忽略feature_results因为包含不可序列化的错误类型）
                let result_details = serde_json::json!({
                    "missing_features": missing_features,
                    "wsl_executable_check": wsl_manager::WslManager::check_wsl_executable()
                });

                if all_enabled {
                    // 所有功能都已启用, 继续检查 WSL installer
                    check_wsl_installer_status(context_config, state_sender).await
                } else if auto_fix_enabled {
                    // 自动修复：启用缺失的功能
                    enable_wsl_features_with_helper(
                        helper,
                        missing_features,
                        state_sender,
                        context_config,
                    )
                    .await
                } else {
                    // 不启用自动修复
                    Ok(WslEnvironmentCheckResult {
                        status: CheckStatus::AutoFixFailed,
                        message: format!(
                            "WSL环境未完全配置，缺少功能: {}",
                            missing_features.join(", ")
                        ),
                        manual_action: Some(ManualAction::OpenWindowsFeatures),
                        progress: None,
                        details: Some(result_details),
                        wsl_manager_status: WSLManagerStatus::default(),
                        wsl_manager_version: None,
                    })
                }
            }
            Err(e) => {
                tracing::warn!("Helper WSL environment check failed: {}", e);
                if helper_fallback_enabled {
                    // 回退到本地检查
                    check_wsl_environment_local(state_sender, context_config).await
                } else {
                    Err(CoreError::operation_failed(&format!(
                        "Helper WSL environment check failed: {}",
                        e
                    )))
                }
            }
        }
    } else {
        // helper不可用，使用本地检查
        tracing::info!("Helper adapter not available, using local WSL environment check");
        check_wsl_environment_local(state_sender, context_config).await
    }
}

#[cfg(not(target_os = "windows"))]
pub async fn check_wsl_environment(
    _helper_adapter: Option<Arc<HelperAdapter>>,
    _auto_fix_enabled: bool,
    _helper_fallback_enabled: bool,
    _state_sender: &watch::Sender<super::super::types::SystemState>,
    _context_config: &crate::config::ContextConfig,
) -> CoreResult<WslEnvironmentCheckResult> {
    tracing::info!("Checking WSL environment");

    tracing::warn!("WSL environment check not supported on non-Windows systems");
    return Ok(WslEnvironmentCheckResult {
        status: CheckStatus::AutoFixFailed,
        message: "不支持的系统".to_string(),
        manual_action: Some(ManualAction::ContactSupport),
        progress: None,
        details: Some(serde_json::json!({
            "platform": std::env::consts::OS,
            "error": "WSL only supported on Windows"
        })),
        wsl_manager_status: WSLManagerStatus::default(),
        wsl_manager_version: None,
    });
}

/// 本地WSL环境检查（回退实现）
#[cfg(target_os = "windows")]
async fn check_wsl_environment_local(
    state_sender: &watch::Sender<super::super::types::SystemState>,
    context_config: &crate::config::ContextConfig,
) -> CoreResult<WslEnvironmentCheckResult> {
    tracing::info!("Performing local WSL environment check");

    // 使用综合检测方法检查WSL环境
    let wsl_executable_exists = wsl_manager::WslManager::check_wsl_executable();

    // 通过WMI检查WSL相关功能状态
    let wmi_result = windows_feature::wsl::check_wsl_via_wmi();

    // 通过注册表检查WSL服务
    let registry_check = windows_feature::wsl::check_wsl_via_reg();

    let mut details = serde_json::json!({
        "wsl_executable_exists": wsl_executable_exists,
        "registry_check": registry_check,
        "platform": std::env::consts::OS,
        "check_type": "comprehensive_local"
    });

    // 处理WMI检查结果
    let (wsl_feature_enabled, vmp_feature_enabled) = match wmi_result {
        Ok((wsl_enabled, vmp_enabled)) => {
            details["wmi_check"] = serde_json::json!({
                "success": true,
                "wsl_feature_enabled": wsl_enabled,
                "virtual_machine_platform_enabled": vmp_enabled
            });
            (wsl_enabled, vmp_enabled)
        }
        Err(e) => {
            tracing::warn!("WMI WSL check failed: {}", e);
            details["wmi_check"] = serde_json::json!({
                "success": false,
                "error": e.to_string()
            });
            (false, false)
        }
    };

    // 通过Windows服务检查WSL管理器
    let service_running = match windows_feature::wsl::check_wsl_via_service() {
        Ok(running) => {
            details["service_check"] = serde_json::json!({
                "success": true,
                "lxss_manager_running": running
            });
            running
        }
        Err(e) => {
            tracing::warn!("WSL service check failed: {}", e);
            details["service_check"] = serde_json::json!({
                "success": false,
                "error": e.to_string()
            });
            false
        }
    };

    // 综合判断WSL环境状态
    let wsl_fully_functional = wsl_feature_enabled && vmp_feature_enabled && registry_check;
    let wsl_partially_functional = wsl_executable_exists || registry_check || service_running;

    if wsl_fully_functional {
        // WSL环境完全可用，继续检查installer
        check_wsl_installer_status(context_config, state_sender).await
    } else if wsl_partially_functional {
        // WSL部分可用，但可能缺少某些组件
        let mut missing_components = Vec::new();
        if !wsl_feature_enabled {
            missing_components.push("WSL功能未启用");
        }
        if !vmp_feature_enabled {
            missing_components.push("虚拟机平台未启用");
        }
        if !registry_check {
            missing_components.push("WSL服务未注册");
        }

        Ok(WslEnvironmentCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: format!("WSL环境部分可用，缺少: {}", missing_components.join(", ")),
            manual_action: Some(ManualAction::OpenWindowsFeatures),
            progress: None,
            details: Some(details),
            wsl_manager_status: WSLManagerStatus::default(),
            wsl_manager_version: None,
        })
    } else {
        // WSL环境完全不可用
        Ok(WslEnvironmentCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: "WSL环境未安装或未配置".to_string(),
            manual_action: Some(ManualAction::OpenWindowsFeatures),
            progress: None,
            details: Some(details),
            wsl_manager_status: WSLManagerStatus::default(),
            wsl_manager_version: None,
        })
    }
}

/// 使用Helper启用WSL功能
#[cfg(target_os = "windows")]
async fn enable_wsl_features_with_helper(
    helper: Arc<HelperAdapter>,
    missing_features: Vec<String>,
    state_sender: &watch::Sender<super::super::types::SystemState>,
    context_config: &crate::config::ContextConfig,
) -> CoreResult<WslEnvironmentCheckResult> {
    // 自动修复：启用缺失的功能
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(4) {
            check_result.status = CheckStatus::AutoFixing;
            check_result.message = "正在启用WSL相关功能...".to_string();
            check_result.progress = Some(ProgressInfo {
                operation: "启用Windows功能".to_string(),
                percent: 0,
                current_step: format!("处理{}个功能", missing_features.len()),
            });
        }
    });

    let mut enable_results = Vec::new();
    let mut any_restart_required = false;

    for (i, feature) in missing_features.iter().enumerate() {
        // 更新进度
        let progress = ((i + 1) as f32 / missing_features.len() as f32 * 100.0) as u8;
        state_sender.send_modify(|state| {
            if let Some(check_result) = state.check_results.get_mut(4) {
                if let Some(progress_info) = &mut check_result.progress {
                    progress_info.percent = progress;
                    progress_info.current_step = format!("启用功能: {}", feature);
                }
            }
        });

        match helper.enable_windows_feature(feature).await {
            Ok((status, restart_required)) => {
                enable_results.push((feature.clone(), status, restart_required));
                if restart_required {
                    any_restart_required = true;
                }
            }
            Err(e) => {
                tracing::warn!("Failed to enable feature {}: {}", feature, e);
                enable_results.push((feature.clone(), FeatureStatus::Unknown, false));
            }
        }
    }

    // 检查启用结果
    let all_enabled_successfully = enable_results
        .iter()
        .all(|(_, status, _)| *status == FeatureStatus::Enabled);

    let final_details = serde_json::json!({
        "enable_results": enable_results,
        "restart_required": any_restart_required,
        "wsl_executable_check": wsl_manager::WslManager::check_wsl_executable()
    });

    if all_enabled_successfully {
        if any_restart_required {
            Ok(WslEnvironmentCheckResult {
                status: CheckStatus::AutoFixFailed,
                message: "WSL功能已启用，需要重启系统".to_string(),
                manual_action: Some(ManualAction::RestartRequired),
                progress: None,
                details: Some(final_details),
                wsl_manager_status: WSLManagerStatus::default(),
                wsl_manager_version: None,
            })
        } else {
            // WSL 功能已启用，继续检查 WSL installer
            check_wsl_installer_status(context_config, state_sender).await
        }
    } else {
        Ok(WslEnvironmentCheckResult {
            status: CheckStatus::AutoFixFailed,
            message: "部分WSL功能启用失败".to_string(),
            manual_action: if any_restart_required {
                Some(ManualAction::RestartRequired)
            } else {
                Some(ManualAction::OpenWindowsFeatures)
            },
            progress: None,
            details: Some(final_details),
            wsl_manager_status: WSLManagerStatus::default(),
            wsl_manager_version: None,
        })
    }
}

/// 检查 WSL installer 状态（第四步检查）
///
/// ## 检查步骤
///
/// 1. 检查 WSL 功能完整性（区分引导程序 vs 管理程序）
/// 2. 验证 WSL 命令的可用性（`wsl --version`, `wsl --list`）
/// 3. 如果只有引导程序，下载并安装完整的 WSL installer
///
/// ## 自动解决步骤
///
/// - 检测到只有 WSL 引导程序时，自动下载 WSL MSI 安装包
/// - 使用 msiexec 安装 WSL 管理程序
/// - 重新验证 WSL 功能完整性
#[cfg(target_os = "windows")]
async fn check_wsl_installer_status(
    _context_config: &crate::config::ContextConfig,
    state_sender: &watch::Sender<super::super::types::SystemState>,
) -> CoreResult<WslEnvironmentCheckResult> {
    tracing::info!("检查 WSL installer 状态（第四步）");

    // 检查 WSL 功能完整性
    match wsl_manager::WslManager::check_wsl_functionality().await {
        Ok((is_fully_functional, version)) => {
            if is_fully_functional {
                // WSL 管理程序已完整安装
                state_sender.send_modify(|state| {
                    state.wsl_installer_status.is_installed = true;
                    state.wsl_installer_status.version = Some("管理程序".to_string());
                });

                Ok(WslEnvironmentCheckResult {
                    status: CheckStatus::Passed,
                    message: "WSL 环境完全可用（管理程序已安装）".to_string(),
                    manual_action: None,
                    progress: None,
                    details: Some(serde_json::json!({
                        "wsl_functionality": "full",
                        "installer_type": "full_manager",
                    })),
                    wsl_manager_status: WSLManagerStatus {
                        is_installed: true,
                        is_downloaded: true,
                        download_progress: 100,
                        install_progress: 100,
                        version: Some("管理程序".to_string()),
                    },
                    wsl_manager_version: version,
                })
            } else {
                // 检测到只有引导程序，需要安装完整的 WSL
                tracing::info!("检测到 WSL 引导程序，需要安装完整的 WSL 管理程序");

                Ok(WslEnvironmentCheckResult {
                    status: CheckStatus::AutoFixFailed,
                    message: "检测到 WSL 引导程序，需要安装完整的 WSL 管理程序".to_string(),
                    manual_action: None,
                    progress: None,
                    details: Some(serde_json::json!({
                        "wsl_functionality": "bootstrap_only",
                        "installer_type": "bootstrap",
                        "required_action": "install_full_wsl"
                    })),
                    wsl_manager_status: WSLManagerStatus::default(),
                    wsl_manager_version: None,
                })
            }
        }
        Err(e) => {
            tracing::warn!("WSL 功能检查失败: {}", e);
            Ok(WslEnvironmentCheckResult {
                status: CheckStatus::AutoFixFailed,
                message: format!("WSL 功能检查失败: {}", e),
                manual_action: None,
                progress: None,
                details: Some(serde_json::json!({
                    "wsl_functionality": "error",
                    "error": e.to_string()
                })),
                wsl_manager_status: WSLManagerStatus::default(),
                wsl_manager_version: None,
            })
        }
    }
}
