//! 系统检查辅助工具模块
//!
//! 提供通用的辅助方法，如状态更新、Helper适配器管理等

use crate::adapters::helper::HelperAdapter;
use crate::CoreResult;
use crate::CoreError;
use super::types::{CheckStatus, ManualAction, ProgressInfo, SystemState};
use tokio::sync::watch;
use std::sync::Arc;

/// 更新检查结果的辅助方法
pub fn update_check_result(
    state_sender: &watch::Sender<SystemState>,
    index: usize,
    status: CheckStatus,
    message: String,
    progress: Option<ProgressInfo>,
    manual_action: Option<ManualAction>,
) {
    state_sender.send_modify(|state| {
        if index < state.check_results.len() {
            state.check_results[index].status = status;
            state.check_results[index].message = message;
            state.check_results[index].progress = progress;
            state.check_results[index].manual_action = manual_action;
        }
    });
}

/// 更新检查结果（包含数据来源信息）
pub fn update_check_result_with_source(
    state_sender: &watch::Sender<SystemState>,
    index: usize,
    status: CheckStatus,
    message: String,
    progress: Option<ProgressInfo>,
    manual_action: Option<ManualAction>,
    source: &str, // "helper" 或 "local"
    details: Option<serde_json::Value>,
) {
    state_sender.send_modify(|state| {
        if index < state.check_results.len() {
            state.check_results[index].status = status;
            state.check_results[index].message = message;
            state.check_results[index].progress = progress;
            state.check_results[index].manual_action = manual_action;

            // 在详情中记录检查来源
            let mut result_details = details.unwrap_or_default();
            if let serde_json::Value::Object(ref mut map) = result_details {
                map.insert(
                    "check_source".to_string(),
                    serde_json::Value::String(source.to_string()),
                );
            } else {
                result_details = serde_json::json!({
                    "check_source": source
                });
            }
            state.check_results[index].details = Some(result_details);
        }
    });
}

/// 更新检查结果（包含详情）
pub fn update_check_result_with_details(
    state_sender: &watch::Sender<SystemState>,
    index: usize,
    status: CheckStatus,
    message: String,
    progress: Option<ProgressInfo>,
    manual_action: Option<ManualAction>,
    details: Option<serde_json::Value>,
) {
    state_sender.send_modify(|state| {
        if index < state.check_results.len() {
            state.check_results[index].status = status;
            state.check_results[index].message = message;
            state.check_results[index].progress = progress;
            state.check_results[index].manual_action = manual_action;
            state.check_results[index].details = details;
        }
    });
}

/// Helper适配器管理器
pub struct HelperManager {
    helper_adapter: Arc<HelperAdapter>,
    helper_enabled: bool,
    helper_fallback_enabled: bool,
}

impl HelperManager {
    pub fn new(
        helper_adapter: Arc<HelperAdapter>,
        helper_enabled: bool,
        helper_fallback_enabled: bool,
    ) -> Self {
        Self {
            helper_adapter,
            helper_enabled,
            helper_fallback_enabled,
        }
    }

    /// 检查 Helper 服务是否可用
    pub async fn is_helper_available(&self) -> bool {
        if !self.helper_enabled {
            return false;
        }
        self.helper_adapter.check_service_availability().await
    }

    /// 安全地使用 Helper 适配器。如果 Helper 不可用或操作失败，返回 None
    /// 这允许调用者决定是否回退到本地实现
    pub async fn with_helper<T>(&self, operation_name: &str) -> CoreResult<Option<Arc<HelperAdapter>>> {
        if !self.is_helper_available().await {
            tracing::debug!(
                "Helper service not available for operation: {}",
                operation_name
            );
            if !self.helper_fallback_enabled {
                return Err(CoreError::operation_failed(&format!(
                    "Helper service required for {} but not available",
                    operation_name
                )));
            }
            return Ok(None);
        }

        Ok(Some(self.helper_adapter.clone()))
    }

    /// 请求系统重启（延迟30秒）
    pub async fn request_system_restart(&self) -> CoreResult<()> {
        match self.with_helper::<()>("system_restart").await? {
            Some(helper) => match helper.restart_system(30).await {
                Ok(_) => {
                    tracing::info!("系统重启已安排在30秒后执行");
                    Ok(())
                }
                Err(e) => {
                    if self.helper_fallback_enabled {
                        tracing::warn!("Helper 重启失败，但没有本地实现: {}", e);
                        Err(CoreError::operation_failed(
                            "Helper服务重启失败，无本地实现",
                        ))
                    } else {
                        Err(CoreError::operation_failed(&format!(
                            "Helper重启失败: {}",
                            e
                        )))
                    }
                }
            },
            None => Err(CoreError::operation_failed(
                "Helper服务不可用，无法自动重启",
            )),
        }
    }
}

/// 检查结果缓存项
#[derive(Debug, Clone)]
pub struct CachedCheckResult {
    pub result: super::types::CheckResult,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 进度更新辅助函数
pub fn update_progress(
    state_sender: &watch::Sender<SystemState>,
    check_index: usize,
    operation: String,
    percent: u8,
    current_step: String,
) {
    state_sender.send_modify(|state| {
        if let Some(check_result) = state.check_results.get_mut(check_index) {
            check_result.progress = Some(ProgressInfo {
                operation,
                percent,
                current_step,
            });
        }
    });
} 