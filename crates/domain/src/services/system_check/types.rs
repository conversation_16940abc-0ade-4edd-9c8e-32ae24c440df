//! 系统检查相关的类型定义
//!
//! 包含检查类型、状态、结果等核心数据结构的定义

use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq)]
pub enum SystemCheckKind {
    Platform,
    OsVersion,
    PowerShell,
    Virtualization,
    WslEnvironment,
    WslMirror,
}

impl SystemCheckKind {
    // 检查项顺序
    // 前面的修改改为使用 SystemState，不要重复定义

    pub fn name(&self) -> &'static str {
        match self {
            SystemCheckKind::Platform => "platform",
            SystemCheckKind::OsVersion => "os_version",
            SystemCheckKind::PowerShell => "powershell",
            SystemCheckKind::Virtualization => "virtualization",
            SystemCheckKind::WslEnvironment => "wsl_environment",
            SystemCheckKind::WslMirror => "wsl_mirror",
        }
    }

    pub fn from_index(index: usize) -> SystemCheckKind {
        SystemState::EXECUTION_ORDER
            .get(index)
            .copied()
            .unwrap_or(SystemCheckKind::Platform)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum CheckStatus {
    NotStarted,
    Checking,      // 正在检查
    AutoFixing,    // 正在自动修复（下载、安装等）
    Passed,        // 检查通过
    AutoFixFailed, // 自动修复失败，需要用户介入
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ManualAction {
    OpenWindowsFeatures, // 打开Windows功能对话框（虚拟化设置）
    RestartRequired,     // 需要重启系统
    ChangeInstallPath,   // 更改安装路径（磁盘空间不足）
    ContactSupport,      // 联系技术支持
    OpenFaqPage,         // 打开FAQ页面（BIOS虚拟化设置指南）
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProgressInfo {
    pub operation: String,    // "正在下载WSL镜像"、"正在安装PowerShell"
    pub percent: u8,          // 0-100
    pub current_step: String, // 当前步骤描述
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckResult {
    pub kind: SystemCheckKind,
    pub status: CheckStatus,
    pub message: String,
    pub progress: Option<ProgressInfo>,      // 自动化过程的进度
    pub manual_action: Option<ManualAction>, // 只有AutoFixFailed时才有值
    pub details: Option<serde_json::Value>,
}

impl CheckResult {
    pub fn new(kind: SystemCheckKind) -> Self {
        Self {
            kind,
            status: CheckStatus::NotStarted,
            message: "".to_string(),
            progress: None,
            manual_action: None,
            details: None,
        }
    }
}

/// 系统状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemState {
    pub check_results: Vec<CheckResult>,
    pub powershell_status: PowerShellStatus,
    pub wsl_mirror_status: WSLMirrorStatus,
    pub wsl_installer_status: WSLManagerStatus,
    pub is_check_running: bool,
    pub last_check_time: Option<String>,
}

impl SystemState {
    pub fn all_checks_passed(&self) -> bool {
        self.check_results
            .iter()
            .all(|r| r.status == CheckStatus::Passed)
    }

    // 检查项顺序
    pub const EXECUTION_ORDER: [SystemCheckKind; 6] = [
        SystemCheckKind::Platform,
        SystemCheckKind::OsVersion,
        SystemCheckKind::PowerShell,
        SystemCheckKind::Virtualization,
        SystemCheckKind::WslEnvironment,
        SystemCheckKind::WslMirror,
    ];
}

impl Default for SystemState {
    fn default() -> Self {
        Self {
            check_results: Self::EXECUTION_ORDER
                .iter()
                .map(|kind| CheckResult::new(*kind))
                .collect(),
            powershell_status: PowerShellStatus::default(),
            wsl_mirror_status: WSLMirrorStatus::default(),
            wsl_installer_status: WSLManagerStatus::default(),
            is_check_running: false,
            last_check_time: None,
        }
    }
}

/// PowerShell 状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PowerShellStatus {
    pub is_installed: bool,
    pub version: Option<String>,
    pub installation_progress: u8,
}

impl Default for PowerShellStatus {
    fn default() -> Self {
        Self {
            is_installed: false,
            version: None,
            installation_progress: 0,
        }
    }
}

/// WSL 镜像状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WSLMirrorStatus {
    pub is_downloaded: bool,
    pub is_installed: bool,
    pub download_progress: u8,
    pub install_progress: u8,
    pub version: Option<String>,
}

impl Default for WSLMirrorStatus {
    fn default() -> Self {
        Self {
            is_downloaded: false,
            is_installed: false,
            download_progress: 0,
            install_progress: 0,
            version: None,
        }
    }
}

/// WSL 安装器状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WSLManagerStatus {
    pub is_installed: bool,
    pub is_downloaded: bool,
    pub download_progress: u8,
    pub install_progress: u8,
    pub version: Option<String>,
}

impl Default for WSLManagerStatus {
    fn default() -> Self {
        Self {
            is_installed: false,
            is_downloaded: false,
            download_progress: 0,
            install_progress: 0,
            version: None,
        }
    }
}

/// 系统条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConditions {
    pub user_logged_in: bool,
    pub all_checks_passed: bool,
    pub currently_accepting: bool,
}

impl Default for SystemConditions {
    fn default() -> Self {
        Self {
            user_logged_in: false,
            all_checks_passed: false,
            currently_accepting: false,
        }
    }
}

/// 系统检查服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemCheckConfig {
    /// 检查超时时间（秒）
    pub check_timeout: u64,
    /// 是否启用自动修复
    pub auto_fix_enabled: bool,
    /// 是否启用 Helper 服务
    pub helper_enabled: bool,
    /// Helper 失败时是否回退到本地实现
    pub helper_fallback_enabled: bool,
}

impl Default for SystemCheckConfig {
    fn default() -> Self {
        Self {
            check_timeout: 30,
            auto_fix_enabled: false,
            helper_enabled: true,
            helper_fallback_enabled: true,
        }
    }
}
