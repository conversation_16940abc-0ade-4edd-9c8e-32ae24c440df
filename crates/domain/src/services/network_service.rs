//! 网络状态管理服务
//!
//! 负责监控网络连接状态、Tailscale延迟等网络相关功能
//!
//! - 网络连接状态：通过多重检测方法判断（DNS解析、HTTP请求）
//! - Tailscale 延迟：通过调用 agent adapter 实现

use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::Arc;
use std::sync::atomic::{AtomicI64, AtomicU32};
use std::time::Duration;
use tokio::sync::{Mutex, watch};
use tokio::time::sleep;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, instrument, warn};

use crate::adapters::ModuleAdapter;
use crate::adapters::agent::AgentAdapter;
use crate::services::{CoreService, ServiceFuture, ServiceStatus};
use crate::{CoreError, CoreResult};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionHistoryItem {
    pub timestamp: String,
    pub status: String,
    pub latency: Option<u32>,
}

/// 网络状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkState {
    pub tailscale_latency: Option<u32>,
    pub internet_connectivity: bool,
    pub last_update_time: String,
    pub connection_history: VecDeque<ConnectionHistoryItem>,
}

impl Default for NetworkState {
    fn default() -> Self {
        Self {
            tailscale_latency: None,
            internet_connectivity: false,
            last_update_time: chrono::Utc::now().to_rfc3339(),
            connection_history: VecDeque::new(),
        }
    }
}

/// 网络服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkServiceConfig {
    /// 网络状态检查间隔（秒）
    pub check_interval: u64,
    /// Tailscale延迟检测超时时间（毫秒）
    pub ping_timeout_ms: u64,
    /// 连接历史记录保留数量
    pub history_limit: usize,
    /// 自动重试次数
    pub retry_attempts: u32,

    // === 延迟检测配置 ===
    /// 延迟检测间隔（秒）
    /// 控制多久检测一次 Tailscale 延迟
    pub latency_check_interval: u64,
    /// 延迟检测启用标志
    /// 是否启用 Tailscale 延迟检测功能
    pub latency_check_enabled: bool,
    /// 延迟警告阈值（毫秒）
    /// 延迟超过此值时记录警告日志
    pub latency_warning_threshold_ms: u32,
    /// 延迟错误阈值（毫秒）
    /// 延迟超过此值时记录错误日志并可能触发恢复机制
    pub latency_error_threshold_ms: u32,

    // === 重连策略配置 ===
    /// 最大重连尝试次数
    /// Agent 连接失败时的最大重连次数
    pub max_reconnect_attempts: u32,
    /// 重连初始间隔（秒）
    /// 第一次重连的等待时间
    pub reconnect_initial_delay_secs: u64,
    /// 重连最大间隔（秒）
    /// 重连等待时间的上限
    pub reconnect_max_delay_secs: u64,
    /// 重连指数退避因子
    /// 每次重连失败后，等待时间乘以此因子
    pub reconnect_backoff_multiplier: f64,
}

impl Default for NetworkServiceConfig {
    fn default() -> Self {
        Self {
            // === 基础配置 ===
            check_interval: 30,
            ping_timeout_ms: 5000,
            history_limit: 100,
            retry_attempts: 3,

            // === 延迟检测配置 ===
            latency_check_interval: 30,        // 每30秒检测一次延迟
            latency_check_enabled: true,       // 默认启用延迟检测
            latency_warning_threshold_ms: 200, // 延迟超过200ms记录警告
            latency_error_threshold_ms: 500,   // 延迟超过500ms记录错误

            // === 重连策略配置 ===
            max_reconnect_attempts: 3,         // 最多重连3次
            reconnect_initial_delay_secs: 1,   // 初始等待1秒
            reconnect_max_delay_secs: 30,      // 最大等待30秒
            reconnect_backoff_multiplier: 2.0, // 每次失败后等待时间翻倍
        }
    }
}

/// 连接质量级别
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ConnectionQuality {
    Excellent, // < 50ms
    Good,      // 50-100ms
    Fair,      // 100-200ms
    Poor,      // 200-500ms
    Bad,       // > 500ms
    Offline,   // 无连接
}

impl ConnectionQuality {
    pub fn from_latency(latency_ms: u32) -> Self {
        match latency_ms {
            0..=49 => Self::Excellent,
            50..=99 => Self::Good,
            100..=199 => Self::Fair,
            200..=499 => Self::Poor,
            _ => Self::Bad,
        }
    }
}

/// 网络服务
pub struct NetworkService {
    config: NetworkServiceConfig,
    status: watch::Sender<ServiceStatus>,
    state: watch::Sender<NetworkState>,
    last_check_time: Arc<AtomicI64>,
    agent_adapter: Arc<AgentAdapter>,
    monitoring_handle: Arc<Mutex<Option<(tokio::task::JoinHandle<()>, CancellationToken)>>>,
}

/// 网络服务监控器（用于在后台任务中执行检查）
pub struct NetworkServiceMonitor {
    config: NetworkServiceConfig,
    state: watch::Sender<NetworkState>,
    last_check_time: Arc<AtomicI64>,
    consecutive_same_results: Arc<AtomicU32>,
    last_connectivity_state: Arc<Mutex<Option<bool>>>,
    agent_adapter: Arc<AgentAdapter>,
    /// Agent连接状态缓存，避免频繁检查
    agent_connection_cache: Arc<Mutex<Option<(bool, std::time::Instant)>>>,
}

impl NetworkServiceMonitor {
    /// 智能检查Agent连接状态（带缓存）
    /// 缓存Agent连接状态5秒，避免频繁检查影响性能
    async fn is_agent_connected_cached(&self) -> bool {
        const CACHE_DURATION: Duration = Duration::from_secs(5);

        let mut cache = self.agent_connection_cache.lock().await;

        // 检查缓存是否仍有效
        if let Some((cached_state, cached_time)) = *cache {
            if cached_time.elapsed() < CACHE_DURATION {
                return cached_state;
            }
        }

        // 缓存过期，重新检查
        let is_connected = self.agent_adapter.is_connected().await;
        *cache = Some((is_connected, std::time::Instant::now()));

        is_connected
    }
    /// 带重试的网络连接检查（带日志节流）
    async fn check_connectivity_with_retry(&self) -> bool {
        let retry_attempts = self.config.retry_attempts;

        for attempt in 1..=retry_attempts {
            // 尝试多种检测方法
            if self.check_dns_resolution().await
                || self.check_http_connectivity().await
                || self.check_backup_connectivity().await
            {
                self.log_connectivity_result(true, attempt).await;
                return true;
            }

            if attempt < retry_attempts {
                // 重试间隔：1秒、2秒、3秒...
                let delay = Duration::from_secs(attempt as u64);
                tokio::time::sleep(delay).await;
            }
        }

        self.log_connectivity_result(false, retry_attempts).await;
        false
    }

    /// 智能日志输出 - 状态变化时立即输出，相同状态连续出现时节流
    async fn log_connectivity_result(&self, is_connected: bool, attempts: u32) {
        let mut last_state = self.last_connectivity_state.lock().await;
        let consecutive_count = self
            .consecutive_same_results
            .load(std::sync::atomic::Ordering::Relaxed);

        let state_changed = last_state.map_or(true, |last| last != is_connected);

        if state_changed {
            // 状态变化时重置计数器并输出详细日志
            self.consecutive_same_results
                .store(1, std::sync::atomic::Ordering::Relaxed);
            *last_state = Some(is_connected);

            if is_connected {
                info!("Network connectivity confirmed on attempt {}", attempts);
            } else {
                warn!(
                    "Network connectivity check failed after {} attempts",
                    attempts
                );
            }
        } else {
            // 相同状态时增加计数器
            let new_count = consecutive_count + 1;
            self.consecutive_same_results
                .store(new_count, std::sync::atomic::Ordering::Relaxed);

            // 每10次相同结果输出一次总结
            if new_count % 10 == 0 {
                let status = if is_connected {
                    "connected"
                } else {
                    "disconnected"
                };
                info!(
                    "Network status remains {} for {} consecutive checks",
                    status, new_count
                );
            }
        }
    }

    /// DNS解析检查
    async fn check_dns_resolution(&self) -> bool {
        use std::net::ToSocketAddrs;

        // 测试多个知名的公共DNS域名
        let test_domains = ["api.echowave.cn:80", "cloudflare.com:80", "baidu.com:80"];

        for domain in &test_domains {
            let domain_clone = domain.to_string();
            let result = tokio::task::spawn_blocking(move || domain_clone.to_socket_addrs()).await;

            match result {
                Ok(Ok(mut addrs)) => {
                    if addrs.next().is_some() {
                        return true;
                    }
                }
                Ok(Err(_)) | Err(_) => {
                    // 静默失败，由上层处理日志
                    continue;
                }
            }
        }

        false
    }

    /// HTTP连接检查
    async fn check_http_connectivity(&self) -> bool {
        let timeout = Duration::from_millis(5000); // 5秒超时
        let client = reqwest::Client::builder().timeout(timeout).build();

        let client = match client {
            Ok(c) => c,
            Err(_) => {
                return false;
            }
        };

        // 尝试访问几个可靠的服务
        let test_urls = [
            "https://www.baidu.com",
            "https://www.cloudflare.com",
            "https://www.echowave.cn",
        ];

        for url in &test_urls {
            match client.head(*url).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        return true;
                    }
                }
                Err(_) => {
                    // 静默失败，由上层处理日志
                    continue;
                }
            }
        }

        false
    }

    /// 备用连接检查（使用项目自己的服务）
    async fn check_backup_connectivity(&self) -> bool {
        let timeout = Duration::from_millis(8000); // 8秒超时
        let client = reqwest::Client::builder().timeout(timeout).build();

        let client = match client {
            Ok(c) => c,
            Err(_) => {
                return false;
            }
        };

        // 尝试连接到项目的API服务器
        let api_url = "http://api.echowave.cn:8090/health"; // 假设有健康检查端点

        match client.head(api_url).send().await {
            Ok(response) => response.status().is_success() || response.status().is_client_error(),
            Err(_) => false,
        }
    }

    /// 添加连接历史记录
    async fn add_to_history(&self, item: ConnectionHistoryItem) {
        let history_limit = self.config.history_limit;

        self.state.send_modify(|state| {
            state.connection_history.push_back(item);
            if state.connection_history.len() > history_limit {
                state.connection_history.pop_front();
            }
        });
    }

    /// 检测Tailscale延迟（监控器版本）- 优化版本
    async fn check_tailscale_latency(&self) -> Option<u32> {
        // 检查是否启用延迟检测
        if !self.config.latency_check_enabled {
            return None;
        }

        // 检查 agent 是否连接（使用缓存优化性能）
        if !self.is_agent_connected_cached().await {
            debug!("Agent 未连接，跳过 Tailscale 延迟检测");
            return None;
        }

        let trace_id = crate::logging::current_trace_id();
        debug!("监控循环中检测 Tailscale 延迟 (trace_id: {})", trace_id);

        match self.agent_adapter.get_network_latency(trace_id).await {
            Ok(latency_info) => {
                if let Some(error) = &latency_info.error {
                    debug!("延迟测量失败: {}", error);
                    return None;
                }

                match latency_info.server_latency {
                    Some(latency) => {
                        let latency_ms = latency as u32;

                        // 根据配置的阈值记录不同级别的日志
                        if latency_ms >= self.config.latency_error_threshold_ms {
                            error!(
                                "Tailscale 延迟过高: {}ms (错误阈值: {}ms)",
                                latency_ms, self.config.latency_error_threshold_ms
                            );
                        } else if latency_ms >= self.config.latency_warning_threshold_ms {
                            warn!(
                                "Tailscale 延迟较高: {}ms (警告阈值: {}ms)",
                                latency_ms, self.config.latency_warning_threshold_ms
                            );
                        } else {
                            info!("Tailscale 延迟正常: {}ms", latency_ms);
                        }

                        // 更新网络状态
                        self.state.send_modify(|state| {
                            state.tailscale_latency = Some(latency_ms);
                            state.last_update_time = chrono::Utc::now().to_rfc3339();
                        });

                        Some(latency_ms)
                    }
                    None => {
                        debug!("延迟信息为空，可能网络未就绪");
                        None
                    }
                }
            }
            Err(err) => {
                debug!("调用 Agent 获取网络延迟失败: {}", err);
                None
            }
        }
    }
}

impl NetworkService {
    /// 创建新的网络服务实例
    pub fn new(agent_adapter: Arc<AgentAdapter>) -> CoreResult<Self> {
        let current_state = NetworkState {
            tailscale_latency: None,
            internet_connectivity: false,
            last_update_time: chrono::Utc::now().to_rfc3339(),
            connection_history: VecDeque::new(),
        };

        Ok(Self {
            config: NetworkServiceConfig::default(),
            status: watch::Sender::new(ServiceStatus::Stopped),
            state: watch::Sender::new(current_state),
            last_check_time: Arc::new(AtomicI64::new(0)),
            agent_adapter,
            monitoring_handle: Arc::new(Mutex::new(None)),
        })
    }

    /// 检查网络连接状态
    #[instrument(skip(self))]
    pub async fn check_connectivity(&self) -> CoreResult<bool> {
        info!("Checking internet connectivity");

        let connectivity = self.check_connectivity_with_retry().await;

        // 更新状态
        self.state.send_modify(|state| {
            state.internet_connectivity = connectivity;
            state.last_update_time = chrono::Utc::now().to_rfc3339();
        });

        // 添加到历史记录
        let status = if connectivity {
            "connected"
        } else {
            "disconnected"
        };
        let history_item = ConnectionHistoryItem {
            timestamp: chrono::Utc::now().to_rfc3339(),
            status: status.to_string(),
            latency: None, // 这里可以后续添加延迟测量
        };
        self.add_to_history(history_item).await;

        Ok(connectivity)
    }

    /// 带重试的网络连接检查
    async fn check_connectivity_with_retry(&self) -> bool {
        let retry_attempts = self.config.retry_attempts;

        for attempt in 1..=retry_attempts {
            info!(
                "Network connectivity check attempt {}/{}",
                attempt, retry_attempts
            );

            // 尝试多种检测方法
            if self.check_dns_resolution().await || self.check_http_connectivity().await {
                info!("Network connectivity confirmed on attempt {}", attempt);
                return true;
            }

            if attempt < retry_attempts {
                // 重试间隔：1秒、2秒、3秒...
                let delay = Duration::from_secs(attempt as u64);
                tokio::time::sleep(delay).await;
            }
        }

        warn!(
            "Network connectivity check failed after {} attempts",
            retry_attempts
        );
        false
    }

    /// DNS解析检查
    async fn check_dns_resolution(&self) -> bool {
        use std::net::ToSocketAddrs;

        info!("Checking DNS resolution");

        // 测试多个知名的公共DNS域名
        let test_domains = ["google.com:80", "cloudflare.com:80", "dns.google:80"];

        for domain in &test_domains {
            let domain_clone = domain.to_string();
            let result = tokio::task::spawn_blocking(move || domain_clone.to_socket_addrs()).await;

            match result {
                Ok(Ok(mut addrs)) => {
                    if addrs.next().is_some() {
                        info!("DNS resolution successful for {}", domain);
                        return true;
                    } else {
                        warn!("DNS resolution returned no addresses for {}", domain);
                    }
                }
                Ok(Err(e)) => {
                    warn!("DNS resolution failed for {}: {}", domain, e);
                }
                Err(e) => {
                    warn!("DNS resolution task failed for {}: {}", domain, e);
                }
            }
        }

        warn!("All DNS resolution attempts failed");
        false
    }

    /// HTTP连接检查
    async fn check_http_connectivity(&self) -> bool {
        info!("Checking HTTP connectivity");

        let timeout = Duration::from_millis(5000); // 5秒超时
        let client = reqwest::Client::builder().timeout(timeout).build();

        let client = match client {
            Ok(c) => c,
            Err(e) => {
                warn!("Failed to create HTTP client: {}", e);
                return false;
            }
        };

        // 尝试访问几个可靠的服务
        let test_urls = [
            "https://www.google.com",
            "https://www.cloudflare.com",
            "https://httpbin.org/get",
        ];

        for url in &test_urls {
            match client.head(*url).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        info!("HTTP connectivity confirmed via {}", url);
                        return true;
                    } else {
                        warn!(
                            "HTTP request to {} returned status: {}",
                            url,
                            response.status()
                        );
                    }
                }
                Err(e) => {
                    warn!("HTTP request to {} failed: {}", url, e);
                }
            }
        }

        false
    }

    /// 检测Tailscale延迟
    #[instrument(skip(self))]
    pub async fn check_tailscale_latency(&self) -> CoreResult<u32> {
        info!("开始检测 Tailscale 网络延迟");

        // 检查 agent 是否连接
        if !self.agent_adapter.is_connected().await {
            warn!("Agent 未连接，跳过 Tailscale 延迟检测");
            return Err(CoreError::service_error("Agent 未连接，无法检测网络延迟"));
        }

        let trace_id = crate::logging::current_trace_id();
        debug!("调用 Agent 获取网络延迟信息 (trace_id: {})", trace_id);

        match self.agent_adapter.get_network_latency(trace_id).await {
            Ok(latency_info) => {
                debug!("成功获取延迟信息: {:?}", latency_info);

                if let Some(error) = &latency_info.error {
                    warn!("延迟测量失败: {}", error);
                    return Err(CoreError::service_error(&format!(
                        "延迟测量失败: {}",
                        error
                    )));
                }

                match latency_info.server_latency {
                    Some(latency) => {
                        info!("Tailscale 延迟测量成功: {}ms", latency);

                        // 更新网络状态
                        self.state.send_modify(|state| {
                            state.tailscale_latency = Some(latency as u32);
                            state.last_update_time = chrono::Utc::now().to_rfc3339();
                        });

                        Ok(latency as u32)
                    }
                    None => {
                        warn!("延迟信息为空，可能网络未就绪");
                        Err(CoreError::service_error("延迟信息为空"))
                    }
                }
            }
            Err(err) => {
                error!("调用 Agent 获取网络延迟失败: {}", err);
                Err(CoreError::AgentError(err))
            }
        }
    }

    /// 获取连接质量
    pub fn get_connection_quality(&self) -> ConnectionQuality {
        let state = self.state.borrow();

        // 如果没有互联网连接，直接返回离线
        if !state.internet_connectivity {
            return ConnectionQuality::Offline;
        }

        // 如果有互联网连接但没有 Tailscale 延迟数据，返回 Fair
        // 这表示基础网络连接正常，但 Tailscale 网络状态未知
        match state.tailscale_latency {
            Some(latency) => ConnectionQuality::from_latency(latency),
            None => ConnectionQuality::Fair, // 有互联网但没有 Tailscale 延迟数据
        }
    }

    /// 获取当前网络状态
    pub fn get_current_state(&self) -> NetworkState {
        self.state.borrow().clone()
    }

    pub fn state_receiver(&self) -> watch::Receiver<NetworkState> {
        self.state.subscribe()
    }

    /// 启动网络状态监控
    async fn start_monitoring(&self) -> CoreResult<()> {
        let mut guard = self.monitoring_handle.lock().await;
        if guard.is_some() {
            return Ok(()); // 已经在监控中
        }

        let check_interval = Duration::from_secs(self.config.check_interval);
        let signal = CancellationToken::new();
        let signal_clone = signal.clone();
        let status_tx = self.status.clone();

        // 创建用于监控任务的服务克隆
        let service_for_monitoring = Arc::new(NetworkServiceMonitor {
            config: self.config.clone(),
            state: self.state.clone(),
            last_check_time: self.last_check_time.clone(),
            consecutive_same_results: Arc::new(AtomicU32::new(0)),
            last_connectivity_state: Arc::new(Mutex::new(None)),
            agent_adapter: self.agent_adapter.clone(),
            agent_connection_cache: Arc::new(Mutex::new(None)),
        });

        let handle = tokio::spawn(async move {
            info!(
                "Network monitoring started with interval of {} seconds",
                check_interval.as_secs()
            );

            let latency_check_interval =
                Duration::from_secs(service_for_monitoring.config.latency_check_interval);
            let mut last_latency_check = std::time::Instant::now();

            info!(
                "Tailscale 延迟检测间隔: {} 秒, 启用状态: {}",
                latency_check_interval.as_secs(),
                service_for_monitoring.config.latency_check_enabled
            );

            while !signal_clone.is_cancelled() {
                let check_start = std::time::Instant::now();

                // 执行网络连接检查
                let connectivity = service_for_monitoring.check_connectivity_with_retry().await;

                // 智能延迟检测：仅在需要时执行
                let latency = if connectivity
                    && service_for_monitoring.config.latency_check_enabled
                    && check_start.duration_since(last_latency_check) >= latency_check_interval
                {
                    debug!("执行定期 Tailscale 延迟检测");
                    last_latency_check = check_start;
                    service_for_monitoring.check_tailscale_latency().await
                } else {
                    // 智能跳过：记录跳过原因
                    if !connectivity {
                        debug!("跳过延迟检测：网络未连接");
                    } else if !service_for_monitoring.config.latency_check_enabled {
                        debug!("跳过延迟检测：功能已禁用");
                    } else {
                        let time_since_last = check_start.duration_since(last_latency_check);
                        debug!(
                            "跳过延迟检测：距离上次检测仅 {} 秒 (间隔: {} 秒)",
                            time_since_last.as_secs(),
                            latency_check_interval.as_secs()
                        );
                    }

                    // 保持当前状态中的延迟值
                    service_for_monitoring.state.borrow().tailscale_latency
                };

                // 更新状态
                service_for_monitoring.state.send_modify(|state| {
                    state.internet_connectivity = connectivity;
                    state.last_update_time = chrono::Utc::now().to_rfc3339();
                    // 只有在实际检测到新延迟时才更新，否则保持现有值
                });

                // 添加到历史记录
                let status = if connectivity {
                    "connected"
                } else {
                    "disconnected"
                };
                let history_item = ConnectionHistoryItem {
                    timestamp: chrono::Utc::now().to_rfc3339(),
                    status: status.to_string(),
                    latency,
                };
                service_for_monitoring.add_to_history(history_item).await;

                // 更新最后检查时间
                service_for_monitoring.last_check_time.store(
                    chrono::Utc::now().timestamp_millis(),
                    std::sync::atomic::Ordering::Relaxed,
                );

                // 性能监控：记录检查耗时
                let check_duration = check_start.elapsed();
                if check_duration > Duration::from_secs(10) {
                    warn!("网络检查耗时过长: {:?}, 考虑调整配置", check_duration);
                } else {
                    debug!("网络检查完成，耗时: {:?}", check_duration);
                }

                // 等待下次检查
                tokio::select! {
                    _ = sleep(check_interval) => {},
                    _ = signal_clone.cancelled() => break,
                }
            }

            status_tx.send_replace(ServiceStatus::Stopped);
            info!("Network monitoring stopped");
        });

        *guard = Some((handle, signal));
        Ok(())
    }

    /// 停止网络状态监控
    async fn stop_monitoring(&self) -> CoreResult<()> {
        // 设置停止标志
        let mut guard = self.monitoring_handle.lock().await;
        if let Some((handle, signal)) = guard.take() {
            self.status.send_replace(ServiceStatus::Stopping);
            signal.cancel();
            let _ = handle.await;
            self.status.send_replace(ServiceStatus::Stopped);
        }
        Ok(())
    }

    /// 添加连接历史记录
    async fn add_to_history(&self, item: ConnectionHistoryItem) {
        let history_limit = self.config.history_limit;

        self.state.send_modify(|state| {
            state.connection_history.push_back(item);
            if state.connection_history.len() > history_limit {
                state.connection_history.pop_front();
            }
        });
    }

    /// 获取网络统计信息
    pub async fn get_network_statistics(&self) -> NetworkStatistics {
        let state = self.state.borrow();
        let history = &state.connection_history;

        if history.is_empty() {
            return NetworkStatistics::default();
        }
        let history_len = history.len();

        let connected_count = history
            .iter()
            .filter(|item| item.status == "connected")
            .count();

        let latencies: Vec<u32> = history.iter().filter_map(|item| item.latency).collect();

        let avg_latency = if latencies.is_empty() {
            None
        } else {
            Some(latencies.iter().sum::<u32>() / latencies.len() as u32)
        };

        let min_latency = latencies.iter().min().copied();
        let max_latency = latencies.iter().max().copied();

        NetworkStatistics {
            total_checks: history_len,
            successful_connections: connected_count,
            connection_rate: connected_count as f32 / history_len as f32,
            average_latency: avg_latency,
            min_latency,
            max_latency,
            current_quality: self.get_connection_quality(),
        }
    }

    /// 重置网络统计
    pub async fn reset_statistics(&self) {
        self.state.send_modify(|state| {
            state.connection_history.clear();
        });
    }
}

/// 网络统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkStatistics {
    pub total_checks: usize,
    pub successful_connections: usize,
    pub connection_rate: f32,
    pub average_latency: Option<u32>,
    pub min_latency: Option<u32>,
    pub max_latency: Option<u32>,
    pub current_quality: ConnectionQuality,
}

impl Default for NetworkStatistics {
    fn default() -> Self {
        Self {
            total_checks: 0,
            successful_connections: 0,
            connection_rate: 0.0,
            average_latency: None,
            min_latency: None,
            max_latency: None,
            current_quality: ConnectionQuality::Offline,
        }
    }
}

impl CoreService for NetworkService {
    fn name(&self) -> &str {
        "network_service"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(self.health_check_impl())
    }
}

impl NetworkService {
    async fn start_impl(&self) -> CoreResult<()> {
        info!("Starting network service");
        self.status.send_modify(|status| {
            *status = ServiceStatus::Starting;
        });

        // 启动网络监控
        self.start_monitoring().await?;

        // 执行初始网络检查
        let _ = self.check_connectivity().await;
        let _ = self.check_tailscale_latency().await;

        self.status.send_modify(|status| {
            *status = ServiceStatus::Running;
        });
        self.last_check_time.store(
            chrono::Utc::now().timestamp_millis(),
            std::sync::atomic::Ordering::Relaxed,
        );

        info!("Network service started successfully");
        Ok(())
    }

    async fn stop_impl(&self) -> CoreResult<()> {
        info!("Stopping network service");
        self.status.send_replace(ServiceStatus::Stopping);

        // 停止网络监控
        self.stop_monitoring().await?;

        self.status.send_replace(ServiceStatus::Stopped);
        self.last_check_time
            .store(0, std::sync::atomic::Ordering::Relaxed);

        info!("Network service stopped successfully");
        Ok(())
    }

    async fn health_check_impl(&self) -> CoreResult<bool> {
        // 检查服务状态
        if *self.status.borrow() != ServiceStatus::Running {
            return Ok(false);
        }

        // 检查最近是否有网络检查
        {
            let last_check_time = self
                .last_check_time
                .load(std::sync::atomic::Ordering::Relaxed);
            if last_check_time != 0 {
                let check_interval = self.config.check_interval;
                let check_timeout = Duration::from_secs(check_interval * 2);
                let now = chrono::Utc::now().timestamp_millis();
                if now - last_check_time > check_timeout.as_millis() as i64 {
                    warn!("Network service health check: no recent checks");
                    return Ok(false);
                }
            }
        }

        Ok(true)
    }
}
