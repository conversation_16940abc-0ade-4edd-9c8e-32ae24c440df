use std::collections::HashMap;
use std::ops::Deref;
use std::path::{Path, PathBuf};
use std::pin::Pin;
use std::sync::Arc;
use std::sync::atomic::{AtomicI64, Ordering};
use std::time::Duration;

use chrono::{DateTime, Utc};
use futures::Future;
use regex::Regex;
use serde::{Deserialize, Serialize};
use shared::hash::{HashAlgorithm, HashOutputEncoding, HashValidator};
use shared::{version::Version, version_spec::VersionSpec};
use tokio::sync::Mutex;
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tokio::time::sleep;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use crate::adapters::{file::FileAdapter, http::HttpAdapter};
use crate::services::{DeviceService, SettingsService};
use anyhow::{Result, anyhow};

// 数据定义

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentArtifact {
    pub name: ComponentType,
    pub url: String,
    pub version: Version,
    pub sha256: Option<String>,
    pub sha512: Option<String>,
    pub size: u64,
    pub platform: Option<String>,
    pub dependencies: Option<HashMap<ComponentType, VersionSpec>>,
}

pub struct ManifestDependency {
    version: Version,
    path: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Manifest {
    version: Version,
    files: Vec<ComponentArtifact>,
    canary: Option<CanaryManifest>,
    release_date: DateTime<Utc>,
    release_notes: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CanaryManifest {
    version: Version,
    conditions: Vec<CanaryCondition>,
    files: Vec<ComponentArtifact>,
    release_date: DateTime<Utc>,
    release_notes: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "UPPERCASE")]
pub enum MatchableField {
    MachineId,
    DeviceName,
    OsName,
    OsVersion,
    KernelVersion,
    CpuBrand,
    CpuArch,
    CpuCoreCount,
    MemoryTier,
    TotalMemoryGb,
    GpuName,
    GpuBrand,
    Hostname,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CanaryCondition {
    Regexp {
        field: MatchableField,
        #[serde(with = "serde_regex")]
        value: Regex,
    },
}
mod serde_regex {
    use regex::Regex;
    use serde::{self, Deserialize, Deserializer, Serializer};

    // 序列化：将 Regex 对象转换回其字符串表示形式
    pub fn serialize<S>(re: &Regex, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_str(re.as_str())
    }

    // 反序列化：将字符串解析为 Regex 对象
    pub fn deserialize<'de, D>(deserializer: D) -> Result<Regex, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Regex::new(&s).map_err(serde::de::Error::custom)
    }
}

/// 更新策略类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum UpdateStrategy {
    /// 手动更新
    Manual,
    /// 自动下载但手动安装
    AutoDownload,
    /// 完全自动更新
    AutoUpdate,
}

/// 组件类型定义
#[derive(Debug, Clone, Copy, Serialize, Deserialize, PartialEq, Eq, Hash)]
#[serde(rename_all = "kebab-case")]
pub enum ComponentType {
    /// 应用组件（客户端主程序）
    Client,
    /// Agent组件（WSL任务代理）
    Agent,
    /// WSL组件（Windows子系统）
    #[serde(rename = "wsl")]
    WslManager,
    /// 分发版组件（Linux分发版）
    #[serde(rename = "engine")]
    Distro,
}

impl ComponentType {
    /// 获取组件的显示名称
    pub fn display_name(&self) -> &'static str {
        match self {
            ComponentType::Client => "客户端应用",
            ComponentType::Agent => "任务代理",
            ComponentType::WslManager => "WSL管理器",
            ComponentType::Distro => "Linux分发版",
        }
    }

    /// 获取组件更新优先级（数值越小优先级越高）
    pub fn update_priority(&self) -> u8 {
        match self {
            ComponentType::Client => 1, // 最高优先级
            ComponentType::WslManager => 2,
            ComponentType::Distro => 3,
            ComponentType::Agent => 4, // 最低优先级
        }
    }

    /// 按更新优先级排序的组件列表
    pub const fn update_order() -> &'static [ComponentType] {
        &[
            ComponentType::Client,
            ComponentType::WslManager,
            ComponentType::Distro,
            ComponentType::Agent,
        ]
    }
}

pub struct ComponentVersions(HashMap<ComponentType, Version>);

impl Default for ComponentVersions {
    fn default() -> Self {
        Self(HashMap::from([
            (ComponentType::Client, Version::default()),
            (ComponentType::WslManager, Version::default()),
            (ComponentType::Distro, Version::default()),
            (ComponentType::Agent, Version::default()),
        ]))
    }
}

impl ComponentVersions {
    pub fn with_client_version(mut self, version: Version) -> Self {
        self.0.insert(ComponentType::Client, version);
        self
    }

    pub fn with_wsl_manager_version(mut self, version: Version) -> Self {
        self.0.insert(ComponentType::WslManager, version);
        self
    }

    pub fn with_distro_version(mut self, version: Version) -> Self {
        self.0.insert(ComponentType::Distro, version);
        self
    }

    pub fn with_agent_version(mut self, version: Version) -> Self {
        self.0.insert(ComponentType::Agent, version);
        self
    }
}
impl Deref for ComponentVersions {
    type Target = HashMap<ComponentType, Version>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

/// 下载进度
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct DownloadProgress {
    // 已下载字节数
    pub downloaded_bytes: u64,
    // 总字节数
    pub total_bytes: u64,
    // 下载进度百分比
    pub percentage: f64,
    // 下载速度字节每秒
    pub speed_bytes_per_sec: u64,
    // 预计剩余时间秒
    pub eta_seconds: Option<u64>,
}

/// 更新检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateCheckResult {
    /// 是否有可用更新
    pub update_available: bool,
    /// 当前版本
    pub current_version: Version,
    /// 可用的更新版本（如果有）
    pub update_info: Option<UpdateInfo>,
    /// 检查时间
    pub checked_at: DateTime<Utc>,
}

/// 更新信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateInfo {
    /// 更新版本
    pub version: Version,
    /// 发布时间
    pub release_date: DateTime<Utc>,
    /// 发布说明
    pub release_notes: Option<String>,
    /// 清单文件
    pub files: Vec<ComponentArtifact>,
}

/// 组件更新结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComponentUpdateResult {
    /// 组件类型
    pub component_type: ComponentType,
    /// 更新是否成功
    pub success: bool,
    /// 更新前版本
    pub from_version: Option<Version>,
    /// 更新后版本
    pub to_version: Option<Version>,
    /// 错误信息（如果失败）
    pub error_message: Option<String>,
    /// 更新耗时（毫秒）
    pub duration_ms: u64,
}

/// 组件更新特征
pub trait ComponentUpdater: Send + Sync {
    /// 组件类型
    fn component_type(&self) -> ComponentType;

    /// 检查组件更新前置条件，在引用更新前检查
    fn check_prerequisites<'a>(
        &'a self,
        trace_id: &'a Uuid,
    ) -> Pin<Box<dyn Future<Output = anyhow::Result<bool>> + Send + 'a>>;

    /// 获取当前组件版本
    fn get_current_version<'a>(
        &'a self,
        trace_id: &'a Uuid,
    ) -> Pin<Box<dyn Future<Output = anyhow::Result<Version>> + Send + 'a>>;

    /// 应用组件更新
    fn apply_update<'a>(
        &'a self,
        update_info: &'a UpdateInfo,
        trace_id: &'a Uuid,
    ) -> Pin<Box<dyn Future<Output = anyhow::Result<()>> + Send + 'a>>;

    /// 验证更新是否成功
    fn verify_update<'a>(
        &'a self,
        expected_version: &'a Version,
        trace_id: &'a Uuid,
    ) -> Pin<Box<dyn Future<Output = anyhow::Result<bool>> + Send + 'a>>;

    /// 回滚更新（如果支持）
    fn rollback<'a>(
        &'a self,
        trace_id: &'a Uuid,
    ) -> Pin<Box<dyn Future<Output = anyhow::Result<()>> + Send + 'a>> {
        Box::pin(async move {
            tracing::warn!(
                trace_id = %trace_id,
                component_type = self.component_type().display_name(),
                "组件不支持回滚操作",
            );
            Err(anyhow!(
                "组件 {} 不支持回滚操作",
                self.component_type().display_name()
            ))
        })
    }
}

/// Manifest 更新器
pub struct ManifestUpdater {
    /// CDN 基础 URL
    cdn_base_url: String,
    /// 更新策略
    strategy: UpdateStrategy,
    /// 是否启用预发布版本
    allow_prerelease: bool,
    /// 设备信息服务
    device_service: Arc<DeviceService>,
    /// 设置服务
    settings_service: Arc<SettingsService>,
    /// HTTP 适配器
    http_adapter: Arc<HttpAdapter>,
    /// 文件适配器
    file_adapter: Arc<FileAdapter>,
    /// 组件更新器映射
    component_updaters: std::collections::HashMap<ComponentType, Arc<dyn ComponentUpdater>>,
    /// 上次同步时间
    last_synced_at: Arc<AtomicI64>,
    /// 上次同步的清单
    last_manifest: Arc<Mutex<Option<Manifest>>>,
}

impl ManifestUpdater {
    /// 创建新的 Manifest 更新器
    pub fn new(
        cdn_base_url: String,
        strategy: UpdateStrategy,
        allow_prerelease: bool,
        device_service: Arc<DeviceService>,
        settings_service: Arc<SettingsService>,
        http_adapter: Arc<HttpAdapter>,
        file_adapter: Arc<FileAdapter>,
    ) -> Self {
        Self {
            cdn_base_url,
            strategy,
            allow_prerelease,
            device_service,
            settings_service,
            http_adapter,
            file_adapter,
            component_updaters: std::collections::HashMap::new(),
            last_synced_at: Arc::new(AtomicI64::new(0)),
            last_manifest: Arc::new(Mutex::new(None)),
        }
    }

    /// *注册组件更新器
    pub fn register_component_updater(
        &mut self,
        component_type: ComponentType,
        updater: Arc<dyn ComponentUpdater>,
    ) {
        self.component_updaters.insert(component_type, updater);
    }

    /// *获取 CDN 清单文件
    async fn fetch_latest_manifest(&self, trace_id: &Uuid) -> Result<Manifest> {
        let manifest_url = format!("{}/latest.yml", self.cdn_base_url.trim_end_matches('/'));

        debug!(
            trace_id = %trace_id,
            url = %manifest_url,
            "正在获取最新清单文件"
        );

        let response: serde_json::Value =
            self.http_adapter.get(&manifest_url).await.map_err(|e| {
                error!(
                    trace_id = %trace_id,
                    url = %manifest_url,
                    error = %e,
                    "获取清单文件失败"
                );
                anyhow!("获取清单文件失败: {}", e)
            })?;

        let manifest: Manifest = serde_json::from_value(response).map_err(|e| {
            error!(
                trace_id = %trace_id,
                url = %manifest_url,
                error = %e,
                "解析清单文件失败"
            );
            anyhow!("解析清单文件失败: {}", e)
        })?;

        debug!(
            trace_id = %trace_id,
            version = %manifest.version,
            "成功获取清单文件"
        );

        *self.last_manifest.lock().await = Some(manifest.clone());
        self.last_synced_at
            .store(Utc::now().timestamp_millis(), Ordering::Relaxed);

        Ok(manifest)
    }

    /// *评估清单以确定是否需要更新
    async fn evaluate_manifest(
        &self,
        manifest: Manifest,
        version: &Version,
        trace_id: &Uuid,
    ) -> Result<Option<UpdateInfo>> {
        debug!(
            trace_id = %trace_id,
            manifest_version = %manifest.version,
            current_version = %version,
            "正在评估清单以确定更新"
        );

        // 检查基础版本
        if manifest.version <= *version {
            debug!(
                trace_id = %trace_id,
                "清单版本不高于当前版本，检查金丝雀版本"
            );

            // 检查 canary 版本
            if let Some(canary) = manifest.canary {
                if self.should_use_canary(version, &canary, trace_id).await? {
                    return self.create_update_info_from_canary(canary).await;
                }
            }
            return Ok(None);
        }

        // 检查是否为预发布版本
        if manifest.version.pre_release.is_some() && !self.allow_prerelease {
            debug!(
                trace_id = %trace_id,
                "跳过预发布版本，因为预发布更新已禁用"
            );
            return Ok(None);
        }

        // 创建更新信息
        self.create_update_info_from_manifest(manifest).await
    }

    /// *检查是否应该使用 canary 版本
    async fn should_use_canary(
        &self,
        version: &Version,
        canary: &CanaryManifest,
        trace_id: &Uuid,
    ) -> Result<bool> {
        if canary.version <= *version {
            debug!(
                trace_id = %trace_id,
                "金丝雀版本不高于当前版本"
            );
            return Ok(false);
        }

        debug!(
            trace_id = %trace_id,
            canary_version = %canary.version,
            conditions_count = canary.conditions.len(),
            "正在评估金丝雀版本条件"
        );

        // 检查 canary 条件
        for (index, condition) in canary.conditions.iter().enumerate() {
            if !self.evaluate_canary_condition(condition, trace_id).await? {
                debug!(
                    trace_id = %trace_id,
                    condition_index = index,
                    "金丝雀版本条件不满足"
                );
                return Ok(false);
            }
        }

        info!(
            trace_id = %trace_id,
            canary_version = %canary.version,
            "所有金丝雀版本条件均满足"
        );

        Ok(true)
    }

    /// *评估 canary 条件
    async fn evaluate_canary_condition(
        &self,
        condition: &CanaryCondition,
        trace_id: &Uuid,
    ) -> Result<bool> {
        match condition {
            CanaryCondition::Regexp { field, value } => {
                let field_value = self.get_matchable_field_value(field).await;
                let matches = field_value.as_ref().map_or(false, |it| value.is_match(&it));

                debug!(
                    trace_id = %trace_id,
                    field = ?field,
                    field_value = ?field_value,
                    pattern = %value.as_str(),
                    matches = matches,
                    "评估金丝雀版本条件完成"
                );

                Ok(matches)
            }
        }
    }

    /// *获取可匹配字段的值
    async fn get_matchable_field_value(&self, field: &MatchableField) -> Option<String> {
        match field {
            MatchableField::MachineId => Some(self.device_service.get_machine_id()),
            MatchableField::DeviceName => self.device_service.get_device_name(),
            MatchableField::OsName => self.device_service.get_os_name(),
            MatchableField::OsVersion => self.device_service.get_os_version(),
            MatchableField::KernelVersion => self.device_service.get_kernel_version(),
            MatchableField::CpuBrand => self.device_service.get_cpu_brand(),
            MatchableField::CpuArch => self.device_service.get_cpu_arch(),
            MatchableField::CpuCoreCount => self.device_service.get_cpu_core_count(),
            MatchableField::MemoryTier => self.device_service.get_memory_tier(),
            MatchableField::TotalMemoryGb => self.device_service.get_total_memory_gb(),
            MatchableField::GpuName => self.device_service.get_gpu_name(),
            MatchableField::GpuBrand => self.device_service.get_gpu_brand(),
            MatchableField::Hostname => self.device_service.get_hostname(),
        }
    }

    /// *从清单创建更新信息
    async fn create_update_info_from_manifest(
        &self,
        manifest: Manifest,
    ) -> Result<Option<UpdateInfo>> {
        debug!(
            version = %manifest.version,
            "从清单创建更新信息"
        );

        let release_notes = if let Some(release_notes) = manifest.release_notes {
            Some(release_notes)
        } else {
            self.fetch_release_notes(&manifest.version).await.ok()
        };

        Ok(Some(UpdateInfo {
            version: manifest.version,
            release_date: manifest.release_date,
            release_notes: release_notes,
            files: manifest.files,
        }))
    }

    /// *获取发布说明
    async fn fetch_release_notes(&self, version: &Version) -> Result<String> {
        let notes_url = format!(
            "{}/release-notes/{}.md",
            self.cdn_base_url.trim_end_matches('/'),
            version
        );

        match self.http_adapter.get::<String>(&notes_url).await {
            Ok(notes) => {
                debug!(
                    version = %version,
                    "成功获取发布说明"
                );
                Ok(notes)
            }
            Err(e) => {
                warn!(
                    version = %version,
                    error = %e,
                    "获取发布说明失败，使用默认说明"
                );
                Ok(format!("版本 {} 的更新", version))
            }
        }
    }

    /// *从 canary 清单创建更新信息
    async fn create_update_info_from_canary(
        &self,
        canary: CanaryManifest,
    ) -> Result<Option<UpdateInfo>> {
        info!(
            canary_version = %canary.version,
            "从金丝雀清单创建更新信息"
        );

        let release_notes = if let Some(release_notes) = canary.release_notes {
            Some(release_notes)
        } else {
            self.fetch_release_notes(&canary.version).await.ok()
        };

        Ok(Some(UpdateInfo {
            version: canary.version,
            release_date: canary.release_date,
            release_notes: release_notes,
            files: canary.files,
        }))
    }

    /// *查找适合当前平台的文件
    fn find_platform_file<'a>(&self, files: &'a [ComponentArtifact]) -> Result<&'a ComponentArtifact> {
        if files.is_empty() {
            return Err(anyhow!("清单中没有找到文件"));
        }

        // 获取当前平台标识
        let current_platform = self.get_current_platform();

        debug!(
            current_platform = %current_platform,
            available_files = files.len(),
            "正在查找适合当前平台的文件"
        );

        // 首先尝试精确匹配
        for file in files {
            if let Some(platform) = &file.platform {
                if platform == &current_platform {
                    info!(
                        platform = %platform,
                        url = %file.url,
                        "找到精确匹配的平台文件"
                    );
                    return Ok(file);
                }
            }
        }

        // 如果没有精确匹配，返回第一个没有平台限制的文件
        for file in files {
            if file.platform.is_none() {
                warn!(
                    url = %file.url,
                    "使用通用平台文件（无平台限制）"
                );
                return Ok(file);
            }
        }

        // 如果都没有找到，返回第一个文件
        let fallback_file = &files[0];
        warn!(
            platform = ?fallback_file.platform,
            url = %fallback_file.url,
            "未找到适合的平台文件，使用第一个可用文件"
        );

        Ok(fallback_file)
    }

    /// *获取当前平台标识
    fn get_current_platform(&self) -> String {
        #[cfg(target_os = "windows")]
        {
            #[cfg(target_arch = "x86_64")]
            return "win32-x64".to_string();
            #[cfg(target_arch = "x86")]
            return "win32-ia32".to_string();
            #[cfg(target_arch = "aarch64")]
            return "win32-arm64".to_string();
        }

        #[cfg(target_os = "macos")]
        {
            #[cfg(target_arch = "x86_64")]
            return "darwin-x64".to_string();
            #[cfg(target_arch = "aarch64")]
            return "darwin-arm64".to_string();
        }

        #[cfg(target_os = "linux")]
        {
            #[cfg(target_arch = "x86_64")]
            return "linux-x64".to_string();
            #[cfg(target_arch = "x86")]
            return "linux-ia32".to_string();
            #[cfg(target_arch = "aarch64")]
            return "linux-arm64".to_string();
        }
        "unknown".to_string()
    }

    /// *从 URL 提取文件名
    fn extract_filename_from_url(&self, url: &str) -> Result<String> {
        let filename = url
            .split('/')
            .last()
            .and_then(|s| {
                // 移除查询参数
                s.split('?').next()
            })
            .map(|s| s.to_string())
            .ok_or_else(|| anyhow!("无法从 URL 提取文件名: {}", url))?;

        if filename.is_empty() {
            return Err(anyhow!("从 URL 提取的文件名为空: {}", url));
        }

        debug!(
            url = %url,
            filename = %filename,
            "从 URL 提取文件名成功"
        );

        Ok(filename)
    }

    /// *获取下载路径
    fn get_download_path(&self, filename: &str) -> Result<PathBuf> {
        let cache_dir = self.settings_service.get_cache_path();
        let download_path = cache_dir.join("updates").join(filename);

        debug!(
            filename = %filename,
            path = %download_path.display(),
            "生成下载路径"
        );

        Ok(download_path)
    }

    /// *验证已存在的下载文件
    async fn verify_existing_download(&self, path: &Path, manifest: &ComponentArtifact) -> Result<bool> {
        if !path.exists() {
            debug!(
                path = %path.display(),
                "文件不存在，需要下载"
            );
            return Ok(false);
        }

        debug!(
            path = %path.display(),
            "文件已存在，开始验证"
        );

        // 检查文件大小
        let metadata = tokio::fs::metadata(path)
            .await
            .map_err(|e| anyhow!("读取文件元数据失败: {}", e))?;

        if metadata.len() != manifest.size {
            warn!(
                path = %path.display(),
                expected_size = manifest.size,
                actual_size = metadata.len(),
                "文件大小不匹配，需要重新下载"
            );
            return Ok(false);
        }

        // 验证哈希值
        if let Some(sha256) = manifest.sha256.as_ref() {
            let is_base64 = sha256.len() < 64;
            let file_hash = {
                let validator = HashValidator::default().with_encoding(if is_base64 {
                    HashOutputEncoding::Base64
                } else {
                    HashOutputEncoding::Hex
                });
                validator
                    .compute_hash(path, HashAlgorithm::SHA256)
                    .await
                    .map_err(|e| {
                        error!(
                            path = %path.display(),
                            error = %e,
                            "计算文件哈希值失败"
                        );
                        anyhow!("计算文件哈希值失败: {}", e)
                    })?
            };
            if &file_hash != sha256 {
                warn!(
                    path = %path.display(),
                    algorithm = "SHA256",
                    expected_hash = sha256,
                    actual_hash = %file_hash,
                    "文件哈希值不匹配，需要重新下载"
                );
                return Ok(false);
            }
        }

        if let Some(sha512) = manifest.sha512.as_ref() {
            let is_base64 = sha512.len() < 128;
            let file_hash = {
                let validator = HashValidator::default().with_encoding(if is_base64 {
                    HashOutputEncoding::Base64
                } else {
                    HashOutputEncoding::Hex
                });
                validator
                    .compute_hash(path, HashAlgorithm::SHA512)
                    .await
                    .map_err(|e| anyhow!("计算文件哈希值失败: {}", e))?
            };
            if &file_hash != sha512 {
                warn!(
                    path = %path.display(),
                    algorithm = "SHA512",
                    expected_hash = sha512,
                    actual_hash = %file_hash,
                    "文件哈希值不匹配，需要重新下载"
                );
                return Ok(false);
            }
        }

        info!(
            path = %path.display(),
            "文件验证通过，无需重新下载"
        );
        Ok(true)
    }

    /// 找到符合条件的组件文件
    fn find_component_file(
        &self,
        component_type: &ComponentType,
        versions: &ComponentVersions,
        files: &[ComponentArtifact],
    ) -> Option<ComponentArtifact> {
        // 遍历文件，找到符合条件的文件
        let mut manifest_files = Vec::new();
        let current_platform = self.get_current_platform();
        let version = versions.get(&component_type).cloned().unwrap_or_default();
        for file in files {
            if &file.name != component_type {
                continue;
            }
            if file.version < version {
                continue;
            }
            if let Some(platform) = file.platform.as_ref() {
                if platform != &current_platform {
                    continue;
                }
            }
            if let Some(dependencies) = file.dependencies.as_ref() {
                for (component_type, spec) in dependencies {
                    let dependency_version = versions.get(component_type);
                    if !dependency_version.map_or(false, |it| spec.matches(it)) {
                        continue;
                    }
                }
            }
            manifest_files.push(file.clone());
        }
        manifest_files.sort_by(|a, b| a.version.cmp(&b.version));
        manifest_files.into_iter().next()
    }

    /// 在 Windows 上安装更新
    #[cfg(target_os = "windows")]
    async fn install_on_windows(&self, installer_path: &Path, trace_id: &Uuid) -> Result<()> {
        use std::process::Command;

        info!(
            trace_id = %trace_id,
            installer_path = %installer_path.display(),
            "开始 Windows 安装程序"
        );

        // 验证安装程序文件权限
        let metadata = tokio::fs::metadata(installer_path)
            .await
            .map_err(|e| anyhow!("读取安装程序文件信息失败: {}", e))?;

        if metadata.len() == 0 {
            return Err(anyhow!("安装程序文件大小为 0，可能已损坏"));
        }

        debug!(
            trace_id = %trace_id,
            file_size = metadata.len(),
            "安装程序文件验证通过"
        );

        // 使用 Windows 安装程序启动安装
        let output = Command::new(installer_path)
            .arg("/S") // 静默安装
            .arg("/VERYSILENT")
            .arg("/SUPPRESSMSGBOXES")
            .arg("/NORESTART")
            .arg("/LOG") // 启用日志
            .output()
            .map_err(|e| anyhow!("启动安装程序失败: {}", e))?;

        let exit_code = output.status.code().unwrap_or(-1);
        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        debug!(
            trace_id = %trace_id,
            exit_code = exit_code,
            stdout = %stdout,
            stderr = %stderr,
            "安装程序执行完成"
        );

        if !output.status.success() {
            error!(
                trace_id = %trace_id,
                exit_code = exit_code,
                stderr = %stderr,
                "安装程序执行失败"
            );

            let error_msg = if !stderr.is_empty() {
                stderr.to_string()
            } else if !stdout.is_empty() {
                stdout.to_string()
            } else {
                format!("安装程序退出码: {}", exit_code)
            };

            return Err(anyhow!("安装失败，退出码 {}: {}", exit_code, error_msg));
        }

        info!(
            trace_id = %trace_id,
            "Windows 更新安装成功完成"
        );

        Ok(())
    }
}

impl ManifestUpdater {
    /// 获取和现有版本兼容且最新的组件文件
    pub async fn get_latest_component_manifest(
        &self,
        component_type: ComponentType,
        versions: ComponentVersions,
        trace_id: &Uuid,
    ) -> anyhow::Result<Option<ComponentArtifact>> {
        let default_version = Version::const_default();
        let manifest_file = {
            let guard = self.last_manifest.lock().await;
            if let Some(manifest) = guard.clone() {
                let version = versions.get(&component_type).unwrap_or(&default_version);
                let update_info = self.evaluate_manifest(manifest, version, trace_id).await?;
                if let Some(update_info) = update_info {
                    self.find_component_file(&component_type, &versions, &update_info.files)
                } else {
                    None
                }
            } else {
                None
            }
        };
        let manifest_file = {
            if let Some(manifest_file) = manifest_file {
                Some(manifest_file)
            } else {
                let manifest = self.fetch_latest_manifest(trace_id).await?;
                let version = versions.get(&component_type).unwrap_or(&default_version);
                let update_info = self.evaluate_manifest(manifest, version, trace_id).await?;
                if let Some(update_info) = update_info {
                    self.find_component_file(&component_type, &versions, &update_info.files)
                } else {
                    None
                }
            }
        };
        Ok(manifest_file)
    }
}

impl ManifestUpdater {
    /// 创建更新事件循环，每次循环处理一个组件的更新，按优先级顺序处理
    pub fn create_event_loop(
        self: Arc<Self>,
        cancellation_token: CancellationToken,
    ) -> anyhow::Result<JoinHandle<()>> {
        let updater = self;
        let token = cancellation_token;

        let handle = tokio::spawn(async move {
            let mut check_interval = Duration::from_secs(300); // 5分钟检查一次

            info!("更新事件循环已启动");

            loop {
                tokio::select! {
                    _ = token.cancelled() => {
                        info!("更新事件循环收到取消信号，正在退出");
                        break;
                    }
                    _ = sleep(check_interval) => {
                        let trace_id = Uuid::new_v4();

                        debug!(
                            trace_id = %trace_id,
                            "开始新的更新检查循环"
                        );

                        // 只在自动更新策略下执行
                        if !matches!(updater.strategy, UpdateStrategy::AutoUpdate) {
                            debug!(
                                trace_id = %trace_id,
                                strategy = ?updater.strategy,
                                "跳过自动更新检查，当前策略不支持自动更新"
                            );
                            continue;
                        }

                        // 按优先级顺序检查每个组件，找到第一个需要更新的组件
                        if let Some(component_to_update) = updater.find_next_component_to_update(&trace_id).await {
                            info!(
                                trace_id = %trace_id,
                                component = ?component_to_update,
                                "发现需要更新的组件"
                            );

                            // 处理单个组件的完整更新流程
                            if let Err(e) = updater.process_component_update(component_to_update, &trace_id).await {
                                error!(
                                    trace_id = %trace_id,
                                    component = ?component_to_update,
                                    error = %e,
                                    "组件更新失败"
                                );

                                // 更新失败后增加检查间隔，避免频繁重试
                                check_interval = Duration::from_secs(600); // 10分钟
                            } else {
                                info!(
                                    trace_id = %trace_id,
                                    component = ?component_to_update,
                                    "组件更新成功完成"
                                );

                                // 成功后恢复正常检查间隔
                                check_interval = Duration::from_secs(300);
                            }
                        } else {
                            debug!(
                                trace_id = %trace_id,
                                "没有发现需要更新的组件"
                            );
                        }
                    }
                }
            }

            info!("更新事件循环已退出");
        });

        Ok(handle)
    }

    /// 按优先级顺序查找下一个需要更新的组件
    async fn find_next_component_to_update(&self, trace_id: &Uuid) -> Option<ComponentType> {
        // 获取当前所有组件版本
        let current_versions = self.get_current_component_versions(trace_id).await.ok()?;

        // 按优先级顺序检查每个组件
        for component_type in ComponentType::update_order() {
            if let Some(updater) = self.component_updaters.get(component_type) {
                // 检查前置条件
                if let Ok(prerequisites_met) = updater.check_prerequisites(trace_id).await {
                    if !prerequisites_met {
                        debug!(
                            trace_id = %trace_id,
                            component = ?component_type,
                            "组件更新前置条件不满足，跳过"
                        );
                        continue;
                    }
                }

                // 检查是否有可用更新
                let current_version = current_versions
                    .get(&component_type)
                    .cloned()
                    .unwrap_or_default();
                let versions_wrapper = ComponentVersions(current_versions.0.clone());
                match self
                    .get_latest_component_manifest(*component_type, versions_wrapper, trace_id)
                    .await
                {
                    Ok(Some(manifest_file)) => {
                        if manifest_file.version > current_version {
                            info!(
                                trace_id = %trace_id,
                                component = ?component_type,
                                current_version = %current_version,
                                available_version = %manifest_file.version,
                                "发现组件更新"
                            );
                            return Some(*component_type);
                        }
                    }
                    Ok(None) => {
                        debug!(
                            trace_id = %trace_id,
                            component = ?component_type,
                            "没有找到适合的组件清单文件"
                        );
                    }
                    Err(e) => {
                        warn!(
                            trace_id = %trace_id,
                            component = ?component_type,
                            error = %e,
                            "检查组件更新时出错"
                        );
                    }
                }
            }
        }

        None
    }

    /// 处理单个组件的完整更新流程
    async fn process_component_update(
        &self,
        component_type: ComponentType,
        trace_id: &Uuid,
    ) -> anyhow::Result<()> {
        let start_time = std::time::Instant::now();

        info!(
            trace_id = %trace_id,
            component = ?component_type,
            "开始处理组件更新"
        );

        // 获取组件更新器
        let updater = self
            .component_updaters
            .get(&component_type)
            .ok_or_else(|| anyhow!("未找到组件 {:?} 的更新器", component_type))?;

        // 获取当前版本
        let current_version = updater.get_current_version(trace_id).await?;

        // 获取当前所有组件版本（用于依赖检查）
        let current_versions = self.get_current_component_versions(trace_id).await?;

        // 获取更新信息
        let manifest_file = self
            .get_latest_component_manifest(component_type, current_versions, trace_id)
            .await?
            .ok_or_else(|| anyhow!("没有找到组件 {:?} 的可用更新", component_type))?;

        let update_info = UpdateInfo {
            version: manifest_file.version.clone(),
            release_date: Utc::now(), // 使用当前时间作为默认值
            release_notes: None,
            files: vec![manifest_file.clone()],
        };

        // 下载更新文件
        self.download_component_update(&manifest_file, trace_id)
            .await?;

        // 应用更新
        updater.apply_update(&update_info, trace_id).await?;

        // 验证更新
        let update_successful = updater
            .verify_update(&manifest_file.version, trace_id)
            .await?;
        if !update_successful {
            return Err(anyhow!("更新验证失败"));
        }

        let duration = start_time.elapsed();
        info!(
            trace_id = %trace_id,
            component = ?component_type,
            from_version = %current_version,
            to_version = %manifest_file.version,
            duration_ms = duration.as_millis(),
            "组件更新成功完成"
        );

        Ok(())
    }

    /// 获取当前所有组件的版本信息
    async fn get_current_component_versions(
        &self,
        trace_id: &Uuid,
    ) -> anyhow::Result<ComponentVersions> {
        let mut versions = ComponentVersions::default();

        for (component_type, updater) in &self.component_updaters {
            match updater.get_current_version(trace_id).await {
                Ok(version) => {
                    versions.0.insert(*component_type, version);
                }
                Err(e) => {
                    warn!(
                        trace_id = %trace_id,
                        component = ?component_type,
                        error = %e,
                        "获取组件当前版本失败，使用默认版本"
                    );
                }
            }
        }

        Ok(versions)
    }

    /// 下载组件更新文件
    async fn download_component_update(
        &self,
        manifest_file: &ComponentArtifact,
        trace_id: &Uuid,
    ) -> anyhow::Result<PathBuf> {
        let filename = self.extract_filename_from_url(&manifest_file.url)?;
        let download_path = self.get_download_path(&filename)?;

        // 确保下载目录存在
        if let Some(parent_dir) = download_path.parent() {
            self.file_adapter.create_dir_all(parent_dir).await?;
        }

        // 检查文件是否已存在且有效
        if self
            .verify_existing_download(&download_path, manifest_file)
            .await?
        {
            info!(
                trace_id = %trace_id,
                path = %download_path.display(),
                "文件已存在且验证通过，跳过下载"
            );
            return Ok(download_path);
        }

        info!(
            trace_id = %trace_id,
            url = %manifest_file.url,
            path = %download_path.display(),
            size = manifest_file.size,
            "开始下载组件更新文件"
        );

        // 使用分块下载器下载文件
        use shared::downloader::{ChunkedDownloader, DownloadConfig};
        let downloader = ChunkedDownloader::with_config(DownloadConfig::default())?;
        let expected_hash = manifest_file
            .sha256
            .as_deref()
            .or(manifest_file.sha512.as_deref());

        let empty_callback =
            std::sync::Arc::new(|_progress: shared::downloader::DownloadProgress| {
                // 空回调
            });

        downloader
            .download(
                &manifest_file.url,
                &download_path,
                expected_hash,
                empty_callback,
                tokio_util::sync::CancellationToken::new(),
                trace_id,
            )
            .await?;

        // 验证下载的文件
        if !self
            .verify_existing_download(&download_path, manifest_file)
            .await?
        {
            return Err(anyhow!("下载的文件验证失败"));
        }

        info!(
            trace_id = %trace_id,
            path = %download_path.display(),
            "组件更新文件下载完成"
        );

        Ok(download_path)
    }
}

impl ManifestUpdater {
    /// 检查所有组件的可用更新
    pub async fn check_for_updates(&self, trace_id: &Uuid) -> anyhow::Result<UpdateCheckResult> {
        info!(
            trace_id = %trace_id,
            "开始检查组件更新"
        );

        // 获取当前所有组件版本
        let current_versions = self.get_current_component_versions(trace_id).await?;

        // 获取最新清单
        let manifest = self.fetch_latest_manifest(trace_id).await?;

        // 找到需要更新的组件（按优先级排序，返回第一个）
        let mut update_available = false;
        let mut update_info = None;

        for component_type in ComponentType::update_order() {
            if let Some(updater) = self.component_updaters.get(component_type) {
                // 检查前置条件
                if let Ok(prerequisites_met) = updater.check_prerequisites(trace_id).await {
                    if !prerequisites_met {
                        debug!(
                            trace_id = %trace_id,
                            component = ?component_type,
                            "组件更新前置条件不满足，跳过"
                        );
                        continue;
                    }
                }

                let current_version = current_versions
                    .get(&component_type)
                    .cloned()
                    .unwrap_or_default();

                // 评估清单以确定是否需要更新
                if let Ok(Some(component_update_info)) = self
                    .evaluate_manifest(manifest.clone(), &current_version, trace_id)
                    .await
                {
                    // 找到符合当前组件的文件
                    if let Some(component_file) = self.find_component_file(
                        &component_type,
                        &current_versions,
                        &component_update_info.files,
                    ) {
                        if component_file.version > current_version {
                            let available_version = component_file.version.clone();
                            update_available = true;
                            update_info = Some(UpdateInfo {
                                version: available_version.clone(),
                                release_date: component_update_info.release_date,
                                release_notes: component_update_info.release_notes,
                                files: vec![component_file],
                            });

                            info!(
                                trace_id = %trace_id,
                                component = ?component_type,
                                current_version = %current_version,
                                available_version = %available_version,
                                "发现可用的组件更新"
                            );
                            break; // 只处理第一个找到的更新（按优先级）
                        }
                    }
                }
            }
        }

        let result = UpdateCheckResult {
            update_available,
            current_version: Version::default(), // 这里可以返回主应用版本
            update_info,
            checked_at: Utc::now(),
        };

        info!(
            trace_id = %trace_id,
            update_available = update_available,
            "更新检查完成"
        );

        Ok(result)
    }

    /// 下载更新文件，支持进度回调和取消机制
    pub async fn download_update(
        &self,
        progress_callback: Option<Box<dyn Fn(DownloadProgress) + Send + Sync>>,
        cancellation_token: CancellationToken,
        trace_id: &Uuid,
    ) -> anyhow::Result<()> {
        use shared::downloader::{ChunkedDownloader, DownloadConfig};
        use std::sync::Arc;

        info!(
            trace_id = %trace_id,
            "开始下载更新"
        );

        // 创建默认的空进度回调，并使用 Arc 来共享
        let progress_callback = Arc::new(progress_callback.unwrap_or_else(|| {
            Box::new(|_progress: DownloadProgress| {
                // 空回调，不做任何事情
            })
        }));

        // 从缓存的清单检查是否有可用更新
        let manifest_guard = self.last_manifest.lock().await;
        let manifest = manifest_guard
            .as_ref()
            .ok_or_else(|| anyhow!("没有缓存的清单文件，请先运行更新检查"))?
            .clone();
        drop(manifest_guard);

        // 获取当前所有组件版本
        let current_versions = self.get_current_component_versions(trace_id).await?;

        // 找到需要更新的组件（按优先级排序，返回第一个）
        let mut update_info = None;

        for component_type in ComponentType::update_order() {
            if let Some(updater) = self.component_updaters.get(&component_type) {
                // 检查前置条件
                if let Ok(prerequisites_met) = updater.check_prerequisites(trace_id).await {
                    if !prerequisites_met {
                        continue;
                    }
                }

                let current_version = current_versions
                    .get(&component_type)
                    .cloned()
                    .unwrap_or_default();

                // 评估清单以确定是否需要更新
                if let Ok(Some(component_update_info)) = self
                    .evaluate_manifest(manifest.clone(), &current_version, trace_id)
                    .await
                {
                    // 找到符合当前组件的文件
                    if let Some(component_file) = self.find_component_file(
                        &component_type,
                        &current_versions,
                        &component_update_info.files,
                    ) {
                        if component_file.version > current_version {
                            let file_version = component_file.version.clone();
                            update_info = Some(UpdateInfo {
                                version: file_version,
                                release_date: component_update_info.release_date,
                                release_notes: component_update_info.release_notes,
                                files: vec![component_file],
                            });
                            break; // 只处理第一个找到的更新（按优先级）
                        }
                    }
                }
            }
        }

        let update_info = update_info.ok_or_else(|| anyhow!("没有找到可用的更新"))?;

        info!(
            trace_id = %trace_id,
            version = %update_info.version,
            files_count = update_info.files.len(),
            "开始下载更新文件"
        );

        // 创建分块下载器
        let downloader = ChunkedDownloader::with_config(DownloadConfig::default())?;

        for (index, manifest_file) in update_info.files.iter().enumerate() {
            // 检查取消信号
            if cancellation_token.is_cancelled() {
                info!(
                    trace_id = %trace_id,
                    "下载被取消"
                );
                return Err(anyhow!("下载被用户取消"));
            }

            let filename = self.extract_filename_from_url(&manifest_file.url)?;
            let download_path = self.get_download_path(&filename)?;

            // 确保下载目录存在
            if let Some(parent_dir) = download_path.parent() {
                self.file_adapter.create_dir_all(parent_dir).await?;
            }

            // 检查文件是否已存在且有效
            if self
                .verify_existing_download(&download_path, manifest_file)
                .await?
            {
                info!(
                    trace_id = %trace_id,
                    file_index = index + 1,
                    total_files = update_info.files.len(),
                    path = %download_path.display(),
                    "文件已存在且验证通过，跳过下载"
                );
                continue;
            }

            info!(
                trace_id = %trace_id,
                file_index = index + 1,
                total_files = update_info.files.len(),
                url = %manifest_file.url,
                path = %download_path.display(),
                size = manifest_file.size,
                "开始下载文件"
            );

            // 转换进度回调
            let progress_callback_clone = progress_callback.clone();
            let chunked_progress_callback = Arc::new(
                move |chunked_progress: shared::downloader::DownloadProgress| {
                    let converted_progress = DownloadProgress {
                        downloaded_bytes: chunked_progress.downloaded_bytes,
                        total_bytes: chunked_progress.total_bytes,
                        percentage: if chunked_progress.total_bytes > 0 {
                            (chunked_progress.downloaded_bytes as f64
                                / chunked_progress.total_bytes as f64)
                                * 100.0
                        } else {
                            0.0
                        },
                        speed_bytes_per_sec: chunked_progress.current_speed as u64,
                        eta_seconds: chunked_progress.eta_seconds,
                    };
                    progress_callback_clone(converted_progress);
                },
            );

            // 使用分块下载器下载文件
            let expected_hash = manifest_file
                .sha256
                .as_deref()
                .or(manifest_file.sha512.as_deref());

            downloader
                .download(
                    &manifest_file.url,
                    &download_path,
                    expected_hash,
                    chunked_progress_callback,
                    cancellation_token.clone(),
                    trace_id,
                )
                .await
                .map_err(|e| {
                    error!(
                        trace_id = %trace_id,
                        file_index = index + 1,
                        url = %manifest_file.url,
                        error = %e,
                        "文件下载失败"
                    );
                    anyhow!("文件下载失败: {}", e)
                })?;

            info!(
                trace_id = %trace_id,
                file_index = index + 1,
                path = %download_path.display(),
                "文件下载完成"
            );
        }

        info!(
            trace_id = %trace_id,
            version = %update_info.version,
            "所有更新文件下载完成"
        );

        Ok(())
    }

    /// 安装已下载的更新文件
    pub async fn install_update(&self, trace_id: &Uuid) -> anyhow::Result<()> {
        info!(
            trace_id = %trace_id,
            "开始安装更新"
        );

        // 从缓存的清单检查是否有可用更新
        let manifest_guard = self.last_manifest.lock().await;
        let manifest = manifest_guard
            .as_ref()
            .ok_or_else(|| anyhow!("没有缓存的清单文件，请先运行更新检查"))?
            .clone();
        drop(manifest_guard);

        // 获取当前所有组件版本
        let current_versions = self.get_current_component_versions(trace_id).await?;

        // 按优先级顺序处理第一个需要更新的组件
        for component_type in ComponentType::update_order() {
            if let Some(updater) = self.component_updaters.get(&component_type) {
                // 检查前置条件
                if let Ok(prerequisites_met) = updater.check_prerequisites(trace_id).await {
                    if !prerequisites_met {
                        debug!(
                            trace_id = %trace_id,
                            component = ?component_type,
                            "组件安装前置条件不满足，跳过"
                        );
                        continue;
                    }
                }

                let current_version = current_versions
                    .get(&component_type)
                    .cloned()
                    .unwrap_or_default();

                // 评估清单以确定是否需要更新
                if let Ok(Some(component_update_info)) = self
                    .evaluate_manifest(manifest.clone(), &current_version, trace_id)
                    .await
                {
                    // 找到符合当前组件的文件
                    if let Some(component_file) = self.find_component_file(
                        &component_type,
                        &current_versions,
                        &component_update_info.files,
                    ) {
                        if component_file.version > current_version {
                            info!(
                                trace_id = %trace_id,
                                component = ?component_type,
                                from_version = %current_version,
                                to_version = %component_file.version,
                                "开始安装组件更新"
                            );

                            // 创建更新信息
                            let update_info = UpdateInfo {
                                version: component_file.version.clone(),
                                release_date: component_update_info.release_date,
                                release_notes: component_update_info.release_notes,
                                files: vec![component_file],
                            };

                            // 应用更新
                            updater
                                .apply_update(&update_info, trace_id)
                                .await
                                .map_err(|e| {
                                    error!(
                                        trace_id = %trace_id,
                                        component = ?component_type,
                                        error = %e,
                                        "组件更新应用失败"
                                    );
                                    anyhow!(
                                        "组件 {} 更新应用失败: {}",
                                        component_type.display_name(),
                                        e
                                    )
                                })?;

                            // 验证更新
                            let update_successful = updater
                                .verify_update(&update_info.version, trace_id)
                                .await
                                .map_err(|e| {
                                    error!(
                                        trace_id = %trace_id,
                                        component = ?component_type,
                                        error = %e,
                                        "组件更新验证失败"
                                    );
                                    anyhow!(
                                        "组件 {} 更新验证失败: {}",
                                        component_type.display_name(),
                                        e
                                    )
                                })?;

                            if !update_successful {
                                error!(
                                    trace_id = %trace_id,
                                    component = ?component_type,
                                    "组件更新验证不通过"
                                );
                                return Err(anyhow!(
                                    "组件 {} 更新验证不通过",
                                    component_type.display_name()
                                ));
                            }

                            info!(
                                trace_id = %trace_id,
                                component = ?component_type,
                                to_version = %update_info.version,
                                "组件更新安装成功"
                            );

                            // 只处理第一个需要更新的组件，按优先级
                            return Ok(());
                        }
                    }
                }
            }
        }

        info!(
            trace_id = %trace_id,
            "没有找到需要安装的更新"
        );

        Ok(())
    }
}