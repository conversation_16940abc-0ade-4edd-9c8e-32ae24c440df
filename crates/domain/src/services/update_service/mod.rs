//! 更新管理服务
//!
//! 负责版本检查、更新下载和安装，集成Windows服务模块更新安装
//!
//! 职责：
//! - 检查和更新应用软件
//! - 检查和更新 WSL 镜像
//! - 检查和更新 Agent
//! - 检查和更新 WSL
//!
//! 应用更新策略：
//! - Manifest: 类 Electron updater，从 CDN 获取 latest.yml 文件，解析出最新版本号，然后下载对应版本的安装包
//! - Coordinated: 从服务器通过 API 获取更新信息(需传递 machine_id 和 当前版本号)，用于支持灰度更新
//!
//! 外部依赖：
//! - Helper 服务，在更新应用软件时，需要依赖 Helper 服务，用于需要权限的安装更新

use futures::lock::Mutex;
use std::sync::Arc;
use std::sync::atomic::AtomicU64;
use tokio::sync::watch;
use tokio::task::JoinHandle;
use tokio_util::sync::CancellationToken;

use super::{CoreService, ServiceConfig, ServiceFuture, ServiceStatus};
use crate::services::update_service::updaters::{ManifestUpdater, UpdateCheckResult};
use crate::{
    CoreError, CoreResult,
    adapters::{agent::AgentAdapter, file::FileAdapter, helper::HelperAdapter, http::HttpAdapter},
};
use tracing::{info, warn};
use uuid::Uuid;

mod components;
pub mod updaters;

/// 更新服务配置
#[derive(Debug, Clone)]
pub struct UpdateConfig {
    /// 更新检查间隔（秒）
    pub check_interval: u64,
    /// 下载超时时间（秒）
    pub download_timeout: u64,
    /// 是否自动下载更新
    pub auto_download_enabled: bool,
    /// 是否自动安装更新
    pub auto_install_enabled: bool,
    /// 更新安装重试次数（仅当使用 Manifest 策略时）
    pub install_retries: u32,
    /// CDN 基础 URL（仅当使用 Manifest 策略时）
    pub cdn_base_url: String,
    /// 是否允许预发布版本（仅当使用 Manifest 策略时）
    pub allow_prerelease: bool,
}

impl Default for UpdateConfig {
    fn default() -> Self {
        Self {
            check_interval: 3600,   // 1小时
            download_timeout: 1800, // 30分钟
            auto_download_enabled: false,
            auto_install_enabled: false,
            install_retries: 3,
            cdn_base_url: "".to_string(),
            allow_prerelease: false,
        }
    }
}

impl ServiceConfig for UpdateConfig {
    fn validate(&self) -> CoreResult<()> {
        if self.check_interval == 0 {
            return Err(CoreError::config_error(
                "check_interval must be greater than 0",
            ));
        }
        if self.download_timeout == 0 {
            return Err(CoreError::config_error(
                "download_timeout must be greater than 0",
            ));
        }

        if self.cdn_base_url.trim().is_empty() {
            return Err(CoreError::config_error("cdn_base_url cannot be empty"));
        }

        // 基本的 URL 格式验证
        if !self.cdn_base_url.starts_with("http://") && !self.cdn_base_url.starts_with("https://") {
            return Err(CoreError::config_error(
                "cdn_base_url must start with http:// or https://",
            ));
        }

        Ok(())
    }

    fn name(&self) -> &str {
        "update"
    }
}

/// 更新管理服务
pub struct UpdateService {
    // adapters
    http_adapter: Arc<HttpAdapter>,
    file_adapter: Arc<FileAdapter>,
    helper_adapter: Arc<HelperAdapter>,
    agent_adapter: Arc<AgentAdapter>,
    // services
    device_service: Arc<super::DeviceService>,
    settings_service: Arc<super::SettingsService>,
    status: watch::Sender<ServiceStatus>,
    // updater
    updater: Arc<ManifestUpdater>,
    /// 主循环运行控制
    event_loop: Arc<Mutex<Option<(JoinHandle<()>, CancellationToken)>>>,
    /// 当前轮询间隔（秒）
    poll_interval: Arc<AtomicU64>,
}

impl UpdateService {
    /// 创建新的更新服务
    pub fn new(
        config: UpdateConfig,
        device_service: Arc<super::DeviceService>,
        settings_service: Arc<super::SettingsService>,
        http_adapter: Arc<HttpAdapter>,
        file_adapter: Arc<FileAdapter>,
        helper_adapter: Arc<HelperAdapter>,
        agent_adapter: Arc<AgentAdapter>,
    ) -> CoreResult<Self> {
        config.validate()?;

        let updater = ManifestUpdater::new(
            config.cdn_base_url,
            {
                if config.auto_download_enabled && config.auto_install_enabled {
                    updaters::manifest::UpdateStrategy::AutoUpdate
                } else if config.auto_download_enabled {
                    updaters::manifest::UpdateStrategy::AutoDownload
                } else {
                    updaters::manifest::UpdateStrategy::Manual
                }
            },
            config.allow_prerelease,
            device_service.clone(),
            settings_service.clone(),
            http_adapter.clone(),
            file_adapter.clone(),
        );

        Ok(Self {
            http_adapter,
            file_adapter,
            helper_adapter,
            agent_adapter,
            settings_service,
            device_service,
            status: watch::Sender::new(ServiceStatus::Stopped),
            event_loop: Arc::new(Mutex::new(None)),
            poll_interval: Arc::new(AtomicU64::new(300)), // 默认5分钟
            updater: Arc::new(updater),
        })
    }

    /// 启动更新协调器主循环
    pub async fn start_updater(&self) -> anyhow::Result<()> {
        // 检查是否已经在运行
        let mut guard = self.event_loop.lock().await;
        if guard.is_some() {
            info!("Update coordinator loop already running");
            return Ok(());
        }

        info!("Starting update coordinator main loop");

        let signal = CancellationToken::new();

        // 启动主循环任务
        let handle = ManifestUpdater::create_event_loop(self.updater.clone(), signal.clone())?;

        *guard = Some((handle, signal));

        info!("Update coordinator main loop started");
        Ok(())
    }

    /// 停止更新协调器主循环
    pub async fn stop_updater(&self) {
        info!("Stopping update coordinator main loop");

        let mut guard = self.event_loop.lock().await;
        if let Some((handle, signal)) = guard.take() {
            signal.cancel();
            if let Err(err) = handle.await {
                warn!("Updater main loop error: {}", err);
            }
        } else {
            warn!("Updater main loop not running");
        }

        info!("Updater main loop stopped");
    }

    /// 检查更新 - 支持多种更新策略
    pub async fn check_for_updates(&self, trace_id: &Uuid) -> anyhow::Result<UpdateCheckResult> {
        self.updater.check_for_updates(trace_id).await
    }

    /// 下载更新
    pub async fn download_update(
        &self,
        progress_callback: Option<Box<dyn Fn(updaters::DownloadProgress) + Send + Sync>>,
        cancellation_token: CancellationToken,
        trace_id: &Uuid,
    ) -> anyhow::Result<()> {
        self.updater
            .download_update(progress_callback, cancellation_token, trace_id)
            .await
    }

    /// 安装更新
    pub async fn install_update(&self, trace_id: &Uuid) -> anyhow::Result<()> {
        self.updater.install_update(trace_id).await
    }

    /// 获取组件最新版本
    pub async fn get_latest_component_manifest(
        &self,
        component_type: updaters::ComponentType,
        versions: updaters::ComponentVersions,
        trace_id: &Uuid,
    ) -> anyhow::Result<Option<updaters::manifest::ComponentArtifact>> {
        self.updater
            .get_latest_component_manifest(component_type, versions, trace_id)
            .await
    }
}

impl CoreService for UpdateService {
    fn name(&self) -> &str {
        "update"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(self.health_check_impl())
    }
}

impl UpdateService {
    async fn start_impl(&self) -> CoreResult<()> {
        info!("Starting Update Service");

        self.status.send_replace(ServiceStatus::Starting);
        self.start_updater().await?;

        // 设置服务状态为运行中
        self.status.send_replace(ServiceStatus::Running);

        Ok(())
    }
    async fn stop_impl(&self) -> CoreResult<()> {
        self.status.send_replace(ServiceStatus::Stopping);
        self.stop_updater().await;
        self.status.send_replace(ServiceStatus::Stopped);
        Ok(())
    }

    async fn health_check_impl(&self) -> CoreResult<bool> {
        Ok(true)
    }
}
