//! 任务接单服务
//!
//! 统一管理任务接单的所有相关功能，包括：
//! - 手动启动/停止接单
//! - 自动接单逻辑（包含等待条件满足）
//! - 任务状态轮询和管理
//! - 切换接单时向 agent 发送开始和停止 指令

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::sync::atomic::{AtomicU32, Ordering};
use std::time::Duration;
use tokio::sync::{Mutex, watch};
use tokio::time::sleep;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, instrument, trace, warn};

use crate::adapters::AdapterError;
use crate::adapters::agent::AgentAdapter;
use crate::adapters::http::HttpAdapter;
use crate::adapters::ModuleAdapter;
use crate::services::{CoreService, DeviceService, ServiceFuture, ServiceStatus, UserService};
use crate::{CoreError, CoreResult};
use shared::backoff::Backoff;

/// API 数据结构定义

/// 节点注册请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeRegisterRequest {
    /// 镜像节点ID
    pub code: String,
    /// 设备ID
    pub machine_id: String,
    /// 用户ID
    pub uid: String,
}

/// 节点操作请求（启用/禁用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeOperationRequest {
    /// 设备ID
    pub machine_id: String,
}

/// 通用API响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    #[serde(default)]
    pub err_code: Option<u32>,
    #[serde(default)]
    pub err_message: Option<String>,
    pub data: Option<T>,
}

/// 节点状态响应数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeStatusData {
    /// 任务状态，如 "RUNNING", "IDLE" 等
    #[serde(default)]
    pub task_status: Option<String>,
    /// 任务开始时间
    #[serde(default)]
    pub task_start_time: Option<String>,
    /// 其他节点状态信息
    #[serde(flatten)]
    pub extra: serde_json::Map<String, serde_json::Value>,
}

/// 任务接单服务配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskAcceptanceConfig {
    /// 任务状态轮询间隔（秒）
    pub polling_interval: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
    /// 节点空闲检查间隔（秒）
    pub idle_check_interval: u64,
    /// 自动接单条件检查间隔（秒）
    pub auto_accept_check_interval: u64,
    /// 轮询失败熔断阈值
    pub polling_failure_threshold: u32,
    /// 熔断后的恢复间隔（秒）
    pub circuit_breaker_recovery_interval: u64,
    /// 节点注册失败时是否停止接单流程
    pub stop_on_registration_failure: bool,
}

impl Default for TaskAcceptanceConfig {
    fn default() -> Self {
        Self {
            polling_interval: 60, // 每分钟轮询一次节点状态
            max_retries: 3,
            heartbeat_interval: 60,
            idle_check_interval: 120,
            auto_accept_check_interval: 5,
            polling_failure_threshold: 5, // 连续失败5次触发熔断
            circuit_breaker_recovery_interval: 300, // 熔断后5分钟尝试恢复
            stop_on_registration_failure: true, // 默认注册失败不停止流程
        }
    }
}

/// 任务接单状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskAcceptanceStatus {
    /// 停止接单
    Stopped,
    /// 正在启动接单
    Starting,
    /// 正在接单
    Accepting,
    /// 正在停止接单
    Stopping,
}

/// 任务执行状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskExecutionStatus {
    /// 空闲状态
    Idle,
    /// 正在执行任务
    Executing {
        start_time: chrono::DateTime<chrono::Local>,
    },
}
impl PartialEq<bool> for TaskExecutionStatus {
    fn eq(&self, other: &bool) -> bool {
        match self {
            TaskExecutionStatus::Idle => *other == false,
            TaskExecutionStatus::Executing { .. } => *other == true,
        }
    }
}

/// 接单触发类型
#[derive(Debug, Clone, PartialEq)]
pub enum AcceptanceTrigger {
    /// 手动触发
    Manual,
    /// 自动触发
    Automatic,
}

/// 任务状态（用于全局状态管理）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskState {
    pub acceptance: TaskAcceptanceStatus,
    pub execution: TaskExecutionStatus,
}

impl Default for TaskState {
    fn default() -> Self {
        Self {
            acceptance: TaskAcceptanceStatus::Stopped,
            execution: TaskExecutionStatus::Idle,
        }
    }
}

/// 任务接单服务
pub struct TaskAcceptanceService {
    config: TaskAcceptanceConfig,
    status: watch::Sender<ServiceStatus>,
    state: watch::Sender<TaskState>,
    http_adapter: Arc<HttpAdapter>,
    agent_adapter: Arc<AgentAdapter>,
    device_service: Arc<DeviceService>,
    user_service: Arc<UserService>,
    // 异步任务句柄
    polling_handle: Arc<Mutex<Option<(tokio::task::JoinHandle<()>, CancellationToken)>>>,
    // 启用节点的重试任务句柄
    enable_node_handle: Arc<Mutex<Option<(tokio::task::JoinHandle<()>, CancellationToken)>>>,
    // 禁用节点的重试任务句柄
    disable_node_handle: Arc<Mutex<Option<(tokio::task::JoinHandle<()>, CancellationToken)>>>,
    // 轮询失败计数
    polling_failure_count: Arc<AtomicU32>,
    // 全局取消信号，用于中断整个服务
    service_cancellation: CancellationToken,
    // 启动接单流程的取消令牌
    startup_cancellation: Arc<Mutex<Option<CancellationToken>>>,
}

impl TaskAcceptanceService {
    /// 创建新的任务接单服务实例
    pub fn new(
        config: TaskAcceptanceConfig,
        http_adapter: Arc<HttpAdapter>,
        agent_adapter: Arc<AgentAdapter>,
        device_service: Arc<DeviceService>,
        user_service: Arc<UserService>,
    ) -> CoreResult<Self> {
        Ok(Self {
            config,
            status: watch::Sender::new(ServiceStatus::Stopped),
            state: watch::Sender::new(TaskState::default()),
            http_adapter,
            agent_adapter,
            device_service,
            user_service,
            polling_handle: Arc::new(Mutex::new(None)),
            enable_node_handle: Arc::new(Mutex::new(None)),
            disable_node_handle: Arc::new(Mutex::new(None)),
            polling_failure_count: Arc::new(AtomicU32::new(0)),
            service_cancellation: CancellationToken::new(),
            startup_cancellation: Arc::new(Mutex::new(None)),
        })
    }

    /// 手动开始任务接单
    #[instrument(skip(self), fields(service = "task_acceptance"))]
    pub async fn start_accepting_tasks(&self) -> CoreResult<()> {
        info!("开始手动启动任务接单服务");
        if let Err(err) = self
            .start_accepting_tasks_internal(AcceptanceTrigger::Manual)
            .await
        {
            error!("手动启动任务接单失败: {}", err);
            self.state.send_modify(|state: &mut TaskState| {
                state.acceptance = TaskAcceptanceStatus::Stopped;
            });
            return Err(err);
        }
        info!("手动启动任务接单服务成功完成");
        Ok(())
    }

    /// 自动开始任务接单
    #[instrument(skip(self), fields(service = "task_acceptance"))]
    pub async fn start_auto_accepting_tasks(&self) -> CoreResult<()> {
        info!("开始自动启动任务接单服务");
        if let Err(err) = self
            .start_accepting_tasks_internal(AcceptanceTrigger::Automatic)
            .await
        {
            error!("自动启动任务接单失败: {}", err);
            self.state.send_modify(|state: &mut TaskState| {
                state.acceptance = TaskAcceptanceStatus::Stopped;
            });
            return Err(err);
        }
        info!("自动启动任务接单服务成功完成");
        Ok(())
    }

    /// 取消正在进行的启动接单流程
    #[instrument(skip(self), fields(service = "task_acceptance"))]
    pub async fn cancel_startup(&self) -> CoreResult<()> {
        info!("开始取消启动接单流程");

        // 检查当前状态
        let current_status = self.state.borrow().acceptance.clone();
        if !matches!(current_status, TaskAcceptanceStatus::Starting) {
            warn!("当前不在启动状态 ({:?})，无法取消启动", current_status);
            return Ok(());
        }

        // 触发取消信号
        if let Some(token) = self.startup_cancellation.lock().await.as_ref() {
            info!("发送启动取消信号");
            token.cancel();
        }

        // 等待一小段时间让取消信号生效
        tokio::time::sleep(Duration::from_millis(100)).await;

        // 执行清理
        info!("执行启动取消后的资源清理");
        self.cleanup_startup_resources().await;

        info!("启动接单流程已成功取消");
        Ok(())
    }

    /// 手动停止任务接单
    #[instrument(skip(self), fields(service = "task_acceptance"))]
    pub async fn stop_accepting_tasks(&self) -> CoreResult<()> {
        info!("开始手动停止任务接单服务");
        let result = self.stop_accepting_tasks_internal().await;
        match &result {
            Ok(()) => info!("手动停止任务接单服务成功完成"),
            Err(err) => error!("手动停止任务接单服务失败: {}", err),
        }
        result
    }

    /// 获取当前节点状态（从服务端）
    #[instrument(skip(self), fields(service = "task_acceptance"))]
    pub async fn get_node_status(&self) -> CoreResult<serde_json::Value> {
        info!("开始获取节点状态");

        let machine_id = self.device_service.get_machine_id();
        let status_request = NodeOperationRequest {
            machine_id: machine_id.clone(),
        };

        debug!("发送节点状态查询请求 - 设备ID: {}", machine_id);

        match self
            .http_adapter
            .post::<NodeOperationRequest, ApiResponse<serde_json::Value>>(
                "/api/node/get",
                &status_request,
            )
            .await
        {
            Ok(response) => {
                if response.success {
                    if let Some(data) = response.data {
                        info!("成功获取节点状态");
                        Ok(data)
                    } else {
                        debug!("节点状态响应数据为空");
                        Ok(serde_json::json!({}))
                    }
                } else {
                    let error_msg = response
                        .err_message
                        .unwrap_or_else(|| "Unknown error".to_string());
                    error!(
                        "获取节点状态失败 - 错误码: {:?}, 错误信息: {}",
                        response.err_code, error_msg
                    );
                    Err(CoreError::HttpError(AdapterError::RequestFailed(format!(
                        "获取节点状态失败: {}",
                        error_msg
                    ))))
                }
            }
            Err(err) => {
                error!("节点状态查询API调用失败: {}", err);
                Err(CoreError::HttpError(AdapterError::RequestFailed(format!(
                    "节点状态查询失败: {}",
                    err
                ))))
            }
        }
    }

    /// 获取当前任务状态（统一接口）
    pub fn get_current_state(&self) -> TaskState {
        self.state.borrow().clone()
    }

    pub fn get_current_acceptance(&self) -> TaskAcceptanceStatus {
        self.state.borrow().acceptance.clone()
    }

    pub fn state_receiver(&self) -> watch::Receiver<TaskState> {
        self.state.subscribe()
    }

    /// 检查节点是否空闲
    pub async fn is_node_idle(&self) -> CoreResult<bool> {
        info!("检查节点是否空闲");

        let trace_id = crate::logging::current_trace_id();
        match self.agent_adapter.get_node_status(trace_id.clone()).await {
            Ok((idle, node_info, tasks)) => {
                info!(
                    "节点空闲状态检查结果 - 空闲: {}, 节点ID: {}, 任务数: {} (trace_id: {})",
                    idle,
                    node_info.node_id,
                    tasks.len(),
                    trace_id
                );
                Ok(idle)
            }
            Err(err) => {
                error!("无法获取节点空闲状态 (trace_id: {}): {}", trace_id, err);
                Err(CoreError::AgentError(AdapterError::RequestFailed(format!(
                    "获取节点空闲状态失败: {}",
                    err
                ))))
            }
        }
    }

    // === 内部实现方法 ===

    /// 清理启动过程中的资源
    async fn cleanup_startup_resources(&self) {
        info!("开始清理启动过程中的资源");

        // 1. 停止轮询任务
        if let Some((handle, signal)) = self.polling_handle.lock().await.take() {
            info!("停止轮询任务");
            signal.cancel();
            if let Err(err) = handle.await {
                error!("停止轮询任务时发生错误: {}", err);
            }
        }

        // 2. 停止启用节点重试任务
        if let Some((handle, signal)) = self.enable_node_handle.lock().await.take() {
            info!("停止启用节点重试任务");
            signal.cancel();
            if let Err(err) = handle.await {
                error!("停止启用节点重试任务时发生错误: {}", err);
            }
        }

        // 3. 尝试停止守护进程（如果已启动）
        let trace_id = crate::logging::current_trace_id();
        if let Err(err) = self.agent_adapter.stop_daemon(trace_id.clone()).await {
            warn!("清理时停止守护进程失败 (trace_id: {}): {}", trace_id, err);
        } else {
            info!("清理时成功停止守护进程 (trace_id: {})", trace_id);
        }

        // 4. 重置状态
        self.state.send_modify(|state: &mut TaskState| {
            state.acceptance = TaskAcceptanceStatus::Stopped;
        });

        info!("资源清理完成");
    }

    /// 检查启动是否被取消
    fn check_startup_cancelled(&self, startup_token: &CancellationToken) -> CoreResult<()> {
        if startup_token.is_cancelled() {
            info!("检测到启动取消信号");
            return Err(CoreError::operation_cancelled("启动接单流程被用户取消"));
        }
        Ok(())
    }

    /// 内部启动接单实现
    async fn start_accepting_tasks_internal(&self, trigger: AcceptanceTrigger) -> CoreResult<()> {
        // 创建启动取消令牌
        let startup_token = CancellationToken::new();
        *self.startup_cancellation.lock().await = Some(startup_token.clone());

        // 检查当前状态
        let current_status = self.state.borrow().acceptance.clone();
        if matches!(
            current_status,
            TaskAcceptanceStatus::Accepting
                | TaskAcceptanceStatus::Starting
                | TaskAcceptanceStatus::Stopping
        ) {
            warn!(
                "任务接单服务已在运行中 (当前状态: {:?})，忽略此次请求",
                current_status
            );
            return Ok(());
        }

        info!("当前任务接单状态: {:?}，开始启动流程", current_status);

        // 更新接单状态
        self.state.send_modify(|state: &mut TaskState| {
            state.acceptance = TaskAcceptanceStatus::Starting;
        });
        info!("任务接单状态已更新为启动中");

        match trigger {
            AcceptanceTrigger::Manual => info!("执行手动任务接单启动流程"),
            AcceptanceTrigger::Automatic => info!("执行自动任务接单启动流程"),
        }
        // 调用 agent 启动服务

        // 检查是否被取消
        self.check_startup_cancelled(&startup_token)?;

        // 启动 agent（关键步骤）
        info!("正在启动 Agent 适配器...");
        if let Err(err) = self.agent_adapter.start().await {
            error!("无法启动 Agent 适配器: {}", err);
            warn!("Agent 适配器启动失败，开始清理资源");
            self.cleanup_startup_resources().await;
            return Err(CoreError::AgentError(AdapterError::ConnectionFailed(
                err.to_string(),
            )));
        };
        info!("Agent 适配器启动成功");

        // 检查是否被取消
        self.check_startup_cancelled(&startup_token)?;

        // 启动守护进程（关键步骤）
        let trace_id = crate::logging::current_trace_id();
        info!(
            "Agent 适配器已启动，开始启动任务编排守护进程 (trace_id: {})",
            trace_id
        );
        if let Err(err) = self.agent_adapter.start_daemon(trace_id.clone()).await {
            error!(trace_id = %trace_id, "无法启动任务编排守护进程: {}", err);
            warn!("守护进程启动失败，开始清理资源");
            self.cleanup_startup_resources().await;
            return Err(CoreError::AgentError(AdapterError::ConnectionFailed(
                err.to_string(),
            )));
        };
        info!("任务编排守护进程启动成功 (trace_id: {})", trace_id);

        // 获取节点状态和 ID（关键步骤）
        let trace_id = crate::logging::current_trace_id();
        info!("正在获取节点状态信息 (trace_id: {})", trace_id);
        let (idle, node_info, tasks) =
            match self.agent_adapter.get_node_status(trace_id.clone()).await {
                Ok(result) => result,
                Err(err) => {
                    error!("无法获取节点状态信息 (trace_id: {}): {}", trace_id, err);
                    warn!("获取节点状态失败，开始清理资源");
                    self.cleanup_startup_resources().await;
                    return Err(CoreError::AgentError(AdapterError::ConnectionFailed(
                        err.to_string(),
                    )));
                }
            };
        let node_id = node_info.node_id.clone();
        info!(
            "成功获取节点信息 - 节点ID: {}, 空闲状态: {}, 当前任务数: {}, trace_id: {}",
            node_id,
            idle,
            tasks.len(),
            trace_id
        );

        // 调用服务端API注册节点（关键步骤）
        info!("正在向服务端注册节点: {}", node_id);
        if let Err(err) = self.register_node(node_id.clone()).await {
            error!("向服务端注册节点失败 (节点ID: {}): {}", node_id, err);

            if self.config.stop_on_registration_failure {
                error!("配置要求注册失败时停止接单流程，正在清理资源并退出");
                self.cleanup_startup_resources().await;
                return Err(CoreError::service_error(&format!(
                    "节点注册失败，停止接单流程: {}",
                    err
                )));
            } else {
                warn!("节点注册失败，但不影响任务接单启动流程，将继续后续步骤");
            }
        } else {
            info!("成功向服务端注册节点: {}", node_id);
        }

        // 并行执行启用节点和启动任务轮询
        info!("正在并行启动节点启用和任务轮询 (节点ID: {})", node_id);

        let enable_future = async {
            // 启动启用节点的重试任务（后台运行）
            self.start_enable_node_retry().await;

            // 尝试立即启用一次，如果失败则由后台重试任务继续处理
            tokio::time::timeout(Duration::from_secs(2), self.enable_node()).await
        };

        let polling_future = async {
            info!(
                "正在启动任务状态轮询机制 (轮询间隔: {}秒)",
                self.config.polling_interval
            );
            self.start_task_polling().await
        };

        // 并行执行两个任务
        let (enable_result, polling_result) = tokio::join!(enable_future, polling_future);

        // 处理启用节点结果
        match enable_result {
            Ok(Ok(())) => {
                info!("成功启用节点接单功能 (节点ID: {})", node_id);
            }
            Ok(Err(err)) => {
                warn!("节点启用失败但将由后台重试: {} (节点ID: {})", err, node_id);
            }
            Err(_) => {
                warn!(
                    "节点启用超时，将由后台重试任务继续处理 (节点ID: {})",
                    node_id
                );
            }
        }

        // 处理任务轮询结果
        if let Err(err) = polling_result {
            error!("启动任务状态轮询机制失败: {}", err);
            warn!("轮询机制启动失败，但接单服务将继续运行，任务状态可能不会实时更新");
        } else {
            info!("任务状态轮询机制启动成功");
        }

        // 更新状态为接单中
        self.state.send_modify(|state: &mut TaskState| {
            state.acceptance = TaskAcceptanceStatus::Accepting;
        });
        info!("任务接单状态已更新为正在接单中");

        info!("任务接单服务启动流程全部完成，开始接单");
        Ok(())
    }

    /// 内部停止接单实现
    async fn stop_accepting_tasks_internal(&self) -> CoreResult<()> {
        let current_status = self.state.borrow().acceptance.clone();
        info!("开始停止任务接单流程 (当前状态: {:?})", current_status);

        // 更新接单状态
        self.state.send_modify(|state: &mut TaskState| {
            state.acceptance = TaskAcceptanceStatus::Stopping;
        });
        info!("任务接单状态已更新为停止中");

        // 设置停止标志

        // 停止轮询任务
        info!("正在停止任务状态轮询机制");
        if let Some((handle, signal)) = self.polling_handle.lock().await.take() {
            signal.cancel();
            if let Err(err) = handle.await {
                error!("停止任务状态轮询机制时发生错误: {}", err);
            } else {
                info!("任务状态轮询机制已成功停止");
            }
        } else {
            info!("任务状态轮询机制未在运行，无需停止");
        }

        // 并行执行禁用节点和停止守护进程以提高效率
        info!("正在并行执行节点禁用和守护进程停止");

        let disable_future = async {
            info!("正在禁用节点接单功能");

            // 启动禁用节点的重试任务（后台运行）
            self.start_disable_node_retry().await;

            // 尝试立即禁用一次，如果失败则由后台重试任务继续处理
            tokio::time::timeout(Duration::from_secs(2), self.disable_node()).await
        };

        let daemon_stop_future = async {
            let trace_id = crate::logging::current_trace_id();
            info!("正在停止任务编排守护进程 (trace_id: {})", trace_id);
            self.agent_adapter
                .stop_daemon(trace_id.clone())
                .await
                .map_err(|err| {
                    error!(trace_id = %trace_id, "停止任务编排守护进程失败: {}", err);
                    CoreError::AgentError(AdapterError::ConnectionFailed(err.to_string()))
                })
        };

        // 并行执行两个任务
        let (disable_result, daemon_result) = tokio::join!(disable_future, daemon_stop_future);

        // 处理禁用节点结果
        match disable_result {
            Ok(Ok(())) => {
                info!("成功禁用节点接单功能");
            }
            Ok(Err(err)) => {
                warn!("节点禁用失败但将由后台重试: {}", err);
                warn!("节点禁用失败，但本地停止流程将继续，服务端可能仍认为节点可用");
            }
            Err(_) => {
                warn!("节点禁用超时，将由后台重试任务继续处理");
                warn!("节点禁用超时，但本地停止流程将继续，服务端可能仍认为节点可用");
            }
        }

        // 处理守护进程停止结果
        match daemon_result {
            Ok(()) => {
                info!("任务编排守护进程已成功停止");
            }
            Err(err) => {
                error!("停止任务编排守护进程失败: {}", err);
                return Err(err);
            }
        }

        // 更新状态为停止
        self.state.send_modify(|state: &mut TaskState| {
            state.acceptance = TaskAcceptanceStatus::Stopped;
        });
        info!("任务接单状态已更新为已停止");

        info!("任务接单服务停止流程全部完成");
        Ok(())
    }

    /// 启动任务轮询
    async fn start_task_polling(&self) -> CoreResult<()> {
        let mut guard = self.polling_handle.lock().await;
        if guard.is_some() {
            warn!("任务状态轮询机制已在运行中，跳过重复启动");
            return Ok(());
        }

        info!(
            "准备启动任务状态轮询机制 (轮询间隔: {}秒)",
            self.config.polling_interval
        );

        let http_adapter = self.http_adapter.clone();
        let agent_adapter = self.agent_adapter.clone();
        let polling_interval = self.config.polling_interval;
        let state_tx = self.state.clone();
        let signal_handler = CancellationToken::new();
        let signal = signal_handler.clone();
        let machine_id = self.device_service.get_machine_id();
        let failure_count = self.polling_failure_count.clone();
        let failure_threshold = self.config.polling_failure_threshold;
        let recovery_interval = self.config.circuit_breaker_recovery_interval;

        let handle = tokio::spawn(async move {
            let lop = async {
                let mut is_circuit_open = false;
                let mut circuit_opened_at: Option<tokio::time::Instant> = None;

                loop {
                    // 检查熔断器状态
                    if is_circuit_open {
                        if let Some(opened_at) = circuit_opened_at {
                            let elapsed = opened_at.elapsed();
                            if elapsed >= Duration::from_secs(recovery_interval) {
                                info!("熔断器恢复期已到，尝试恢复轮询");
                                is_circuit_open = false;
                                circuit_opened_at = None;
                                failure_count.store(0, Ordering::SeqCst);
                            } else {
                                debug!(
                                    "熔断器仍处于开启状态，等待恢复 (剩余: {}秒)",
                                    recovery_interval - elapsed.as_secs()
                                );
                                sleep(Duration::from_secs(polling_interval)).await;
                                continue;
                            }
                        }
                    }

                    // 首先检查 Agent 连接状态，如果断开则尝试重连
                    // 这样可以确保轮询任务状态时 Agent 处于可用状态
                    if !agent_adapter.is_connected().await {
                        warn!("检测到 Agent 连接已断开，开始自动重连流程");
                        
                        // 使用已有的自动重连机制
                        match agent_adapter.start_with_auto_reconnect().await {
                            Ok(()) => {
                                info!("Agent 自动重连成功，继续任务状态轮询");
                            }
                            Err(e) => {
                                error!("Agent 自动重连失败: {}，将在下个轮询周期重试", e);
                                // 不中断轮询循环，让任务状态轮询继续进行
                                // 这样可以在Agent恢复时立即恢复正常状态
                            }
                        }
                    }
                    
                    // 轮询任务状态
                    match Self::poll_task_status(&http_adapter, &machine_id).await {
                        Ok((is_running, start_time)) => {
                            // 重置失败计数
                            failure_count.store(0, Ordering::SeqCst);

                            let status_changed =
                                state_tx.send_if_modified(|state: &mut TaskState| {
                                    if state.execution == is_running {
                                        return false;
                                    }
                                    let old_status = state.execution.clone();
                                    state.execution = if is_running {
                                        TaskExecutionStatus::Executing {
                                            start_time: start_time
                                                .unwrap_or_else(|| chrono::Local::now()),
                                        }
                                    } else {
                                        TaskExecutionStatus::Idle
                                    };
                                    debug!(
                                        "任务执行状态变更: {:?} -> {:?}",
                                        old_status, state.execution
                                    );
                                    true
                                });

                            if !status_changed {
                                debug!(
                                    "任务状态轮询完成，状态无变化 (当前: {})",
                                    if is_running { "执行中" } else { "空闲" }
                                );
                            }
                        }
                        Err(e) => {
                            error!("轮询任务状态失败: {}", e);

                            // 增加失败计数
                            let current_failures = failure_count.fetch_add(1, Ordering::SeqCst) + 1;
                            warn!(
                                "任务状态轮询失败，连续失败次数: {} (trace_id: {})",
                                current_failures,
                                crate::logging::current_trace_id()
                            );

                            // 检查是否需要触发熔断
                            if current_failures >= failure_threshold && !is_circuit_open {
                                error!(
                                    "任务状态轮询连续失败 {} 次，触发熔断保护 (恢复间隔: {}秒)",
                                    failure_threshold, recovery_interval
                                );
                                is_circuit_open = true;
                                circuit_opened_at = Some(tokio::time::Instant::now());
                            } else if !is_circuit_open {
                                warn!("任务状态轮询失败，将在下个周期重试");
                            }
                        }
                    }

                    // 等待下一次轮询
                    sleep(Duration::from_secs(polling_interval)).await;
                }
            };
            tokio::select! {
                _ = signal.cancelled() => {
                    info!("任务状态轮询收到停止信号，退出轮询循环");
                }
                _ = lop => {
                    info!("任务状态轮询周期性检查结束");
                }
            };
        });

        // 保存轮询句柄
        *guard = Some((handle, signal_handler));
        Ok(())
    }

    /// 注册节点（调用服务端API）
    #[instrument(skip(self), fields(node_id = %node_id))]
    async fn register_node(&self, node_id: String) -> anyhow::Result<()> {
        info!("开始注册节点到服务端 (节点ID: {})", node_id);

        // 获取设备ID
        let machine_id = self.device_service.get_machine_id();

        // 获取用户ID
        let uid = match self.user_service.get_user_id() {
            Some(id) => id.to_string(),
            None => {
                warn!("获取用户 ID 失败，用户未登录");
                return Err(anyhow::anyhow!("用户未登录"));
            }
        };

        // 构建注册请求
        let register_request = NodeRegisterRequest {
            code: node_id.clone(),
            machine_id: machine_id.clone(),
            uid: uid.clone(),
        };

        // 使用 backoff 重试机制
        let mut backoff = Backoff::new(self.config.max_retries);

        loop {
            info!(
                "发送节点注册请求 - 节点ID: {}, 设备ID: {}, 用户ID: {}, 尝试次数: {}",
                node_id,
                machine_id,
                uid,
                backoff.attempts() + 1
            );

            // 调用API
            match self
                .http_adapter
                .post::<NodeRegisterRequest, ApiResponse<serde_json::Value>>(
                    "/api/node/register",
                    &register_request,
                )
                .await
            {
                Ok(response) => {
                    if response.success {
                        info!("节点注册成功 (节点ID: {})", node_id);
                        debug!("注册响应数据: {:?}", response.data);
                        return Ok(());
                    } else {
                        let error_msg = response
                            .err_message
                            .unwrap_or_else(|| "Unknown error".to_string());
                        error!(
                            "节点注册失败 - 错误码: {:?}, 错误信息: {}, 尝试次数: {}",
                            response.err_code,
                            error_msg,
                            backoff.attempts() + 1
                        );

                        // 如果服务端明确返回失败，不再重试
                        if response.err_code.is_some() {
                            return Err(anyhow::anyhow!("节点注册失败: {}", error_msg));
                        }
                    }
                }
                Err(err) => {
                    error!(
                        "节点注册API调用失败: {}, 尝试次数: {}",
                        err,
                        backoff.attempts() + 1
                    );
                }
            }

            // 检查是否可以重试
            match backoff.next().await {
                Ok(()) => {
                    info!("将在下次回退间隔后重试节点注册");
                }
                Err(_) => {
                    error!("节点注册达到最大重试次数，放弃重试");
                    return Err(anyhow::anyhow!("节点注册失败，已达到最大重试次数"));
                }
            }
        }
    }

    /// 启用节点（调用服务端API）
    #[instrument(skip(self))]
    async fn enable_node(&self) -> anyhow::Result<()> {
        info!("开始启用节点接单");

        // 获取设备ID
        let machine_id = self.device_service.get_machine_id();

        // 构建启用请求
        let enable_request = NodeOperationRequest {
            machine_id: machine_id.clone(),
        };

        info!("发送节点启用请求 - 设备ID: {}", machine_id);

        // 调用API
        match self
            .http_adapter
            .post::<NodeOperationRequest, ApiResponse<serde_json::Value>>(
                "/api/node/enable",
                &enable_request,
            )
            .await
        {
            Ok(response) => {
                if response.success {
                    info!("节点启用成功 (设备ID: {})", machine_id);
                    debug!("启用响应数据: {:?}", response.data);
                    Ok(())
                } else {
                    let error_msg = response
                        .err_message
                        .unwrap_or_else(|| "Unknown error".to_string());
                    error!(
                        "节点启用失败 - 错误码: {:?}, 错误信息: {}",
                        response.err_code, error_msg
                    );
                    Err(anyhow::anyhow!("节点启用失败"))
                }
            }
            Err(err) => {
                error!("节点启用API调用失败: {}", err);
                Err(anyhow::anyhow!("节点启用失败"))
            }
        }
    }

    /// 启动启用节点的重试任务
    async fn start_enable_node_retry(&self) {
        // 检查是否已有运行中的任务
        let mut guard = self.enable_node_handle.lock().await;
        if guard.is_some() {
            warn!("启用节点重试任务已在运行中，跳过重复启动");
            return;
        }

        info!("启动节点启用重试任务");

        let http_adapter = self.http_adapter.clone();
        let device_service = self.device_service.clone();
        let max_retries = self.config.max_retries;
        let signal_handler = CancellationToken::new();
        let signal = signal_handler.clone();

        let handle = tokio::spawn(async move {
            let mut backoff = Backoff::new(max_retries);
            let machine_id = device_service.get_machine_id();
            let enable_request = NodeOperationRequest {
                machine_id: machine_id.clone(),
            };

            loop {
                tokio::select! {
                    _ = signal.cancelled() => {
                        info!("节点启用重试任务收到停止信号");
                        break;
                    }
                    _ = async {
                        info!(
                            "尝试启用节点 - 设备ID: {}, 尝试次数: {}",
                            machine_id, backoff.attempts() + 1
                        );

                        match http_adapter
                            .post::<NodeOperationRequest, ApiResponse<serde_json::Value>>(
                                "/api/node/enable",
                                &enable_request,
                            )
                            .await
                        {
                            Ok(response) => {
                                if response.success {
                                    info!("节点启用成功 (设备ID: {})", machine_id);
                                    return;
                                } else {
                                    let error_msg = response
                                        .err_message
                                        .unwrap_or_else(|| "Unknown error".to_string());
                                    error!(
                                        "节点启用失败 - 错误码: {:?}, 错误信息: {}",
                                        response.err_code, error_msg
                                    );
                                }
                            }
                            Err(err) => {
                                error!("节点启用API调用失败: {}", err);
                            }
                        }

                        // 等待下次重试
                        if backoff.next().await.is_err() {
                            error!("节点启用达到最大重试次数，停止重试");
                            return;
                        }
                    } => {
                        // 退出循环
                        break;
                    }
                }
            }
        });

        *guard = Some((handle, signal_handler));
    }

    /// 禁用节点（调用服务端API）
    #[instrument(skip(self))]
    async fn disable_node(&self) -> anyhow::Result<()> {
        info!("开始禁用节点接单功能");

        // 获取设备ID
        let machine_id = self.device_service.get_machine_id();

        // 构建禁用请求
        let disable_request = NodeOperationRequest {
            machine_id: machine_id.clone(),
        };

        info!("发送节点禁用请求 - 设备ID: {}", machine_id);

        // 调用API
        match self
            .http_adapter
            .post::<NodeOperationRequest, ApiResponse<serde_json::Value>>(
                "/api/node/disable",
                &disable_request,
            )
            .await
        {
            Ok(response) => {
                if response.success {
                    info!("节点禁用成功 (设备ID: {})", machine_id);
                    debug!("禁用响应数据: {:?}", response.data);
                    Ok(())
                } else {
                    let error_msg = response
                        .err_message
                        .unwrap_or_else(|| "Unknown error".to_string());
                    error!(
                        "节点禁用失败 - 错误码: {:?}, 错误信息: {}",
                        response.err_code, error_msg
                    );
                    Err(anyhow::anyhow!("节点禁用失败"))
                }
            }
            Err(err) => {
                error!("节点禁用API调用失败: {}", err);
                Err(anyhow::anyhow!("节点禁用失败"))
            }
        }
    }

    /// 启动禁用节点的重试任务
    async fn start_disable_node_retry(&self) {
        // 检查是否已有运行中的任务
        let mut guard = self.disable_node_handle.lock().await;
        if guard.is_some() {
            warn!("禁用节点重试任务已在运行中，跳过重复启动");
            return;
        }

        info!("启动节点禁用重试任务");

        let http_adapter = self.http_adapter.clone();
        let device_service = self.device_service.clone();
        let max_retries = self.config.max_retries;
        let signal_handler = CancellationToken::new();
        let signal = signal_handler.clone();

        let handle = tokio::spawn(async move {
            let mut backoff = Backoff::new(max_retries);
            let machine_id = device_service.get_machine_id();
            let disable_request = NodeOperationRequest {
                machine_id: machine_id.clone(),
            };

            loop {
                tokio::select! {
                    _ = signal.cancelled() => {
                        info!("节点禁用重试任务收到停止信号");
                        break;
                    }
                    _ = async {
                        info!(
                            "尝试禁用节点 - 设备ID: {}, 尝试次数: {}",
                            machine_id, backoff.attempts() + 1
                        );

                        match http_adapter
                            .post::<NodeOperationRequest, ApiResponse<serde_json::Value>>(
                                "/api/node/disable",
                                &disable_request,
                            )
                            .await
                        {
                            Ok(response) => {
                                if response.success {
                                    info!("节点禁用成功 (设备ID: {})", machine_id);
                                    return;
                                } else {
                                    let error_msg = response
                                        .err_message
                                        .unwrap_or_else(|| "Unknown error".to_string());
                                    error!(
                                        "节点禁用失败 - 错误码: {:?}, 错误信息: {}",
                                        response.err_code, error_msg
                                    );

                                    // 如果服务端明确返回失败，不再重试
                                    if response.err_code.is_some() {
                                        error!("服务端明确拒绝禁用请求，停止重试");
                                        return;
                                    }
                                }
                            }
                            Err(err) => {
                                error!("节点禁用API调用失败: {}", err);
                            }
                        }

                        // 等待下次重试
                        if backoff.next().await.is_err() {
                            error!("节点禁用达到最大重试次数，停止重试");
                            return;
                        }
                    } => {
                        // 退出循环
                        break;
                    }
                }
            }
        });

        *guard = Some((handle, signal_handler));
    }

    /// 轮询任务状态
    #[instrument(skip(http_adapter))]
    async fn poll_task_status(
        http_adapter: &HttpAdapter,
        machine_id: &str,
    ) -> CoreResult<(bool, Option<chrono::DateTime<chrono::Local>>)> {
        debug!("开始轮询节点任务状态 (端点: /api/node/get)");

        // 构建状态查询请求
        let status_request = NodeOperationRequest {
            machine_id: machine_id.to_string(),
        };

        trace!("发送节点状态查询请求 - 设备ID: {}", machine_id);

        // 调用API
        match http_adapter
            .post::<NodeOperationRequest, ApiResponse<NodeStatusData>>(
                "/api/node/get",
                &status_request,
            )
            .await
        {
            Ok(response) => {
                if response.success {
                    if let Some(node_data) = response.data {
                        let task_status = node_data.task_status.as_deref().unwrap_or("IDLE");
                        let is_running = task_status == "RUNNING";

                        // 解析任务开始时间
                        let start_time = if let Some(time_str) =
                            node_data.task_start_time.as_deref()
                        {
                            // 尝试解析时间字符串，如果解析失败则使用当前时间
                            chrono::DateTime::parse_from_rfc3339(time_str)
                                .or_else(|_| {
                                    chrono::DateTime::parse_from_str(time_str, "%Y-%m-%d %H:%M:%S")
                                })
                                .map(|dt| dt.with_timezone(&chrono::Local))
                                .ok()
                        } else {
                            None
                        };

                        debug!(
                            "节点状态轮询成功 - 设备ID: {}, 任务状态: {}, 是否运行中: {}, 开始时间: {:?}",
                            machine_id, task_status, is_running, start_time
                        );

                        Ok((is_running, start_time))
                    } else {
                        debug!("节点状态响应数据为空，返回空闲状态");
                        Ok((false, None))
                    }
                } else {
                    let error_msg = response
                        .err_message
                        .unwrap_or_else(|| "Unknown error".to_string());
                    warn!(
                        "节点状态查询失败 - 错误码: {:?}, 错误信息: {}",
                        response.err_code, error_msg
                    );

                    // 查询失败时返回上次状态，避免频繁状态切换
                    Ok((false, None))
                }
            }
            Err(err) => {
                error!("节点状态查询API调用失败: {}", err);
                // API调用失败时返回空闲状态，避免影响轮询流程
                Ok((false, None))
            }
        }
    }
}

impl CoreService for TaskAcceptanceService {
    fn name(&self) -> &str {
        "task_acceptance"
    }

    fn status(&self) -> ServiceStatus {
        self.status.borrow().clone()
    }

    fn start(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.start_impl())
    }

    fn stop(&self) -> ServiceFuture<'_, ()> {
        Box::pin(self.stop_impl())
    }

    fn health_check(&self) -> ServiceFuture<'_, bool> {
        Box::pin(self.health_check_impl())
    }
}

impl TaskAcceptanceService {
    async fn start_impl(&self) -> CoreResult<()> {
        info!("正在启动任务接单服务核心组件");

        self.status.send_replace(ServiceStatus::Running);
        info!("任务接单服务状态已更新为运行中");

        info!("任务接单服务核心组件启动成功");
        Ok(())
    }

    async fn stop_impl(&self) -> CoreResult<()> {
        info!("正在停止任务接单服务核心组件");

        self.status.send_replace(ServiceStatus::Stopping);
        info!("任务接单服务状态已更新为停止中");

        // 如果正在接单，先停止接单
        let current_acceptance_status = self.state.borrow().acceptance.clone();
        if matches!(current_acceptance_status, TaskAcceptanceStatus::Accepting) {
            info!("检测到正在接单中，先执行停止接单流程");
            self.stop_accepting_tasks().await?;
        } else {
            info!(
                "当前未在接单状态 ({:?})，无需停止接单",
                current_acceptance_status
            );
        }

        // 停止所有重试任务
        info!("正在停止所有后台重试任务");

        // 停止启用节点重试任务
        if let Some((handle, signal)) = self.enable_node_handle.lock().await.take() {
            signal.cancel();
            if let Err(err) = handle.await {
                error!("停止启用节点重试任务时发生错误: {}", err);
            } else {
                info!("启用节点重试任务已成功停止");
            }
        }

        // 停止禁用节点重试任务
        if let Some((handle, signal)) = self.disable_node_handle.lock().await.take() {
            signal.cancel();
            if let Err(err) = handle.await {
                error!("停止禁用节点重试任务时发生错误: {}", err);
            } else {
                info!("禁用节点重试任务已成功停止");
            }
        }

        self.status.send_replace(ServiceStatus::Stopped);
        info!("任务接单服务状态已更新为已停止");

        info!("任务接单服务核心组件停止成功");
        Ok(())
    }

    async fn health_check_impl(&self) -> CoreResult<bool> {
        let service_status = self.status.borrow().clone();
        let task_state = self.state.borrow().clone();

        debug!(
            "执行任务接单服务健康检查 (服务状态: {:?}, 接单状态: {:?})",
            service_status, task_state.acceptance
        );

        // 1. 检查服务状态
        let service_running = service_status == ServiceStatus::Running;
        debug!(
            "服务状态检查: {}",
            if service_running { "正常" } else { "异常" }
        );

        // 2. 检查Agent连接状态
        let agent_healthy = match self.agent_adapter.check_health().await {
            Ok(()) => {
                debug!("Agent连接状态检查: 正常");
                true
            }
            Err(err) => {
                warn!("Agent连接状态检查失败: {}", err);
                false
            }
        };

        // 3. 检查HTTP适配器状态
        let http_healthy = match self.http_adapter.health_check().await {
            Ok(healthy) => {
                debug!(
                    "HTTP适配器状态检查: {}",
                    if healthy { "正常" } else { "异常" }
                );
                healthy
            }
            Err(err) => {
                warn!("HTTP适配器状态检查失败: {}", err);
                false
            }
        };

        // 4. 检查轮询任务状态
        let polling_healthy = {
            let handle_guard = self.polling_handle.lock().await;
            let is_running = handle_guard.is_some();
            let should_be_running =
                matches!(task_state.acceptance, TaskAcceptanceStatus::Accepting);

            let healthy = if should_be_running {
                is_running
            } else {
                true // 如果不应该运行，则认为健康
            };

            debug!(
                "轮询任务状态检查: {} (运行中: {}, 应该运行: {})",
                if healthy { "正常" } else { "异常" },
                is_running,
                should_be_running
            );
            healthy
        };

        // 5. 检查轮询失败计数
        let failure_count = self.polling_failure_count.load(Ordering::SeqCst);
        let failure_threshold_healthy = failure_count < self.config.polling_failure_threshold;
        debug!(
            "轮询失败计数检查: {} (当前失败: {}, 阈值: {})",
            if failure_threshold_healthy {
                "正常"
            } else {
                "异常"
            },
            failure_count,
            self.config.polling_failure_threshold
        );

        // 6. 检查启动取消状态
        let startup_token_healthy = {
            let token_guard = self.startup_cancellation.lock().await;
            match token_guard.as_ref() {
                Some(token) => !token.is_cancelled(),
                None => true, // 没有启动令牌也是正常的
            }
        };
        debug!(
            "启动取消状态检查: {}",
            if startup_token_healthy {
                "正常"
            } else {
                "异常"
            }
        );

        // 综合健康状态评估
        let is_healthy = service_running
            && agent_healthy
            && http_healthy
            && polling_healthy
            && failure_threshold_healthy
            && startup_token_healthy;

        debug!(
            "任务接单服务健康检查结果: {} (服务: {}, Agent: {}, HTTP: {}, 轮询: {}, 失败计数: {}, 启动状态: {})",
            if is_healthy { "健康" } else { "不健康" },
            service_running,
            agent_healthy,
            http_healthy,
            polling_healthy,
            failure_threshold_healthy,
            startup_token_healthy
        );

        Ok(is_healthy)
    }
}
