//! Domain 文件发送器
//!
//! 负责将文件分块发送给 Agent，管理传输过程和重传机制。

use protocol::events::transfer::chunk_sizes::ChunkSize;
use protocol::events::transfer::{ChunkData, TransferEvent};
use protocol::frame::{EventCode, send_message_frame};
use sha2::{Digest, Sha256};
use std::path::Path;
use tokio::fs;
use tokio::io::AsyncWriteExt;
use uuid::Uuid;

/// 文件发送器状态
#[derive(Debug, PartialEq)]
enum SenderStatus {
    /// 准备发送
    Ready,
    /// 正在发送
    Sending,
    /// 等待响应
    WaitingResponse,
    /// 发送完成
    Completed,
    /// 发送失败
    Failed,
}

/// 管理单个文件传输的发送过程
pub struct FileSender {
    /// 传输会话ID
    pub transfer_id: u32,
    /// 追踪此传输的 trace_id
    pub trace_id: Uuid,
    /// 当前状态
    pub status: SenderStatus,
    /// 文件路径
    pub file_path: std::path::PathBuf,
    /// 文件内容
    pub file_content: Vec<u8>,
    /// 文件大小
    pub file_size: u64,
    /// 文件 SHA256 哈希
    pub file_hash: String,
    /// 分块大小
    pub chunk_size: u32,
    /// 分块总数
    pub total_chunks: u32,
    /// 已发送分块的位图
    pub sent_chunks: Vec<bool>,
    /// 已确认分块的位图
    pub confirmed_chunks: Vec<bool>,
    /// 重传计数器
    pub retransmission_count: u32,
    /// 最大重传次数
    pub max_retransmissions: u32,
}

impl FileSender {
    /// 创建新的文件发送器
    pub async fn new(
        transfer_id: u32,
        trace_id: Uuid,
        file_path: &Path,
        chunk_size: Option<ChunkSize>,
    ) -> anyhow::Result<Self> {
        // 读取文件内容
        let file_content = fs::read(file_path).await?;
        let file_size = file_content.len() as u64;

        // 计算文件哈希
        let mut hasher = Sha256::new();
        hasher.update(&file_content);
        let file_hash = format!("{:x}", hasher.finalize());

        // 设置分块大小
        let chunk_size = chunk_size.unwrap_or(ChunkSize::Kb16);
        let chunk_size_bytes = chunk_size as u32;

        // 计算分块总数
        let total_chunks = ((file_size as f64) / (chunk_size_bytes as f64)).ceil() as u32;

        tracing::info!(
            transfer_id = transfer_id,
            trace_id = %trace_id,
            file_path = %file_path.display(),
            file_size = file_size,
            chunk_size = chunk_size_bytes,
            total_chunks = total_chunks,
            "创建文件发送器"
        );

        Ok(Self {
            transfer_id,
            trace_id,
            status: SenderStatus::Ready,
            file_path: file_path.to_path_buf(),
            file_content,
            file_size,
            file_hash,
            chunk_size: chunk_size_bytes,
            total_chunks,
            sent_chunks: vec![false; total_chunks as usize],
            confirmed_chunks: vec![false; total_chunks as usize],
            retransmission_count: 0,
            max_retransmissions: 3,
        })
    }

    /// 获取当前状态
    pub fn status(&self) -> &SenderStatus {
        &self.status
    }

    /// 开始传输流程
    pub async fn start_transfer<W>(&mut self, writer: &mut W) -> anyhow::Result<()>
    where
        W: AsyncWriteExt + Unpin,
    {
        if self.status != SenderStatus::Ready {
            anyhow::bail!("传输状态错误: 期望 Ready，实际 {:?}", self.status);
        }

        self.status = SenderStatus::Sending;

        // 发送 StartTransfer 事件
        let start_event = TransferEvent::StartTransfer {
            trace_id: self.trace_id,
            transfer_id: self.transfer_id,
            file_size: self.file_size,
            file_hash: self.file_hash.clone(),
            total_chunks: self.total_chunks,
            chunk_size: self.chunk_size,
        };

        let payload = serde_json::to_vec(&start_event)?;
        send_message_frame(writer, EventCode::Data, self.transfer_id, false, &payload).await?;

        tracing::info!(
            transfer_id = self.transfer_id,
            trace_id = %self.trace_id,
            "发送 StartTransfer 事件"
        );

        // 开始发送所有分块
        self.send_all_chunks(writer).await?;

        self.status = SenderStatus::WaitingResponse;
        Ok(())
    }

    /// 发送所有分块
    async fn send_all_chunks<W>(&mut self, writer: &mut W) -> anyhow::Result<()>
    where
        W: AsyncWriteExt + Unpin,
    {
        for chunk_id in 0..self.total_chunks {
            if !self.sent_chunks[chunk_id as usize] {
                self.send_chunk(writer, chunk_id).await?;
            }
        }
        Ok(())
    }

    /// 发送单个分块
    async fn send_chunk<W>(&mut self, writer: &mut W, chunk_id: u32) -> anyhow::Result<()>
    where
        W: AsyncWriteExt + Unpin,
    {
        let start_offset = (chunk_id as u64) * (self.chunk_size as u64);
        let end_offset = std::cmp::min(start_offset + (self.chunk_size as u64), self.file_size);

        let chunk_data_slice = &self.file_content[start_offset as usize..end_offset as usize];

        let chunk_data = ChunkData {
            transfer_id: self.transfer_id,
            chunk_id,
            data: chunk_data_slice,
        };

        let binary_payload = chunk_data.to_bytes();

        send_message_frame(
            writer,
            EventCode::Binary,
            self.transfer_id,
            false,
            &binary_payload,
        )
        .await?;

        self.sent_chunks[chunk_id as usize] = true;

        tracing::debug!(
            transfer_id = self.transfer_id,
            chunk_id = chunk_id,
            data_size = chunk_data_slice.len(),
            "发送分块数据"
        );

        Ok(())
    }

    /// 处理分块接收确认
    pub fn handle_chunk_received(&mut self, chunk_id: u32) -> anyhow::Result<bool> {
        if chunk_id >= self.total_chunks {
            anyhow::bail!("无效的分块ID: {}", chunk_id);
        }

        self.confirmed_chunks[chunk_id as usize] = true;

        tracing::debug!(
            transfer_id = self.transfer_id,
            chunk_id = chunk_id,
            "分块接收确认"
        );

        // 检查是否所有分块都已确认
        let all_confirmed = self.confirmed_chunks.iter().all(|&confirmed| confirmed);

        if all_confirmed {
            self.status = SenderStatus::Completed;
            tracing::info!(
                transfer_id = self.transfer_id,
                trace_id = %self.trace_id,
                "所有分块传输完成"
            );
        }

        Ok(all_confirmed)
    }

    /// 处理重传请求
    pub async fn handle_retransmission_request<W>(
        &mut self,
        writer: &mut W,
        chunk_ids: &[u32],
    ) -> anyhow::Result<()>
    where
        W: AsyncWriteExt + Unpin,
    {
        if self.retransmission_count >= self.max_retransmissions {
            self.status = SenderStatus::Failed;
            anyhow::bail!("达到最大重传次数: {}", self.max_retransmissions);
        }

        self.retransmission_count += 1;

        tracing::warn!(
            transfer_id = self.transfer_id,
            chunk_count = chunk_ids.len(),
            retransmission_count = self.retransmission_count,
            "处理重传请求"
        );

        // 重传指定的分块
        for &chunk_id in chunk_ids {
            if chunk_id < self.total_chunks {
                self.send_chunk(writer, chunk_id).await?;
            }
        }

        Ok(())
    }

    /// 处理传输完成响应
    pub fn handle_transfer_completed(&mut self, success: bool, error_message: Option<String>) {
        if success {
            self.status = SenderStatus::Completed;
            tracing::info!(
                transfer_id = self.transfer_id,
                trace_id = %self.trace_id,
                "传输成功完成"
            );
        } else {
            self.status = SenderStatus::Failed;
            tracing::error!(
                transfer_id = self.transfer_id,
                trace_id = %self.trace_id,
                error = ?error_message,
                "传输失败"
            );
        }
    }

    /// 获取未确认的分块ID列表
    pub fn get_unconfirmed_chunks(&self) -> Vec<u32> {
        self.confirmed_chunks
            .iter()
            .enumerate()
            .filter_map(
                |(index, &confirmed)| {
                    if !confirmed { Some(index as u32) } else { None }
                },
            )
            .collect()
    }

    /// 是否传输完成
    pub fn is_completed(&self) -> bool {
        self.status == SenderStatus::Completed
    }

    /// 是否传输失败
    pub fn is_failed(&self) -> bool {
        self.status == SenderStatus::Failed
    }

    /// 获取传输进度（0.0 - 1.0）
    pub fn get_progress(&self) -> f64 {
        if self.total_chunks == 0 {
            return 1.0;
        }

        let confirmed_count = self
            .confirmed_chunks
            .iter()
            .filter(|&&confirmed| confirmed)
            .count();
        confirmed_count as f64 / self.total_chunks as f64
    }

    /// 获取文件大小
    pub fn file_size(&self) -> u64 {
        self.file_size
    }

    /// 获取文件哈希
    pub fn file_hash(&self) -> &str {
        &self.file_hash
    }

    /// 获取总分块数
    pub fn total_chunks(&self) -> u32 {
        self.total_chunks
    }

    /// 获取分块大小
    pub fn chunk_size(&self) -> u32 {
        self.chunk_size
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_file_sender_creation() {
        // 创建临时文件
        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = b"Hello, World! This is a test file.";
        temp_file.write_all(test_data).unwrap();
        temp_file.flush().unwrap();

        let transfer_id = 123;
        let trace_id = Uuid::new_v4();

        let sender = FileSender::new(
            transfer_id,
            trace_id,
            temp_file.path(),
            Some(ChunkSize::Kb16),
        )
        .await
        .unwrap();

        assert_eq!(sender.transfer_id, transfer_id);
        assert_eq!(sender.trace_id, trace_id);
        assert_eq!(sender.file_size, test_data.len() as u64);
        assert_eq!(sender.total_chunks, 1); // 34 bytes should fit in one 16KB chunk
        assert!(!sender.file_hash.is_empty());
    }

    #[tokio::test]
    async fn test_chunk_confirmation() {
        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = vec![0u8; 32768]; // 32KB data, should create 2 chunks with 16KB size
        temp_file.write_all(&test_data).unwrap();
        temp_file.flush().unwrap();

        let mut sender =
            FileSender::new(1, Uuid::new_v4(), temp_file.path(), Some(ChunkSize::Kb16))
                .await
                .unwrap();

        assert_eq!(sender.total_chunks, 2);

        // 确认第一个分块
        let all_done = sender.handle_chunk_received(0).unwrap();
        assert!(!all_done);
        assert!(!sender.is_completed());

        // 确认第二个分块
        let all_done = sender.handle_chunk_received(1).unwrap();
        assert!(all_done);
        assert!(sender.is_completed());
    }

    #[tokio::test]
    async fn test_progress_calculation() {
        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = vec![0u8; 49152]; // 48KB data, should create 3 chunks with 16KB size
        temp_file.write_all(&test_data).unwrap();
        temp_file.flush().unwrap();

        let mut sender =
            FileSender::new(1, Uuid::new_v4(), temp_file.path(), Some(ChunkSize::Kb16))
                .await
                .unwrap();

        assert_eq!(sender.total_chunks, 3);
        assert_eq!(sender.get_progress(), 0.0);

        sender.handle_chunk_received(0).unwrap();
        assert!((sender.get_progress() - 0.333).abs() < 0.01);

        sender.handle_chunk_received(1).unwrap();
        assert!((sender.get_progress() - 0.666).abs() < 0.01);

        sender.handle_chunk_received(2).unwrap();
        assert_eq!(sender.get_progress(), 1.0);
    }
}
