use tracing::Id;
use tracing_subscriber::layer::{Context, Layer};
use tracing_subscriber::registry::LookupSpan;

use crate::logging::visitor::TraceIdVisitor;

pub type TraceId = uuid::Uuid;

/// 自定义 Layer，用于捕获和存储 trace_id
#[derive(Default)]
pub struct TraceIdLayer;

impl<S> Layer<S> for TraceIdLayer
where
    S: tracing::Subscriber + for<'lookup> LookupSpan<'lookup>,
{
    fn on_new_span(&self, attrs: &tracing::span::Attributes<'_>, id: &Id, ctx: Context<'_, S>) {
        let Some(span) = ctx.span(id) else {
            return;
        };
        let mut extensions = span.extensions_mut();

        let mut trace_id = None;
        attrs.record(&mut TraceIdVisitor {
            trace_id: &mut trace_id,
        });
        if trace_id.is_none() {
            return;
        }

        extensions.insert(trace_id);
    }

    fn on_enter(&self, id: &Id, ctx: Context<'_, S>) {
        let Some(span) = ctx.span(id) else {
            return;
        };
        let extensions = span.extensions();

        if let Some(trace_id) = extensions.get::<Option<TraceId>>().and_then(|id| *id) {
            super::CURRENT_TRACE_ID.with(|id_cell| {
                *id_cell.borrow_mut() = Some(trace_id);
            });
        }
    }

    fn on_exit(&self, id: &Id, ctx: Context<'_, S>) {
        let Some(span) = ctx.span(id) else {
            return;
        };

        // // After exiting the current span, the context will return to the parent.
        // // We find the parent's trace_id and set it as the current one.
        // // If there is no parent, or the parent has no trace_id, the result is None.
        let parent_trace_id = span
            .parent()
            .and_then(|parent| parent.extensions().get::<Option<TraceId>>().cloned())
            .flatten();

        super::CURRENT_TRACE_ID.with(|id_cell| {
            *id_cell.borrow_mut() = parent_trace_id;
        });
    }
}
