use crate::logging::{LogWriter, StructuredLogEntry};
use anyhow::Result;
use colored::{Color, Colorize};
use protocol::events::LogLevel;
use std::collections::HashMap;
use std::fmt;
use std::fmt::Write;
use std::fmt::{Debug, Display};
use tracing::field::{Field, Visit};
use tracing::{Event, Level, Subscriber};
use tracing_subscriber::fmt::format::Writer;
use tracing_subscriber::fmt::{FmtContext, FormatEvent, FormatFields, FormattedFields};
use tracing_subscriber::registry::LookupSpan;

pub(super) struct Formatter {}

impl Formatter {
    pub(super) fn new() -> Self {
        Self {}
    }
}

impl<S, N> FormatEvent<S, N> for Formatter
where
    S: Subscriber + for<'a> LookupSpan<'a>,
    N: for<'a> FormatFields<'a> + 'static,
{
    fn format_event(
        &self,
        ctx: &FmtContext<'_, S, N>,
        mut writer: Writer<'_>,
        event: &Event<'_>,
    ) -> fmt::Result {
        let meta = event.metadata();
        let now = chrono::Local::now();
        let mut visitor = Visitor::new();
        event.record(&mut visitor);
        let message = visitor.message;
        let mut fields_str = String::new();
        let mut trace_id = String::new();
        let mut fields = visitor.fields;

        if fields.contains_key("skip_console_layer") {
            return fmt::Result::Ok(());
        }
        // 从上下文获取格式化后的字段
        for span in ctx
            .event_scope()
            .into_iter()
            .flat_map(tracing_subscriber::registry::Scope::from_root)
        {
            let exts = span.extensions();
            let formatted_fields = if let Some(fields) = exts.get::<FormattedFields<N>>() {
                if fields.is_empty() {
                    continue;
                }
                fields.as_str()
            } else {
                continue;
            };
            // `FormattedFields` 可能包含多个 "key=value" 对，用空格分隔
            // 我们需要正确地解析它们
            for part in formatted_fields.split(' ') {
                if part.is_empty() {
                    continue;
                }
                let Some((key, value)) = part.split_once("=") else {
                    continue;
                };
                if !fields.contains_key(key) {
                    fields.insert(key.to_string(), value.to_string());
                }
            }
        }
        if let Some(value) = fields.remove("trace_id") {
            trace_id.push_str("@");
            trace_id.push_str(&value[..value.find('-').unwrap_or(8)]);
        }
        if !fields.is_empty() {
            let mut first = true;
            for (key, value) in fields {
                if first {
                    fields_str.push_str(" | {");
                }
                write!(&mut fields_str, " {}:{}", key, value).ok();
                first = false;
            }
            if !fields_str.is_empty() {
                fields_str.push_str(" }");
                fields_str = fields_str.italic().bright_black().to_string();
            }
        }

        write!(
            writer,
            "[{} {}] {} {}{}",
            ColoredText::bright_black(now.format("%X%.3f")),
            LogLevelFormat::format_level_colored(meta.level(), true),
            ColoredText::bright_black(format!(
                "{}{}:",
                meta.file()
                    .and_then(|file| meta.line().map(|line| format!("{}:{}", file, line)))
                    .unwrap_or_else(|| meta.target().to_string()),
                trace_id
            )),
            message,
            fields_str
        )?;

        writeln!(writer)
    }
}

struct Visitor {
    message: String,
    fields: HashMap<String, String>,
}

impl Visitor {
    fn new() -> Self {
        Self {
            message: String::new(),
            fields: HashMap::new(),
        }
    }
}

impl Visit for Visitor {
    fn record_debug(&mut self, field: &Field, value: &dyn Debug) {
        if field.name() == "message" {
            write!(self.message, "{:?}", value).ok();
        } else {
            self.fields
                .insert(field.name().to_string(), format!("{:?}", value));
        }
    }
    fn record_str(&mut self, field: &Field, value: &str) {
        if field.name() == "message" {
            write!(self.message, "{}", value).ok();
        } else {
            self.fields
                .insert(field.name().to_string(), value.to_string());
        }
    }
}

impl Display for Visitor {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.write_str(&self.message)
    }
}

struct LogLevelFormat {
    label: &'static str,
    color: Option<Color>,
}

impl LogLevelFormat {
    fn format_level_colored(level: &Level, use_colors: bool) -> Self {
        let (label, color) = match level {
            &Level::ERROR => ("ERR", Color::BrightRed),
            &Level::WARN => ("WRN", Color::BrightYellow),
            &Level::INFO => ("INF", Color::BrightBlue),
            &Level::DEBUG => ("DBG", Color::BrightMagenta),
            &Level::TRACE => ("TRC", Color::BrightWhite),
        };
        Self {
            label,
            color: if use_colors { Some(color) } else { None },
        }
    }
    fn format_level_char(level: &Level) -> Self {
        let label = match level {
            &Level::ERROR => "[E]",
            &Level::WARN => "[W]",
            &Level::INFO => "[I]",
            &Level::DEBUG => "[D]",
            &Level::TRACE => "[T]",
        };
        Self { label, color: None }
    }
}

impl Display for LogLevelFormat {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        if let Some(color) = self.color {
            write!(f, "\x1B[{}m", color.to_fg_str())?;
            write!(f, "{}", self.label)?;
            write!(f, "\x1B[0m")?;
        } else {
            write!(f, "{}", self.label)?;
        }
        Ok(())
    }
}

struct ColoredText<T> {
    content: T,
    color: Color,
}
impl<T> ColoredText<T> {
    fn bright_black(value: T) -> ColoredText<T> {
        ColoredText {
            content: value,
            color: Color::BrightBlack,
        }
    }
}
impl<T: Display> Display for ColoredText<T> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "\x1B[{}m", self.color.to_fg_str())?;
        write!(f, "{}", self.content)?;
        write!(f, "\x1B[0m")?;
        Ok(())
    }
}

/// Console 日志写入器
///
/// 实现 LogWriter trait，用于将外部日志直接输出到控制台
pub struct ConsoleWriter {}

impl ConsoleWriter {
    /// 创建新的控制台写入器
    pub fn new() -> Self {
        Self {}
    }

    /// 格式化并输出日志条目
    fn format_and_print(&self, entry: &StructuredLogEntry) -> Result<()> {
        let now = chrono::DateTime::<chrono::Utc>::from_timestamp_millis(entry.timestamp as i64)
            .unwrap_or_else(|| chrono::Utc::now());
        let now = now.with_timezone(&chrono::Local);

        // 构建 trace_id 显示
        let trace_id = if let Some(id) = entry.trace_id {
            format!("@{}", &id.to_string()[..8])
        } else {
            String::new()
        };

        // 构建字段显示
        let mut fields_str = String::new();
        if !entry.fields.is_empty() {
            let mut first = true;
            for (key, value) in &entry.fields {
                if first {
                    fields_str.push_str(" | {");
                }
                write!(&mut fields_str, " {}:{}", key, value)
                    .map_err(|e| anyhow::anyhow!("格式化字段失败: {}", e))?;
                first = false;
            }
            if !fields_str.is_empty() {
                fields_str.push_str(" }");
                fields_str = fields_str.italic().bright_black().to_string();
            }
        }

        // 构建文件信息显示
        let file_info = if let Some(line) = entry.line {
            if line > 0 {
                format!(
                    "{}:{}{}:",
                    entry.file.as_deref().unwrap_or("unknown"),
                    line,
                    trace_id
                )
            } else {
                format!(
                    "{}{}:",
                    entry.file.as_deref().unwrap_or(&entry.target),
                    trace_id
                )
            }
        } else if let Some(file) = &entry.file {
            if !file.is_empty() {
                format!("{}{}:", file, trace_id)
            } else {
                format!("{}{}:", entry.target, trace_id)
            }
        } else {
            format!("{}{}:", entry.target, trace_id)
        };

        // 格式化级别
        let level_str =
            LogLevelFormat::format_level_colored_from_string(&entry.level, true).to_string();

        // 输出格式化的日志

        println!(
            "[{} {}] {} {}{}",
            ColoredText::bright_black(now.format("%X%.3f")),
            level_str,
            ColoredText::bright_black(file_info),
            entry.message,
            fields_str
        );

        Ok(())
    }
}

impl LogWriter for ConsoleWriter {
    fn write_log(&self, entry: StructuredLogEntry) -> Result<()> {
        self.format_and_print(&entry)
    }
}

impl Default for ConsoleWriter {
    fn default() -> Self {
        Self::new()
    }
}

impl LogLevelFormat {
    /// 从字符串格式化日志级别（带颜色）
    fn format_level_colored_from_string(level: &LogLevel, use_colors: bool) -> Self {
        let (label, color) = match level {
            LogLevel::Error => ("ERR", Color::BrightRed),
            LogLevel::Warn => ("WRN", Color::BrightYellow),
            LogLevel::Info => ("INF", Color::BrightBlue),
            LogLevel::Debug => ("DBG", Color::BrightMagenta),
            LogLevel::Trace => ("TRC", Color::BrightWhite),
        };
        Self {
            label,
            color: if use_colors { Some(color) } else { None },
        }
    }

    /// 从字符串格式化日志级别（字符）
    fn format_level_char_from_string(level: &LogLevel) -> Self {
        let label = match level {
            LogLevel::Error => "[E]",
            LogLevel::Warn => "[W]",
            LogLevel::Info => "[I]",
            LogLevel::Debug => "[D]",
            LogLevel::Trace => "[T]",
            // _ => "[?]",
        };
        Self { label, color: None }
    }
}
