//! # 日志管理模块
//!
//! 该模块实现了 EchoWave 核心的日志分流系统，支持三个方向的日志输出：
//!
//! ## 核心功能
//!
//! ### 1. Console 方向 (已实现)
//! - 自动检测终端环境，非终端环境不输出
//! - 支持通过环境变量控制日志级别
//! - 全量日志输出，包含 DEBUG 和 TRACE 级别
//! - 彩色输出，包含文件信息和线程ID
//!
//! ### 2. File 方向 (已实现)
//! - INFO 及以上级别直接写入文件 (JSON 格式)
//! - DEBUG 和 TRACE 级别存储在 LRU 缓存中 (容量: 1000 条)
//! - 当出现 ERROR 时，从缓存中根据 trace_id 获取完整调用链写入文件
//! - 支持错误自动上报接口预留
//! - 统一平面目录存储: `{userdata_dir}/logs/{source}-YYYY-MM-DD.jsonl`
//! - 支持的 source: core, daemon, desktop, desktop-adapter, runtime, network, scheduler
//! - 使用 LogSource 枚举强制约束，防止滥用
//! - 自动清理超过保留期的日志文件 (默认保留7天)
//! - 支持日志文件压缩 (zstd 格式) 和轮转管理
//!
//! ### 3. UI 方向 (已实现)
//! - 通过异步消息队列向 UI 发送日志 (tokio::mpsc::channel)
//! - 仅显示 INFO 级别和特定 target 的日志 (target 以 "echowave" 开头)
//! - 支持连接状态检测和日志积压管理 (缓存上限: 50 条)
//! - 当 UI 连接恢复时自动发送积压的日志
//!
//! ## 全链路追踪系统
//!
//! 基于 trace_id 的分布式追踪机制：
//! - 每个用户操作生成唯一的 UUID v4 作为 trace_id
//! - trace_id 在模块间通信时自动传递
//! - 所有日志都包含 trace_id 字段，便于问题排查
//! - 支持基于 trace_id 的错误回溯和调试信息关联
//!
//! ## 架构设计
//!
//! - **TraceIdLayer**: 负责捕获和传递 trace_id
//! - **FileLayer**: 处理文件日志输出和 LRU 缓存管理
//! - **UILayer**: 处理 UI 日志输出和消息队列管理
//! - **MessageVisitor**: 提取和格式化日志消息内容
//! - **StructuredLogEntry**: 统一的日志结构化格式
//!
//! ## 使用示例
//!
//! ```rust
//! use crate::logging::registry_logs;
//! use std::sync::Arc;
//!
//! // 初始化日志系统
//! let config = Arc::new(ContextConfig::default());
//! registry_logs(config)?;
//!
//! // 使用 tracing 宏记录日志
//! tracing::info!(trace_id = %uuid::Uuid::new_v4(), "System started");
//! tracing::error!(trace_id = %uuid::Uuid::new_v4(), "Critical error occurred");
//! ```
//!
//! ## 环境变量支持
//!
//! - `RUST_LOG`: 控制日志级别和过滤器
//! - 示例: `RUST_LOG=debug cargo run`
//!
//! ## 错误回溯机制
//!
//! 当发生 ERROR 级别日志时，系统会：
//! 1. 立即写入错误日志到文件
//! 2. 从 LRU 缓存中查找相同 trace_id 的 DEBUG/TRACE 日志
//! 3. 将完整的调用链写入文件，便于问题诊断
//! 4. 触发错误自动上报流程 (预留接口)
//!
//! ## 日志清理机制
//!
//! 为防止日志文件占用过多磁盘空间，系统实现了自动清理机制：
//!
//! ### 清理时机
//! - 日志系统初始化时执行一次清理
//! - 日志文件按日期轮转时自动触发清理
//!
//! ### 清理策略
//! - 默认保留期：7天 (可通过 `LOG_RETENTION_DAYS` 常量调整)
//! - 支持清理原始日志文件 (.jsonl) 和压缩文件 (.zst)
//! - 支持带序号的压缩文件 (.zst.1, .zst.2 等)
//! - 支持时间戳轮转文件 (如 core-2024-01-15-143022.jsonl)
//!
//! ### 清理过程
//! - 扫描日志目录中的所有文件
//! - 解析文件名中的日期信息
//! - 删除超过保留期的文件
//! - 记录清理统计信息 (删除文件数量、释放空间大小)

use crate::config::ContextConfig;
use crate::context::event::CoreEvent;
use crate::logging::file::FileLayer;
use crate::logging::trace_id::TraceIdLayer;
use crate::logging::ui::UILayer;
use anyhow::Result;
use shared::lru_cache::LruCache;
use std::cell::RefCell;
use std::io;
use std::path::PathBuf;
use std::sync::{Arc, Once};
use tokio::sync::mpsc;
use tracing_subscriber::layer::Layer;
use tracing_subscriber::{EnvFilter, fmt, prelude::*};
use uuid::Uuid;

mod console;
pub mod file;
pub mod forward;
pub mod level;
pub mod source;
mod trace_id;
mod ui;
mod visitor;

pub use console::ConsoleWriter;
use forward::{LogWriter, register_log_writer};
pub use level::LogLevel;
pub use trace_id::TraceId;
// --- Trace ID Handling ---

// 1. 定义线程局部变量 (TLS) 来存储 trace_id
thread_local! {
    static CURRENT_TRACE_ID: RefCell<Option<Uuid>> = RefCell::new(None);
}

// 2. 提供公共访问函数
/// 从当前 tracing span 的上下文中获取 trace_id
pub fn current_trace_id() -> TraceId {
    CURRENT_TRACE_ID.with(|id| {
        id.borrow().unwrap_or_else(|| {
            // #[cfg(debug_assertions)]
            // tracing::warn!(
            //     trace_id = %crate::context::get_global_trace_id(),
            //     "Warning: trace_id not found in current context, using fallback"
            // );
            crate::context::get_global_trace_id()
        })
    })
}

/// 为特定模块初始化日志
///
/// 初始化日志系统
///
/// 实现日志分流，总共三个分流方向：console，file，ui
/// - console 方向：可能需要检测是否为 console 模式，如果不是在终端则不输出，全量输出，根据环境变量控制级别
/// - file 方向，级别为 INFO 和以上的级别，DEBUG 和 TRACE 使用 LRU 存储在内存。
///     实现错误回溯，向文件写入的文件默认最小级别 INFO，当出现 ERROR 时需要从 LRU 缓存中根据 trace_id 获取所有的 trace 和 debug 一并写入文件。
///     预留接口，当出现 ERROR 时自动上报当前日志文件到服务器
/// - ui 方向，UI 通过异步消息队列订阅，只显示 INFO 和特定的 target，需要判断消息队列是否连接，如果为否则积压一定数量的日志，当连接后发送积压的日志
pub fn registry_logs(
    config: Arc<ContextConfig>,
    desktop_tx: mpsc::Sender<CoreEvent>,
    subscription_manager: Arc<crate::context::subscription::SubscriptionManager>,
) -> Result<()> {
    static INITIALIZED: Once = Once::new();

    let mut init_result = Ok(());

    INITIALIZED.call_once(|| {
        let result = init_logging_system(config, desktop_tx, subscription_manager);
        match result {
            Ok(_) => {
                tracing::debug!(
                    trace_id = %crate::context::get_global_trace_id(),"日志系统初始化成功");
            }
            Err(e) => {
                init_result = Err(e);
                eprintln!("日志系统初始化失败: {}", init_result.as_ref().unwrap_err());
            }
        }
    });

    init_result
}

/// 实际的日志系统初始化函数
fn init_logging_system(
    config: Arc<ContextConfig>,
    desktop_tx: mpsc::Sender<CoreEvent>,
    subscription_manager: Arc<crate::context::subscription::SubscriptionManager>,
) -> Result<()> {
    // 创建日志目录
    let log_dir = config.userdata_dir.join("logs");
    std::fs::create_dir_all(&log_dir)?;

    // 环境变量控制日志级别
    let env_filter = EnvFilter::try_from_default_env().unwrap_or_else(|_| EnvFilter::new("info"));

    // 启动后台任务清理旧日志文件
    let log_dir_for_cleanup = log_dir.clone();
    tokio::spawn(async move {
        let span = tracing::info_span!("开始清理旧日志文件", trace_id = %crate::context::get_global_trace_id(),);
        let _enter = span.enter();
        if let Err(e) =
            FileLayer::cleanup_old_logs_in_dir(&log_dir_for_cleanup, FileLayer::LOG_RETENTION_DAYS)
                .await
        {
            tracing::warn!("初始化时清理旧日志文件失败: {}", e);
        }
    });

    // 创建三路日志分流系统
    let mut layers = Vec::new();

    // 检测是否为终端环境并添加控制台输出
    if io::IsTerminal::is_terminal(&io::stdout()) && !config.app_mode.is_prod() {
        let console_layer = fmt::layer()
            .with_ansi(false)
            .event_format(console::Formatter::new())
            .with_writer(io::stdout);
        layers.push(console_layer.boxed());
        // layers.push(
        //     fmt::layer()
        //         .with_target(false)
        //         .with_thread_ids(false)
        //         .with_file(true)
        //         .with_line_number(true)
        //         .with_ansi(true)
        //         .with_writer(io::stdout)
        //         .boxed(),
        // );

        // 注册 Console 日志写入器
        create_console_writer()?;
    }
    layers.push(create_file_layer(log_dir.clone())?.boxed());
    layers.push(create_ui_layer(desktop_tx, subscription_manager)?.boxed());

    // 初始化 tracing subscriber
    tracing_subscriber::Registry::default()
        .with(env_filter)
        .with(TraceIdLayer::default())
        .with(layers)
        .init();

    Ok(())
}

/// 创建文件日志层
fn create_file_layer(log_dir: PathBuf) -> Result<FileLayer> {
    // 为 DEBUG/TRACE 创建 LRU 缓存
    let debug_cache = Arc::new(LruCache::new(1000)); // 缓存最近 1000 条 DEBUG/TRACE 日志

    let file_layer = FileLayer::new(log_dir, debug_cache);

    // 注册文件日志写入器
    register_log_writer(Arc::new(file_layer.clone()));

    Ok(file_layer)
}

/// 创建 UI 日志层
fn create_ui_layer(
    desktop_tx: mpsc::Sender<CoreEvent>,
    subscription_manager: Arc<crate::context::subscription::SubscriptionManager>,
) -> Result<UILayer> {
    let ui_layer = UILayer::new(desktop_tx, subscription_manager);

    // 注册 UI 日志写入器
    register_log_writer(Arc::new(ui_layer.clone()));

    Ok(ui_layer)
}

/// 创建 Console 日志写入器
fn create_console_writer() -> Result<()> {
    // 创建控制台写入器，在终端环境中使用颜色
    let console_writer = ConsoleWriter::new();

    // 注册 Console 日志写入器
    register_log_writer(Arc::new(console_writer));

    Ok(())
}

pub use protocol::events::StructuredLogEntry;
