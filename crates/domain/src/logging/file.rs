use crate::logging::TraceId;
use crate::logging::level::LogLevel;
use crate::logging::source::LogSource;
use crate::logging::{StructuredLogEntry, forward::LogWriter, visitor::MessageVisitor};
use anyhow::Result;
use chrono::{DateTime, Local, TimeZone};
use dashmap::DashMap;
use shared::lru_cache::LruCache;
use std::cell::Cell;
use std::collections::HashMap;
use std::io::Write;
use std::path::PathBuf;
use std::sync::atomic::AtomicI64;
use std::sync::{
    Arc,
    atomic::{AtomicBool, AtomicU64, Ordering},
};
use std::time::{Duration, Instant};
use tokio::sync::{Mutex, mpsc};
use tokio::task::JoinHandle;
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, warn};
use tracing_subscriber::layer::Context;
use tracing_subscriber::{Layer, registry::LookupSpan};

thread_local! {
    /// 这个标志位在当前线程有效。如果为 true，表示我们正在执行写入逻辑。
    static IS_LOGGING_TO_FILE: Cell<bool> = Cell::new(false);
}
// RAII Guard
struct FileLoggingGuard;
impl FileLoggingGuard {
    fn new() -> Self {
        IS_LOGGING_TO_FILE.with(|is_logging| is_logging.set(true));
        Self
    }
}
impl Drop for FileLoggingGuard {
    fn drop(&mut self) {
        IS_LOGGING_TO_FILE.with(|is_logging| is_logging.set(false));
    }
}

/// 性能统计信息
#[derive(Debug, Clone)]
pub struct PerformanceStats {
    pub source: LogSource,
    pub write_count: u64,
    pub rotation_count: u64,
    pub total_rotation_time_ms: u64,
    pub avg_rotation_time_ms: u64,
    pub current_file_size: u64,
    pub last_rotation_time: i64,
}

/// 每个 source 的文件状态
///
/// 注意：虽然外层用了 DashMap，但单个 source 内部仍需要同步原语：
/// - DashMap: 解决不同 source 间的并发访问
/// - 内部锁: 解决同一 source 内的文件写入、轮转等操作的原子性
pub struct LogFileMetadata {
    writer: Mutex<Option<std::fs::File>>,
    current_timestamp: AtomicI64,
    size_bytes: AtomicU64,   // 原子操作避免文件大小统计的竞态条件
    is_rotating: AtomicBool, // 防止递归或重复轮转的标记
    write_count: AtomicU64,  // 写入计数，用于优化轮转检查频率

    retention_days: u32,

    // 性能监控指标
    rotation_count: AtomicU64,         // 轮转次数统计
    total_rotation_time_ms: AtomicU64, // 总轮转时间（毫秒）
    last_rotation_time: AtomicI64,     // 上次轮转时间（Timestamp）
}

impl LogFileMetadata {
    fn new() -> Self {
        Self {
            writer: Mutex::new(None),
            current_timestamp: AtomicI64::new(FileLayer::get_current_timestamp()),
            size_bytes: AtomicU64::new(0),
            is_rotating: AtomicBool::new(false),
            write_count: AtomicU64::new(0),

            retention_days: FileLayer::LOG_RETENTION_DAYS,

            // 初始化性能监控指标
            rotation_count: AtomicU64::new(0),
            total_rotation_time_ms: AtomicU64::new(0),
            last_rotation_time: AtomicI64::new(0),
        }
    }
}

/// 文件日志层实现 - 统一平面目录存储
#[derive(Clone)]
pub struct FileLayer {
    logs_dir: PathBuf,
    metadata_map: Arc<DashMap<LogSource, LogFileMetadata>>,
    debug_cache: Arc<LruCache<TraceId, Vec<StructuredLogEntry>>>,
    background_manager: Arc<BackgroundManager>,
}

/// 轮转间隔
#[derive(Debug, Clone, Copy, PartialEq, Eq, Default)]
pub enum RotationInterval {
    Hourly,
    #[default]
    Daily,
    Weekly,
    Custom {
        hours: u32,
    },
}

impl RotationInterval {
    fn duration(&self) -> Duration {
        match self {
            Self::Hourly => Duration::from_secs(3600),
            Self::Daily => Duration::from_secs(86400),
            Self::Weekly => Duration::from_secs(604800),
            Self::Custom { hours } => Duration::from_secs((hours * 3600) as u64),
        }
    }
}
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum RotationStrategy {
    /// 仅基于时间轮转
    Time(RotationInterval),
    /// 仅基于文件大小轮转
    Size { limit_bytes: u64 },
    /// 复合策略：时间或大小任一条件满足即轮转
    Compound {
        interval: RotationInterval,
        limit_bytes: u64,
    },
    /// 从不轮转
    Never,
}

/// 轮转请求类型
#[derive(Debug, Clone)]
pub enum RotationRequest {
    /// 立即轮转请求
    Immediate {
        source: LogSource,
        reason: RotationReason,
    },
    /// 定时检查所有source的轮转条件
    PeriodicCheck,
}

/// 轮转原因
#[derive(Debug, Clone)]
pub enum RotationReason {
    /// 文件大小超过限制
    SizeExceeded(u64),
    /// 日期发生变化
    DateChanged(String),
    /// 手动触发轮转
    Manual,
}

/// 后台管理器
///
/// ## 轮转
/// 负责在独立的异步任务中处理所有的日志轮转操作，避免阻塞写入线程。
/// 支持三种轮转触发机制：
/// 1. 阈值触发：文件大小超过限制时立即轮转
/// 2. 定时检查：每30秒检查所有source的轮转条件
/// 3. 事件驱动：日期变更时立即轮转
///
/// ## 日志通道
pub struct BackgroundManager {
    /// 轮转请求发送端
    request_sender: mpsc::Sender<RotationRequest>,
    /// 日志发送端
    log_sender: mpsc::Sender<StructuredLogEntry>,
    /// 关闭信号
    shutdown_signal: CancellationToken,
    /// 后台任务句柄
    rotation_handle: Option<JoinHandle<()>>,
    /// 日志写入句柄
    write_handle: Option<JoinHandle<()>>,
}

impl BackgroundManager {
    /// 创建新的后台轮转管理器
    pub fn new(
        metadata_map: &Arc<DashMap<LogSource, LogFileMetadata>>,
        logs_dir: &PathBuf,
    ) -> Self {
        let (request_sender, request_receiver) = mpsc::channel(64);
        let (log_sender, log_receiver) = mpsc::channel(512);
        let shutdown_signal = CancellationToken::new();

        let rotation_handle = Some(tokio::spawn(Self::rotation_loop(
            request_receiver,
            metadata_map.clone(),
            logs_dir.clone(),
            RotationStrategy::Compound {
                interval: RotationInterval::Daily,
                limit_bytes: FileLayer::MAX_LOG_FILE_SIZE,
            },
            shutdown_signal.clone(),
        )));

        let write_handle = Some(tokio::spawn(Self::write_loop(
            log_receiver,
            metadata_map.clone(),
            logs_dir.clone(),
            request_sender.clone(),
            shutdown_signal.clone(),
        )));

        Self {
            request_sender,
            log_sender,
            shutdown_signal,
            rotation_handle,
            write_handle,
        }
    }

    /// 发送日志
    pub fn send_log(&self, entry: StructuredLogEntry) {
        if let Err(err) = self.log_sender.try_send(entry) {
            error!(skip_file_layer = true, "无法发送日志，因为: {}", err);
        }
    }

    /// 发送轮转请求（非阻塞）
    pub fn request_rotation(&self, request: RotationRequest) {
        if let Err(err) = self.request_sender.try_send(request) {
            warn!("无法发送轮转请求，因为: {}", err);
        }
    }

    /// 关闭轮转管理器
    pub async fn shutdown(mut self) -> Result<()> {
        // 发送关闭信号
        self.shutdown_signal.cancel();

        if let Some(handle) = self.write_handle.take() {
            if let Err(err) = handle.await {
                eprintln!("轮转任务异常结束: {}", err)
            }
        }

        // 等待后台任务完成
        if let Some(handle) = self.rotation_handle.take() {
            if let Err(err) = handle.await {
                eprintln!("轮转任务异常结束: {}", err)
            }
        }

        Ok(())
    }

    /// 后台日志写入
    async fn write_loop(
        mut log_receiver: mpsc::Receiver<StructuredLogEntry>,
        metadata_map: Arc<DashMap<LogSource, LogFileMetadata>>,
        logs_dir: PathBuf,
        rotation_sender: mpsc::Sender<RotationRequest>,
        shutdown_signal: CancellationToken,
    ) {
        loop {
            let _guard = FileLoggingGuard::new();
            tokio::select! {
                entry = log_receiver.recv() => {
                    let Some(entry) = entry else {
                        tracing::warn!(
                            skip_file_layer = true,
                            "日志写入通道已关闭"
                        );
                        break;
                    };
                    if let Err(err) = FileLayer::handle_log_entry(&logs_dir, &entry, &metadata_map, &rotation_sender).await {
                        tracing::error!(
                            skip_file_layer = true,
                            source = ?entry.source,
                            error = %err,
                            "日志写入失败"
                        );
                    }
                }
                _ = shutdown_signal.cancelled() => {
                    tracing::info!("后台日志写入管理器收到关闭信号");
                    break;
                }
            }
        }
    }

    /// 后台轮转循环
    async fn rotation_loop(
        mut request_receiver: mpsc::Receiver<RotationRequest>,
        metadata_map: Arc<DashMap<LogSource, LogFileMetadata>>,
        logs_dir: PathBuf,
        strategy: RotationStrategy,
        shutdown_signal: CancellationToken,
    ) {
        let mut periodic_timer = tokio::time::interval(Duration::from_secs(30));

        // 跳过第一次触发，因为我们刚启动
        periodic_timer.tick().await;

        loop {
            tokio::select! {
                // 分支 1: 等待定时器触发
                slept_for = Self::wait_for_next_rotation(strategy) => {
                    if let Some(duration) = slept_for {
                         debug!(
                            "🔔 [定时器触发] 等待了 {}s 后，执行轮转检查...",
                            duration.as_secs()
                        );
                        Self::perform_rotation_check(strategy, &metadata_map, &logs_dir).await;
                    } else {
                        // 通常意味着发生了计算或时钟错误
                        warn!("定时器等待出错，将在一分钟后重试...");
                        tokio::time::sleep(Duration::from_secs(60)).await;
                    }
                }
                // 分支 2: 处理轮转请求
                request = request_receiver.recv() => {
                    match request {
                        Some(RotationRequest::Immediate { source, reason: _reason }) => {
                            if let Err(e) = Self::handle_immediate_rotation(
                                &metadata_map,
                                &logs_dir,
                                source,
                            ).await {
                                tracing::error!(
                                    source = ?source,
                                    error = %e,
                                    "立即轮转失败"
                                );
                            }
                        }
                        Some(RotationRequest::PeriodicCheck) => {
                            Self::perform_rotation_check(strategy, &metadata_map, &logs_dir).await;
                        }
                        None => {
                            tracing::warn!("轮转请求通道已关闭");
                            break;
                        }
                    }
                }

                // 定时检查
                _ = shutdown_signal.cancelled() => {
                    tracing::info!("后台轮转管理器收到关闭信号");
                    break
                }
            }
        }

        tracing::info!("后台轮转管理器已退出");
    }

    /// 根据当前时间戳和时间间隔，计算下一个触发点的时间戳。
    fn calculate_next_trigger_timestamp(
        current_ts_secs: i64,
        interval: RotationInterval,
    ) -> Option<u64> {
        use chrono::{Datelike, Timelike};
        // 1. 将 u64 时间戳转换为 chrono::DateTime 以便进行日历计算
        let now_dt = DateTime::from_timestamp(current_ts_secs, 0).unwrap_or_default();

        // 2. 使用 chrono 的强大功能计算下一个触发时间点 (逻辑和之前一样)
        let next_dt_opt = match interval {
            RotationInterval::Hourly => now_dt
                .with_minute(0)
                .and_then(|it| it.with_second(0))
                .and_then(|it| it.with_nanosecond(0))
                .map(|start_of_hour| start_of_hour + chrono::Duration::hours(1)),
            RotationInterval::Daily => {
                now_dt
                    .date_naive()
                    .succ_opt() // 获取明天的日期
                    .and_then(|tomorrow| tomorrow.and_hms_opt(0, 0, 0)) // 设置为午夜
                    .and_then(
                        |naive_dt| match chrono::Utc.from_local_datetime(&naive_dt) {
                            chrono::LocalResult::Single(dt) => Some(dt), // 明确处理 LocalResult
                            _ => None,
                        },
                    )
            }
            RotationInterval::Weekly => {
                let days_to_next_monday = (7 - now_dt.weekday().num_days_from_monday()) % 7 + 1;
                now_dt
                    .date_naive()
                    .checked_add_days(chrono::Days::new(days_to_next_monday as u64))
                    .and_then(|next_monday_date| next_monday_date.and_hms_opt(0, 0, 0))
                    .and_then(
                        |naive_dt| match chrono::Utc.from_local_datetime(&naive_dt) {
                            chrono::LocalResult::Single(dt) => Some(dt),
                            _ => None,
                        },
                    )
            }
            RotationInterval::Custom { hours } => {
                // 防止除以零，自定义小时数必须大于0
                if hours == 0 {
                    return None;
                }

                // 1. 找到当天 UTC 午夜的时间点
                let start_of_day = now_dt.date_naive().and_hms_opt(0, 0, 0)?;
                let start_of_day_dt = match chrono::Utc.from_local_datetime(&start_of_day) {
                    chrono::LocalResult::Single(dt) => dt,
                    _ => return None,
                };

                // 2. 计算从午夜到“现在”经过了多少秒
                let secs_since_midnight = now_dt.timestamp() - start_of_day_dt.timestamp();

                // 3. 计算自定义周期的秒数
                let interval_secs = i64::from(hours) * 3600;

                // 4. 计算下一个触发点相对于午夜的秒数
                // (secs_since_midnight / interval_secs) -> 已经过去了多少个完整的周期
                // (...) + 1 -> 下一个周期的序号
                let next_trigger_offset_secs =
                    (secs_since_midnight / interval_secs + 1) * interval_secs;

                // 5. 最终触发时间 = 今天午夜 + 计算出的偏移量
                start_of_day_dt
                    .checked_add_signed(chrono::Duration::seconds(next_trigger_offset_secs))
            }
        };

        // 3. 将计算出的 DateTime 转换回 u64 时间戳并返回
        next_dt_opt.map(|dt| dt.timestamp() as u64)
    }

    /// 计算并等待直到下一个固定的轮转时间点。
    /// 如果策略与时间无关，它将永远等待，从而在 select! 中有效地“禁用”此分支。
    /// 返回实际休眠的时间，用于日志记录。
    async fn wait_for_next_rotation(strategy: RotationStrategy) -> Option<Duration> {
        let interval = match strategy {
            RotationStrategy::Time(interval) | RotationStrategy::Compound { interval, .. } => {
                interval
            }
            _ => {
                // 对于非时间策略，我们永远等待，因为它没有固定的触发点。
                // select! 会忽略这个分支，直到其他事件发生。
                futures::future::pending::<()>().await;
                return None;
            }
        };

        let current_ts = match std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH) {
            Ok(d) => d.as_secs(),
            Err(_) => {
                // 如果时钟错误，等待一小段时间后让外部循环决定如何处理
                tokio::time::sleep(Duration::from_secs(5)).await;
                return None;
            }
        };

        let Some(next_trigger_ts) =
            Self::calculate_next_trigger_timestamp(current_ts as i64, interval)
        else {
            // 如果计算失败，也等待一小段时间
            tokio::time::sleep(Duration::from_secs(10)).await;
            return None;
        };

        let sleep_secs = next_trigger_ts.saturating_sub(current_ts);
        let sleep_duration = Duration::from_secs(sleep_secs);

        if !sleep_duration.is_zero() {
            tokio::time::sleep(sleep_duration).await;
        }

        Some(sleep_duration)
    }

    /// 处理立即轮转请求
    async fn handle_immediate_rotation(
        metadata_map: &Arc<DashMap<LogSource, LogFileMetadata>>,
        logs_dir: &PathBuf,
        source: LogSource,
    ) -> Result<()> {
        if let Some(metadata) = metadata_map.get(&source) {
            // 检查是否已经在轮转中
            if metadata
                .is_rotating
                .compare_exchange(false, true, Ordering::SeqCst, Ordering::Relaxed)
                .is_err()
            {
                return Ok(()); // 已在轮转中，直接返回
            }

            let _guard = RotationGuard::new(&metadata.is_rotating);

            // 执行轮转操作
            FileLayer::execute_rotation_internal(&metadata, logs_dir, source).await?;
        }

        Ok(())
    }

    /// 处理定时检查
    async fn perform_rotation_check(
        strategy: RotationStrategy,
        metadata_map: &Arc<DashMap<LogSource, LogFileMetadata>>,
        logs_dir: &PathBuf,
    ) {
        for entry in metadata_map.iter() {
            let source = *entry.key();
            let metadata = entry.value();

            if !Self::should_rotate(strategy, metadata) {
                continue;
            }
            if let Err(e) = Self::handle_immediate_rotation(metadata_map, logs_dir, source).await {
                tracing::error!(
                    source = ?source,
                    error = %e,
                    "轮转失败"
                );
            }
        }
    }

    fn should_rotate(strategy: RotationStrategy, metadata: &LogFileMetadata) -> bool {
        match strategy {
            RotationStrategy::Time(interval) => {
                let current_timestamp = FileLayer::get_current_timestamp();
                let stored_timestamp = metadata.current_timestamp.load(Ordering::Relaxed);
                let interval = interval.duration().as_secs() as i64;
                current_timestamp > stored_timestamp + interval
            }
            RotationStrategy::Size { limit_bytes } => {
                let current_size = metadata.size_bytes.load(Ordering::Relaxed);
                current_size >= limit_bytes
            }
            RotationStrategy::Compound {
                interval,
                limit_bytes,
            } => {
                let current_timestamp = FileLayer::get_current_timestamp();
                let stored_timestamp = metadata.current_timestamp.load(Ordering::Relaxed);
                let current_size = metadata.size_bytes.load(Ordering::Relaxed);
                let interval = interval.duration().as_secs() as i64;
                current_timestamp > stored_timestamp + interval || current_size >= limit_bytes
            }
            RotationStrategy::Never => false,
        }
    }
}

/// RAII 守卫，确保轮转标记在离开作用域时被清除
struct RotationGuard<'a> {
    flag: &'a AtomicBool,
}

impl<'a> RotationGuard<'a> {
    fn new(flag: &'a AtomicBool) -> Self {
        Self { flag }
    }
}

impl<'a> Drop for RotationGuard<'a> {
    fn drop(&mut self) {
        self.flag.store(false, Ordering::Relaxed);
    }
}

impl FileLayer {
    /// 日志文件最大大小（10MB）
    pub const MAX_LOG_FILE_SIZE: u64 = 10 * 1024 * 1024;

    /// 日志保留天数（7天）
    pub const LOG_RETENTION_DAYS: u32 = 7;

    pub fn new(
        logs_dir: PathBuf,
        debug_cache: Arc<LruCache<TraceId, Vec<StructuredLogEntry>>>,
    ) -> Self {
        let source_states = Arc::new(DashMap::new());
        let rotation_manager = Arc::new(BackgroundManager::new(&source_states, &logs_dir));

        Self {
            logs_dir,
            metadata_map: source_states,
            debug_cache,
            background_manager: rotation_manager,
        }
    }

    /// 获取当前日期字符串 (YYYY-MM-DD 格式)
    fn get_current_date_string() -> String {
        Local::now().format("%Y-%m-%d").to_string()
    }

    /// 获取当前时间戳（秒）
    fn get_current_timestamp() -> i64 {
        Local::now().timestamp()
    }

    /// 获取当前日志文件路径（统一平面目录）
    fn get_current_log_file_path(logs_dir: &PathBuf, source: LogSource, date: &str) -> PathBuf {
        logs_dir.join(format!("{}-{}.jsonl", source, date))
    }

    /// 从文件名中解析日期
    ///
    /// 支持的文件名格式：
    /// - {source}-YYYY-MM-DD.jsonl
    /// - {source}-YYYY-MM-DD.zst
    /// - {source}-YYYY-MM-DD.zst.{counter}
    /// - {source}-YYYY-MM-DD-HHMMSS.jsonl
    pub fn parse_date_from_filename(filename: &str) -> Option<chrono::NaiveDate> {
        // 移除可能的扩展名和序号
        let name_without_ext = filename.replace(".jsonl", "").replace(".zst", "");

        // 移除可能的序号后缀 (如 .1, .2, .3...)
        let name_clean = if let Some(last_dot_pos) = name_without_ext.rfind('.') {
            let suffix = &name_without_ext[last_dot_pos + 1..];
            if suffix.chars().all(|c| c.is_ascii_digit()) {
                &name_without_ext[..last_dot_pos]
            } else {
                &name_without_ext
            }
        } else {
            &name_without_ext
        };

        // 寻找日期模式 YYYY-MM-DD
        // 可能的格式：
        // - source-YYYY-MM-DD (4 个部分)
        // - source-YYYY-MM-DD-HHMMSS (5 个部分)
        // - complex-source-YYYY-MM-DD (>4 个部分)
        let parts: Vec<&str> = name_clean.split('-').collect();

        if parts.len() == 4 {
            // 处理 "source-YYYY-MM-DD" 格式
            let year_str = parts[1];
            let month_str = parts[2];
            let day_str = parts[3];

            // 检查是否为有效的日期格式
            if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                if let (Ok(year), Ok(month), Ok(day)) = (
                    year_str.parse::<i32>(),
                    month_str.parse::<u32>(),
                    day_str.parse::<u32>(),
                ) {
                    // 使用 chrono 解析日期
                    if let Some(date) = chrono::NaiveDate::from_ymd_opt(year, month, day) {
                        return Some(date);
                    }
                }
            }
        } else if parts.len() >= 5 {
            // 处理复杂格式，如 "complex-source-YYYY-MM-DD" 或 "source-YYYY-MM-DD-HHMMSS"
            // 先尝试解析倒数第四、三、二个部分作为日期 (适用于 "source-YYYY-MM-DD-HHMMSS" 格式)
            if parts.len() >= 5 {
                let year_str = parts[parts.len() - 4];
                let month_str = parts[parts.len() - 3];
                let day_str = parts[parts.len() - 2];

                // 检查是否为有效的日期格式
                if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                    if let (Ok(year), Ok(month), Ok(day)) = (
                        year_str.parse::<i32>(),
                        month_str.parse::<u32>(),
                        day_str.parse::<u32>(),
                    ) {
                        // 使用 chrono 解析日期
                        if let Some(date) = chrono::NaiveDate::from_ymd_opt(year, month, day) {
                            return Some(date);
                        }
                    }
                }
            }

            // 如果倒数第四、三、二个部分不是有效日期，尝试倒数第三、二、一个部分
            // 这处理 "complex-source-YYYY-MM-DD" 格式
            let year_str = parts[parts.len() - 3];
            let month_str = parts[parts.len() - 2];
            let day_str = parts[parts.len() - 1];

            // 检查是否为有效的日期格式
            if year_str.len() == 4 && month_str.len() == 2 && day_str.len() == 2 {
                if let (Ok(year), Ok(month), Ok(day)) = (
                    year_str.parse::<i32>(),
                    month_str.parse::<u32>(),
                    day_str.parse::<u32>(),
                ) {
                    // 使用 chrono 解析日期
                    if let Some(date) = chrono::NaiveDate::from_ymd_opt(year, month, day) {
                        return Some(date);
                    }
                }
            }
        }

        None
    }

    /// 清理超过保留期的日志文件
    ///
    /// 扫描日志目录，删除超过 retention_days 天的日志文件
    /// 支持删除原始日志文件(.jsonl)和压缩文件(.zst)
    pub async fn cleanup_old_logs_in_dir(logs_dir: &PathBuf, retention_days: u32) -> Result<()> {
        let current_date = chrono::Local::now().date_naive();
        let retention_days = chrono::Duration::days(retention_days as i64);
        let cutoff_date = current_date - retention_days;

        tracing::info!(
            "开始清理旧日志文件，保留期：{} 天，截止日期：{}",
            retention_days,
            cutoff_date
        );

        // 扫描日志目录
        let mut entries = match tokio::fs::read_dir(logs_dir).await {
            Ok(entries) => entries,
            Err(e) => {
                tracing::error!("无法读取日志目录 {}: {}", logs_dir.display(), e);
                return Err(e.into());
            }
        };

        let mut deleted_count = 0;
        let mut total_size_freed = 0u64;
        let mut files_to_delete = Vec::new();
        let mut files_to_compress = Vec::new();

        // 收集需要删除的文件
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();

            // 只处理文件，跳过目录
            if !path.is_file() {
                continue;
            }

            let filename = match path.file_name().and_then(|name| name.to_str()) {
                Some(name) => name,
                None => {
                    tracing::debug!("跳过无效文件名的文件: {}", path.display());
                    continue;
                }
            };

            // 只处理日志文件（.jsonl 或 .zst）
            if !filename.ends_with(".jsonl") && !filename.ends_with(".zst") {
                continue;
            }

            // 解析文件日期
            if let Some(file_date) = Self::parse_date_from_filename(filename) {
                // 检查是否超过保留期
                if file_date < cutoff_date {
                    // 获取文件大小
                    let file_size = match tokio::fs::metadata(&path).await {
                        Ok(metadata) => metadata.len(),
                        Err(e) => {
                            tracing::warn!("无法获取文件大小 {}: {}", path.display(), e);
                            0
                        }
                    };

                    files_to_delete.push((path.clone(), file_size));
                    total_size_freed += file_size;

                    tracing::debug!(
                        "标记删除过期日志文件: {} (日期: {}, 大小: {} 字节)",
                        filename,
                        file_date,
                        file_size
                    );
                }
                // 如果文件日期不是当前日期，并且文件是 .jsonl 文件，则标记为需要压缩
                else if file_date != current_date && filename.ends_with(".jsonl") {
                    files_to_compress.push(path.clone());
                    tracing::debug!("标记压缩日志文件: {} (日期: {})", filename, file_date);
                } else {
                    tracing::debug!("保留日志文件: {} (日期: {})", filename, file_date);
                }
            } else {
                tracing::debug!("无法解析文件日期，跳过: {}", filename);
            }
        }

        // 执行删除操作
        for (path, size) in files_to_delete {
            match tokio::fs::remove_file(&path).await {
                Ok(()) => {
                    deleted_count += 1;
                    tracing::debug!(
                        "成功删除过期日志文件: {} (大小: {} 字节)",
                        path.display(),
                        size
                    );
                }
                Err(e) => {
                    tracing::warn!("删除过期日志文件失败: {} - {}", path.display(), e);
                    // 删除失败时，从释放的总大小中减去这个文件的大小
                    total_size_freed -= size;
                }
            }
        }
        if deleted_count > 0 {
            tracing::info!(
                "日志清理完成：删除了 {} 个过期文件，释放了 {} 字节 ({:.2} MB) 空间",
                deleted_count,
                total_size_freed,
                total_size_freed as f64 / (1024.0 * 1024.0)
            );
        } else {
            tracing::info!("日志清理完成：未发现需要清理的过期文件");
        }

        // 执行压缩操作
        for path in files_to_compress {
            Self::compress_log_file_by_path(logs_dir, &path).await?;
        }

        Ok(())
    }

    /// 执行轮转的内部实现
    ///
    /// 1. 准备阶段，检查并“挪动”已经存在的归档文件
    /// 2. ｜锁定与关闭，获取当前正在写入的文件句柄
    /// 3. ｜重命名旧文件
    /// 4. ｜创建新文件并替换句柄
    /// 5. 释放锁
    pub(crate) async fn execute_rotation_internal(
        metadata: &LogFileMetadata,
        logs_dir: &PathBuf,
        source: LogSource,
    ) -> Result<()> {
        // 开始性能监控
        let rotation_start = Instant::now();
        let formatted_current_date = Self::get_current_date_string();

        tracing::info!(
            "后台轮转开始执行({}), 当前日期: {}",
            source,
            formatted_current_date
        );

        // 确保日志目录存在
        std::fs::create_dir_all(logs_dir)?;

        // 关闭当前文件写入器
        let mut guard = metadata.writer.lock().await;
        let Some(mut writer) = guard.take() else {
            tracing::warn!("日志文件写入器意外关闭({})", source);
            return Err(anyhow::anyhow!("日志文件写入器意外关闭"));
        };
        let _ = writer.flush();
        drop(writer);

        // 获取存储的日期并确定轮转策略
        let stored_date =
            chrono::DateTime::from_timestamp(metadata.current_timestamp.load(Ordering::Relaxed), 0)
                .map(|it| it.with_timezone(&chrono::Local))
                .unwrap_or_else(|| chrono::Local::now());
        let formatted_stored_date = stored_date.format("%Y-%m-%d").to_string();

        let get_current_log_file_path = |source: LogSource, date: &str| -> PathBuf {
            logs_dir.join(format!("{}-{}.jsonl", source, date))
        };

        let get_timestamped_log_file_path = |source: LogSource, date: &str| -> PathBuf {
            let timestamp = Local::now().format("%H%M%S").to_string();
            logs_dir.join(format!("{}-{}-{}.jsonl", source, date, timestamp))
        };

        // 确定要轮转的文件路径和新文件路径
        let (old_log_path, new_log_path, rotation_type) = if formatted_stored_date
            != formatted_current_date
        {
            // 日期轮转：旧文件用旧日期，新文件用新日期
            let old_path = if !formatted_stored_date.is_empty() {
                Some(get_current_log_file_path(source, &formatted_stored_date))
            } else {
                None
            };
            let new_path = get_current_log_file_path(source, &formatted_current_date);
            (old_path, new_path, "日期轮转")
        } else {
            // 大小轮转：将当前文件重命名为带时间戳的文件，新文件仍用当前日期
            let old_path = get_current_log_file_path(source, &formatted_current_date);
            let timestamped_path = get_timestamped_log_file_path(source, &formatted_current_date);

            // 重命名当前文件
            if old_path.exists() {
                std::fs::rename(&old_path, &timestamped_path)?;
                tracing::debug!(
                    "文件重命名成功({}): {} -> {}",
                    source,
                    old_path.display(),
                    timestamped_path.display()
                );
            }

            (Some(timestamped_path), old_path, "大小轮转")
        };

        tracing::info!(
            "执行{}({}), 新文件路径: {}",
            rotation_type,
            source,
            new_log_path.display()
        );

        // 重置文件大小
        let old_size = metadata.size_bytes.swap(0, Ordering::Relaxed);
        tracing::debug!("重置文件大小计数器({}), 原大小: {} 字节", source, old_size);

        // 如果有旧的日志文件，启动后台压缩任务
        if let Some(old_path) = old_log_path {
            if old_path.exists() {
                let source_name = source.to_string();
                let logs_dir_clone = logs_dir.clone();

                tracing::info!("启动后台压缩任务({}): {}", source_name, old_path.display());

                tokio::spawn(async move {
                    let span = tracing::info_span!(
                        "日志压缩",
                        trace_id = %crate::context::get_global_trace_id()
                    );
                    let _enter = span.enter();

                    if let Err(e) =
                        Self::compress_log_file_by_path(&logs_dir_clone, &old_path).await
                    {
                        tracing::warn!(
                            "压缩日志文件失败({}) {}: {}",
                            source_name,
                            old_path.display(),
                            e
                        );
                    }
                });
            }
        }

        // 在日志轮转时触发清理任务（仅在日期轮转时执行，避免频繁清理）
        if rotation_type == "日期轮转" {
            let logs_dir_clone: PathBuf = logs_dir.clone();
            let retention_days = metadata.retention_days;
            tokio::spawn(async move {
                let span = tracing::info_span!(
                    "日期轮转",
                    trace_id = %crate::context::get_global_trace_id()
                );
                let _enter = span.enter();

                if let Err(e) = Self::cleanup_old_logs_in_dir(&logs_dir_clone, retention_days).await
                {
                    tracing::warn!("清理旧日志文件失败: {}", e);
                }
            });
        }

        // 创建新的日志文件
        let new_file = std::fs::OpenOptions::new()
            .create(true)
            .append(true)
            .open(&new_log_path)?;

        tracing::debug!("创建新日志文件成功({}): {}", source, new_log_path.display());

        *guard = Some(new_file);

        // 更新性能统计
        let rotation_duration = rotation_start.elapsed();
        metadata.rotation_count.fetch_add(1, Ordering::Relaxed);
        metadata
            .total_rotation_time_ms
            .fetch_add(rotation_duration.as_millis() as u64, Ordering::Relaxed);

        // 更新最后轮转时间
        metadata
            .last_rotation_time
            .store(chrono::Utc::now().timestamp_millis(), Ordering::Relaxed);

        tracing::info!(
            "后台日志轮转完成({}), 已切换到新文件, 耗时: {:?}",
            source,
            rotation_duration
        );

        Ok(())
    }

    /// 获取指定 source 的性能统计信息
    pub fn get_performance_stats(&self, source: LogSource) -> Option<PerformanceStats> {
        self.metadata_map.get(&source).map(|state| {
            let rotation_count = state.rotation_count.load(Ordering::Relaxed);
            let total_rotation_time_ms = state.total_rotation_time_ms.load(Ordering::Relaxed);
            let write_count = state.write_count.load(Ordering::Relaxed);
            let file_size = state.size_bytes.load(Ordering::Relaxed);

            let avg_rotation_time_ms = if rotation_count > 0 {
                total_rotation_time_ms / rotation_count
            } else {
                0
            };

            let last_rotation_time = state.last_rotation_time.load(Ordering::Relaxed);

            PerformanceStats {
                source,
                write_count,
                rotation_count,
                total_rotation_time_ms,
                avg_rotation_time_ms,
                current_file_size: file_size,
                last_rotation_time,
            }
        })
    }

    /// 获取所有 source 的性能统计信息
    pub fn get_all_performance_stats(&self) -> Vec<PerformanceStats> {
        self.metadata_map
            .iter()
            .filter_map(|entry| {
                let source = *entry.key();
                self.get_performance_stats(source)
            })
            .collect()
    }

    /// 重置指定 source 的性能统计
    pub fn reset_performance_stats(&self, source: LogSource) {
        if let Some(state) = self.metadata_map.get(&source) {
            state.rotation_count.store(0, Ordering::Relaxed);
            state.total_rotation_time_ms.store(0, Ordering::Relaxed);
            state.last_rotation_time.store(0, Ordering::Relaxed);
        }
    }

    /// 压缩指定路径的日志文件
    async fn compress_log_file_by_path(log_dir: &PathBuf, log_path: &PathBuf) -> Result<()> {
        if !log_path.exists() {
            tracing::debug!("压缩跳过：日志文件不存在 {}", log_path.display());
            return Ok(());
        }
        let Some(file_name) = log_path.file_stem().and_then(|s| s.to_str()) else {
            tracing::warn!("压缩跳过：无法获取文件名 {}", log_path.display());
            return Ok(());
        };
        let Some(ext_name) = log_path.extension().and_then(|s| s.to_str()) else {
            tracing::warn!("压缩跳过：无法获取文件扩展名 {}", log_path.display());
            return Ok(());
        };

        if ext_name == "zst" {
            tracing::debug!("压缩跳过：日志文件已压缩 {}", log_path.display());
            return Ok(());
        }

        tracing::debug!("开始压缩日志文件: {}", log_path.display());

        let compressed_path = log_dir.join(format!("{}.zst", file_name));

        // 如果压缩文件已存在，生成序号后缀
        let final_compressed_path = if compressed_path.exists() {
            let mut counter = 1;
            loop {
                let numbered_path = log_dir.join(format!("{}.zst.{}", file_name, counter));
                if !numbered_path.exists() {
                    tracing::debug!("压缩文件已存在，使用序号后缀: {}", numbered_path.display());
                    break numbered_path;
                }
                counter += 1;
            }
        } else {
            compressed_path
        };

        // 使用 zstd 压缩文件
        let input_data = tokio::fs::read(log_path).await?;
        let original_size = input_data.len();

        tracing::debug!("读取原始文件完成，大小: {} 字节", original_size);

        let compressed_data = tokio::task::spawn_blocking(move || {
            zstd::bulk::compress(&input_data, 3) // 使用压缩级别 3
        })
        .await??;

        let compressed_size = compressed_data.len();
        let compression_ratio = (compressed_size as f64 / original_size as f64) * 100.0;

        tokio::fs::write(&final_compressed_path, compressed_data).await?;

        // 删除原始文件
        tokio::fs::remove_file(log_path).await?;

        tracing::info!(
            "日志文件压缩完成: {} -> {} (原大小: {} 字节, 压缩后: {} 字节, 压缩率: {:.1}%)",
            log_path.display(),
            final_compressed_path.display(),
            original_size,
            compressed_size,
            compression_ratio
        );

        Ok(())
    }
    fn write_log_entry(&self, entry: StructuredLogEntry) {
        self.background_manager.send_log(entry)
    }
    /// 写入日志条目到文件
    async fn handle_log_entry(
        logs_dir: &PathBuf,
        entry: &StructuredLogEntry,
        metadata_map: &Arc<DashMap<LogSource, LogFileMetadata>>,
        rotation_sender: &mpsc::Sender<RotationRequest>,
    ) -> Result<()> {
        let source = entry.source;
        // 获取或创建 source 状态
        let metadata = metadata_map
            .entry(source)
            .or_insert_with(LogFileMetadata::new);

        // 增加写入计数（用于统计）
        let _current_write_count = metadata.write_count.fetch_add(1, Ordering::Relaxed) + 1;

        let log_line = serde_json::to_string(entry)?;
        let log_line_with_newline = format!("{}\n", log_line);
        let bytes_to_write = log_line_with_newline.len() as u64;

        // 如果当前没有写入器，创建一个
        let mut writer = metadata.writer.lock().await;
        if writer.is_none() {
            // 确保日志目录存在
            std::fs::create_dir_all(logs_dir)?;

            let current_date = Self::get_current_date_string();
            let current_timestamp = Self::get_current_timestamp();
            let log_path = Self::get_current_log_file_path(logs_dir, source, &current_date);

            let file = std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(&log_path)?;

            *writer = Some(file);

            tracing::debug!(
                trace_id = %crate::context::get_global_trace_id(),
                "创建新的日志文件写入器({}): {}",
                source,
                log_path.display()
            );

            // 更新当前日期和初始化文件大小
            metadata
                .current_timestamp
                .store(current_timestamp, Ordering::Relaxed);

            // 获取现有文件大小
            if let Ok(filemeta) = std::fs::metadata(&log_path) {
                let existing_size = filemeta.len();
                metadata.size_bytes.store(existing_size, Ordering::Relaxed);
                tracing::debug!(
                    "检测到现有日志文件({}), 大小: {} 字节",
                    source,
                    existing_size
                );
            } else {
                metadata.size_bytes.store(0, Ordering::Relaxed);
                tracing::debug!("创建新的日志文件({}), 初始大小: 0 字节", source);
            }
        }

        let Some(writer) = writer.as_mut() else {
            tracing::error!(
                skip_file_layer = true,
                "获取日志句柄锁意外失败({})(写入时)",
                source
            );
            return Err(anyhow::anyhow!("获取日志句柄锁意外失败(写入时)"));
        };

        // 写入日志
        writer.write_all(log_line_with_newline.as_bytes())?;
        writer.flush()?;

        // 更新文件大小计数器
        let new_size = metadata
            .size_bytes
            .fetch_add(bytes_to_write, Ordering::Relaxed)
            + bytes_to_write;

        // 定期记录文件大小状态（每1MB记录一次）
        if new_size % (1024 * 1024) < bytes_to_write {
            tracing::debug!(
                "日志文件当前大小({}): {} 字节 ({:.1} MB)",
                source,
                new_size,
                new_size as f64 / (1024.0 * 1024.0)
            );
        }

        // 检查是否需要发送轮转请求（非阻塞）
        if let Err(err) = rotation_sender.send(RotationRequest::PeriodicCheck).await {
            tracing::warn!(skip_file_layer = true, "无法发送轮转请求: {}", err);
        };

        Ok(())
    }

    /// 当发生错误时，从缓存中获取相关的调试信息
    fn flush_debug_logs_for_trace(&self, trace_id: TraceId) {
        if let Some(debug_entries) = self.debug_cache.remove(&trace_id) {
            for entry in debug_entries.into_iter() {
                self.write_log_entry(entry);
            }
        } else {
            tracing::warn!("未找到调试日志缓存({})", trace_id);
        }
    }

    /// 错误自动上报预留接口
    #[allow(dead_code)]
    fn report_error_to_server(&self) -> Result<()> {
        // TODO: 实现错误自动上报逻辑
        // 这里可以调用 HTTP 客户端将错误信息发送到服务器
        // info!("开始上报错误日志（未实现）");
        Ok(())
    }

    /// 公共处理逻辑，用于处理日志条目
    fn process_log_entry(&self, entry: StructuredLogEntry) {
        match entry.level {
            LogLevel::Error => {
                // 错误级别：写入文件并刷新调试缓存
                let trace_id = entry.trace_id.clone();
                self.write_log_entry(entry);

                // 刷新相关的调试日志
                if let Some(trace_id) = trace_id {
                    self.flush_debug_logs_for_trace(trace_id);
                }

                // 预留：错误自动上报
                let _ = self.report_error_to_server();
            }
            LogLevel::Warn | LogLevel::Info => {
                // WARN/INFO 级别：直接写入文件
                self.write_log_entry(entry);
            }
            LogLevel::Debug | LogLevel::Trace => {
                // DEBUG/TRACE 级别：缓存到 LRU
                if let Some(trace_id) = entry.trace_id {
                    if let Some(mut cached_entries) = self.debug_cache.get_mut(&trace_id) {
                        cached_entries.push(entry);
                    } else {
                        self.debug_cache.insert(trace_id, vec![entry]);
                    }
                }
            }
        }
    }
}

impl LogWriter for FileLayer {
    fn write_log(&self, entry: StructuredLogEntry) -> anyhow::Result<()> {
        self.process_log_entry(entry);
        Ok(())
    }
}

impl<S> Layer<S> for FileLayer
where
    S: tracing::Subscriber + for<'lookup> LookupSpan<'lookup>,
{
    fn on_event(&self, event: &tracing::Event<'_>, _ctx: Context<'_, S>) {
        if IS_LOGGING_TO_FILE.with(|is_logging| is_logging.get()) {
            return;
        }
        let metadata = event.metadata();
        let level = metadata.level();
        let target = metadata.target();

        // 提取消息内容和 source
        let mut message = String::new();
        let mut explicit_source = None;
        let mut trace_id = None;
        let mut fields: HashMap<&'static str, String> = HashMap::new();
        event.record(&mut MessageVisitor {
            message: &mut message,
            source: &mut explicit_source,
            trace_id: &mut trace_id,
            fields: &mut fields,
        });
        if fields.contains_key("skip_file_layer") {
            return;
        }

        // 获取当前的 trace_id
        let trace_id = trace_id.unwrap_or_else(|| super::current_trace_id());

        // 推断 source
        let source = LogSource::from_target(target, explicit_source.as_deref());

        // 创建结构化日志条目
        let mut entry = StructuredLogEntry::new(
            match *level {
                tracing::Level::ERROR => LogLevel::Error,
                tracing::Level::WARN => LogLevel::Warn,
                tracing::Level::INFO => LogLevel::Info,
                tracing::Level::DEBUG => LogLevel::Debug,
                tracing::Level::TRACE => LogLevel::Trace,
            },
            "core",
            target,
            &message,
        )
        .with_trace_id(trace_id)
        .with_fields(
            fields
                .into_iter()
                .map(|(k, v)| (k.to_string(), v))
                .collect(),
        )
        .with_source(source);

        // 添加文件信息
        if let Some(file) = metadata.file() {
            if let Some(line) = metadata.line() {
                entry = entry.with_file_info(file, line);
            }
        }
        self.process_log_entry(entry);
    }
}
