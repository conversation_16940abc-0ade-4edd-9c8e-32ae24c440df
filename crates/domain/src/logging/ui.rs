use std::{
    collections::{HashMap, VecDeque},
    sync::{
        Arc,
        atomic::{AtomicBool, Ordering},
    },
};

use std::sync::Mutex;
use tokio::sync::mpsc;
use tracing_subscriber::{Layer, layer::Context, registry::LookupSpan};

use crate::{
    context::event::CoreEvent,
    logging::{StructuredLogEntry, forward::LogWriter, level::LogLevel, source::LogSource},
};

/// UI 日志层实现
#[derive(Clone)]
pub struct UILayer {
    desktop_tx: mpsc::Sender<CoreEvent>,
    buffer: Arc<Mutex<VecDeque<CoreEvent>>>, // 使用 VecDeque 实现高效的 FIFO 缓冲
    buffer_size: usize,
    subscription_manager: Arc<crate::context::subscription::SubscriptionManager>,
    flushing: Arc<AtomicBool>,
}

impl UILayer {
    pub fn new(
        desktop_tx: mpsc::Sender<CoreEvent>,
        subscription_manager: Arc<crate::context::subscription::SubscriptionManager>,
    ) -> Self {
        Self {
            desktop_tx,
            buffer: Arc::new(Mutex::new(VecDeque::new())),
            buffer_size: 150, // 最多缓存 150 条日志
            subscription_manager: subscription_manager,
            flushing: Arc::new(AtomicBool::new(false)),
        }
    }

    /// 将 StructuredLogEntry 转换为 CoreEvent::LogMessage
    fn structured_log_to_core_event(&self, entry: StructuredLogEntry) -> CoreEvent {
        CoreEvent::LogMessage {
            level: match entry.level.as_str() {
                "error" => LogLevel::Error,
                "warn" => LogLevel::Warn,
                "info" => LogLevel::Info,
                "debug" => LogLevel::Debug,
                "trace" => LogLevel::Trace,
                _ => LogLevel::Info, // 默认值
            },
            message: entry.message,
            module: entry.module,
            timestamp: chrono::DateTime::from_timestamp_millis(entry.timestamp as i64)
                .unwrap_or_else(|| chrono::Utc::now())
                .to_rfc3339(),
            trace_id: entry.trace_id,
        }
    }

    /// 检查是否有日志订阅
    fn has_log_subscription(&self) -> bool {
        self.subscription_manager
            .has_active_subscription(crate::context::subscription::event_types::LOG_MESSAGE)
    }

    /// 发送日志到 UI
    fn send_to_ui(&self, entry: StructuredLogEntry) -> anyhow::Result<()> {
        // 将 StructuredLogEntry 转换为 CoreEvent::LogMessage
        let core_event = self.structured_log_to_core_event(entry);

        if self.has_log_subscription()
            && self.desktop_tx.capacity() > 10
            && !self.flushing.load(Ordering::Relaxed)
        {
            // 如果有日志订阅，发送缓冲的日志
            let logs = if let Ok(mut buffer) = self.buffer.lock() {
                let mut logs = buffer.drain(..).collect::<Vec<_>>();
                logs.push(core_event);
                logs
            } else {
                vec![core_event]
            };
            self.flushing.store(true, Ordering::Relaxed);
            let flushing = self.flushing.clone();
            let desktop_tx = self.desktop_tx.clone();
            tokio::spawn(async move {
                for log in logs {
                    if let Err(e) = desktop_tx.send(log).await {
                        tracing::debug!("Failed to send log to UI: {}", e);
                        break;
                    }
                }
                flushing.store(false, Ordering::Relaxed);
            });
        } else {
            // 如果没有日志订阅，缓存日志
            if let Ok(mut buffer) = self.buffer.lock() {
                buffer.push_back(core_event);

                // 如果缓存超过限制，移除最老的日志（FIFO）
                if buffer.len() > self.buffer_size {
                    buffer.pop_front();
                }
            }
        }

        Ok(())
    }

    // get_receiver 方法已移除，因为现在直接通过 desktop_tx 发送到前端

    /// 公共处理逻辑，用于处理日志条目
    fn process_log_entry(&self, entry: StructuredLogEntry) -> anyhow::Result<()> {
        // UILayer 只处理 INFO 级别和特定 target 的日志
        if entry.level != LogLevel::Info && !entry.target.starts_with("echowave") {
            return Ok(());
        }

        // 发送到 UI
        self.send_to_ui(entry)?;

        Ok(())
    }
}

impl LogWriter for UILayer {
    fn write_log(&self, entry: StructuredLogEntry) -> anyhow::Result<()> {
        self.process_log_entry(entry)
    }
}

impl<S> Layer<S> for UILayer
where
    S: tracing::Subscriber + for<'lookup> LookupSpan<'lookup>,
{
    fn on_event(&self, event: &tracing::Event<'_>, _ctx: Context<'_, S>) {
        let metadata = event.metadata();
        let level = metadata.level();
        let target = metadata.target();
        let module = metadata.module_path().unwrap_or("unknown");

        // 只处理 INFO 级别和特定 target 的日志
        if *level != tracing::Level::INFO && !target.starts_with("echowave") {
            return;
        }

        // 提取消息内容和 source
        let mut message = String::new();
        let mut explicit_source = None;
        let mut trace_id = None;
        let mut fields = HashMap::new();
        event.record(&mut super::visitor::MessageVisitor {
            message: &mut message,
            source: &mut explicit_source,
            trace_id: &mut trace_id,
            fields: &mut fields,
        });
        if fields.contains_key("skip_ui_layer") {
            return;
        }

        // 获取当前的 trace_id
        let trace_id = trace_id.unwrap_or_else(|| super::current_trace_id());

        // 推断 source
        let source = LogSource::from_target(target, explicit_source.as_deref());

        // 创建结构化日志条目
        let mut entry = StructuredLogEntry::new(LogLevel::Info, module, target, &message)
            .with_trace_id(trace_id)
            .with_source(source);

        // 添加文件信息
        if let Some(file) = metadata.file() {
            if let Some(line) = metadata.line() {
                entry = entry.with_file_info(file, line);
            }
        }

        // 发送到 UI
        if let Err(e) = self.send_to_ui(entry) {
            tracing::debug!("Failed to send log to UI layer: {}", e);
        }
    }
}
