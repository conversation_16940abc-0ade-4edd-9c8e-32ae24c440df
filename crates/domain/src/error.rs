use crate::adapters::AdapterError;
use uuid::Uuid;

/// 核心模块统一错误类型
#[derive(Debug, thiserror::Error)]
pub enum CoreError {
    #[error("Agent communication failed: {0}")]
    AgentError(AdapterError),

    #[error("Helper service error: {0}")]
    HelperError(AdapterError),

    #[error("HTTP request failed: {0}")]
    HttpError(AdapterError),

    #[error("File operation failed: {0}")]
    FileError(AdapterError),

    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("Service error: {0}")]
    ServiceError(String),

    #[error("Validation error: {0}")]
    ValidationError(String),

    #[error("Timeout error: {0}")]
    TimeoutError(String),

    #[error("Operation cancelled: {0}")]
    OperationCancelled(String),

    #[error("Internal error: {0}")]
    InternalError(#[from] anyhow::Error),
}

impl CoreError {
    /// 创建配置错误
    pub fn config_error(msg: &str) -> Self {
        CoreError::ConfigError(msg.to_string())
    }

    /// 创建服务错误
    pub fn service_error(msg: &str) -> Self {
        CoreError::ServiceError(msg.to_string())
    }

    /// 创建验证错误
    pub fn validation_error(msg: &str) -> Self {
        CoreError::ValidationError(msg.to_string())
    }

    /// 创建操作失败错误
    pub fn operation_failed(msg: &str) -> Self {
        CoreError::ServiceError(msg.to_string())
    }

    /// 创建无效输入错误
    pub fn invalid_input(msg: &str) -> Self {
        CoreError::ValidationError(msg.to_string())
    }

    /// 创建无效状态错误
    pub fn invalid_state(msg: &str) -> Self {
        CoreError::ServiceError(msg.to_string())
    }

    /// 创建未找到错误
    pub fn not_found(msg: &str) -> Self {
        CoreError::ServiceError(msg.to_string())
    }

    /// 创建适配器错误
    pub fn adapter_error(msg: &str) -> Self {
        CoreError::ServiceError(msg.to_string())
    }

    /// 创建解析错误
    pub fn parse_error(msg: &str) -> Self {
        CoreError::ValidationError(msg.to_string())
    }

    /// 创建操作取消错误
    pub fn operation_cancelled(msg: &str) -> Self {
        CoreError::OperationCancelled(msg.to_string())
    }

    /// 判断错误是否可恢复
    pub fn is_recoverable(&self) -> bool {
        match self {
            CoreError::AgentError(e) => e.is_recoverable(),
            CoreError::HelperError(e) => e.is_recoverable(),
            CoreError::HttpError(e) => e.is_recoverable(),
            CoreError::FileError(e) => e.is_recoverable(),
            CoreError::ConfigError(_) => false,
            CoreError::ServiceError(_) => true,
            CoreError::ValidationError(_) => false,
            CoreError::TimeoutError(_) => true,
            CoreError::OperationCancelled(_) => false,
            CoreError::InternalError(_) => false,
        }
    }

    /// 获取错误码
    pub fn error_code(&self) -> u32 {
        match self {
            CoreError::AgentError(e) => 2000 + e.error_code(),
            CoreError::HelperError(e) => 3000 + e.error_code(),
            CoreError::HttpError(e) => 4000 + e.error_code(),
            CoreError::FileError(e) => 5000 + e.error_code(),
            CoreError::ConfigError(_) => 6001,
            CoreError::ServiceError(_) => 6002,
            CoreError::ValidationError(_) => 6003,
            CoreError::TimeoutError(_) => 6004,
            CoreError::OperationCancelled(_) => 6005,
            CoreError::InternalError(_) => 6999,
        }
    }

    /// 获取错误类别
    pub fn category(&self) -> &'static str {
        match self {
            CoreError::AgentError(_) => "agent",
            CoreError::HelperError(_) => "helper",
            CoreError::HttpError(_) => "http",
            CoreError::FileError(_) => "file",
            CoreError::ConfigError(_) => "config",
            CoreError::ServiceError(_) => "service",
            CoreError::ValidationError(_) => "validation",
            CoreError::TimeoutError(_) => "timeout",
            CoreError::OperationCancelled(_) => "cancelled",
            CoreError::InternalError(_) => "internal",
        }
    }
}

/// 带有上下文的错误信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ErrorContext {
    pub trace_id: Uuid,
    pub module: String,
    pub operation: String,
    pub error_code: u32,
    pub error_category: String,
    pub message: String,
    pub recoverable: bool,
    pub timestamp: u64,
    pub details: Option<serde_json::Value>,
}

impl ErrorContext {
    /// 从 CoreError 创建错误上下文
    pub fn from_error(error: &CoreError, trace_id: Uuid, module: &str, operation: &str) -> Self {
        Self {
            trace_id,
            module: module.to_string(),
            operation: operation.to_string(),
            error_code: error.error_code(),
            error_category: error.category().to_string(),
            message: error.to_string(),
            recoverable: error.is_recoverable(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            details: None,
        }
    }

    /// 添加详细信息
    pub fn with_details(mut self, details: serde_json::Value) -> Self {
        self.details = Some(details);
        self
    }

    /// 转换为 JSON
    pub fn to_json(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(self)
    }
}

/// 错误恢复策略
#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    /// 不尝试恢复
    None,
    /// 重试操作
    Retry { max_attempts: u32, delay_ms: u64 },
    /// 降级到备用方案
    Fallback {
        fallback_fn: fn() -> Result<(), CoreError>,
    },
    /// 重置连接
    ResetConnection,
}

/// 错误处理器
pub struct ErrorHandler {
    pub trace_id: Uuid,
    pub module: String,
    pub operation: String,
}

impl ErrorHandler {
    pub fn new(trace_id: Uuid, module: &str, operation: &str) -> Self {
        Self {
            trace_id,
            module: module.to_string(),
            operation: operation.to_string(),
        }
    }

    /// 处理错误并返回上下文
    pub fn handle_error(&self, error: CoreError) -> ErrorContext {
        let context =
            ErrorContext::from_error(&error, self.trace_id, &self.module, &self.operation);

        // 记录错误日志
        tracing::error!(
            trace_id = %self.trace_id,
            module = %self.module,
            operation = %self.operation,
            error_code = context.error_code,
            error_category = %context.error_category,
            recoverable = context.recoverable,
            error = %error,
            "Error occurred"
        );

        context
    }

    /// 处理错误并尝试恢复
    pub async fn handle_with_recovery(
        &self,
        error: CoreError,
        strategy: RecoveryStrategy,
    ) -> Result<(), CoreError> {
        let context = self.handle_error(error);

        if !context.recoverable {
            return Err(CoreError::InternalError(anyhow::anyhow!(
                "Error is not recoverable: {}",
                context.message
            )));
        }

        match strategy {
            RecoveryStrategy::None => Err(CoreError::InternalError(anyhow::anyhow!(
                "No recovery strategy specified"
            ))),
            RecoveryStrategy::Retry {
                max_attempts,
                delay_ms,
            } => {
                tracing::info!(
                    trace_id = %self.trace_id,
                    max_attempts = max_attempts,
                    delay_ms = delay_ms,
                    "Attempting error recovery with retry strategy"
                );

                // 这里应该由调用者实现具体的重试逻辑
                // 这个方法主要是记录恢复尝试
                Ok(())
            }
            RecoveryStrategy::Fallback { fallback_fn } => {
                tracing::info!(
                    trace_id = %self.trace_id,
                    "Attempting error recovery with fallback strategy"
                );

                fallback_fn()
            }
            RecoveryStrategy::ResetConnection => {
                tracing::info!(
                    trace_id = %self.trace_id,
                    "Attempting error recovery with connection reset"
                );

                // 这里应该由调用者实现具体的连接重置逻辑
                Ok(())
            }
        }
    }
}

/// 结果类型别名
pub type CoreResult<T> = Result<T, CoreError>;

/// 便捷宏：创建配置错误
#[macro_export]
macro_rules! config_error {
    ($msg:expr) => {
        CoreError::ConfigError($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        CoreError::ConfigError(format!($fmt, $($arg)*))
    };
}

/// 便捷宏：创建服务错误
#[macro_export]
macro_rules! service_error {
    ($msg:expr) => {
        CoreError::ServiceError($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        CoreError::ServiceError(format!($fmt, $($arg)*))
    };
}

/// 便捷宏：创建验证错误
#[macro_export]
macro_rules! validation_error {
    ($msg:expr) => {
        CoreError::ValidationError($msg.to_string())
    };
    ($fmt:expr, $($arg:tt)*) => {
        CoreError::ValidationError(format!($fmt, $($arg)*))
    };
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_core_error_properties() {
        let agent_error = CoreError::AgentError(AdapterError::ConnectionFailed("test".to_string()));
        assert!(agent_error.is_recoverable());
        assert_eq!(agent_error.category(), "agent");
        assert!(agent_error.error_code() >= 3000 && agent_error.error_code() < 4000);

        let config_error = CoreError::ConfigError("test".to_string());
        assert!(!config_error.is_recoverable());
        assert_eq!(config_error.category(), "config");
        assert_eq!(config_error.error_code(), 6001);
    }

    #[test]
    fn test_error_context_creation() {
        let trace_id = Uuid::new_v4();
        let error = CoreError::ConfigError("test error".to_string());
        let context = ErrorContext::from_error(&error, trace_id, "test_module", "test_operation");

        assert_eq!(context.trace_id, trace_id);
        assert_eq!(context.module, "test_module");
        assert_eq!(context.operation, "test_operation");
        assert_eq!(context.error_category, "config");
        assert!(!context.recoverable);
    }

    #[test]
    fn test_error_context_serialization() {
        let trace_id = Uuid::new_v4();
        let error = CoreError::ServiceError("test error".to_string());
        let context = ErrorContext::from_error(&error, trace_id, "test_module", "test_operation")
            .with_details(serde_json::json!({"key": "value"}));

        let json = context.to_json().unwrap();
        let deserialized: ErrorContext = serde_json::from_str(&json).unwrap();

        assert_eq!(context.trace_id, deserialized.trace_id);
        assert_eq!(context.message, deserialized.message);
        assert_eq!(context.details, deserialized.details);
    }

    #[test]
    fn test_error_macros() {
        let config_err = config_error!("test config error");
        assert!(matches!(config_err, CoreError::ConfigError(_)));

        let service_err = service_error!("test {} error", "service");
        assert!(matches!(service_err, CoreError::ServiceError(_)));

        let validation_err = validation_error!("test validation error");
        assert!(matches!(validation_err, CoreError::ValidationError(_)));
    }
}
