#[cfg(target_os = "windows")]
use std::collections::HashMap;
#[cfg(target_os = "windows")]
use std::path::PathBuf;
#[cfg(target_os = "windows")]
use sysinfo::System;
#[cfg(target_os = "windows")]
use tokio::fs;
#[cfg(target_os = "windows")]
use tracing::{error, info};

const BANNER: &'static str = "# Generated By EchoWave";

#[derive(Debug, Clone)]
#[cfg(target_os = "windows")]
pub struct SystemInfo {
    pub cpu_count: usize,
    pub total_memory_gb: f64,
}

#[cfg(target_os = "windows")]
impl SystemInfo {
    pub fn get() -> anyhow::Result<Self> {
        let mut sys = System::new_all();
        sys.refresh_memory();

        // 获取 CPU 核心数
        let cpu_count = sys.cpus().len();

        // 获取总内存（字节），转换为 GB
        let total_memory_bytes = sys.total_memory();
        let total_memory_gb = total_memory_bytes as f64 / (1024.0 * 1024.0 * 1024.0);

        Ok(SystemInfo {
            cpu_count,
            total_memory_gb,
        })
    }
}

#[derive(Debug, Clone)]
#[cfg(target_os = "windows")]
pub struct WslConfig {
    pub processors: String,
    pub memory: String,
    pub swap: String,
}

#[cfg(target_os = "windows")]
impl WslConfig {
    pub fn from_system_info(system_info: &SystemInfo) -> Self {
        // Calculate processors: 70% of CPU count, minimum 2
        let processors =
            std::cmp::max((system_info.cpu_count as f64 * 0.7).floor() as usize, 2).to_string();

        // Calculate memory: 80% of total memory, minimum 1GB
        let memory_gb = std::cmp::max((system_info.total_memory_gb * 0.8).floor() as usize, 1);
        let memory = format!("{}GB", memory_gb);

        let swap = "8GB".to_string();

        info!(
            "WSL 配置: 处理器={} 内存={} 交换空间={}",
            processors, memory, swap
        );

        WslConfig {
            processors,
            memory,
            swap,
        }
    }

    pub fn to_file_content(&self) -> String {
        format!(
            "{}\n[wsl2]\nprocessors={}\nmemory={}\nswap={}",
            BANNER, self.processors, self.memory, self.swap
        )
    }
    pub fn to_hashmap(&self) -> HashMap<&'static str, String> {
        let mut map = HashMap::new();
        map.insert("processors", self.processors.clone());
        map.insert("memory", self.memory.clone());
        map.insert("swap", self.swap.clone());
        map
    }
}

#[cfg(target_os = "windows")]
fn get_wsl_config_path() -> anyhow::Result<PathBuf> {
    Ok(dirs::home_dir()
        .ok_or_else(|| anyhow::anyhow!("无法获取用户主目录"))?
        .join(".wslconfig"))
}

#[cfg(target_os = "windows")]
fn get_wsl_config_backup_path() -> anyhow::Result<PathBuf> {
    Ok(dirs::home_dir()
        .ok_or_else(|| anyhow::anyhow!("无法获取用户主目录"))?
        .join(".wslconfig.bak.6563686f77617665"))
}

#[cfg(target_os = "windows")]
async fn backup_user_wsl_config_file() -> anyhow::Result<()> {
    let src = get_wsl_config_path()?;
    let dst = get_wsl_config_backup_path()?;

    if !src.exists() {
        use tracing::debug;

        debug!("用户配置文件不存在，跳过备份");
        return Ok(());
    }

    if dst.exists() {
        use tracing::debug;

        debug!("备份文件已存在，跳过备份");
        return Ok(());
    }

    let content = fs::read_to_string(&src).await.map_err(|err| {
        error!("读取用户配置文件失败: {}", err);
        anyhow::anyhow!("读取用户配置文件失败: {}", err)
    })?;

    if content.starts_with(BANNER) {
        info!("发现旧版配置文件");
        return Ok(());
    }

    info!("用户配置文件存在，开始备份");
    fs::rename(&src, &dst).await.map_err(|err| {
        error!("备份用户配置文件失败: {}", err);
        anyhow::anyhow!("备份用户配置文件失败: {}", err)
    })?;

    Ok(())
}

#[cfg(target_os = "windows")]
async fn restore_user_wsl_config_file() -> anyhow::Result<bool> {
    let src = get_wsl_config_backup_path()?;
    let dst = get_wsl_config_path()?;

    if !dst.exists() {
        error!("意外错误，WSL 配置文件未找到");
        return Ok(false);
    }

    if !src.exists() {
        info!("没有用户配置文件，保留配置文件供下次使用");
        return Ok(false);
    }

    info!("正在删除 WSL 配置文件");
    fs::remove_file(&dst).await.map_err(|err| {
        error!("删除 WSL 配置文件失败: {}", err);
        anyhow::anyhow!("删除 WSL 配置文件失败: {}", err)
    })?;

    info!("正在恢复用户配置文件");
    fs::rename(&src, &dst).await.map_err(|err| {
        error!("恢复用户配置文件失败: {}", err);
        anyhow::anyhow!("恢复用户配置文件失败: {}", err)
    })?;

    Ok(true)
}

#[cfg(target_os = "windows")]
async fn restart_wsl() -> anyhow::Result<()> {
    use crate::utils::wsl_manager;

    info!("正在重启 WSL");

    let _ = wsl_manager::WslManager::stop_wsl().await;

    info!("WSL 重启完成");
    Ok(())
}

#[cfg(target_os = "windows")]
pub async fn set_limited_wsl_config() -> anyhow::Result<()> {
    let system_info = SystemInfo::get().map_err(|err| {
        error!("获取系统信息失败: {}", err);
        anyhow::anyhow!("获取系统信息失败: {}", err)
    })?;

    let config = WslConfig::from_system_info(&system_info);
    let content = config.to_file_content();
    let config_map = config.to_hashmap();

    let config_path = get_wsl_config_path()?;

    if config_path.exists() {
        info!("WSL 配置文件存在，开始比较");

        let existing_content = fs::read_to_string(&config_path).await.map_err(|err| {
            error!("读取现有 WSL 配置失败: {}", err);
            anyhow::anyhow!("读取现有 WSL 配置失败: {}", err)
        })?;

        // Parse existing config and compare with desired config
        let matches: Vec<_> = existing_content
            .lines()
            .filter_map(|line| {
                let parts: Vec<&str> = line.trim().split('=').collect();
                if parts.len() == 2 {
                    let key = parts[0].trim();
                    let value = parts[1].trim();
                    if let Some(expected_value) = config_map.get(key) {
                        if expected_value == value {
                            return Some((key, value));
                        }
                    }
                }
                None
            })
            .collect();

        if matches.len() == config_map.len() {
            info!("配置成功");
            return Ok(());
        }
    }

    backup_user_wsl_config_file().await.map_err(|err| {
        error!("备份用户配置文件失败: {}", err);
        anyhow::anyhow!("备份用户配置文件失败: {}", err)
    })?;

    fs::write(&config_path, content).await.map_err(|err| {
        error!("写入 WSL 配置文件失败: {}", err);
        anyhow::anyhow!("写入 WSL 配置文件失败: {}", err)
    })?;

    match restart_wsl().await {
        Ok(_) => {
            info!("配置成功");
            Ok(())
        }
        Err(e) => {
            error!("配置失败: {}", e);
            Err(e)
        }
    }
}

#[cfg(not(target_os = "windows"))]
pub async fn set_limited_wsl_config() -> anyhow::Result<()> {
    tracing::info!("非 Windows 平台，跳过设置配置");
    Ok(())
}

#[cfg(target_os = "windows")]
pub async fn unset_limited_wsl_config() -> anyhow::Result<()> {
    match restore_user_wsl_config_file().await {
        Ok(restored) => {
            if restored {
                restart_wsl().await.map_err(|err| {
                    error!("重启 WSL 失败: {}", err);
                    anyhow::anyhow!("重启 WSL 失败: {}", err)
                })?;
            }
            info!("取消配置成功");
            Ok(())
        }
        Err(e) => {
            error!("取消配置失败: {}", e);
            Err(e)
        }
    }
}

#[cfg(not(target_os = "windows"))]
pub async fn unset_limited_wsl_config() -> anyhow::Result<()> {
    tracing::info!("非 Windows 平台，跳过取消配置");
    Ok(())
}
