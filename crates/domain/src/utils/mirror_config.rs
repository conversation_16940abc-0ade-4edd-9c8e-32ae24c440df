//! WSL 镜像管理
//!
//! 管理 WSL 镜像的下载配置，包括：
//! - 镜像 CDN 地址配置
//! - 默认镜像名称和版本
//! - 分块下载参数配置
//! - 校验算法配置
//! - 磁盘空间要求

use crate::{
    config::{AppMode, ContextConfig},
    utils::{downloader::DownloadConfig, hash::HashAlgorithm},
};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// WSL 镜像配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MirrorConfig {
    /// 镜像下载完整 URL (从 ContextConfig 环境配置中获取)
    pub mirror_url: String,
    /// 镜像文件名 (从 URL 中提取)
    pub mirror_filename: String,
    /// 默认分发名称 (从 ContextConfig 中获取)
    pub distro_name: String,
    /// 最小磁盘空间要求（字节）
    pub min_disk_space: u64,
    /// 下载配置
    pub download_config: DownloadConfig,
    /// 镜像哈希 (从 ContextConfig 环境配置中获取)
    pub mirror_hash: Option<String>,
    /// 哈希算法 (从 ContextConfig 环境配置中获取)
    pub hash_algorithm: HashAlgorithm,
}

impl MirrorConfig {
    /// 从 ContextConfig 创建配置
    pub fn from_context_config(context_config: &ContextConfig) -> Self {
        let mirror_url = &context_config.environment.mirror_url;
        // 从 URL 中提取文件名
        let mirror_filename = mirror_url
            .split('/')
            .last()
            .unwrap_or("echowave-engine.tar")
            .to_string();

        Self {
            mirror_url: mirror_url.clone(),
            mirror_filename,
            distro_name: context_config.distro_name.clone(),
            min_disk_space: 50 * 1024 * 1024 * 1024, // 50GB
            download_config: DownloadConfig {
                default_chunk_size: 64 * 1024 * 1024, // 64MB
                min_chunk_size: 32 * 1024 * 1024,     // 32MB
                max_chunk_size: 128 * 1024 * 1024,    // 128MB
                adaptive_chunk_sizing: true,
                max_concurrent_chunks: 4,
                hash_algorithm: context_config.environment.mirror_algorithm,
                chunk_retry_count: 3,
                chunk_timeout: 120, // 2分钟
                connect_timeout: 30,
                total_timeout: 3600, // 1小时
                user_agent: "EchoWave-Client/1.0".to_string(),
            },
            mirror_hash: if context_config.environment.mirror_hash.is_empty() {
                None
            } else {
                Some(context_config.environment.mirror_hash.clone())
            },
            hash_algorithm: context_config.environment.mirror_algorithm,
        }
    }

    /// 为开发环境调整配置
    pub fn adjust_for_development(&mut self) {
        // 开发环境使用较小的并发数和超时时间
        self.download_config.max_concurrent_chunks = 2;
        self.download_config.chunk_timeout = 60;
    }

    /// 获取完整的镜像下载 URL
    pub fn get_mirror_url(&self) -> String {
        self.mirror_url.clone()
    }

    /// 获取镜像校验和 URL
    pub fn get_checksum_url(&self) -> String {
        let checksum_extension = match self.hash_algorithm {
            HashAlgorithm::MD5 => "md5",
            HashAlgorithm::SHA256 => "sha256",
        };
        format!("{}.{}", self.mirror_url, checksum_extension)
    }

    /// 验证配置有效性
    pub fn validate(&self) -> Result<(), String> {
        // 验证 URL 格式
        if !self.mirror_url.starts_with("http://") && !self.mirror_url.starts_with("https://") {
            return Err("mirror_url 必须是有效的 HTTP/HTTPS URL".to_string());
        }

        // 验证分发名称
        if self.distro_name.is_empty() {
            return Err("distro_name 不能为空".to_string());
        }

        // 验证镜像文件名
        if self.mirror_filename.is_empty() {
            return Err("mirror_filename 不能为空".to_string());
        }

        // 验证镜像 URL
        if self.mirror_url.is_empty() {
            return Err("mirror_url 不能为空".to_string());
        }

        // 验证磁盘空间要求
        if self.min_disk_space < 1024 * 1024 * 1024 {
            return Err("min_disk_space 不能小于 1GB".to_string());
        }

        // 验证下载配置
        if self.download_config.max_concurrent_chunks == 0 {
            return Err("max_concurrent_chunks 必须大于 0".to_string());
        }

        if self.download_config.chunk_retry_count == 0 {
            return Err("chunk_retry_count 必须大于 0".to_string());
        }

        if self.download_config.chunk_timeout == 0 {
            return Err("chunk_timeout 必须大于 0".to_string());
        }

        Ok(())
    }

    /// 获取缓存目录中的镜像文件路径
    pub fn get_cache_path(&self, cache_dir: &std::path::Path) -> PathBuf {
        cache_dir.join(&self.mirror_filename)
    }

    /// 获取缓存目录中的校验和文件路径
    pub fn get_checksum_cache_path(&self, cache_dir: &std::path::Path) -> PathBuf {
        let checksum_extension = match self.hash_algorithm {
            HashAlgorithm::MD5 => "md5",
            HashAlgorithm::SHA256 => "sha256",
        };
        cache_dir.join(format!("{}.{}", self.mirror_filename, checksum_extension))
    }

    /// 更新镜像
    pub fn set_checksum(&mut self, checksum: String) {
        self.mirror_hash = Some(checksum);
    }

    /// 获取镜像校验和
    pub fn get_checksum(&self) -> Option<&str> {
        self.mirror_hash.as_deref()
    }

    /// 估算下载时间（秒）
    ///
    /// 基于文件大小和网络速度估算
    ///
    /// # 参数
    /// - `file_size`: 文件大小（字节）
    /// - `network_speed`: 网络速度（字节/秒）
    pub fn estimate_download_time(&self, file_size: u64, network_speed: f64) -> u64 {
        if network_speed <= 0.0 {
            return 0;
        }

        // 考虑并发下载的加速效果（理论上最多加速到并发数倍）
        let speedup_factor = (self.download_config.max_concurrent_chunks as f64).min(4.0);
        let effective_speed = network_speed * speedup_factor * 0.8; // 80% 效率

        (file_size as f64 / effective_speed) as u64
    }

    /// 检查是否需要更新镜像
    ///
    /// 基于 URL 变化或校验和不匹配决定是否需要更新
    ///
    /// # 参数
    /// - `current_url`: 当前安装的镜像 URL
    /// - `current_checksum`: 当前安装的镜像校验和
    pub fn needs_update(&self, current_url: Option<&str>, current_checksum: Option<&str>) -> bool {
        match current_url {
            Some(url) => {
                // URL 不同需要更新
                if url != self.mirror_url {
                    return true;
                }
                // 如果有校验和，检查校验和是否匹配
                if let (Some(current), Some(expected)) = (current_checksum, &self.mirror_checksum) {
                    current != expected
                } else {
                    false // URL 相同且没有校验和信息，不需要更新
                }
            }
            None => true, // 没有安装，需要下载
        }
    }
}

/// 镜像管理器
pub struct MirrorManager {
    config: MirrorConfig,
}

impl MirrorManager {
    /// 创建新的镜像管理器
    pub fn new(config: MirrorConfig) -> Result<Self, String> {
        config.validate()?;
        Ok(Self { config })
    }

    /// 获取配置的引用
    pub fn config(&self) -> &MirrorConfig {
        &self.config
    }

    /// 更新配置
    pub fn update_config(&mut self, config: MirrorConfig) -> Result<(), String> {
        config.validate()?;
        self.config = config;
        Ok(())
    }

    /// 获取镜像信息
    pub fn get_mirror_info(&self) -> MirrorInfo {
        MirrorInfo {
            name: self.config.distro_name.clone(),
            filename: self.config.mirror_filename.clone(),
            download_url: self.config.get_mirror_url(),
            checksum_url: self.config.get_checksum_url(),
            checksum: self.config.get_checksum().map(|s| s.to_string()),
            checksum_algorithm: self.config.hash_algorithm,
            min_disk_space: self.config.min_disk_space,
        }
    }

    /// 从 ContextConfig 创建镜像管理器
    pub fn from_context_config(context_config: &ContextConfig) -> Result<Self, String> {
        let mut config = MirrorConfig::from_context_config(context_config);

        // 根据应用模式调整配置
        if context_config.app_mode.is_dev() {
            config.adjust_for_development();
        }

        config.validate()?;
        Ok(Self { config })
    }
}

/// 镜像信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MirrorInfo {
    pub name: String,
    pub filename: String,
    pub download_url: String,
    pub checksum_url: String,
    pub checksum: Option<String>,
    pub checksum_algorithm: HashAlgorithm,
    pub min_disk_space: u64,
}
