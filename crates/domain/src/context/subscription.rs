//! # 事件订阅管理模块
//!
//! 该模块提供框架无关的事件订阅管理功能，支持：
//! - 精确的订阅状态跟踪
//! - 订阅者生命周期管理
//! - 多种桌面框架适配（Tauri、Electron等）
//! - 订阅状态持久化和恢复

use chrono::{DateTime, Utc};
use dashmap::DashMap;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use std::sync::atomic::AtomicU32;

/// 事件类型常量定义
pub mod event_types {
    pub const LOG_MESSAGE: &str = "log_message";
    pub const USER_STATE: &str = "user_state_updated";
    pub const SYSTEM_STATE: &str = "system_state_updated";
    pub const TASK_STATE: &str = "task_state_updated";
    pub const NETWORK_STATE: &str = "network_state_updated";
    pub const SETTINGS_STATE: &str = "settings_state_updated";
    pub const ALL_STATE: &str = "all_state_updated";
    pub const ERROR: &str = "error";
}

/// 订阅者信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriberInfo {
    /// 订阅者唯一标识
    pub id: u32,
    /// 订阅者类型（tauri、electron等）
    pub subscriber_type: &'static str,
    /// 订阅的事件类型列表
    pub subscribed_events: HashSet<String>,
    /// 订阅时间
    pub subscribed_at: DateTime<Utc>,
    /// 最后活跃时间
    pub last_active_at: DateTime<Utc>,
    /// 是否活跃
    pub is_active: bool,
}

impl SubscriberInfo {
    pub fn new(id: u32, subscriber_type: &'static str) -> Self {
        let now = Utc::now();
        Self {
            id,
            subscriber_type,
            subscribed_events: HashSet::new(),
            subscribed_at: now,
            last_active_at: now,
            is_active: true,
        }
    }

    /// 添加事件订阅
    pub fn subscribe_event(&mut self, event_type: String) {
        self.subscribed_events.insert(event_type);
        self.last_active_at = Utc::now();
    }

    /// 取消事件订阅
    pub fn unsubscribe_event(&mut self, event_type: &str) {
        self.subscribed_events.remove(event_type);
        self.last_active_at = Utc::now();
    }

    /// 检查是否订阅了特定事件
    pub fn is_subscribed_to(&self, event_type: &str) -> bool {
        self.is_active && self.subscribed_events.contains(event_type)
    }

    /// 更新活跃状态
    pub fn update_activity(&mut self) {
        self.last_active_at = Utc::now();
        self.is_active = true;
    }

    /// 标记为非活跃
    pub fn mark_inactive(&mut self) {
        self.is_active = false;
    }
}

/// 核心订阅管理器 - 框架无关
pub struct SubscriptionManager {
    /// 订阅者信息映射 subscriber_id -> SubscriberInfo
    subscribers: DashMap<u32, SubscriberInfo>,
    /// 事件类型到订阅者ID的映射 event_type -> Set<subscriber_id>
    event_subscriptions: DashMap<String, HashSet<u32>>,
    /// 启动缓冲期（秒），在此期间所有事件都会被缓存
    startup_buffer_duration: u64,
    /// 应用启动时间
    startup_time: DateTime<Utc>,
    subscriber_id_counter: AtomicU32,
}

impl SubscriptionManager {
    /// 创建新的订阅管理器
    pub fn new() -> Self {
        Self {
            subscribers: DashMap::new(),
            event_subscriptions: DashMap::new(),
            startup_buffer_duration: 10, // 10秒启动缓冲期
            startup_time: Utc::now(),
            subscriber_id_counter: AtomicU32::new(0),
        }
    }

    /// 创建带自定义缓冲期的订阅管理器
    pub fn with_startup_buffer(startup_buffer_duration: u64) -> Self {
        Self {
            subscribers: DashMap::new(),
            event_subscriptions: DashMap::new(),
            startup_buffer_duration,
            startup_time: Utc::now(),
            subscriber_id_counter: AtomicU32::new(0),
        }
    }

    /// 注册新的订阅者
    pub fn register_subscriber(&self, subscriber_type: &'static str) -> anyhow::Result<u32> {
        let subscriber_id = self
            .subscriber_id_counter
            .fetch_add(1, std::sync::atomic::Ordering::SeqCst);
        let subscriber_info = SubscriberInfo::new(subscriber_id, subscriber_type);

        self.subscribers.insert(subscriber_id, subscriber_info);

        tracing::info!(subscriber_id = subscriber_id, "New subscriber registered");

        Ok(subscriber_id)
    }

    /// 注销订阅者
    pub fn unregister_subscriber(&self, subscriber_id: u32) -> anyhow::Result<()> {
        // 从订阅者列表中移除
        let removed_subscriber = self.subscribers.remove(&subscriber_id);

        if let Some((_, subscriber)) = removed_subscriber {
            // 从事件订阅映射中移除
            for event_type in subscriber.subscribed_events {
                if let Some(mut subscriber_set) = self.event_subscriptions.get_mut(&event_type) {
                    subscriber_set.remove(&subscriber_id);
                    // 如果该事件类型没有任何订阅者，删除整个条目
                    if subscriber_set.is_empty() {
                        drop(subscriber_set);
                        self.event_subscriptions.remove(&event_type);
                    }
                }
            }

            tracing::info!(
                subscriber_id = subscriber_id,
                subscriber_type = %subscriber.subscriber_type,
                "Subscriber unregistered"
            );
        }

        Ok(())
    }

    /// 订阅事件
    pub fn subscribe_event(&self, subscriber_id: u32, event_type: String) -> anyhow::Result<()> {
        // 更新订阅者信息
        if let Some(mut subscriber) = self.subscribers.get_mut(&subscriber_id) {
            subscriber.subscribe_event(event_type.clone());
        } else {
            return Err(anyhow::anyhow!("Subscriber not found: {}", subscriber_id));
        }

        // 更新事件订阅映射
        self.event_subscriptions
            .entry(event_type.clone())
            .or_insert_with(HashSet::new)
            .insert(subscriber_id);

        tracing::info!(
            subscriber_id = subscriber_id,
            event_type = %event_type,
            "Event subscribed"
        );

        Ok(())
    }

    /// 取消订阅事件
    pub fn unsubscribe_event(&self, subscriber_id: u32, event_type: &str) -> anyhow::Result<()> {
        // 更新订阅者信息
        if let Some(mut subscriber) = self.subscribers.get_mut(&subscriber_id) {
            subscriber.unsubscribe_event(event_type);
        } else {
            return Err(anyhow::anyhow!("Subscriber not found: {}", subscriber_id));
        }

        // 更新事件订阅映射
        if let Some(mut subscriber_set) = self.event_subscriptions.get_mut(event_type) {
            subscriber_set.remove(&subscriber_id);
            // 如果该事件类型没有任何订阅者，删除整个条目
            if subscriber_set.is_empty() {
                drop(subscriber_set);
                self.event_subscriptions.remove(event_type);
            }
        }

        tracing::info!(
            subscriber_id = subscriber_id,
            event_type = %event_type,
            "Event unsubscribed"
        );

        Ok(())
    }

    /// 检查是否有活跃的订阅者订阅了特定事件
    pub fn has_active_subscription(&self, event_type: &str) -> bool {
        // 检查是否有真实的活跃订阅者
        if let Some(subscriber_ids) = self.event_subscriptions.get(event_type) {
            for subscriber_id in subscriber_ids.iter() {
                if let Some(subscriber) = self.subscribers.get(subscriber_id) {
                    if subscriber.is_active && subscriber.is_subscribed_to(event_type) {
                        return true;
                    }
                }
            }
        }

        // 只有在启动缓冲期内且没有任何订阅者时才返回 true
        self.is_in_startup_buffer() && self.subscribers.is_empty()
    }

    /// 获取订阅了特定事件的活跃订阅者列表
    pub fn get_active_subscribers(&self, event_type: &str) -> Vec<u32> {
        let mut active_subscribers = Vec::new();

        if let Some(subscriber_ids) = self.event_subscriptions.get(event_type) {
            for subscriber_id in subscriber_ids.iter() {
                if let Some(subscriber) = self.subscribers.get(subscriber_id) {
                    if subscriber.is_subscribed_to(event_type) {
                        active_subscribers.push(*subscriber_id);
                    }
                }
            }
        }

        active_subscribers
    }

    /// 更新订阅者活跃状态
    pub fn update_subscriber_activity(&self, subscriber_id: u32) -> anyhow::Result<()> {
        if let Some(mut subscriber) = self.subscribers.get_mut(&subscriber_id) {
            subscriber.update_activity();
        }

        Ok(())
    }

    /// 标记订阅者为非活跃状态
    pub fn mark_subscriber_inactive(&self, subscriber_id: u32) -> anyhow::Result<()> {
        if let Some(mut subscriber) = self.subscribers.get_mut(&subscriber_id) {
            subscriber.mark_inactive();
            tracing::warn!(
                subscriber_id = subscriber_id,
                "Subscriber marked as inactive"
            );
        }

        Ok(())
    }

    /// 检查是否在启动缓冲期内
    pub fn is_in_startup_buffer(&self) -> bool {
        let elapsed = Utc::now().timestamp() - self.startup_time.timestamp();
        elapsed < self.startup_buffer_duration as i64
    }

    /// 获取订阅统计信息
    pub fn get_subscription_stats(&self) -> anyhow::Result<SubscriptionStats> {
        let total_subscribers = self.subscribers.len();
        let active_subscribers = self
            .subscribers
            .iter()
            .filter(|entry| entry.value().is_active)
            .count();
        let total_event_types = self.event_subscriptions.len();

        let mut event_subscriber_counts = HashMap::new();
        for entry in self.event_subscriptions.iter() {
            let event_type = entry.key().clone();
            let subscriber_ids = entry.value();
            let active_count = subscriber_ids
                .iter()
                .filter_map(|id| self.subscribers.get(id))
                .filter(|s| s.is_active)
                .count();
            event_subscriber_counts.insert(event_type, active_count);
        }

        Ok(SubscriptionStats {
            total_subscribers,
            active_subscribers,
            total_event_types,
            event_subscriber_counts,
            is_in_startup_buffer: self.is_in_startup_buffer(),
        })
    }

    /// 清理非活跃的订阅者
    pub fn cleanup_inactive_subscribers(&self) -> anyhow::Result<usize> {
        let inactive_threshold = chrono::Duration::minutes(5); // 5分钟无活动视为非活跃
        let now = Utc::now();

        let mut subscribers_to_remove = Vec::new();

        for entry in self.subscribers.iter() {
            let subscriber_id = *entry.key();
            let subscriber = entry.value();
            if !subscriber.is_active || (now - subscriber.last_active_at) > inactive_threshold {
                subscribers_to_remove.push(subscriber_id);
            }
        }

        let removed_count = subscribers_to_remove.len();
        for subscriber_id in subscribers_to_remove {
            self.unregister_subscriber(subscriber_id)?;
        }

        if removed_count > 0 {
            tracing::info!(
                removed_count = removed_count,
                "Cleaned up inactive subscribers"
            );
        }

        Ok(removed_count)
    }
}

impl Default for SubscriptionManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 订阅统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionStats {
    pub total_subscribers: usize,
    pub active_subscribers: usize,
    pub total_event_types: usize,
    pub event_subscriber_counts: HashMap<String, usize>,
    pub is_in_startup_buffer: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_subscription_manager_basic() {
        let manager = SubscriptionManager::new();

        // 注册订阅者
        let subscriber_id = manager.register_subscriber("tauri").unwrap();

        // 订阅事件
        manager
            .subscribe_event(subscriber_id, event_types::LOG_MESSAGE.to_string())
            .unwrap();

        // 检查订阅状态
        assert!(manager.has_active_subscription(event_types::LOG_MESSAGE));
        assert!(!manager.has_active_subscription(event_types::USER_STATE));

        // 取消订阅
        manager
            .unsubscribe_event(subscriber_id, event_types::LOG_MESSAGE)
            .unwrap();

        // 注销订阅者
        manager.unregister_subscriber(subscriber_id).unwrap();
    }

    #[test]
    fn test_startup_buffer() {
        let manager = SubscriptionManager::with_startup_buffer(1); // 1秒缓冲期

        // 在缓冲期内，所有事件都应该有订阅
        assert!(manager.has_active_subscription(event_types::LOG_MESSAGE));

        // 等待缓冲期结束
        std::thread::sleep(std::time::Duration::from_secs(2));

        // 缓冲期结束后，没有真实订阅者的事件应该返回false
        assert!(!manager.has_active_subscription(event_types::LOG_MESSAGE));
    }

    #[test]
    fn test_multiple_subscribers() {
        let manager = SubscriptionManager::with_startup_buffer(0); // 无缓冲期

        let subscriber1 = manager.register_subscriber("tauri").unwrap();
        let subscriber2 = manager.register_subscriber("electron").unwrap();

        manager
            .subscribe_event(subscriber1, event_types::LOG_MESSAGE.to_string())
            .unwrap();
        manager
            .subscribe_event(subscriber2, event_types::LOG_MESSAGE.to_string())
            .unwrap();

        let active_subscribers = manager.get_active_subscribers(event_types::LOG_MESSAGE);
        assert_eq!(active_subscribers.len(), 2);

        // 一个订阅者取消订阅
        manager
            .unsubscribe_event(subscriber1, event_types::LOG_MESSAGE)
            .unwrap();

        let active_subscribers = manager.get_active_subscribers(event_types::LOG_MESSAGE);
        assert_eq!(active_subscribers.len(), 1);

        // 仍然有订阅者
        assert!(manager.has_active_subscription(event_types::LOG_MESSAGE));
    }
}
