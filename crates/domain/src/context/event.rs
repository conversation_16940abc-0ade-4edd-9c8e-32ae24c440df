use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;

use crate::logging::{LogLevel, TraceId};

use super::state;

/// Core事件枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CoreEvent {
    // 状态更新事件
    StateUpdated {
        state_type: state::StateType,
        data: serde_json::Value,
        trace_id: TraceId,
    },

    // 日志事件
    LogMessage {
        level: LogLevel,
        message: String,
        module: String,
        timestamp: String,
        trace_id: Option<TraceId>,
    },

    // 错误事件
    Error {
        module: String,
        message: String,
        trace_id: TraceId,
    },
}

impl CoreEvent {
    pub fn event_name(&self) -> &'static str {
        match self {
            CoreEvent::StateUpdated { state_type, .. } => match state_type {
                state::StateType::User => "user_state_updated",
                state::StateType::System => "system_state_updated",
                state::StateType::Task => "task_state_updated",
                state::StateType::Network => "network_state_updated",
                state::StateType::Settings => "settings_state_updated",
                state::StateType::Computed => "computed_state_updated",
                state::StateType::Device => "device_state_updated",
                state::StateType::All => "all_state_updated",
            },
            CoreEvent::LogMessage { .. } => "log_message",
            CoreEvent::Error { .. } => "error",
        }
    }
}

/// 事件发射器
pub struct EventEmitter {
    pub desktop_tx: mpsc::Sender<CoreEvent>,
}
impl EventEmitter {
    pub async fn emit(&self, event: CoreEvent) -> anyhow::Result<()> {
        self.desktop_tx
            .send(event)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to emit event: {}", e))?;
        Ok(())
    }
}
