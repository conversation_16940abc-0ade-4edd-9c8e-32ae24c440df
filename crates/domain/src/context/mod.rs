//! # EchoWave 核心上下文模块
//!
//! 该模块提供了 EchoWave 客户端应用程序的全局上下文管理功能，是整个应用程序的
//! 核心协调器和状态管理中心。
//!
//! ## 主要职责
//!
//! ### 1. 状态投射和管理
//! - 各个业务服务维护自己的权威状态，作为单一数据源
//! - 通过 [`StateProjection`] 将服务状态投射到前端，而不是存储重复的状态
//! - 提供初始化状态聚合和跨服务状态计算功能
//! - 支持平台无关的状态投射机制，可适配 Tauri、Electron(By napi-rs) 等前端技术栈
//!
//! ### 2. 服务编排和协调
//! - 集中管理所有业务服务组件 ([`Services`])
//! - 协调系统检查服务、用户服务、网络服务、任务接单服务等
//! - 提供统一的服务访问接口和生命周期管理
//!
//! ### 3. 命令处理和事件分发
//! - 处理来自 UI 层的业务命令 ([`CoreCommand`])
//! - 执行相应的业务逻辑并返回结果 ([`CommandResponse`])
//! - 通过异步消息通道向前端发送状态更新事件
//!
//! ### 4. 应用程序生命周期管理
//! - 管理应用程序的启动、运行和关闭流程
//! - 防止重复创建上下文实例 (单例模式)
//! - 提供优雅的资源清理和关闭机制
//!
//! ## 架构设计
//!
//! 该模块采用分层架构设计，以 StateProjection 作为状态投射层：
//!
//! ```text
//! ┌─────────────────────────────────────────────────────────────┐
//! │                    前端 UI (Vue/Pinia)                      │
//! └─────────────────────────┬───────────────────────────────────┘
//!                           │ 异步消息通道 (平台无关)
//! ┌─────────────────────────▼───────────────────────────────────┐
//! │                      StateProjection                        │
//! │  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
//! │  │   状态投射接口   │ │   跨服务计算     │ │   消息通道管理   │ │
//! │  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
//! └─────────────────────────┬───────────────────────────────────┘
//!                           │
//! ┌─────────────────────────▼───────────────────────────────────┐
//! │              业务服务层 (Services)                           │
//! │  SystemCheck │ User │ Network │ Task │ Settings │ Update    │
//! │  (权威状态)  │(权威状态)│(权威状态)│(权威状态)│(权威状态)│    │
//! └─────────────────────────┬───────────────────────────────────┘
//!                           │
//! ┌─────────────────────────▼───────────────────────────────────┐
//! │                WSL Agent / 外部服务                         │
//! │          Docker │ Nomad │ Tailscale │ 服务端 API            │
//! └─────────────────────────────────────────────────────────────┘
//! ```
//!
//! ## StateProjection 设计
//!
//! StateProjection 作为状态投射层，具有以下特性：
//!
//! ### 1. 平台无关性
//! - 通过异步消息通道与前端通信，不依赖特定的前端技术栈
//! - 支持 Tauri、Electron(By napi-rs) 等多种前端框架
//! - 可通过前端适配器模式扩展支持新的前端技术
//!
//! ### 2. 状态投射职责
//! - **初始化状态聚合**: 应用启动时收集所有服务的初始状态
//! - **状态变更投射**: 将服务状态变更推送到前端，实现响应式更新
//! - **跨服务计算**: 提供基于多个服务状态的计算属性
//! - **消息路由**: 统一管理所有状态变更消息的路由和分发
//!
//! ### 3. 消息通道机制
//! - 使用 `tokio::sync::mpsc` 实现异步消息传递
//! - 支持消息缓冲和批处理优化
//! - 提供背压控制和错误恢复机制
//!
//! ### 4. 状态消息类型
//! - `UserStateChanged`: 用户状态变更
//! - `SystemStateChanged`: 系统状态变更
//! - `TaskStateChanged`: 任务状态变更
//! - `NetworkStateChanged`: 网络状态变更
//! - `SettingsStateChanged`: 设置状态变更
//! - `ComputedStateChanged`: 计算属性状态变更
//! - `InitialState`: 初始化状态聚合
//!
//! ## 使用示例
//!
//! ### 基本用法
//!
//! ```rust,no_run
//! use tokio::sync::mpsc;
//! use crate::config::ContextConfig;
//! use crate::context::{create_context, CoreCommand, StateProjection};
//! use uuid::Uuid;
//!
//! // 创建状态投射消息通道
//! let (state_tx, state_rx) = mpsc::unbounded_channel();
//!
//! // 创建全局上下文
//! let config = ContextConfig::default();
//! let context = create_context(state_tx, config).await?;
//!
//! // 执行系统检查命令
//! let command = CoreCommand::TriggerSystemCheck {
//!     trace_id: Uuid::new_v4(),
//! };
//! let response = context.execute_command(command).await?;
//! ```
//!
//! ### StateProjection 用法
//!
//! ```rust,no_run
//! // 服务通过 StateProjection 投射状态变更
//! let user_state = UserState::new();
//! state_projection.project_user_state(user_state).await?;
//!
//! // 获取初始化状态
//! let initial_state = state_projection.get_initial_state().await?;
//!
//! // 计算跨服务属性
//! let can_accept = state_projection.can_accept_tasks().await?;
//! ```
//!
//! ## 并发安全性
//!
//! 该模块设计为完全线程安全：
//! - 各服务内部状态使用 `Arc<RwLock<T>>` 或 Actor 模式管理
//! - 服务组件使用 `Arc<T>` 共享，支持并发访问
//! - StateProjection 使用 `mpsc::UnboundedSender` 实现异步消息传递
//! - 使用 `CancellationToken` 支持优雅的任务取消
//!
//! ## 状态管理最佳实践
//!
//! ### 1. 单一数据源原则
//! - 每个服务是其领域状态的权威来源
//! - 避免在多个地方存储相同的状态数据
//! - 通过 StateProjection 统一管理状态投射
//!
//! ### 2. 响应式更新
//! - 服务状态变更时立即通过 StateProjection 推送到前端
//! - 前端通过监听消息实现响应式状态更新
//! - 避免轮询获取状态，使用事件驱动的方式
//!
//! ### 3. 跨服务协调
//! - 通过 StateProjection 提供的跨服务计算属性处理复杂业务逻辑
//! - 服务间通过消息或直接调用进行协调
//! - 避免在前端处理复杂的业务逻辑
//!
//! ## 错误处理
//!
//! 所有操作都返回 `Result<T, anyhow::Error>`，提供统一的错误处理机制。
//! 错误会被记录到日志系统并通过消息通道通知前端。
use crate::{
    config::ContextConfig,
    logging::TraceId,
    services::{
        ServiceManager, device_service::DeviceService, network_service::NetworkService,
        settings_service::SettingsService, system_check::SystemCheckService,
        task_acceptance_service::TaskAcceptanceService, update_service::UpdateService,
        user_service::UserService,
    },
};
use std::sync::{
    Arc, LazyLock,
    atomic::{AtomicBool, Ordering},
};
use tokio::sync::mpsc;
use tokio_util::sync::CancellationToken;
use tracing::{info, warn};

pub mod command;
pub mod event;
pub mod state;
pub mod subscription;

/// 全局应用上下文
pub struct GlobalContext {
    /// 业务服务层
    pub services: Services,
    /// 状态投射层
    pub state_projection: state::StateProjection,
    /// 订阅管理器
    pub subscription_manager: Arc<subscription::SubscriptionManager>,
    /// 配置信息
    pub config: Arc<ContextConfig>,
    pub tasks: Vec<tokio::task::JoinHandle<()>>,
    /// 任务取消令牌
    pub cancellation_token: CancellationToken,
}

/// 业务服务层
#[derive(Clone)]
pub struct Services {
    pub system_check: Arc<SystemCheckService>,
    pub user_service: Arc<UserService>,
    pub network_service: Arc<NetworkService>,
    pub settings_service: Arc<SettingsService>,
    pub task_acceptance_service: Arc<TaskAcceptanceService>,
    pub update_service: Arc<UpdateService>,
    pub device_service: Arc<DeviceService>,
}

// 用于防止上下文被重复创建
static CONTEXT_CREATED: AtomicBool = AtomicBool::new(false);
static GLOBAL_TRACE_ID: LazyLock<TraceId> = LazyLock::new(|| TraceId::from_u64_pair(0, 0));

pub fn get_global_trace_id() -> TraceId {
    *GLOBAL_TRACE_ID
}

pub fn create_context(
    desktop_tx: mpsc::Sender<event::CoreEvent>,
    config: ContextConfig,
) -> anyhow::Result<GlobalContext> {
    if CONTEXT_CREATED.swap(true, Ordering::Relaxed) {
        return Err(anyhow::anyhow!("GlobalContext already created"));
    }
    let trace_id = *GLOBAL_TRACE_ID;
    let config = Arc::new(config);

    // 创建订阅管理器
    let subscription_manager = Arc::new(subscription::SubscriptionManager::new());

    // 初始化日志
    // 将 UI Layer 的日志，通过 CoreEvent + desktop_tx 发送给前端
    if let Err(err) = crate::logging::registry_logs(
        config.clone(),
        desktop_tx.clone(),
        subscription_manager.clone(),
    ) {
        anyhow::bail!("Failed to registry logs: {}", err);
    }
    let span = tracing::span!(tracing::Level::INFO, "create_context",trace_id = %trace_id);
    let _enter = span.enter();

    info!(
        "Creating GlobalContext with mode {}",
        config.app_mode.as_str()
    );
    info!("GlobalContext trace_id: {}", trace_id);
    let signal = CancellationToken::new();
    // 创建服务管理器
    let service_manager = ServiceManager::new(config.clone(), signal.clone())
        .map_err(|e| anyhow::anyhow!("Failed to create service manager: {}", e))?;
    // 创建服务结构
    let services = Services {
        system_check: service_manager.system_check.clone(),
        user_service: service_manager.user.clone(),
        network_service: service_manager.network.clone(),
        settings_service: service_manager.settings.clone(),
        task_acceptance_service: service_manager.task_acceptance.clone(),
        update_service: service_manager.update.clone(),
        device_service: service_manager.device.clone(),
    };
    // 创建状态消息通道
    let (state_tx, mut state_rx) = mpsc::channel::<state::StateMessage>(100);

    // 创建状态投射层
    let state_projection = state::StateProjection::new(services.clone(), state_tx.clone());

    let lifecycle_manager = ServiceManager::start_lifecycle_manager(service_manager);
    // 启动状态消息路由任务
    let desktop_tx_clone = desktop_tx.clone();
    let event_loop = tokio::spawn(async move {
        tracing::info!("Starting event loop");
        while let Some(state_message) = state_rx.recv().await {
            if let Err(e) = route_state_message(state_message, &desktop_tx_clone).await {
                tracing::error!("Failed to route state message: {}", e);
            }
        }
        tracing::info!("Event loop stopped");
    });

    // 启动状态监听器
    let mut tasks = Vec::new();
    state_projection.start_state_watchers(&mut tasks, signal.clone());
    state_projection.start_computed_properties_watcher(&mut tasks, signal.clone());
    tasks.push(lifecycle_manager);
    tasks.push(event_loop);

    let context = GlobalContext {
        services,
        state_projection,
        subscription_manager,
        config,
        tasks,
        cancellation_token: signal,
    };

    info!("GlobalContext created successfully");
    Ok(context)
}

/// 路由状态消息到前端
async fn route_state_message(
    state_message: state::StateMessage,
    desktop_tx: &mpsc::Sender<event::CoreEvent>,
) -> anyhow::Result<()> {
    let core_event = match state_message {
        state::StateMessage::UserStateChanged(user_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::User,
                data: serde_json::to_value(user_state)?,
                trace_id,
            }
        }
        state::StateMessage::SystemStateChanged(system_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::System,
                data: serde_json::to_value(system_state)?,
                trace_id,
            }
        }
        state::StateMessage::TaskStateChanged(task_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::Task,
                data: serde_json::to_value(task_state)?,
                trace_id,
            }
        }
        state::StateMessage::NetworkStateChanged(network_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::Network,
                data: serde_json::to_value(network_state)?,
                trace_id,
            }
        }
        state::StateMessage::SettingsStateChanged(settings_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::Settings,
                data: serde_json::to_value(settings_state)?,
                trace_id,
            }
        }
        state::StateMessage::DeviceStateChanged(device_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::Device,
                data: serde_json::to_value(device_state)?,
                trace_id,
            }
        }
        state::StateMessage::ComputedStateChanged(computed_properties, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::Computed,
                data: serde_json::to_value(computed_properties)?,
                trace_id,
            }
        }
        state::StateMessage::InitialState(projected_state, trace_id) => {
            event::CoreEvent::StateUpdated {
                state_type: state::StateType::All,
                data: serde_json::to_value(projected_state)?,
                trace_id,
            }
        }
    };

    desktop_tx
        .send(core_event)
        .await
        .map_err(|e| anyhow::anyhow!("Failed to send core event: {}", e))?;

    Ok(())
}

impl GlobalContext {
    /// 处理注册订阅者命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, subscriber_type = %subscriber_type))]
    pub fn register_subscriber(
        &self,
        subscriber_type: &'static str,
        trace_id: TraceId,
    ) -> anyhow::Result<u32> {
        info!("Registering new subscriber: {}", subscriber_type);

        let subscriber_id = self
            .subscription_manager
            .register_subscriber(subscriber_type)
            .map_err(|e| anyhow::anyhow!("Failed to register subscriber: {}", e))?;

        Ok(subscriber_id)
    }

    /// 处理注销订阅者命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, subscriber_id = %subscriber_id))]
    pub fn unregister_subscriber(
        &self,
        subscriber_id: u32,
        trace_id: TraceId,
    ) -> anyhow::Result<()> {
        info!("Unregistering subscriber: {}", subscriber_id);

        self.subscription_manager
            .unregister_subscriber(subscriber_id)
            .map_err(|e| anyhow::anyhow!("Failed to unregister subscriber: {}", e))?;

        Ok(())
    }

    /// 处理订阅事件命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, subscriber_id = %subscriber_id, event_type = %event_type))]
    pub fn subscribe_event(
        &self,
        subscriber_id: u32,
        event_type: String,
        trace_id: TraceId,
    ) -> anyhow::Result<()> {
        info!(
            "Subscribing to event: {} for subscriber: {}",
            event_type, subscriber_id
        );

        self.subscription_manager
            .subscribe_event(subscriber_id, event_type)
            .map_err(|e| anyhow::anyhow!("Failed to subscribe to event: {}", e))?;

        Ok(())
    }

    /// 处理取消订阅事件命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, subscriber_id = %subscriber_id, event_type = %event_type))]
    pub fn unsubscribe_event(
        &self,
        subscriber_id: u32,
        event_type: String,
        trace_id: TraceId,
    ) -> anyhow::Result<()> {
        info!(
            "Unsubscribing from event: {} for subscriber: {}",
            event_type, subscriber_id
        );

        self.subscription_manager
            .unsubscribe_event(subscriber_id, &event_type)
            .map_err(|e| anyhow::anyhow!("Failed to unsubscribe from event: {}", e))?;

        Ok(())
    }
}

pub async fn destroy_context(context: GlobalContext) -> anyhow::Result<()> {
    let trace_id = crate::context::get_global_trace_id();
    let span = tracing::info_span!("destroy_context", trace_id = %trace_id);
    let _enter = span.enter();

    info!("Starting GlobalContext destruction");

    // 1. 触发所有任务的优雅取消
    info!("Triggering cancellation for all tasks");
    context.cancellation_token.cancel();

    // 2. 等待任务优雅停止，然后清理剩余任务
    info!(
        "Waiting for {} background tasks to finish gracefully",
        context.tasks.len()
    );
    for task in context.tasks {
        // 等待任务优雅结束（最多3秒）
        if let Err(_) = tokio::time::timeout(std::time::Duration::from_secs(3), task).await {
            warn!(
                "Task did not finish within timeout, task was likely already completed or cancelled"
            );
        }
    }

    // 3. 停止所有服务
    info!("Shutting down services");

    // 停止任务接单服务
    if let Err(e) = context
        .services
        .task_acceptance_service
        .stop_accepting_tasks()
        .await
    {
        warn!("Error stopping task acceptance service: {}", e);
    }

    // 4. 关闭网络连接和外部进程
    info!("Cleaning up network and external processes");
    // 这里会通过各服务的Drop实现来清理资源

    // 5. 重置全局状态
    CONTEXT_CREATED.store(false, Ordering::Relaxed);

    info!("GlobalContext destruction completed");
    Ok(())
}
