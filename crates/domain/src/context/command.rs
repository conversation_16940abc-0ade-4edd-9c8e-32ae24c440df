use std::path::PathBuf;
use std::sync::Arc;

use serde::{Deserialize, Serialize};
use tracing::{error, info, instrument, warn};

use crate::{
    logging::{StructuredLogEntry, TraceId},
    services::{TaskAcceptanceStatus, UserSettingsState},
};

/// Core命令枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CoreCommand {
    /// 系统检查命令
    TriggerSystemCheck {
        trace_id: TraceId,
    },

    /// 用户使用密码登录命令
    Login {
        username: String,
        password: String,
        trace_id: TraceId,
    },
    /// 用户使用验证码登录命令
    LoginWithCode {
        phone: String,
        code: String,
        trace_id: TraceId,
    },
    /// 发送验证码命令
    SendVerificationCode {
        phone: String,
        trace_id: TraceId,
    },
    /// 用户登出命令
    Logout {
        trace_id: TraceId,
    },
    /// 刷新钱包余额命令
    RefreshWalletBalance {
        trace_id: TraceId,
    },
    /// 打开外部页面命令
    OpenExternalPage {
        page_type: ExternalPageType,
        trace_id: TraceId,
    },
    /// 切换任务接单状态命令
    ToggleTaskAcceptance {
        trace_id: TraceId,
    },
    /// 网络管理命令
    RefreshNetworkStatus {
        trace_id: TraceId,
    },
    /// 设置管理命令
    UpdateSettings {
        settings: UserSettingsState,
        trace_id: TraceId,
    },
    /// 设置数据目录命令
    SetDataRootPath {
        new_path: PathBuf,
        trace_id: TraceId,
    },
    /// 通用命令
    GetCurrentState {
        trace_id: TraceId,
    },
    /// 更新窗口焦点
    UpdateWindowsFocus {
        focused: bool,
        trace_id: TraceId,
    },
    /// 推送日志
    PushLog {
        entry: StructuredLogEntry,
    },
    PushBatchLogs {
        entries: Vec<StructuredLogEntry>,
    },
}
/// 命令响应
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum CommandResponse {
    Success,
    Error { message: String },
}
/// 外部页面类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExternalPageType {
    Register,
    ForgotPassword,
    Faq,
}

impl CoreCommand {
    pub fn trace_id(&self) -> TraceId {
        match self {
            CoreCommand::TriggerSystemCheck { trace_id } => *trace_id,
            CoreCommand::Login { trace_id, .. } => *trace_id,
            CoreCommand::LoginWithCode { trace_id, .. } => *trace_id,
            CoreCommand::SendVerificationCode { trace_id, .. } => *trace_id,
            CoreCommand::Logout { trace_id } => *trace_id,
            CoreCommand::RefreshWalletBalance { trace_id } => *trace_id,
            CoreCommand::OpenExternalPage { trace_id, .. } => *trace_id,
            CoreCommand::ToggleTaskAcceptance { trace_id } => *trace_id,
            CoreCommand::RefreshNetworkStatus { trace_id } => *trace_id,
            CoreCommand::UpdateSettings { trace_id, .. } => *trace_id,
            CoreCommand::SetDataRootPath { trace_id, .. } => *trace_id,
            CoreCommand::GetCurrentState { trace_id } => *trace_id,
            CoreCommand::UpdateWindowsFocus { trace_id, .. } => *trace_id,
            // PushLog 属于系统内部事件，并非用户主动触发，使用全局 trace_id
            CoreCommand::PushLog { .. } => *super::GLOBAL_TRACE_ID,
            CoreCommand::PushBatchLogs { .. } => *super::GLOBAL_TRACE_ID,
        }
    }
}

impl super::GlobalContext {
    /// 执行核心命令
    #[instrument(skip(self), fields(trace_id = %command.trace_id()))]
    pub async fn execute_command(&self, command: CoreCommand) -> anyhow::Result<CommandResponse> {
        match command {
            CoreCommand::TriggerSystemCheck { trace_id } => {
                self.handle_system_check(trace_id).await
            }

            CoreCommand::Login {
                username,
                password,
                trace_id,
            } => self.handle_login(username, password, trace_id).await,

            CoreCommand::LoginWithCode {
                phone,
                code,
                trace_id,
            } => self.handle_login_with_code(phone, code, trace_id).await,

            CoreCommand::SendVerificationCode { phone, trace_id } => {
                self.handle_send_verification_code(phone, trace_id).await
            }

            CoreCommand::Logout { trace_id } => self.handle_logout(trace_id).await,

            CoreCommand::RefreshWalletBalance { trace_id } => {
                self.handle_refresh_wallet_balance(trace_id).await
            }
            CoreCommand::OpenExternalPage {
                page_type,
                trace_id,
            } => self.handle_open_external_page(page_type, trace_id).await,

            CoreCommand::ToggleTaskAcceptance { trace_id } => {
                self.handle_toggle_task_acceptance(trace_id).await
            }

            CoreCommand::RefreshNetworkStatus { trace_id } => {
                self.handle_refresh_network_status(trace_id).await
            }

            CoreCommand::UpdateSettings { settings, trace_id } => {
                self.handle_update_settings(settings, trace_id).await
            }

            CoreCommand::SetDataRootPath { new_path, trace_id } => {
                self.handle_set_data_root_path(new_path, trace_id).await
            }

            CoreCommand::GetCurrentState { trace_id } => {
                self.handle_get_current_state(trace_id).await
            }

            CoreCommand::UpdateWindowsFocus { focused, trace_id } => {
                self.handle_update_windows_focus(focused, trace_id).await
            }

            CoreCommand::PushLog { entry } => self.handle_push_log(entry).await,
            CoreCommand::PushBatchLogs { entries } => self.handle_push_batch_logs(entries).await,
        }
    }

    /// 处理系统检查命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_system_check(&self, trace_id: TraceId) -> anyhow::Result<CommandResponse> {
        info!("Triggering system check");

        // 调用SystemCheckService执行检查
        self.services.system_check.run_all_checks(trace_id).await;

        Ok(CommandResponse::Success)
    }

    /// 处理用户登录命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_login(
        &self,
        username: String,
        password: String,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Handling user login for: {}", username);

        // 调用UserService执行登录
        self.services
            .user_service
            .login(username, password, trace_id.into())
            .await?;

        Ok(CommandResponse::Success)
    }

    /// 处理验证码登录命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_login_with_code(
        &self,
        phone: String,
        code: String,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Handling verification code login for: {}", phone);

        // 调用UserService执行验证码登录
        self.services
            .user_service
            .login_with_code(phone, code, trace_id.into())
            .await?;

        Ok(CommandResponse::Success)
    }

    /// 处理发送验证码命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_send_verification_code(
        &self,
        phone: String,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Sending verification code to: {}", phone);

        // 直接调用UserService的send_verification_code方法
        self.services
            .user_service
            .send_verification_code(phone.clone(), trace_id)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send verification code: {}", e))?;

        info!("Verification code sent successfully to: {}", phone);
        Ok(CommandResponse::Success)
    }

    /// 处理用户登出命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_logout(&self, trace_id: TraceId) -> anyhow::Result<CommandResponse> {
        info!("Handling user logout");

        // 调用UserService执行登出
        self.services.user_service.logout(trace_id).await?;

        Ok(CommandResponse::Success)
    }

    /// 处理刷新钱包余额命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_refresh_wallet_balance(
        &self,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Refreshing wallet balance");

        // 调用 UserService 获取余额
        self.services
            .user_service
            .refresh_wallet_balance(trace_id)
            .await?;

        Ok(CommandResponse::Success)
    }

    /// 处理打开外部页面命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, page_type = ?page_type))]
    async fn handle_open_external_page(
        &self,
        page_type: ExternalPageType,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Opening external page: {:?}", page_type);

        // 获取配置中的portal_url
        let portal_url = &self.config.environment.portal_url;

        // 根据页面类型构建完整URL
        let full_url = match page_type {
            ExternalPageType::Register => format!("{}/register", portal_url),
            ExternalPageType::ForgotPassword => format!("{}/login?flag=updatePwdForm", portal_url),
            ExternalPageType::Faq => format!("{}/faq", portal_url),
        };

        // 使用系统默认浏览器打开URL
        if let Err(e) = webbrowser::open(&full_url) {
            error!("Failed to open external page: {}", e);
            return Err(anyhow::anyhow!("Failed to open external page: {}", e));
        }

        info!("Successfully opened external page: {}", full_url);
        Ok(CommandResponse::Success)
    }

    /// 处理切换任务接单命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_toggle_task_acceptance(
        &self,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Toggling task acceptance");

        // 获取当前接单状态并切换
        let task_service = Arc::clone(&self.services.task_acceptance_service);
        let current_acceptance = task_service.get_current_acceptance();

        // 根据当前状态决定操作
        match current_acceptance {
            TaskAcceptanceStatus::Stopped => {
                task_service.start_accepting_tasks().await?;
            }
            TaskAcceptanceStatus::Accepting => {
                task_service.stop_accepting_tasks().await?;
            }
            _ => {
                warn!("不允许在当前状态切换接单状态: {:?}", current_acceptance);
                return Err(anyhow::anyhow!(
                    "Not allowed to toggle task acceptance: {:?}",
                    current_acceptance
                ));
            }
        };

        Ok(CommandResponse::Success)
    }

    /// 处理刷新网络状态命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_refresh_network_status(
        &self,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Refreshing network status");

        // todo: 刷新网络状态或移除该命令

        Ok(CommandResponse::Success)
    }

    /// 处理更新设置命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_update_settings(
        &self,
        settings: UserSettingsState,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Updating settings");

        // 调用SettingsService保存设置
        let settings_service = Arc::clone(&self.services.settings_service);
        settings_service
            .update_settings(settings.clone(), trace_id)
            .await
            .map_err(|e| anyhow::anyhow!("Failed to update settings: {}", e))?;

        Ok(CommandResponse::Success)
    }

    /// 处理获取当前状态命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id))]
    async fn handle_get_current_state(&self, trace_id: TraceId) -> anyhow::Result<CommandResponse> {
        self.state_projection
            .project_initial_state(trace_id)
            .await?;
        Ok(CommandResponse::Success)
    }

    /// 处理更新窗口焦点命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, focused = %focused))]
    async fn handle_update_windows_focus(
        &self,
        focused: bool,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("窗口焦点更新: {}", focused);
        self.services
            .user_service
            .on_focus_changed(focused, trace_id)
            .await;
        Ok(CommandResponse::Success)
    }

    /// 处理推送日志命令
    #[tracing::instrument(skip(self, entry))]
    async fn handle_push_log(&self, entry: StructuredLogEntry) -> anyhow::Result<CommandResponse> {
        // 构建完整的日志字段
        let trace_id = entry.trace_id.unwrap_or_default();
        let timestamp = entry.timestamp;
        let file = entry.file.as_deref().unwrap_or("unknown");
        let line = entry.line.unwrap_or(0);

        // 将日志条目推送到 tracing 系统，保持所有字段信息
        match entry.level.as_str() {
            "error" => tracing::error!(
                target: "web",
                trace_id = %trace_id,
                timestamp = timestamp,
                file = file,
                line = line,
                module = %entry.target,
                fields = ?entry.fields,
                "{}",
                entry.message
            ),
            "warn" => tracing::warn!(
                target: "web",
                trace_id = %trace_id,
                timestamp = timestamp,
                file = file,
                line = line,
                module = %entry.target,
                fields = ?entry.fields,
                "{}",
                entry.message
            ),
            "info" => tracing::info!(
                target: "web",
                trace_id = %trace_id,
                timestamp = timestamp,
                file = file,
                line = line,
                module = %entry.target,
                fields = ?entry.fields,
                "{}",
                entry.message
            ),
            "debug" => tracing::debug!(
                target: "web",
                trace_id = %trace_id,
                timestamp = timestamp,
                file = file,
                line = line,
                module = %entry.target,
                fields = ?entry.fields,
                "{}",
                entry.message
            ),
            "trace" => tracing::trace!(
                target: "web",
                trace_id = %trace_id,
                timestamp = timestamp,
                file = file,
                line = line,
                module = %entry.target,
                fields = ?entry.fields,
                "{}",
                entry.message
            ),
            _ => tracing::info!(
                target: "web",
                trace_id = %trace_id,
                timestamp = timestamp,
                file = file,
                line = line,
                module = %entry.target,
                fields = ?entry.fields,
                "Unknown log level '{}': {}",
                entry.level,
                entry.message
            ),
        }

        Ok(CommandResponse::Success)
    }

    /// 处理批量推送日志命令
    #[tracing::instrument(skip(self, entries), fields(count = entries.len()))]
    async fn handle_push_batch_logs(
        &self,
        entries: Vec<StructuredLogEntry>,
    ) -> anyhow::Result<CommandResponse> {
        info!("Processing batch of {} log entries", entries.len());

        let mut success_count = 0;
        let mut error_count = 0;

        for entry in entries {
            match self.handle_push_log(entry).await {
                Ok(_) => success_count += 1,
                Err(e) => {
                    error_count += 1;
                    warn!("Failed to process log entry: {}", e);
                    // 继续处理其他日志条目，不因单个失败而中断
                }
            }
        }

        info!(
            "Batch log processing completed: {} success, {} errors",
            success_count, error_count
        );
        Ok(CommandResponse::Success)
    }

    /// 处理迁移数据目录命令
    #[tracing::instrument(skip(self), fields(trace_id = %trace_id, new_path = %new_path.display()))]
    async fn handle_set_data_root_path(
        &self,
        new_path: PathBuf,
        trace_id: TraceId,
    ) -> anyhow::Result<CommandResponse> {
        info!("Setting distro install path to: {}", new_path.display());

        // 调用SettingsService执行迁移
        let settings_service = Arc::clone(&self.services.settings_service);
        settings_service
            .set_data_root_path(new_path, trace_id)
            .await
            .map_err(|e| {
                error!("Failed to set distro install path: {}", e);
                anyhow::anyhow!("Failed to set distro install path: {}", e)
            })?;

        info!("Distro install path set successfully");
        Ok(CommandResponse::Success)
    }
}
