use serde::{Deserialize, Serialize};
use tokio::sync::mpsc;
use tokio_util::sync::CancellationToken;

use crate::{logging::TraceId, services::system_check::CheckStatus};

// 重新导出各个服务模块中的状态类型
pub use crate::services::{
    device_service::DeviceInfo, network_service::NetworkState, settings_service::UserSettingsState,
    system_check::SystemState, task_acceptance_service::TaskState, user_service::UserState,
};

/// 状态投射消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StateMessage {
    /// 用户状态变更
    UserStateChanged(UserState, TraceId),
    /// 系统状态变更
    SystemStateChanged(SystemState, TraceId),
    /// 任务状态变更
    TaskStateChanged(TaskState, TraceId),
    /// 网络状态变更
    NetworkStateChanged(NetworkState, TraceId),
    /// 设置状态变更
    SettingsStateChanged(UserSettingsState, TraceId),
    /// 初始状态聚合
    InitialState(ProjectedState, TraceId),
    /// 计算属性变更
    ComputedStateChanged(ComputedProperties, TraceId),
    /// 设备状态变更
    DeviceStateChanged(DeviceInfo, TraceId),
}

/// 投射状态 - 从各服务动态聚合的状态视图
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ProjectedState {
    pub user: UserState,
    pub system: SystemState,
    pub task: TaskState,
    pub network: NetworkState,
    pub settings: UserSettingsState,
    pub computed: ComputedProperties,
    pub device: DeviceInfo,
}

/// 跨服务计算属性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComputedProperties {
    /// 是否准备好工作
    pub is_ready_for_work: bool,
    /// 整体健康状态
    pub overall_health_status: String,
    /// 最后更新时间
    pub last_computed_at: String,
}

impl PartialEq for ComputedProperties {
    fn eq(&self, other: &Self) -> bool {
        self.is_ready_for_work == other.is_ready_for_work
            && self.overall_health_status == other.overall_health_status
    }
}

impl Default for ComputedProperties {
    fn default() -> Self {
        Self {
            is_ready_for_work: false,
            overall_health_status: "unknown".to_string(),
            last_computed_at: chrono::Utc::now().to_rfc3339(),
        }
    }
}

/// 状态投射层 - 负责聚合各服务状态并投射到前端
pub struct StateProjection {
    /// 服务引用 - 作为状态的权威来源
    services: super::Services,
    /// 状态消息发送通道
    state_tx: mpsc::Sender<StateMessage>,
}

impl StateProjection {
    /// 创建新的状态投射实例
    pub fn new(services: super::Services, state_tx: mpsc::Sender<StateMessage>) -> Self {
        Self { services, state_tx }
    }

    /// 启动所有服务的状态监听器
    pub fn start_state_watchers(
        &self,
        tasks: &mut Vec<tokio::task::JoinHandle<()>>,
        cancellation_token: CancellationToken,
    ) {
        fn spawn_watcher<S, F>(
            tasks: &mut Vec<tokio::task::JoinHandle<()>>,
            cancellation_token: CancellationToken,
            // 直接传入 watch receiver，而不是整个服务
            state_tx: &mpsc::Sender<StateMessage>,
            mut state_rx: tokio::sync::watch::Receiver<S>,
            // 使用闭包来构造不同的 StateMessage
            message_constructor: F,
            service_name: &'static str,
        ) where
            // S 是状态的类型 (e.g., UserState, SettingsState)
            S: Clone + Send + Sync + 'static,
            // F 是一个函数/闭包，它接受状态和 trace_id，返回一个 StateMessage
            F: Fn(S, TraceId) -> StateMessage + Send + 'static,
        {
            let state_tx = state_tx.clone();

            let task = tokio::spawn(async move {
                loop {
                    tokio::select! {
                        // 偏好 select! 的 `biased` 模式可以确保优先检查取消信号，但这里默认也可以
                        biased;

                        // 监听取消信号
                        _ = cancellation_token.cancelled() => {
                            tracing::info!("{} state watcher cancelled", service_name);
                            break;
                        }

                        // 监听状态变化
                        result = state_rx.changed() => {
                            if result.is_err() {
                                // 如果 `changed()` 返回 Err，说明发送端已被丢弃，通道关闭
                                tracing::info!("{} state receiver channel closed", service_name);
                                break;
                            }

                            // 获取新状态和 trace_id
                            let state = state_rx.borrow().clone();
                            let trace_id = crate::context::get_global_trace_id();
                            let event = message_constructor(state, trace_id);

                            // 发送状态消息
                            if let Err(e) = state_tx.send(event).await {
                                tracing::error!("Failed to send {} state change: {}", service_name, e);
                                // 发送失败也退出循环
                                break;
                            }
                        }
                    }
                }
            });

            tasks.push(task);
        }
        // 用户服务监听
        spawn_watcher(
            tasks,
            cancellation_token.clone(),
            &self.state_tx,
            self.services.user_service.state_receiver(),
            |state, trace_id| StateMessage::UserStateChanged(state, trace_id),
            "User",
        );
        // 设置服务监听
        spawn_watcher(
            tasks,
            cancellation_token.clone(),
            &self.state_tx,
            self.services.settings_service.state_receiver(),
            |state, trace_id| StateMessage::SettingsStateChanged(state, trace_id),
            "Settings",
        );
        // 网络服务监听
        spawn_watcher(
            tasks,
            cancellation_token.clone(),
            &self.state_tx,
            self.services.network_service.state_receiver(),
            |state, trace_id| StateMessage::NetworkStateChanged(state, trace_id),
            "Network",
        );
        // 系统检查服务监听
        spawn_watcher(
            tasks,
            cancellation_token.clone(),
            &self.state_tx,
            self.services.system_check.state_receiver(),
            |state, trace_id| StateMessage::SystemStateChanged(state, trace_id),
            "System Check",
        );
        // 任务接单服务监听
        spawn_watcher(
            tasks,
            cancellation_token.clone(),
            &self.state_tx,
            self.services.task_acceptance_service.state_receiver(),
            |state, trace_id| StateMessage::TaskStateChanged(state, trace_id),
            "Task Acceptance",
        );
        // 设备信息监听
        spawn_watcher(
            tasks,
            cancellation_token.clone(),
            &self.state_tx,
            self.services.device_service.state_receiver(),
            |state, trace_id| StateMessage::DeviceStateChanged(state, trace_id),
            "Device",
        );
    }

    pub fn start_computed_properties_watcher(
        &self,
        tasks: &mut Vec<tokio::task::JoinHandle<()>>,
        cancellation_token: CancellationToken,
    ) {
        let user_service = self.services.user_service.clone();
        let system_check_service = self.services.system_check.clone();
        let network_service = self.services.network_service.clone();

        let state_tx = self.state_tx.clone();
        let computed_task = tokio::spawn(async move {
            let mut user_state_rx = user_service.state_receiver();
            let mut system_check_state_rx = system_check_service.state_receiver();
            let mut network_state_rx = network_service.state_receiver();
            type R<T> = tokio::sync::watch::Receiver<T>;
            let compute_from_receivers =
                |usr_rx: &R<UserState>, sys_rx: &R<SystemState>, net_rx: &R<NetworkState>| {
                    let user_state = usr_rx.borrow();
                    let system_check_state = sys_rx.borrow();
                    let network_state = net_rx.borrow();
                    // 假设 Self::compute_properties 是一个静态或关联方法
                    Self::compute_properties(&user_state, &system_check_state, &network_state)
                };
            // 初始化计算属性缓存
            let mut computed_cache =
                compute_from_receivers(&user_state_rx, &system_check_state_rx, &network_state_rx);

            loop {
                tokio::select! {
                    // `biased;` 确保优先检查取消信号，能更快地响应关闭请求
                    biased;

                    _ = cancellation_token.cancelled() => {
                        tracing::info!("Computed properties watcher cancelled");
                        break; // 退出循环
                    },

                    // 监听每个状态的变化
                    res = user_state_rx.changed() => {
                        if res.is_err() {
                            tracing::info!("User state channel closed, stopping computed watcher.");
                            break; // 源通道关闭，退出循环
                        }
                    },
                    res = system_check_state_rx.changed() => {
                        if res.is_err() {
                            tracing::info!("System check state channel closed, stopping computed watcher.");
                            break; // 源通道关闭，退出循环
                        }
                    },
                    res = network_state_rx.changed() => {
                        if res.is_err() {
                            tracing::info!("Network state channel closed, stopping computed watcher.");
                            break; // 源通道关闭，退出循环
                        }
                    }
                };
                let new_computed = compute_from_receivers(
                    &user_state_rx,
                    &system_check_state_rx,
                    &network_state_rx,
                );
                Self::project_computed_state(state_tx.clone(), new_computed, &mut computed_cache)
                    .await;
            }
        });
        tasks.push(computed_task);
    }

    /// 投射初始状态到前端
    pub async fn project_initial_state(&self, trace_id: TraceId) -> anyhow::Result<()> {
        tracing::info!("Projecting initial state");
        let projected_state = self.collect_all_states().await?;

        // 发送初始状态消息
        self.state_tx
            .send(StateMessage::InitialState(projected_state, trace_id))
            .await
            .map_err(|e| anyhow::anyhow!("Failed to send initial state: {}", e))?;

        Ok(())
    }

    pub async fn project_computed_state(
        state_tx: mpsc::Sender<StateMessage>,
        computed: ComputedProperties,
        computed_cache: &mut ComputedProperties,
    ) -> () {
        // 检查是否发生变化
        if &computed != computed_cache {
            // 发送状态变更消息，使用延迟序列化
            let event = StateMessage::ComputedStateChanged(
                computed.clone(),
                crate::context::get_global_trace_id(),
            );

            if let Err(e) = state_tx.send(event).await {
                tracing::error!("Failed to send computed state change: {}", e);
                return;
            }
            *computed_cache = computed;
        }
    }

    /// 收集所有服务的当前状态
    async fn collect_all_states(&self) -> anyhow::Result<ProjectedState> {
        // 从各服务获取真实状态
        let user_state = self.services.user_service.get_current_user();
        let system_state = self.services.system_check.get_current_state();
        let task_state = self.services.task_acceptance_service.get_current_state();
        let network_state = self.services.network_service.get_current_state();
        let settings_state = self.services.settings_service.get_current_settings();
        let device_state = self.services.device_service.get_device_info();
        // 计算跨服务属性
        let computed = Self::compute_properties(&user_state, &system_state, &network_state);

        Ok(ProjectedState {
            user: user_state,
            system: system_state,
            task: task_state,
            network: network_state,
            settings: settings_state,
            device: device_state,
            computed,
        })
    }

    /// 计算跨服务属性
    fn compute_properties(
        user: &UserState,
        system: &SystemState,
        network: &NetworkState,
    ) -> ComputedProperties {
        let is_ready_for_work = Self::is_ready_for_work(user, system, network);
        let overall_health_status = Self::overall_health_status(user, system, network).to_string();

        ComputedProperties {
            is_ready_for_work,
            overall_health_status,
            last_computed_at: chrono::Utc::now().to_rfc3339(),
        }
    }

    /// 计算是否准备好工作
    fn is_ready_for_work(user: &UserState, system: &SystemState, network: &NetworkState) -> bool {
        // 用户已登录 && 系统检查通过 && 网络连接正常
        let is_ready_for_work =
            user.is_logged_in && system.all_checks_passed() && network.internet_connectivity;
        is_ready_for_work
    }

    /// 计算整体健康状态
    fn overall_health_status(
        user: &UserState,
        system: &SystemState,
        network: &NetworkState,
    ) -> &'static str {
        if !user.is_logged_in {
            return "not_authenticated";
        }

        if !system
            .check_results
            .iter()
            .all(|r| r.status == CheckStatus::Passed)
        {
            return "system_check_failed";
        }

        if !network.internet_connectivity {
            return "network_disconnected";
        }

        "healthy"
    }
}

/// 状态类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StateType {
    // 来自服务
    User,
    System,
    Task,
    Network,
    Settings,
    Device,
    // 来自计算属性
    Computed,
    // 所有状态，仅初始化会发生
    All,
}
