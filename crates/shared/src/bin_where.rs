use std::env;

pub fn bin_where(bin: &str) -> Option<String> {
    let path = env::var("PATH").ok()?;

    // 使用 env::split_paths 来正确分割 PATH（跨平台兼容）
    let paths = env::split_paths(&path);

    for path in paths {
        // 在 Windows 上需要尝试多个扩展名
        let executable_names = if cfg!(windows) {
            vec![
                format!("{}.exe", bin),
                format!("{}.cmd", bin),
                format!("{}.bat", bin),
                bin.to_string(), // 有些可执行文件可能没有扩展名
            ]
        } else {
            vec![bin.to_string()]
        };

        for executable_name in executable_names {
            let full_path = path.join(&executable_name);
            //println!("full_path: {}", full_path.display());

            if full_path.exists() && full_path.is_file() {
                return Some(full_path.to_string_lossy().to_string());
            }
        }
    }
    None
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_bin_where() {
        // 在 Unix 系统上测试 ls
        if !cfg!(windows) {
            let result = bin_where("ls");
            assert!(result.is_some());
            assert!(result.unwrap().ends_with("/ls"));
        }
    }

    #[test]
    fn test_bin_where_powershell() {
        // 在 Windows 上测试 powershell
        if cfg!(windows) {
            let result = bin_where("powershell");
            if let Some(path) = result {
                assert!(path.ends_with("powershell.exe"));
            }
        }
    }

    #[test]
    fn test_bin_where_wsl() {
        // 在 Windows 上测试 wsl
        if cfg!(windows) {
            let result = bin_where("wsl");
            if let Some(path) = result {
                println!("{path}");
                assert!(path.ends_with("wsl.exe"));
            }
        }
    }
}
