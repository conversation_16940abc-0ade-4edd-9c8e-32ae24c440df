use std::cell::UnsafeCell;
use std::mem::ManuallyDrop;
use std::ops::Deref;
use std::sync::{Arc, Once};
use tokio::sync::Notify;
use tokio::time::{Duration, Instant, sleep};

pub struct Debouncer {
    notify: Arc<Notify>,
}

impl Debouncer {
    pub fn new<F>(delay: Duration, f: F) -> Self
    where
        F: Fn() + Send + 'static,
    {
        let notify = Arc::new(Notify::new());

        {
            let notify = notify.clone();
            tokio::spawn(async move {
                loop {
                    // 等待第一次触发
                    notify.notified().await;
                    let timer = sleep(delay);
                    tokio::pin!(timer);

                    loop {
                        tokio::select! {
                            // 如果再次触发则重置时间
                            _ = notify.notified() => {
                                    timer.as_mut().reset(Instant::now() + delay);
                                }
                            _ = &mut timer => {
                                    f();
                                    break;
                            }
                        }
                    }
                }
            })
        };
        Self { notify }
    }
    pub fn call(&self) {
        self.notify.notify_last();
    }
}

union Data<T, V> {
    params: ManuallyDrop<V>,
    value: ManuallyDrop<T>,
}
pub struct DebouncerConst<F = fn()> {
    once: Once,
    data: UnsafeCell<Data<Debouncer, (Duration, F)>>,
}

/// 防抖函数
///
/// 注意：无法保证在超过 delay 后立即执行，可能有 10 ~ 32ms 延迟
pub const fn debounce<F: Fn()>(delay: Duration, f: F) -> DebouncerConst<F> {
    DebouncerConst {
        once: Once::new(),
        data: UnsafeCell::new(Data {
            params: ManuallyDrop::new((delay, f)),
        }),
    }
}

impl Deref for DebouncerConst {
    type Target = Debouncer;
    fn deref(&self) -> &Self::Target {
        self.once.call_once(|| {
            let data = unsafe { &mut *self.data.get() };
            let (delay, f) = unsafe { ManuallyDrop::take(&mut data.params) };
            let value = Debouncer::new(delay, f);
            data.value = ManuallyDrop::new(value);
        });
        unsafe { &(*self.data.get()).value }
    }
}
unsafe impl<F: Send> Sync for DebouncerConst<F> {}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::AtomicU32;
    use std::sync::atomic::Ordering::Relaxed;

    static COUNT: AtomicU32 = AtomicU32::new(0);

    static DEBOUNCER: DebouncerConst = debounce(Duration::from_millis(100), || {
        COUNT.fetch_add(1, Relaxed);
    });

    #[tokio::test]
    async fn it_works() {
        COUNT.store(0, Relaxed);
        DEBOUNCER.call();
        DEBOUNCER.call();
        sleep(Duration::from_millis(16)).await;
        DEBOUNCER.call();
        sleep(Duration::from_millis(132)).await;
        assert_eq!(COUNT.load(Relaxed), 1);

        DEBOUNCER.call();
        DEBOUNCER.call();
        sleep(Duration::from_millis(5)).await;
        assert_eq!(COUNT.load(Relaxed), 1);
        sleep(Duration::from_millis(126)).await;
        assert_eq!(COUNT.load(Relaxed), 2);
        //
        DEBOUNCER.call();
        sleep(Duration::from_millis(132)).await;
        assert_eq!(COUNT.load(Relaxed), 3);
    }
}
