//! 分块断点续传下载器
//!
//! 提供高性能的分块下载功能：
//! - 自适应块大小策略
//! - 断点续传支持
//! - 并发下载多个块
//! - 块级重试机制
//! - 实时进度回调
//! - 可配置校验算法
//! - 块级哈希验证
//! - MessagePack 元数据存储
//! - 元数据一致性校验

use crate::hash::{HashAlgorithm, HashValidator};
use chrono::{DateTime, Utc};
use futures_util::StreamExt;
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet},
    path::{Path, PathBuf},
    sync::{
        Arc,
        atomic::{AtomicU64, Ordering},
    },
    time::{Duration, Instant},
};
use tokio::{
    fs,
    io::{AsyncSeekExt, AsyncWriteExt},
    sync::Semaphore,
    time::sleep,
};
use tokio_util::sync::CancellationToken;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 元数据版本常量
const METADATA_VERSION: u32 = 2;

/// 下载配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadConfig {
    /// 默认块大小（64MB）
    pub default_chunk_size: u64,
    /// 最小块大小（16MB）
    pub min_chunk_size: u64,
    /// 最大块大小（128MB）
    pub max_chunk_size: u64,
    /// 启用自适应块大小
    pub adaptive_chunk_sizing: bool,
    /// 最大并发下载数（默认 4）
    pub max_concurrent_chunks: usize,
    /// 校验算法
    pub hash_algorithm: HashAlgorithm,
    /// 单块重试次数
    pub chunk_retry_count: u32,
    /// 块下载超时（秒）
    pub chunk_timeout: u64,
    /// 连接超时（秒）
    pub connect_timeout: u64,
    /// 总下载超时（秒，0 表示无限制）
    pub total_timeout: u64,
    /// User-Agent
    pub user_agent: String,
}

impl Default for DownloadConfig {
    fn default() -> Self {
        Self {
            default_chunk_size: 64 * 1024 * 1024, // 64MB
            min_chunk_size: 16 * 1024 * 1024,     // 16MB
            max_chunk_size: 128 * 1024 * 1024,    // 128MB
            adaptive_chunk_sizing: true,
            max_concurrent_chunks: 4,
            hash_algorithm: HashAlgorithm::SHA256,
            chunk_retry_count: 3,
            chunk_timeout: 60,
            connect_timeout: 30,
            total_timeout: 0, // 无限制
            user_agent: "EchoWave-Downloader/1.0".to_string(),
        }
    }
}

/// 下载元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadMetadata {
    pub url: String,
    pub total_size: u64,
    pub chunk_size: u64,
    pub total_chunks: usize,
    pub hash_algorithm: HashAlgorithm,
    pub expected_hash: Option<String>,
    pub completed_chunks: HashSet<usize>,
    /// 块级哈希存储 (块索引 -> 哈希值)
    #[serde(default)]
    pub chunk_hashes: HashMap<usize, String>,
    /// 元数据版本
    #[serde(default = "default_metadata_version")]
    pub metadata_version: u32,
    /// 下载时的用户代理
    #[serde(default)]
    pub user_agent: String,
    pub created_at: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
}

fn default_metadata_version() -> u32 {
    METADATA_VERSION
}

/// 块信息
#[derive(Debug, Clone)]
pub struct ChunkInfo {
    pub index: usize,
    pub start_byte: u64,
    pub end_byte: u64,
    pub size: u64,
    pub downloaded: bool,
    pub hash: Option<String>,
}

/// 下载进度信息
#[derive(Debug, Clone)]
pub struct DownloadProgress {
    pub total_chunks: usize,
    pub completed_chunks: usize,
    pub total_bytes: u64,
    pub downloaded_bytes: u64,
    pub current_speed: f64, // bytes/second
    pub eta_seconds: Option<u64>,
    pub active_chunks: Vec<ChunkProgress>,
}

/// 块下载进度
#[derive(Debug, Clone)]
pub struct ChunkProgress {
    pub index: usize,
    pub downloaded_bytes: u64,
    pub total_bytes: u64,
    pub speed: f64,
}

/// 分块下载器
pub struct ChunkedDownloader {
    config: DownloadConfig,
    client: reqwest::Client,
    validator: HashValidator,
}

impl ChunkedDownloader {
    /// 创建新的分块下载器
    pub fn new() -> anyhow::Result<Self> {
        Self::with_config(DownloadConfig::default())
    }

    pub fn with_config(config: DownloadConfig) -> anyhow::Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(config.chunk_timeout))
            .connect_timeout(Duration::from_secs(config.connect_timeout))
            .user_agent(&config.user_agent)
            .build()
            .map_err(|e| anyhow::anyhow!("创建HTTP客户端失败: {}", e))?;

        let validator = HashValidator::default(); // 64KB 缓冲区

        Ok(Self {
            config,
            client,
            validator,
        })
    }

    /// 开始下载文件
    ///
    /// # 参数
    /// - `url`: 下载URL
    /// - `output_path`: 输出文件路径
    /// - `expected_hash`: 期望的哈希（可选）
    /// - `progress_callback`: 进度回调函数
    /// - `cancellation_token`: 取消令牌
    /// - `trace_id`: 请求追踪ID（可选）
    ///
    /// # 返回值
    /// 下载完成后返回文件的实际哈希
    pub async fn download<F>(
        &self,
        url: &str,
        output_path: &Path,
        expected_hash: Option<&str>,
        progress_callback: Arc<F>,
        cancellation_token: CancellationToken,
        trace_id: &Uuid,
    ) -> anyhow::Result<String>
    where
        F: Fn(DownloadProgress) + Send + Sync + 'static,
    {
        let span = tracing::info_span!("download", trace_id = %trace_id, url = url, output_path = %output_path.display());

        let _enter = span.enter();

        info!(
            url = url,
            output_path = %output_path.display(),
            expected_hash = expected_hash,
            "开始下载文件"
        );

        let start_time = Instant::now();

        // 0. 检查文件是否存在
        if output_path.exists() {
            info!(
                output_path = %output_path.display(),
                "文件已存在，开始校验文件完整性"
            );
            let actual_hash = self
                .validator
                .compute_hash(output_path, self.config.hash_algorithm)
                .await?;
            if let Some(expected) = expected_hash {
                if actual_hash == expected {
                    info!(
                        output_path = %output_path.display(),
                        actual_hash = actual_hash,
                        expected_hash = expected,
                        "文件完整性校验通过，跳过下载"
                    );
                    return Ok(actual_hash);
                } else {
                    warn!(
                        output_path = %output_path.display(),
                        actual_hash = actual_hash,
                        expected_hash = expected,
                        "文件完整性校验失败，开始下载"
                    );
                }
            } else {
                warn!(output_path = %output_path.display(), "没有提供期望哈希，开始下载");
            }
        }

        // 1. 获取文件信息
        let total_size = self.get_file_size(url).await.map_err(|e| {
            error!(error = %e, "获取文件大小失败");
            e.context("获取文件大小失败")
        })?;

        info!(
            total_size = total_size,
            size_mb = total_size / (1024 * 1024),
            "获取文件信息成功"
        );

        // 2. 计算分块策略
        let chunk_size = if self.config.adaptive_chunk_sizing {
            self.calculate_adaptive_chunk_size(total_size)
        } else {
            self.config.default_chunk_size
        };

        let total_chunks = ((total_size + chunk_size - 1) / chunk_size) as usize;
        info!(
            chunk_size = chunk_size,
            chunk_size_mb = chunk_size / (1024 * 1024),
            total_chunks = total_chunks,
            adaptive = self.config.adaptive_chunk_sizing,
            "计算分块策略完成"
        );

        // 3. 创建或加载元数据
        let metadata_path = self.get_metadata_path(output_path);
        let mut metadata = if metadata_path.exists() {
            let loaded_metadata = self.load_metadata(&metadata_path).await?;
            self.validate_metadata_consistency(&loaded_metadata, url, total_size, &self.config)
                .await?
        } else {
            DownloadMetadata {
                url: url.to_string(),
                total_size,
                chunk_size,
                total_chunks,
                hash_algorithm: self.config.hash_algorithm,
                expected_hash: expected_hash.map(|s| s.to_string()),
                completed_chunks: HashSet::new(),
                chunk_hashes: HashMap::new(),
                metadata_version: METADATA_VERSION,
                user_agent: self.config.user_agent.clone(),
                created_at: Utc::now(),
                last_updated: Utc::now(),
            }
        };

        // 4. 扫描已完成的块
        let completed_chunks = self.scan_existing_chunks(&metadata, output_path).await?;
        metadata.completed_chunks = completed_chunks.into_iter().collect();

        // 5. 计算缺失的块
        let missing_chunks = self.get_missing_chunks(&metadata);

        let completed_count = metadata.completed_chunks.len();
        let missing_count = missing_chunks.len();
        let progress_percent = if total_chunks > 0 {
            (completed_count as f64 / total_chunks as f64) * 100.0
        } else {
            0.0
        };

        info!(
            completed_chunks = completed_count,
            missing_chunks = missing_count,
            total_chunks = total_chunks,
            progress_percent = format!("{:.1}%", progress_percent),
            "分析下载进度"
        );

        if missing_chunks.is_empty() {
            info!("所有块已下载完成，开始合并文件");
        } else {
            // 6. 下载缺失的块
            self.download_missing_chunks(
                missing_chunks,
                &mut metadata,
                output_path,
                progress_callback.clone(),
                cancellation_token.clone(),
                trace_id,
            )
            .await
            .map_err(|e| {
                error!(error = %e, "下载缺失块失败");
                e.context("下载缺失块失败")
            })?;

            // 保存元数据
            self.save_metadata(&metadata, &metadata_path)
                .await
                .map_err(|e| {
                    error!(error = %e, "保存元数据失败");
                    e.context("保存元数据失败")
                })?;
        }

        // 7. 合并块文件
        self.merge_chunks(&metadata, output_path).await?;

        // 8. 验证最终文件
        let actual_hash = self
            .validator
            .compute_hash(output_path, metadata.hash_algorithm)
            .await?;

        if let Some(expected) = &metadata.expected_hash {
            if !self
                .validator
                .verify_hash(output_path, expected, metadata.hash_algorithm)
                .await?
            {
                return Err(anyhow::anyhow!(
                    "文件校验失败: 期望={}, 实际={}",
                    expected,
                    actual_hash
                ));
            }
        }

        // 9. 清理临时文件
        self.cleanup_temp_files(&metadata, output_path).await?;

        let elapsed = start_time.elapsed();
        let speed = total_size as f64 / elapsed.as_secs_f64();
        let speed_mbps = speed / (1024.0 * 1024.0);

        info!(
            total_size = total_size,
            total_size_mb = total_size / (1024 * 1024),
            elapsed_seconds = elapsed.as_secs_f64(),
            average_speed_mbps = format!("{:.2}", speed_mbps),
            hash_algorithm = ?metadata.hash_algorithm,
            actual_hash = actual_hash,
            "下载成功完成"
        );

        Ok(actual_hash)
    }

    /// 获取文件大小
    async fn get_file_size(&self, url: &str) -> anyhow::Result<u64> {
        debug!(url = url, "发送HEAD请求获取文件大小");

        let response = self.client.head(url).send().await.map_err(|e| {
            error!(url = url, error = %e, "HEAD请求网络失败");
            anyhow::anyhow!("HEAD请求网络失败: {}", e)
        })?;

        let status = response.status();
        debug!(url = url, status = %status, "收到HEAD响应");

        if !status.is_success() {
            error!(url = url, status = %status, "HEAD请求返回错误状态码");
            return Err(anyhow::anyhow!("HEAD请求失败，状态码: {}", status));
        }

        // 检查是否支持范围请求
        let accepts_ranges = response
            .headers()
            .get(reqwest::header::ACCEPT_RANGES)
            .and_then(|v| v.to_str().ok())
            .unwrap_or("");

        if accepts_ranges != "bytes" {
            warn!(
                url = url,
                accept_ranges = accepts_ranges,
                "服务器可能不支持范围请求，分块下载可能失败"
            );
        }

        let content_length = response
            .headers()
            .get(reqwest::header::CONTENT_LENGTH)
            .and_then(|v| v.to_str().ok())
            .and_then(|s| s.parse::<u64>().ok())
            .ok_or_else(|| {
                error!(url = url, "服务器未提供Content-Length头");
                anyhow::anyhow!("无法获取文件大小，服务器不支持Content-Length")
            })?;

        debug!(
            url = url,
            content_length = content_length,
            accepts_ranges = accepts_ranges,
            "成功获取文件元信息"
        );

        Ok(content_length)
    }

    /// 计算自适应块大小
    fn calculate_adaptive_chunk_size(&self, total_size: u64) -> u64 {
        let size_mb = total_size / (1024 * 1024);

        let chunk_size = match size_mb {
            0..=100 => self.config.min_chunk_size, // < 100MB -> 16MB
            101..=500 => 32 * 1024 * 1024,         // 100-500MB -> 32MB
            501..=2048 => self.config.default_chunk_size, // 500MB-2GB -> 64MB
            _ => self.config.max_chunk_size,       // > 2GB -> 128MB
        };

        chunk_size.clamp(self.config.min_chunk_size, self.config.max_chunk_size)
    }

    /// 扫描已存在的块文件
    async fn scan_existing_chunks(
        &self,
        metadata: &DownloadMetadata,
        output_path: &Path,
    ) -> anyhow::Result<Vec<usize>> {
        let mut completed_chunks = Vec::new();

        for chunk_index in 0..metadata.total_chunks {
            let chunk_path = self.get_chunk_path(output_path, chunk_index);

            if chunk_path.exists() {
                if self
                    .verify_chunk_with_hash(chunk_index, metadata, &chunk_path)
                    .await?
                {
                    completed_chunks.push(chunk_index);
                } else {
                    warn!("块文件损坏，删除: {}", chunk_path.display());
                    if let Err(e) = fs::remove_file(&chunk_path).await {
                        warn!("删除损坏块文件失败: {}", e);
                    }
                }
            }
        }

        debug!("扫描到 {} 个完整的块文件", completed_chunks.len());
        Ok(completed_chunks)
    }

    /// 验证块文件完整性（包含哈希验证）
    async fn verify_chunk_with_hash(
        &self,
        chunk_index: usize,
        metadata: &DownloadMetadata,
        chunk_path: &Path,
    ) -> anyhow::Result<bool> {
        let expected_size = self.get_chunk_size(chunk_index, metadata);

        // 检查文件大小
        match fs::metadata(chunk_path).await {
            Ok(file_meta) => {
                if file_meta.len() != expected_size {
                    debug!(
                        "块 {} 大小不匹配: 期望={}, 实际={}",
                        chunk_index,
                        expected_size,
                        file_meta.len()
                    );
                    return Ok(false);
                }
            }
            Err(_) => return Ok(false),
        }

        // 如果有存储的哈希，则进行验证
        if let Some(expected_hash) = metadata.chunk_hashes.get(&chunk_index) {
            let actual_hash = self
                .validator
                .compute_hash(chunk_path, metadata.hash_algorithm)
                .await?;
            if &actual_hash != expected_hash {
                debug!(
                    "块 {} 哈希验证失败: 期望={}, 实际={}",
                    chunk_index, expected_hash, actual_hash
                );
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// 获取块的大小
    fn get_chunk_size(&self, chunk_index: usize, metadata: &DownloadMetadata) -> u64 {
        if chunk_index == metadata.total_chunks - 1 {
            // 最后一块的大小可能不同
            metadata.total_size - (chunk_index as u64 * metadata.chunk_size)
        } else {
            metadata.chunk_size
        }
    }

    /// 获取缺失的块索引列表
    fn get_missing_chunks(&self, metadata: &DownloadMetadata) -> Vec<usize> {
        (0..metadata.total_chunks)
            .filter(|i| !metadata.completed_chunks.contains(i))
            .collect()
    }

    /// 下载缺失的块
    async fn download_missing_chunks<F>(
        &self,
        missing_chunks: Vec<usize>,
        metadata: &mut DownloadMetadata,
        output_path: &Path,
        progress_callback: Arc<F>,
        cancellation_token: CancellationToken,
        trace_id: &Uuid,
    ) -> anyhow::Result<()>
    where
        F: Fn(DownloadProgress) + Send + Sync + 'static,
    {
        if missing_chunks.is_empty() {
            return Ok(());
        }

        info!(
            missing_chunks_count = missing_chunks.len(),
            max_concurrent = self.config.max_concurrent_chunks,
            chunk_retry_count = self.config.chunk_retry_count,
            "开始并发下载缺失的块"
        );

        let semaphore = Arc::new(Semaphore::new(self.config.max_concurrent_chunks));
        let completed_chunks = Arc::new(AtomicU64::new(metadata.completed_chunks.len() as u64));
        let downloaded_bytes = Arc::new(AtomicU64::new(
            metadata.completed_chunks.len() as u64 * metadata.chunk_size,
        ));

        let start_time = Instant::now();

        // 创建并发下载任务
        let mut handles = Vec::new();

        for chunk_index in missing_chunks {
            let semaphore = semaphore.clone();
            let metadata_clone = metadata.clone();
            let output_path = output_path.to_path_buf();
            let progress_callback = progress_callback.clone();
            let cancellation_token = cancellation_token.clone();
            let completed_chunks = completed_chunks.clone();
            let downloaded_bytes = downloaded_bytes.clone();
            let downloader = self.clone();
            let start_time = start_time;
            let trace_id = trace_id.clone();

            let handle = tokio::spawn(async move {
                // 为块下载任务创建 span
                let chunk_span = tracing::debug_span!(
                    "chunk_download",
                    trace_id = %trace_id,
                    chunk_index = chunk_index
                );

                let _enter = chunk_span.enter();

                let _permit = semaphore.acquire().await.map_err(|e| {
                    error!(chunk_index = chunk_index, error = %e, "获取信号量失败");
                    anyhow::anyhow!("获取信号量失败: {}", e)
                })?;

                debug!(chunk_index = chunk_index, "开始下载块任务");

                let result = downloader
                    .download_single_chunk_with_retry(
                        chunk_index,
                        &metadata_clone,
                        &output_path,
                        cancellation_token,
                    )
                    .await;

                if result.is_ok() {
                    let chunk_size = downloader.get_chunk_size(chunk_index, &metadata_clone);
                    completed_chunks.fetch_add(1, Ordering::Relaxed);
                    downloaded_bytes.fetch_add(chunk_size, Ordering::Relaxed);

                    // 计算进度并回调
                    let completed = completed_chunks.load(Ordering::Relaxed);
                    let total_downloaded = downloaded_bytes.load(Ordering::Relaxed);
                    let elapsed = start_time.elapsed().as_secs_f64();
                    let speed = if elapsed > 0.0 {
                        total_downloaded as f64 / elapsed
                    } else {
                        0.0
                    };

                    let remaining_bytes =
                        metadata_clone.total_size.saturating_sub(total_downloaded);
                    let eta = if speed > 0.0 {
                        Some((remaining_bytes as f64 / speed) as u64)
                    } else {
                        None
                    };

                    let progress = DownloadProgress {
                        total_chunks: metadata_clone.total_chunks,
                        completed_chunks: completed as usize,
                        total_bytes: metadata_clone.total_size,
                        downloaded_bytes: total_downloaded,
                        current_speed: speed,
                        eta_seconds: eta,
                        active_chunks: vec![], // 暂时不跟踪活跃块
                    };

                    progress_callback(progress);
                }

                result.map(|_| chunk_index)
            });

            handles.push(handle);
        }

        // 等待所有下载任务完成
        let mut completed_chunk_indices = Vec::new();
        for (i, handle) in handles.into_iter().enumerate() {
            if cancellation_token.is_cancelled() {
                return Err(anyhow::anyhow!("下载被取消"));
            }

            match handle.await {
                Ok(Ok(chunk_index)) => {
                    debug!("块下载任务 {} 完成，块索引: {}", i, chunk_index);
                    completed_chunk_indices.push(chunk_index);
                }
                Ok(Err(e)) => {
                    error!("块下载任务 {} 失败: {}", i, e);
                    return Err(e);
                }
                Err(e) => {
                    error!("块下载任务 {} 执行失败: {}", i, e);
                    return Err(anyhow::anyhow!("块下载任务执行失败: {}", e));
                }
            }
        }

        // 计算并存储所有已完成块的哈希
        for chunk_index in completed_chunk_indices {
            let chunk_path = self.get_chunk_path(output_path, chunk_index);
            if chunk_path.exists() {
                match self
                    .compute_and_store_chunk_hash(chunk_index, &chunk_path, metadata)
                    .await
                {
                    Ok(hash) => {
                        metadata.chunk_hashes.insert(chunk_index, hash);
                        metadata.completed_chunks.insert(chunk_index);
                    }
                    Err(e) => {
                        error!(chunk_index = chunk_index, error = %e, "计算块哈希失败");
                    }
                }
            }
        }

        // 更新元数据时间戳
        metadata.last_updated = Utc::now();

        info!("所有块下载完成");
        Ok(())
    }

    /// 带重试的单块下载
    async fn download_single_chunk_with_retry(
        &self,
        chunk_index: usize,
        metadata: &DownloadMetadata,
        output_path: &Path,
        cancellation_token: CancellationToken,
    ) -> anyhow::Result<()> {
        let mut attempt = 0;
        let max_retries = self.config.chunk_retry_count;

        while attempt < max_retries {
            if cancellation_token.is_cancelled() {
                return Err(anyhow::anyhow!("下载被取消"));
            }

            match self
                .download_single_chunk(chunk_index, metadata, output_path)
                .await
            {
                Ok(_) => {
                    debug!("块 {} 下载成功", chunk_index);
                    return Ok(());
                }
                Err(e) if attempt < max_retries - 1 => {
                    let delay_ms = 1000 * 2_u64.pow(attempt);
                    warn!(
                        chunk_index = chunk_index,
                        attempt = attempt + 1,
                        max_retries = max_retries,
                        delay_ms = delay_ms,
                        error = %e,
                        "块下载失败，开始重试"
                    );

                    // 指数退避
                    let delay = Duration::from_millis(delay_ms);
                    sleep(delay).await;

                    attempt += 1;
                }
                Err(e) => {
                    error!(
                        chunk_index = chunk_index,
                        max_retries = max_retries,
                        error = %e,
                        "块下载最终失败，已达最大重试次数"
                    );
                    return Err(e);
                }
            }
        }

        Err(anyhow::anyhow!(
            "块 {} 下载失败，已重试 {} 次",
            chunk_index,
            max_retries
        ))
    }

    /// 下载单个块
    async fn download_single_chunk(
        &self,
        chunk_index: usize,
        metadata: &DownloadMetadata,
        output_path: &Path,
    ) -> anyhow::Result<()> {
        let start_byte = chunk_index as u64 * metadata.chunk_size;
        let end_byte = std::cmp::min(
            start_byte + metadata.chunk_size - 1,
            metadata.total_size - 1,
        );

        debug!(
            chunk_index = chunk_index,
            start_byte = start_byte,
            end_byte = end_byte,
            chunk_size = end_byte - start_byte + 1,
            "开始下载块"
        );

        // 发送带 Range 头的请求
        let response = self
            .client
            .get(&metadata.url)
            .header(
                reqwest::header::RANGE,
                format!("bytes={}-{}", start_byte, end_byte),
            )
            .send()
            .await
            .map_err(|e| anyhow::anyhow!("块 {} HTTP请求失败: {}", chunk_index, e))?;

        if !response.status().is_success()
            && response.status() != reqwest::StatusCode::PARTIAL_CONTENT
        {
            return Err(anyhow::anyhow!(
                "块 {} HTTP请求失败: {}",
                chunk_index,
                response.status()
            ));
        }

        // 创建块文件
        let chunk_path = self.get_chunk_path(output_path, chunk_index);
        if let Some(parent) = chunk_path.parent() {
            fs::create_dir_all(parent)
                .await
                .map_err(|e| anyhow::anyhow!("创建目录失败: {}", e))?;
        }

        let mut file = fs::File::create(&chunk_path)
            .await
            .map_err(|e| anyhow::anyhow!("创建块文件失败: {}", e))?;

        // 流式写入块数据
        let mut stream = response.bytes_stream();
        while let Some(chunk_result) = stream.next().await {
            let chunk = chunk_result.map_err(|e| {
                tracing::error!("读取块数据失败: {}", e);
                anyhow::anyhow!("读取块数据失败: {}", e)
            })?;

            file.write_all(&chunk).await.map_err(|e| {
                tracing::error!("写入块文件失败: {}", e);
                anyhow::anyhow!("写入块文件失败: {}", e)
            })?;
        }

        file.flush()
            .await
            .map_err(|e| anyhow::anyhow!("刷新块文件失败: {}", e))?;

        debug!("块 {} 下载完成: {}", chunk_index, chunk_path.display());
        Ok(())
    }

    /// 合并块文件
    async fn merge_chunks(
        &self,
        metadata: &DownloadMetadata,
        output_path: &Path,
    ) -> anyhow::Result<()> {
        info!("开始合并 {} 个块文件", metadata.total_chunks);

        // 创建输出文件
        if let Some(parent) = output_path.parent() {
            fs::create_dir_all(parent)
                .await
                .map_err(|e| anyhow::anyhow!("创建输出目录失败: {}", e))?;
        }

        let mut output_file = fs::File::create(output_path)
            .await
            .map_err(|e| anyhow::anyhow!("创建输出文件失败: {}", e))?;

        // 预分配文件空间
        output_file
            .set_len(metadata.total_size)
            .await
            .map_err(|e| anyhow::anyhow!("预分配文件空间失败: {}", e))?;

        // 按顺序合并块
        for chunk_index in 0..metadata.total_chunks {
            let chunk_path = self.get_chunk_path(output_path, chunk_index);

            if !chunk_path.exists() {
                return Err(anyhow::anyhow!("块文件不存在: {}", chunk_path.display()));
            }

            // 定位到正确位置
            let offset = chunk_index as u64 * metadata.chunk_size;
            output_file
                .seek(std::io::SeekFrom::Start(offset))
                .await
                .map_err(|e| anyhow::anyhow!("文件定位失败: {}", e))?;

            // 复制块数据
            let mut chunk_file = fs::File::open(&chunk_path).await.map_err(|e| {
                tracing::error!("打开块文件失败: {}", e);
                anyhow::anyhow!("打开块文件失败: {}", e)
            })?;

            tokio::io::copy(&mut chunk_file, &mut output_file)
                .await
                .map_err(|e| anyhow::anyhow!("复制块数据失败: {}", e))?;

            debug!("合并块 {} 完成", chunk_index);
        }

        output_file
            .flush()
            .await
            .map_err(|e| anyhow::anyhow!("刷新输出文件失败: {}", e))?;

        info!("文件合并完成: {}", output_path.display());
        Ok(())
    }

    /// 清理临时文件
    async fn cleanup_temp_files(
        &self,
        metadata: &DownloadMetadata,
        output_path: &Path,
    ) -> anyhow::Result<()> {
        info!("清理临时文件");

        // 删除块文件
        for chunk_index in 0..metadata.total_chunks {
            let chunk_path = self.get_chunk_path(output_path, chunk_index);
            if chunk_path.exists() {
                if let Err(e) = fs::remove_file(&chunk_path).await {
                    warn!("删除块文件失败: {} - {}", chunk_path.display(), e);
                }
            }
        }

        // 删除元数据文件
        let metadata_path = self.get_metadata_path(output_path);
        if metadata_path.exists() {
            if let Err(e) = fs::remove_file(&metadata_path).await {
                warn!("删除元数据文件失败: {} - {}", metadata_path.display(), e);
            }
        }

        info!("临时文件清理完成");
        Ok(())
    }

    /// 获取块文件路径
    fn get_chunk_path(&self, output_path: &Path, chunk_index: usize) -> PathBuf {
        output_path.with_extension(format!("part.{}", chunk_index))
    }

    /// 获取元数据文件路径
    fn get_metadata_path(&self, output_path: &Path) -> PathBuf {
        output_path.with_extension("meta.msgpack")
    }

    /// 验证元数据一致性
    async fn validate_metadata_consistency(
        &self,
        metadata: &DownloadMetadata,
        url: &str,
        total_size: u64,
        config: &DownloadConfig,
    ) -> anyhow::Result<DownloadMetadata> {
        // 检查基本字段一致性
        if metadata.url != url || metadata.total_size != total_size {
            warn!(
                metadata_url = metadata.url,
                current_url = url,
                metadata_size = metadata.total_size,
                current_size = total_size,
                "元数据不匹配，重新开始下载"
            );

            return Ok(DownloadMetadata {
                url: url.to_string(),
                total_size,
                chunk_size: if config.adaptive_chunk_sizing {
                    self.calculate_adaptive_chunk_size(total_size)
                } else {
                    config.default_chunk_size
                },
                total_chunks: ((total_size + config.default_chunk_size - 1)
                    / config.default_chunk_size) as usize,
                hash_algorithm: config.hash_algorithm,
                expected_hash: None,
                completed_chunks: HashSet::new(),
                chunk_hashes: HashMap::new(),
                metadata_version: METADATA_VERSION,
                user_agent: config.user_agent.clone(),
                created_at: Utc::now(),
                last_updated: Utc::now(),
            });
        }

        // 检查版本兼容性
        if metadata.metadata_version > METADATA_VERSION {
            return Err(anyhow::anyhow!(
                "元数据版本 {} 高于当前支持的版本 {}，请升级程序",
                metadata.metadata_version,
                METADATA_VERSION
            ));
        }

        // 检查哈希算法一致性
        if metadata.hash_algorithm != config.hash_algorithm {
            warn!(
                metadata_algorithm = ?metadata.hash_algorithm,
                config_algorithm = ?config.hash_algorithm,
                "哈希算法不一致，建议重新下载以确保一致性"
            );
        }

        info!(
            metadata_version = metadata.metadata_version,
            chunks_completed = metadata.completed_chunks.len(),
            chunks_with_hash = metadata.chunk_hashes.len(),
            "元数据验证通过"
        );

        Ok(metadata.clone())
    }

    /// 计算并存储块哈希
    async fn compute_and_store_chunk_hash(
        &self,
        chunk_index: usize,
        chunk_path: &Path,
        metadata: &DownloadMetadata,
    ) -> anyhow::Result<String> {
        let hash = self
            .validator
            .compute_hash(chunk_path, metadata.hash_algorithm)
            .await?;

        debug!(
            chunk_index = chunk_index,
            hash = hash,
            algorithm = ?metadata.hash_algorithm,
            "计算块哈希完成"
        );

        Ok(hash)
    }

    /// 保存元数据（MessagePack 格式）
    async fn save_metadata(
        &self,
        metadata: &DownloadMetadata,
        metadata_path: &Path,
    ) -> anyhow::Result<()> {
        let content =
            rmp_serde::to_vec(metadata).map_err(|e| anyhow::anyhow!("序列化元数据失败: {}", e))?;

        fs::write(metadata_path, content)
            .await
            .map_err(|e| anyhow::anyhow!("保存元数据失败: {}", e))?;

        debug!("元数据已保存: {}", metadata_path.display());
        Ok(())
    }

    /// 加载元数据（MessagePack 格式）
    async fn load_metadata(&self, metadata_path: &Path) -> anyhow::Result<DownloadMetadata> {
        let content = fs::read(metadata_path)
            .await
            .map_err(|e| anyhow::anyhow!("读取元数据失败: {}", e))?;

        let metadata: DownloadMetadata = rmp_serde::from_slice(&content)
            .map_err(|e| anyhow::anyhow!("解析元数据失败: {}", e))?;

        debug!("元数据已加载: {}", metadata_path.display());
        Ok(metadata)
    }
}

// 为了支持clone，我们需要实现Clone
impl Clone for ChunkedDownloader {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            client: self.client.clone(),
            validator: self.validator.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_adaptive_chunk_size() {
        let config = DownloadConfig::default();
        let downloader = ChunkedDownloader::with_config(config).unwrap();

        // 小文件 (50MB)
        let chunk_size = downloader.calculate_adaptive_chunk_size(50 * 1024 * 1024);
        assert_eq!(chunk_size, 16 * 1024 * 1024);

        // 中文件 (300MB)
        let chunk_size = downloader.calculate_adaptive_chunk_size(300 * 1024 * 1024);
        assert_eq!(chunk_size, 32 * 1024 * 1024);

        // 大文件 (1GB)
        let chunk_size = downloader.calculate_adaptive_chunk_size(1024 * 1024 * 1024);
        assert_eq!(chunk_size, 64 * 1024 * 1024);

        // 超大文件 (5GB)
        let chunk_size = downloader.calculate_adaptive_chunk_size(5 * 1024 * 1024 * 1024);
        assert_eq!(chunk_size, 128 * 1024 * 1024);
    }

    #[test]
    fn test_get_missing_chunks() {
        let metadata = DownloadMetadata {
            url: "test".to_string(),
            total_size: 1000,
            chunk_size: 100,
            total_chunks: 10,
            hash_algorithm: HashAlgorithm::SHA256,
            expected_hash: None,
            completed_chunks: HashSet::from([0, 2, 4, 6, 8]),
            chunk_hashes: HashMap::new(),
            metadata_version: METADATA_VERSION,
            user_agent: "test".to_string(),
            created_at: Utc::now(),
            last_updated: Utc::now(),
        };

        let downloader = ChunkedDownloader::with_config(DownloadConfig::default()).unwrap();
        let missing = downloader.get_missing_chunks(&metadata);

        assert_eq!(missing, vec![1, 3, 5, 7, 9]);
    }

    #[test]
    fn test_get_chunk_size() {
        let metadata = DownloadMetadata {
            url: "test".to_string(),
            total_size: 1000,
            chunk_size: 300,
            total_chunks: 4,
            hash_algorithm: HashAlgorithm::SHA256,
            expected_hash: None,
            completed_chunks: HashSet::new(),
            chunk_hashes: HashMap::new(),
            metadata_version: METADATA_VERSION,
            user_agent: "test".to_string(),
            created_at: Utc::now(),
            last_updated: Utc::now(),
        };

        let downloader = ChunkedDownloader::with_config(DownloadConfig::default()).unwrap();

        // 普通块
        assert_eq!(downloader.get_chunk_size(0, &metadata), 300);
        assert_eq!(downloader.get_chunk_size(1, &metadata), 300);
        assert_eq!(downloader.get_chunk_size(2, &metadata), 300);

        // 最后一块（剩余大小）
        assert_eq!(downloader.get_chunk_size(3, &metadata), 100);
    }
}
