use serde::{Deserialize, Serialize};

/// 版本号
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct Version {
    pub major: u16,
    pub minor: u16,
    pub patch: u16,
    pub pre_release: Option<String>,
    pub pre_release_version: Option<u16>,
}

impl Default for Version {
    /// 默认版本号为 0.0.0
    fn default() -> Self {
        Self::const_default()
    }
}

impl std::fmt::Display for Version {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}.{}.{}", self.major, self.minor, self.patch)?;
        if let Some(pre_release) = &self.pre_release {
            write!(f, "-{}", pre_release)?;
        }
        Ok(())
    }
}

impl Version {
    pub fn parse(version: &str) -> anyhow::Result<Self> {
        if version.is_empty() {
            tracing::error!("Version is empty");
            return Err(anyhow::anyhow!("Version is empty"));
        }
        if version.contains(" ") {
            tracing::error!("Version contains spaces");
            return Err(anyhow::anyhow!("Version contains spaces"));
        }
        let version = version.trim();
        // 先分割主版本号和预发布版本
        let (main_version, pre_release_part) = if let Some(dash_pos) = version.find('-') {
            (&version[..dash_pos], Some(&version[dash_pos + 1..]))
        } else {
            (version, None)
        };

        // 解析主版本号
        let parts: Vec<&str> = main_version.split('.').collect();
        if parts.len() != 3 {
            tracing::error!("Invalid version format, expected X.Y.Z");
            return Err(anyhow::anyhow!("Invalid version format, expected X.Y.Z"));
        }

        let major = parts[0].parse().map_err(|e| {
            tracing::error!("Failed to parse major version '{}': {}", parts[0], e);
            e
        })?;
        let minor = parts[1].parse().map_err(|e| {
            tracing::error!("Failed to parse minor version '{}': {}", parts[1], e);
            e
        })?;
        let patch = parts[2].parse().map_err(|e| {
            tracing::error!("Failed to parse patch version '{}': {}", parts[2], e);
            e
        })?;
        // 解析预发布版本
        let (pre_release, pre_release_version) = if let Some(pre_part) = pre_release_part {
            if let Some(dot_pos) = pre_part.find('.') {
                let pre_name = pre_part[..dot_pos].to_string();
                let pre_version = pre_part[dot_pos + 1..].parse().map_err(|e| {
                    tracing::error!("Failed to parse pre-release version: {}", e);
                    e
                })?;
                (Some(pre_name), Some(pre_version))
            } else {
                (Some(pre_part.to_string()), None)
            }
        } else {
            (None, None)
        };

        Ok(Self {
            major,
            minor,
            patch,
            pre_release,
            pre_release_version,
        })
    }
    pub const fn const_default() -> Self {
        Self {
            major: 0,
            minor: 0,
            patch: 0,
            pre_release: None,
            pre_release_version: None,
        }
    }
    pub fn is_zero(&self) -> bool {
        self.major == 0 && self.minor == 0 && self.patch == 0
    }
}

impl PartialEq for Version {
    fn eq(&self, other: &Self) -> bool {
        self.major == other.major
            && self.minor == other.minor
            && self.patch == other.patch
            && self.pre_release == other.pre_release
            && self.pre_release_version == other.pre_release_version
    }
}
impl Eq for Version {}

impl PartialOrd for Version {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for Version {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        // 首先比较主版本号
        let main_cmp = self
            .major
            .cmp(&other.major)
            .then(self.minor.cmp(&other.minor))
            .then(self.patch.cmp(&other.patch));

        if main_cmp != std::cmp::Ordering::Equal {
            return main_cmp;
        }

        // 主版本号相同时，比较预发布版本
        match (&self.pre_release, &other.pre_release) {
            (None, None) => std::cmp::Ordering::Equal,
            (Some(_), None) => std::cmp::Ordering::Less, // 预发布版本 < 正式版本
            (None, Some(_)) => std::cmp::Ordering::Greater, // 正式版本 > 预发布版本
            (Some(pre1), Some(pre2)) => {
                // 都是预发布版本，先比较预发布标识符
                let pre_cmp = pre1.cmp(pre2);
                if pre_cmp != std::cmp::Ordering::Equal {
                    return pre_cmp;
                }

                // 预发布标识符相同，比较预发布版本号
                match (&self.pre_release_version, &other.pre_release_version) {
                    (None, None) => std::cmp::Ordering::Equal,
                    (Some(_), None) => std::cmp::Ordering::Greater, // 有版本号 > 无版本号
                    (None, Some(_)) => std::cmp::Ordering::Less,    // 无版本号 < 有版本号
                    (Some(v1), Some(v2)) => v1.cmp(v2),
                }
            }
        }
    }
}

impl Serialize for Version {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}

impl<'de> Deserialize<'de> for Version {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        Ok(Version::parse(&s).map_err(serde::de::Error::custom)?)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_parse() {
        let version = Version::parse("1.2.3").unwrap();
        assert_eq!(version.major, 1);
        assert_eq!(version.minor, 2);
        assert_eq!(version.patch, 3);
        assert_eq!(version.pre_release, None);
        assert_eq!(version.pre_release_version, None);
    }

    #[test]
    fn test_version_parse_with_prerelease() {
        let version = Version::parse("1.2.3-beta").unwrap();
        assert_eq!(version.major, 1);
        assert_eq!(version.minor, 2);
        assert_eq!(version.patch, 3);
        assert_eq!(version.pre_release, Some("beta".to_string()));
        assert_eq!(version.pre_release_version, None);

        let version_with_number = Version::parse("1.2.3-beta.1").unwrap();
        assert_eq!(version_with_number.major, 1);
        assert_eq!(version_with_number.minor, 2);
        assert_eq!(version_with_number.patch, 3);
        assert_eq!(version_with_number.pre_release, Some("beta".to_string()));
        assert_eq!(version_with_number.pre_release_version, Some(1));
    }

    #[test]
    fn test_version_cmp() {
        let version1 = Version::parse("1.2.3").unwrap();
        let version2 = Version::parse("1.2.4").unwrap();
        assert!(version1 < version2);

        let version3 = Version::parse("1.3.0").unwrap();
        assert!(version1 < version3);

        let version4 = Version::parse("1.2.3").unwrap();
        assert_eq!(version1, version4);

        // 预发布版本 < 正式版本
        let version5 = Version::parse("1.2.3-beta").unwrap();
        assert!(version5 < version1);

        // 预发布版本之间的比较
        let version6 = Version::parse("1.2.3-beta.1").unwrap();
        let version7 = Version::parse("1.2.3-beta.2").unwrap();
        assert!(version6 < version7);

        // 不同预发布标识符的比较
        let version8 = Version::parse("1.2.3-alpha").unwrap();
        assert!(version8 < version5); // alpha < beta

        // 有版本号 vs 无版本号
        assert!(version5 < version6); // beta < beta.1
    }

    #[test]
    fn test_version_parse_errors() {
        assert!(Version::parse("").is_err());
        assert!(Version::parse("1.2 3").is_err());
        assert!(Version::parse("1.2").is_err());
        assert!(Version::parse("1.2.3.4").is_err());
        assert!(Version::parse("a.b.c").is_err());
    }
}
