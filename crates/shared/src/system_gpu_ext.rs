use sysinfo::System;

#[cfg(target_os = "windows")]
use wmi::{COMLibrary, Variant, WMIConnection};

/// GPU information structure compatible with sysinfo style
#[derive(Debug, Clone)]
pub struct Gpu {
    name: String,
    brand: String,
}

impl Gpu {
    pub fn new(name: String, brand: String) -> Self {
        Self { name, brand }
    }

    pub fn name(&self) -> &str {
        &self.name
    }

    pub fn brand(&self) -> &str {
        &self.brand
    }
}

/// Extension trait for sysinfo::System to add GPU detection capability
pub trait SystemGpuExt {
    /// Get all detected GPUs
    fn gpus(&self) -> Vec<Gpu>;
}

impl SystemGpuExt for System {
    fn gpus(&self) -> Vec<Gpu> {
        detect_gpus().unwrap_or_default()
    }
}

/// Detect GPU information based on the current platform
fn detect_gpus() -> Result<Vec<Gpu>, Box<dyn std::error::Error>> {
    #[cfg(target_os = "windows")]
    return detect_gpus_windows();

    #[cfg(target_os = "linux")]
    return detect_gpus_linux();

    #[cfg(target_os = "macos")]
    return detect_gpus_macos();

    #[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
    Ok(vec![])
}

#[cfg(target_os = "windows")]
fn detect_gpus_windows() -> Result<Vec<Gpu>, Box<dyn std::error::Error>> {
    // todo: 参考 machine_id 对 WMI 对访问实现，这样做可能会错误
    let com_con = COMLibrary::new()?;
    let wmi_con = WMIConnection::new(com_con.into())?;

    let results: Vec<std::collections::HashMap<String, Variant>> = wmi_con
        .raw_query("SELECT Name, AdapterCompatibility FROM Win32_VideoController WHERE PNPDeviceID IS NOT NULL")?;

    let mut gpus = Vec::new();
    for result in results {
        let name = result
            .get("Name")
            .and_then(|v| match v {
                Variant::String(s) => Some(s.clone()),
                _ => None,
            })
            .unwrap_or_else(|| "Unknown GPU".to_string());

        let brand = result
            .get("AdapterCompatibility")
            .and_then(|v| match v {
                Variant::String(s) => Some(s.clone()),
                _ => None,
            })
            .or_else(|| extract_brand_from_name(&name))
            .unwrap_or_else(|| "Unknown".to_string());

        gpus.push(Gpu::new(name, brand));
    }

    Ok(gpus)
}

#[cfg(target_os = "linux")]
fn detect_gpus_linux() -> Result<Vec<Gpu>, Box<dyn std::error::Error>> {
    use std::process::Command;

    // Try lspci first
    if let Ok(output) = Command::new("lspci").arg("-nn").output() {
        let output_str = String::from_utf8_lossy(&output.stdout);
        let mut gpus = Vec::new();

        for line in output_str.lines() {
            if line.contains("VGA compatible controller")
                || line.contains("3D controller")
                || line.contains("Display controller")
            {
                let parts: Vec<&str> = line.split(": ").collect();
                if parts.len() >= 2 {
                    let name = parts[1]
                        .split(" [")
                        .next()
                        .unwrap_or("Unknown GPU")
                        .to_string();
                    let brand =
                        extract_brand_from_name(&name).unwrap_or_else(|| "Unknown".to_string());
                    gpus.push(Gpu::new(name, brand));
                }
            }
        }

        if !gpus.is_empty() {
            return Ok(gpus);
        }
    }

    // Fallback: check /proc/driver/nvidia/gpus for NVIDIA GPUs
    if let Ok(entries) = std::fs::read_dir("/proc/driver/nvidia/gpus") {
        let mut gpus = Vec::new();
        for entry in entries.flatten() {
            if let Ok(info_content) = std::fs::read_to_string(entry.path().join("information")) {
                for line in info_content.lines() {
                    if line.starts_with("Model:") {
                        let name = line
                            .split(':')
                            .nth(1)
                            .unwrap_or("Unknown NVIDIA GPU")
                            .trim()
                            .to_string();
                        gpus.push(Gpu::new(name, "NVIDIA".to_string()));
                        break;
                    }
                }
            }
        }
        if !gpus.is_empty() {
            return Ok(gpus);
        }
    }

    Ok(vec![])
}

#[cfg(target_os = "macos")]
fn detect_gpus_macos() -> Result<Vec<Gpu>, Box<dyn std::error::Error>> {
    use std::process::Command;

    let output = Command::new("system_profiler")
        .args(["-xml", "SPDisplaysDataType"])
        .output()?;

    let plist_data = plist::Value::from_reader(std::io::Cursor::new(&output.stdout))?;
    let mut gpus = Vec::new();

    if let plist::Value::Array(items) = plist_data {
        for item in items {
            if let plist::Value::Dictionary(dict) = item {
                if let Some(plist::Value::Array(displays)) = dict.get("_items") {
                    for display in displays {
                        if let plist::Value::Dictionary(display_dict) = display {
                            let name = display_dict
                                .get("sppci_model")
                                .or_else(|| display_dict.get("_name"))
                                .and_then(|v| match v {
                                    plist::Value::String(s) => Some(s.clone()),
                                    _ => None,
                                })
                                .unwrap_or_else(|| "Unknown GPU".to_string());

                            let brand = extract_brand_from_name(&name)
                                .unwrap_or_else(|| "Unknown".to_string());

                            gpus.push(Gpu::new(name, brand));
                        }
                    }
                }
            }
        }
    }

    Ok(gpus)
}

/// Extract GPU brand from the GPU name
fn extract_brand_from_name(name: &str) -> Option<String> {
    let name_lower = name.to_lowercase();

    if name_lower.contains("nvidia")
        || name_lower.contains("geforce")
        || name_lower.contains("quadro")
        || name_lower.contains("tesla")
    {
        Some("NVIDIA".to_string())
    } else if name_lower.contains("amd")
        || name_lower.contains("radeon")
        || name_lower.contains("firepro")
    {
        Some("AMD".to_string())
    } else if name_lower.contains("intel")
        || name_lower.contains("iris")
        || name_lower.contains("uhd")
        || name_lower.contains("hd graphics")
    {
        Some("Intel".to_string())
    } else if name_lower.contains("apple")
        || name_lower.contains("m1")
        || name_lower.contains("m2")
        || name_lower.contains("m3")
    {
        Some("Apple".to_string())
    } else {
        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sysinfo_gpu_extension() {
        // Test the exact user-requested API pattern
        let sys = System::new_all();
        let mut tags = std::collections::HashMap::new();

        if let Some(gpu) = sys.gpus().first() {
            tags.insert("gpu_name".to_string(), gpu.name().to_string());
            tags.insert("gpu_brand".to_string(), gpu.brand().to_string());
        } else {
            tags.insert("gpu_name".to_string(), "N/A".to_string());
            tags.insert("gpu_brand".to_string(), "N/A".to_string());
        }

        // Verify the API works as expected
        assert!(tags.contains_key("gpu_name"));
        assert!(tags.contains_key("gpu_brand"));

        // Print results for verification
        println!("GPU detection result:");
        println!("  Name: {}", tags.get("gpu_name").unwrap());
        println!("  Brand: {}", tags.get("gpu_brand").unwrap());
    }

    #[test]
    fn test_brand_extraction() {
        assert_eq!(
            extract_brand_from_name("NVIDIA GeForce RTX 3080"),
            Some("NVIDIA".to_string())
        );
        assert_eq!(
            extract_brand_from_name("AMD Radeon RX 6800"),
            Some("AMD".to_string())
        );
        assert_eq!(
            extract_brand_from_name("Intel UHD Graphics 630"),
            Some("Intel".to_string())
        );
        assert_eq!(
            extract_brand_from_name("Apple M1 Pro"),
            Some("Apple".to_string())
        );
        assert_eq!(extract_brand_from_name("Some Unknown GPU"), None);
    }

    #[test]
    fn test_gpu_struct() {
        let gpu = Gpu::new("Test GPU".to_string(), "Test Brand".to_string());
        assert_eq!(gpu.name(), "Test GPU");
        assert_eq!(gpu.brand(), "Test Brand");
    }
}
