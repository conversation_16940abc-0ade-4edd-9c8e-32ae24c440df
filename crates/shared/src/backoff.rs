//! A utility for handling retries with incremental backoff delays.
//!
//! This module provides a `Backoff` struct that can be used to manage retry logic
//! with a predefined sequence of wait times. It's useful for scenarios where you
//! need to gracefully handle transient failures, such as network requests.
//!
//! # Example
//!
//! ```rust,no_run
//! use std::time::Duration;
//! use domain::utils::backoff::Backoff;
//!
//! async fn my_flaky_operation() -> Result<(), &'static str> {
//!     // This operation might fail
//!     Err("transient error")
//! }
//!
//! #[tokio::main]
//! async fn main() {
//!     let mut backoff = Backoff::new(5); // Max 5 attempts
//!
//!     loop {
//!         match my_flaky_operation().await {
//!             Ok(_) => {
//!                 println!("Operation succeeded!");
//!                 break;
//!             }
//!             Err(e) => {
//!                 eprintln!("Operation failed: {}. Retrying...", e);
//!                 if backoff.next().await.is_err() {
//!                     eprintln!("Max attempts reached. Aborting.");
//!                     break;
//!                 }
//!             }
//!         }
//!     }
//! }
//! ```

use std::time::Duration;
use tokio::time::sleep;

/// Manages retry attempts with a predefined backoff strategy.
pub struct Backoff {
    attempts: u32,
    max_attempts: u32,
}

/// Error indicating that the maximum number of retry attempts has been reached.
#[derive(Debug, thiserror::Error, PartialEq, Eq)]
#[error("Max attempts reached")]
pub struct MaxAttemptsReached;

impl Backoff {
    const DEFAULT_WAIT_TIMES: [Duration; 9] = [
        Duration::from_secs(1),
        Duration::from_secs(1),
        Duration::from_secs(1),
        Duration::from_secs(2),
        Duration::from_secs(3),
        Duration::from_secs(5),
        Duration::from_secs(10),
        Duration::from_secs(30),
        Duration::from_secs(60),
    ];
    /// Creates a new `Backoff` instance with a specified maximum number of attempts.
    ///
    /// The wait times are predefined as: 1s, 1s, 2s, 3s, 5s, 10s, 30s.
    /// If attempts exceed the number of defined wait times, the last wait time is used.
    pub fn new(max_attempts: u32) -> Self {
        Self {
            attempts: 0,
            max_attempts,
        }
    }

    /// Asynchronously waits for the next backoff interval.
    ///
    /// This should be called after a failed attempt. It will sleep for a duration
    /// determined by the current attempt number.
    ///
    /// # Returns
    ///
    /// - `Ok(())` if the wait was successful and another attempt can be made.
    /// - `Err(MaxAttemptsReached)` if the maximum number of retry attempts has been exceeded.
    pub async fn next(&mut self) -> Result<(), MaxAttemptsReached> {
        if self.attempts >= self.max_attempts {
            return Err(MaxAttemptsReached);
        }

        let wait_time = Self::DEFAULT_WAIT_TIMES
            .get(self.attempts as usize)
            .copied()
            .unwrap_or_else(|| Self::DEFAULT_WAIT_TIMES.last().copied().unwrap_or_default());

        sleep(wait_time).await;

        self.attempts += 1;
        Ok(())
    }

    /// Resets the attempt counter to 0.
    ///
    /// This is useful if the operation succeeds and you want to reuse the `Backoff`
    /// instance for a new sequence of operations.
    pub fn reset(&mut self) {
        self.attempts = 0;
    }

    /// Returns the current number of attempts.
    pub fn attempts(&self) -> u32 {
        self.attempts
    }
}
