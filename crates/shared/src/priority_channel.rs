//! # 异步、有界、带优先级的消息通道
//!
//! 这个模块提供了一个类似于 `tokio::sync::mpsc` 的多生产者、单消费者（MPSC）通道，
//! 但增加了对消息优先级的支持，并内置了防饿死（starvation prevention）机制。
//!
//! ## 核心特性
//!
//! - **优先级队列**: 消息可以被赋予 `High` 或 `Low` 优先级。接收端总是优先处理高优先级的消息。
//! - **有界容量与背压**: 通道具有固定的容量。当通道满时，尝试发送消息的异步任务将会等待，直到通道有可用空间，从而实现背压。
//! - **先进先出 (FIFO)**: 在相同优先级的消息之间，保证先进先出顺序。
//! - **防饿死**: 当大量高优先级消息持续“插队”时，一个内置的阈值机制会确保积压的低优先级消息也能被处理，防止其永远等待。
//! - **优雅关闭**: `Sender` 和 `Receiver` 的行为与 `tokio::sync::mpsc` 类似。当 `Receiver` 被丢弃时，`send` 操作会返回错误。当所有 `Sender` 都被丢弃时，`recv` 会在清空缓冲区后返回 `None`。
//!
//! ## 设计原理
//!
//! 本实现巧妙地组合了 `tokio::sync::mpsc::channel` 和 `std::collections::BinaryHeap`。
//!
//! - **`Sender`**: 发送端非常轻量。它仅负责为每个消息附加一个全局唯一的、递增的序列号，然后将消息发送到底层的 `mpsc` 通道。所有关于容量限制和背压的逻辑都由成熟的 `mpsc` 通道处理。
//!
//! - **`Receiver`**: 接收端是核心逻辑所在。它内部维护一个 `BinaryHeap` 作为排序缓冲区。当 `recv` 被调用时：
//!   1. 它会尝试从底层 `mpsc` 通道拉取一批消息来填满自己的缓冲区。这样做可以更准确地对一批消息进行优先级排序。
//!   2. 它会检查防饿死阈值。如果积压的低优先级消息过多，它会强制从缓冲区中提取一个低优先级消息返回。
//!   3. 否则，它会从 `BinaryHeap` 中弹出优先级最高的消息返回。
//!
//! 这种设计既利用了 `mpsc` 的健壮性，又实现了复杂的优先级和防饿死逻辑。
//!
//! ## 使用示例
//!
//! ```rust
//! use crate::priority_channel::{self, Priority};
//!
//! #[tokio::main]
//! async fn main() {
//!     // 创建一个容量为 10 的通道
//!     let (tx, mut rx) = priority_channel::channel(10);
//!
//!     // 异步发送消息
//!     tokio::spawn(async move {
//!         tx.send("Low Prio Task", Priority::Low).await.unwrap();
//!         tx.send("High Prio Task 1", Priority::High).await.unwrap();
//!         tx.send("High Prio Task 2", Priority::High).await.unwrap();
//!     });
//!
//!     // 接收消息
//!     // 即使 "Low Prio Task" 先发送，高优先级的也会先被接收
//!     let msg1 = rx.recv().await.unwrap();
//!     assert_eq!(msg1.priority, Priority::High);
//!
//!     let msg2 = rx.recv().await.unwrap();
//!     assert_eq!(msg2.priority, Priority::High);
//!
//!     let msg3 = rx.recv().await.unwrap();
//!     assert_eq!(msg3.priority, Priority::Low);
//!     assert_eq!(msg3.item, "Low Prio Task");
//! }
//! ```
//!
//! ## 配置参数
//!
//! `channel_with_config` 函数允许你精细调整通道的行为：
//! - `capacity`: 通道的总缓冲区大小，用于背压。
//! - `buffer_capacity`: `Receiver` 内部用于排序的缓冲区大小。它应该小于或等于 `capacity`。更大的值能提供更准确的优先级排序，但会增加一点点延迟。
//! - `starvation_threshold`: 防饿死阈值。当缓冲区内低优先级消息的数量达到此值时，`Receiver` 会强制处理一个低优先级消息。

use std::collections::BinaryHeap;
use tokio::sync::mpsc::{
    self,
    error::{SendError, TrySendError},
};

/// 消息优先级
///
/// `High` 优先级高于 `Low` 优先级。
/// `BinaryHeap` 是一个最大堆，所以 `High` 的值需要大于 `Low`。
#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum Priority {
    Low = 0,
    High = 1,
}

/// 带有优先级和序列号的消息包装器
///
/// 序列号用于在相同优先级内实现FIFO排序，并支持防饿死机制。
#[derive(Debug)]
pub struct Prioritized<T> {
    pub item: T,
    pub priority: Priority,
    sequence: u64,
}

impl<T> Prioritized<T> {
    pub fn new(item: T, priority: Priority, sequence: u64) -> Self {
        Self {
            item,
            priority,
            sequence,
        }
    }
}

impl<T> Ord for Prioritized<T> {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        // 首先按优先级排序
        match self.priority.cmp(&other.priority) {
            std::cmp::Ordering::Equal => {
                // 相同优先级按序列号排序（较小的序列号优先，实现FIFO）
                // BinaryHeap是最大堆，所以序列号小的应该“更大”
                other.sequence.cmp(&self.sequence)
            }
            other => other,
        }
    }
}

impl<T> PartialOrd for Prioritized<T> {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl<T> PartialEq for Prioritized<T> {
    fn eq(&self, other: &Self) -> bool {
        self.priority == other.priority && self.sequence == other.sequence
    }
}

impl<T> Eq for Prioritized<T> {}

/// 优先级通道的发送端
#[derive(Clone)]
pub struct Sender<T> {
    inner: mpsc::Sender<Prioritized<T>>,
    sequence_counter: std::sync::Arc<std::sync::atomic::AtomicU64>,
}

impl<T> Sender<T> {
    /// 异步发送一个带优先级的消息。
    ///
    /// 如果通道已满，此方法会异步等待直到有空间可用。
    /// 如果接收端被丢弃，则返回 `SendError`。
    pub async fn send(&self, item: T, priority: Priority) -> Result<(), SendError<T>> {
        let sequence = self
            .sequence_counter
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        let prioritized = Prioritized::new(item, priority, sequence);
        self.inner
            .send(prioritized)
            .await
            .map_err(|e| SendError(e.0.item))
    }

    /// 尝试立即发送消息（非阻塞）。
    ///
    /// 如果通道已满或已关闭，会立即返回错误。
    pub fn try_send(&self, item: T, priority: Priority) -> Result<(), TrySendError<T>> {
        let sequence = self
            .sequence_counter
            .fetch_add(1, std::sync::atomic::Ordering::Relaxed);
        let prioritized = Prioritized::new(item, priority, sequence);
        self.inner.try_send(prioritized).map_err(|e| match e {
            TrySendError::Full(msg) => TrySendError::Full(msg.item),
            TrySendError::Closed(msg) => TrySendError::Closed(msg.item),
        })
    }
}

/// 优先级通道的接收端
pub struct Receiver<T> {
    inner: mpsc::Receiver<Prioritized<T>>,
    buffer: BinaryHeap<Prioritized<T>>,
    buffer_capacity: usize,
    low_priority_count: usize,
    starvation_threshold: usize,
}

impl<T> Receiver<T> {
    /// 异步接收一个消息。
    ///
    /// 如果所有发送端都已被丢弃且缓冲区为空，则返回 `None`。
    /// 消息按优先级排序返回，但包含防饿死机制。
    pub async fn recv_with_priority(&mut self) -> Option<Prioritized<T>> {
        loop {
            // 如果缓冲区有消息，检查是否需要优先处理
            if !self.buffer.is_empty() {
                // 防饿死检查：如果低优先级消息积累太多，强制处理一个
                if self.should_process_low_priority() {
                    if let Some(low_item) = self.extract_oldest_low_priority_item() {
                        return Some(low_item);
                    }
                }

                // 正常返回优先级最高的消息
                if let Some(item) = self.buffer.pop() {
                    self.update_low_priority_count_on_remove(&item);
                    return Some(item);
                }
            }

            // 缓冲区为空，从底层通道接收新消息并填充缓冲区
            match self.inner.recv().await {
                Some(item) => {
                    self.buffer.push(item);

                    // 非阻塞地拉取更多消息来填满缓冲区，以优化排序
                    while self.buffer.len() < self.buffer_capacity {
                        match self.inner.try_recv() {
                            Ok(additional_item) => self.buffer.push(additional_item),
                            Err(_) => break, // 通道为空或关闭
                        }
                    }

                    // 重新计算低优先级消息数量
                    self.count_low_priority_messages();
                    // 继续到下一次循环来从缓冲区中提取消息
                }
                None => {
                    // 底层通道已关闭，返回缓冲区中剩余的最后一个消息
                    return if let Some(item) = self.buffer.pop() {
                        self.update_low_priority_count_on_remove(&item);
                        Some(item)
                    } else {
                        None // 缓冲区也为空，通道彻底关闭
                    };
                }
            }
        }
    }

    pub async fn recv(&mut self) -> Option<T> {
        self.recv_with_priority().await.map(|item| item.item)
    }

    /// 检查是否应该强制处理低优先级消息
    fn should_process_low_priority(&self) -> bool {
        self.low_priority_count >= self.starvation_threshold
    }

    /// 从缓冲区中提取序列号最小（最老）的低优先级消息
    fn extract_oldest_low_priority_item(&mut self) -> Option<Prioritized<T>> {
        let mut oldest_low_prio: Option<Prioritized<T>> = None;
        let mut temp_heap = BinaryHeap::with_capacity(self.buffer.len());

        // 遍历寻找最老的低优先级消息
        while let Some(item) = self.buffer.pop() {
            if item.priority == Priority::Low {
                if oldest_low_prio.is_none()
                    || item.sequence < oldest_low_prio.as_ref().unwrap().sequence
                {
                    if let Some(old) = oldest_low_prio.replace(item) {
                        temp_heap.push(old);
                    }
                } else {
                    temp_heap.push(item);
                }
            } else {
                temp_heap.push(item);
            }
        }

        // 将剩余项放回主缓冲区
        self.buffer = temp_heap;

        if oldest_low_prio.is_some() {
            self.low_priority_count = self.low_priority_count.saturating_sub(1);
        }

        oldest_low_prio
    }

    /// 统计缓冲区中的低优先级消息数量
    fn count_low_priority_messages(&mut self) {
        self.low_priority_count = self
            .buffer
            .iter()
            .filter(|item| item.priority == Priority::Low)
            .count();
    }

    /// 当一个消息从缓冲区移除时，更新低优先级计数
    fn update_low_priority_count_on_remove(&mut self, item: &Prioritized<T>) {
        if item.priority == Priority::Low {
            self.low_priority_count = self.low_priority_count.saturating_sub(1);
        }
    }
}

/// 创建一个新的有界优先级通道。
///
/// 这是 `channel_with_config` 的一个便捷包装，提供了合理的默认配置。
///
/// # Arguments
/// * `capacity` - 通道的总容量。
pub fn channel<T>(capacity: usize) -> (Sender<T>, Receiver<T>) {
    // 默认排序缓冲区大小为 capacity 和 32 中的较小者
    let buffer_capacity = capacity.min(32);
    // 默认防饿死阈值为排序缓冲区大小的 1/4，但至少为 1
    let starvation_threshold = (buffer_capacity / 4).max(1);
    channel_with_config(capacity, buffer_capacity, starvation_threshold)
}

/// 创建一个带自定义配置的优先级通道。
///
/// # Arguments
/// * `capacity` - 通道的总容量，用于背压。
/// * `buffer_capacity` - `Receiver` 内部用于排序的缓冲区大小。它应该小于或等于 `capacity`。
/// * `starvation_threshold` - 防饿死阈值。当缓冲区内低优先级消息的数量达到此值时，会强制处理一个。
pub fn channel_with_config<T>(
    capacity: usize,
    buffer_capacity: usize,
    starvation_threshold: usize,
) -> (Sender<T>, Receiver<T>) {
    assert!(capacity > 0, "Capacity must be greater than 0");
    assert!(
        buffer_capacity > 0,
        "Buffer capacity must be greater than 0"
    );

    let (tx, rx) = mpsc::channel(capacity);

    let sender = Sender {
        inner: tx,
        sequence_counter: std::sync::Arc::new(std::sync::atomic::AtomicU64::new(0)),
    };
    let receiver = Receiver {
        inner: rx,
        buffer: BinaryHeap::with_capacity(buffer_capacity),
        buffer_capacity,
        low_priority_count: 0,
        starvation_threshold: starvation_threshold.max(1),
    };

    (sender, receiver)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[tokio::test]
    async fn test_priority_order() {
        let (tx, mut rx) = channel_with_config(5, 5, 999);

        tx.send("low", Priority::Low).await.unwrap();
        tx.send("high", Priority::High).await.unwrap();

        drop(tx);

        let rec1 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec1.priority, Priority::High);
        assert_eq!(rec1.item, "high");

        let rec2 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec2.priority, Priority::Low);
        assert_eq!(rec2.item, "low");
    }

    #[tokio::test]
    async fn test_receiver_waits_for_message() {
        let (tx, mut rx) = channel::<&str>(1);

        let receiver_task = tokio::spawn(async move { rx.recv_with_priority().await });
        tokio::time::sleep(Duration::from_millis(20)).await;

        tx.send("late_message", Priority::Low).await.unwrap();
        let received = receiver_task.await.unwrap().unwrap();
        assert_eq!(received.item, "late_message");
    }

    #[tokio::test]
    async fn test_multiple_priority_messages() {
        let (tx, mut rx) = channel_with_config(10, 10, 999);

        tx.send("low1", Priority::Low).await.unwrap();
        tx.send("high1", Priority::High).await.unwrap();
        tx.send("low2", Priority::Low).await.unwrap();
        tx.send("high2", Priority::High).await.unwrap();

        drop(tx);

        let rec1 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec1.priority, Priority::High);
        let rec2 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec2.priority, Priority::High);
        let rec3 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec3.priority, Priority::Low);
        let rec4 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec4.priority, Priority::Low);

        let high_items = vec![rec1.item, rec2.item];
        assert!(high_items.contains(&"high1"));
        assert!(high_items.contains(&"high2"));

        let low_items = vec![rec3.item, rec4.item];
        assert!(low_items.contains(&"low1"));
        assert!(low_items.contains(&"low2"));
    }

    #[tokio::test]
    async fn test_fifo_within_same_priority() {
        let (tx, mut rx) = channel_with_config(12, 12, 999);

        for i in 0..5 {
            tx.send(i, Priority::High).await.unwrap();
        }
        for i in 5..10 {
            tx.send(i, Priority::Low).await.unwrap();
        }
        drop(tx);

        for i in 0..5 {
            let received = rx.recv_with_priority().await.unwrap();
            assert_eq!(received.item, i);
            assert_eq!(received.priority, Priority::High);
        }
        for i in 5..10 {
            let received = rx.recv_with_priority().await.unwrap();
            assert_eq!(received.item, i);
            assert_eq!(received.priority, Priority::Low);
        }
    }

    #[tokio::test]
    async fn test_starvation_prevention() {
        // 饿死阈值为 2，缓冲区为 5
        let (tx, mut rx) = channel_with_config(10, 5, 2);

        // 发送2个低优先级，填满饿死阈值
        tx.send("low1", Priority::Low).await.unwrap();
        tx.send("low2", Priority::Low).await.unwrap();

        // 发送3个高优先级
        tx.send("high1", Priority::High).await.unwrap();
        tx.send("high2", Priority::High).await.unwrap();
        tx.send("high3", Priority::High).await.unwrap();

        drop(tx);

        // 第一次 recv 会填充缓冲区，此时有2个low, 3个high
        // low_priority_count 会是 2，满足饿死条件
        // 应该先收到最老的 low1
        let rec1 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec1.item, "low1");
        assert_eq!(rec1.priority, Priority::Low);

        // 接下来缓冲区里有 1个low, 3个high，不满足饿死条件
        // 应该收到高优先级的
        let rec2 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec2.priority, Priority::High);
        let rec3 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec3.priority, Priority::High);
        let rec4 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec4.priority, Priority::High);

        // 最后收到剩下的 low2
        let rec5 = rx.recv_with_priority().await.unwrap();
        assert_eq!(rec5.item, "low2");
        assert_eq!(rec5.priority, Priority::Low);
    }
}
