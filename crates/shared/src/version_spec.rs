use std::str::FromStr;

use crate::version::Version;

#[derive(Debu<PERSON>, Clone)]
pub enum Operator {
    GreaterThan,
    GreaterThanOrEqual,
    <PERSON><PERSON><PERSON>,
    LessThanOrEqual,
    Equal,
}

impl std::fmt::Display for Operator {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Operator::GreaterThan => write!(f, ">"),
            Operator::GreaterThanOrEqual => write!(f, ">="),
            Operator::LessThan => write!(f, "<"),
            Operator::LessThanOrEqual => write!(f, "<="),
            Operator::Equal => write!(f, "="),
        }
    }
}

#[derive(Debug, Clone)]
pub struct Constraint {
    pub op: Operator,
    pub version: Version,
}

impl Constraint {
    pub fn is_satisfied(&self, version: &Version) -> bool {
        if version.is_zero() {
            return true;
        }
        match self.op {
            Operator::Greater<PERSON>han => version > &self.version,
            Operator::GreaterThanOrEqual => version >= &self.version,
            Operator::LessThan => version < &self.version,
            Operator::LessThanOrEqual => version <= &self.version,
            Operator::Equal => version == &self.version,
        }
    }
}

impl FromStr for Constraint {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let s = s.trim();

        // 操作符列表，注意长的要放前面，以正确匹配 `>=` 而不是 `>`
        const OPERATORS: [(&str, Operator); 5] = [
            (">=", Operator::GreaterThanOrEqual),
            ("<=", Operator::LessThanOrEqual),
            (">", Operator::GreaterThan),
            ("<", Operator::LessThan),
            ("=", Operator::Equal),
        ];
        for (op_str, op) in OPERATORS {
            if let Some(version_str) = s.strip_prefix(op_str) {
                let version = Version::parse(version_str.trim())?;
                return Ok(Constraint { op, version });
            }
        }
        anyhow::bail!("未知的操作符在约束中: {}", s);
    }
}

impl std::fmt::Display for Constraint {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}{}", self.op, self.version)
    }
}

#[derive(Debug, Clone)]
pub struct VersionSpec {
    pub constraints: Vec<Constraint>,
}

impl VersionSpec {
    pub fn new(constraints: Vec<Constraint>) -> Self {
        Self { constraints }
    }

    pub fn matches(&self, version: &Version) -> bool {
        self.constraints.iter().all(|c| c.is_satisfied(version))
    }
}

impl FromStr for VersionSpec {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let constraints = s
            .split_whitespace() // 按空格分割
            .map(|part| part.parse::<Constraint>()) // 解析每个部分
            .collect::<Result<Vec<Constraint>, _>>()?; // 收集结果，任何一个失败则整体失败

        Ok(VersionSpec { constraints })
    }
}

impl std::fmt::Display for VersionSpec {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        for (i, constraint) in self.constraints.iter().enumerate() {
            if i > 0 {
                write!(f, " ")?;
            }
            write!(f, "{}", constraint)?;
        }
        Ok(())
    }
}

impl serde::Serialize for VersionSpec {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: serde::Serializer,
    {
        serializer.serialize_str(&self.to_string())
    }
}
impl<'de> serde::Deserialize<'de> for VersionSpec {
    fn deserialize<D>(deserializer: D) -> Result<Self, D::Error>
    where
        D: serde::Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        VersionSpec::from_str(&s).map_err(serde::de::Error::custom)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_spec_from_str() {
        let spec = VersionSpec::from_str(">=1.0.0 <2.0.0").unwrap();
        assert!(spec.matches(&Version::parse("1.0.0").unwrap()));
    }
    #[test]
    fn it_works() {
        let constraint_str = ">=2.0.0 <3.0.0";
        println!("--- 解析约束: '{}' ---", constraint_str);

        // 使用我们实现的 FromStr 来解析
        let spec = match constraint_str.parse::<VersionSpec>() {
            Ok(s) => {
                println!("解析成功: {:#?}", s);
                s
            }
            Err(e) => {
                panic!("解析失败: {}", e);
            }
        };

        println!("\n--- 开始版本匹配测试 ---");
        let versions_to_test = ["1.9.9", "2.0.0", "2.1.5", "2.9.9", "3.0.0", "3.1.0"];

        for v_str in versions_to_test {
            let version = Version::parse(v_str).unwrap();
            let does_match = spec.matches(&version);
            println!(
                "版本 '{}' 是否匹配? {}",
                v_str,
                if does_match { "✅ 是" } else { "❌ 否" }
            );
        }

        println!("\n--- 测试一个无效的约束 ---");
        let invalid_constraint_str = ">=2.0.0 &3.0.0";
        match invalid_constraint_str.parse::<VersionSpec>() {
            Ok(_) => panic!("错误：不应解析成功！"),
            Err(e) => println!("成功捕获错误: {}", e),
        }
    }
}
