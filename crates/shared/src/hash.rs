//! 文件校验和计算工具
//!
//! 支持多种校验算法的高性能校验工具：
//! - MD5 校验
//! - SHA256 校验
//! - 流式处理大文件
//! - 块级校验支持
use serde::{Deserialize, Serialize};
use std::path::Path;
use tokio::io::AsyncReadExt;
use tracing::{debug, info};

#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum HashAlgorithm {
    MD5,
    SHA256,
    SHA512,
}

impl std::fmt::Display for HashAlgorithm {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            HashAlgorithm::MD5 => write!(f, "MD5"),
            HashAlgorithm::SHA256 => write!(f, "SHA256"),
            HashAlgorithm::SHA512 => write!(f, "SHA512"),
        }
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum HashOutputEncoding {
    Hex,
    Base64,
}

impl HashOutputEncoding {
    pub fn to_string(&self, hash_bytes: &[u8]) -> String {
        match self {
            HashOutputEncoding::Hex => Self::to_hex(hash_bytes),
            HashOutputEncoding::Base64 => Self::to_base64(hash_bytes),
        }
    }
    fn to_hex(bytes: &[u8]) -> String {
        bytes
            .iter()
            .map(|it| format!("{:02x}", it))
            .collect::<String>()
    }
    fn to_base64(bytes: &[u8]) -> String {
        use base64::{Engine as _, engine::general_purpose};
        general_purpose::STANDARD.encode(bytes)
    }
}

/// 校验和验证器
#[derive(Debug, Clone)]
pub struct HashValidator {
    /// 缓冲区大小，用于流式读取文件
    buffer_size: usize,
    encoding: HashOutputEncoding,
}

impl Default for HashValidator {
    fn default() -> Self {
        Self {
            buffer_size: 65536, // 64KB
            encoding: HashOutputEncoding::Hex,
        }
    }
}

impl HashValidator {
    /// 创建新的哈希验证器
    ///
    /// # 参数
    /// - `buffer_size`: 读取缓冲区大小，默认 64KB
    /// - `encoding`: 哈希编码格式，默认 Hex
    pub fn new(buffer_size: usize, encoding: HashOutputEncoding) -> Self {
        Self {
            buffer_size: buffer_size,
            encoding: encoding,
        }
    }

    /// 设置读取缓冲区大小
    ///
    /// # 参数
    /// - `buffer_size`: 读取缓冲区大小
    ///
    /// # 返回值
    /// 返回新的哈希验证器
    pub fn with_buffer_size(self, buffer_size: usize) -> Self {
        Self {
            buffer_size,
            ..self
        }
    }

    /// 设置哈希编码格式
    ///
    /// # 参数
    /// - `encoding`: 哈希编码格式
    ///
    /// # 返回值
    /// 返回新的哈希验证器
    pub fn with_encoding(self, encoding: HashOutputEncoding) -> Self {
        Self { encoding, ..self }
    }

    /// 计算文件哈希
    ///
    /// 使用流式处理，适合大文件
    ///
    /// # 参数
    /// - `file_path`: 文件路径
    /// - `algorithm`: 哈希算法
    ///
    /// # 返回值
    /// 返回十六进制格式的哈希字符串
    pub async fn compute_hash(
        &self,
        file_path: &Path,
        algorithm: HashAlgorithm,
    ) -> anyhow::Result<String> {
        info!(
            "计算文件哈希: {} (算法: {})",
            file_path.display(),
            algorithm
        );

        match algorithm {
            HashAlgorithm::MD5 => self.compute_md5(file_path).await,
            HashAlgorithm::SHA256 => self.compute_sha256(file_path).await,
            HashAlgorithm::SHA512 => self.compute_sha512(file_path).await,
        }
    }

    /// 验证文件哈希
    ///
    /// # 参数
    /// - `file_path`: 文件路径
    /// - `expected_hash`: 期望的哈希
    /// - `algorithm`: 哈希算法
    ///
    /// # 返回值
    /// 如果校验通过返回 `true`，否则返回 `false`
    pub async fn verify_hash(
        &self,
        file_path: &Path,
        expected_hash: &str,
        algorithm: HashAlgorithm,
    ) -> anyhow::Result<bool> {
        let actual_hash = self.compute_hash(file_path, algorithm).await?;
        let expected_lower = expected_hash.to_lowercase();
        let actual_lower = actual_hash.to_lowercase();

        debug!(
            "哈希验证: 期望={}, 实际={}, 匹配={}",
            expected_lower,
            actual_lower,
            expected_lower == actual_lower
        );

        Ok(expected_lower == actual_lower)
    }

    /// 计算字节数据的哈希
    ///
    /// 用于块级校验
    ///
    /// # 参数
    /// - `data`: 字节数据
    /// - `algorithm`: 哈希算法
    ///
    /// # 返回值
    /// 返回十六进制格式的哈希字符串
    pub fn compute_bytes_hash(&self, data: &[u8], algorithm: HashAlgorithm) -> String {
        match algorithm {
            HashAlgorithm::MD5 => {
                use md5::Context as Md5;
                let mut hasher = Md5::new();
                hasher.consume(data);
                format!("{:x}", hasher.finalize())
            }
            HashAlgorithm::SHA256 => {
                use sha2::{Digest, Sha256};
                let mut hasher = Sha256::new();
                hasher.update(data);
                format!("{:x}", hasher.finalize())
            }
            HashAlgorithm::SHA512 => {
                use sha2::{Digest, Sha512};
                let mut hasher = Sha512::new();
                hasher.update(data);
                format!("{:x}", hasher.finalize())
            }
        }
    }

    /// 流式计算 MD5 哈希
    async fn compute_md5(&self, file_path: &Path) -> anyhow::Result<String> {
        use md5::Context as Md5;

        let mut file = tokio::fs::File::open(file_path).await.map_err(|e| {
            tracing::error!("打开文件失败 {}: {}", file_path.display(), e);
            anyhow::anyhow!("打开文件失败 {}: {}", file_path.display(), e)
        })?;

        let mut hasher = Md5::new();
        let mut buffer = vec![0; self.buffer_size];

        loop {
            let bytes_read = file.read(&mut buffer).await.map_err(|e| {
                tracing::error!("读取文件失败 {}: {}", file_path.display(), e);
                anyhow::anyhow!("读取文件失败 {}: {}", file_path.display(), e)
            })?;

            if bytes_read == 0 {
                break;
            }

            hasher.consume(&buffer[..bytes_read]);
        }

        Ok(self.encoding.to_string(&hasher.finalize().0))
    }

    /// 流式计算 SHA256 哈希
    async fn compute_sha256(&self, file_path: &Path) -> anyhow::Result<String> {
        use sha2::{Digest, Sha256};

        let mut file = tokio::fs::File::open(file_path).await.map_err(|e| {
            tracing::error!("打开文件失败 {}: {}", file_path.display(), e);
            anyhow::anyhow!("打开文件失败 {}: {}", file_path.display(), e)
        })?;

        let mut hasher = Sha256::new();
        let mut buffer = vec![0; self.buffer_size];

        loop {
            let bytes_read = file.read(&mut buffer).await.map_err(|e| {
                tracing::error!("读取文件失败 {}: {}", file_path.display(), e);
                anyhow::anyhow!("读取文件失败 {}: {}", file_path.display(), e)
            })?;

            if bytes_read == 0 {
                break;
            }

            hasher.update(&buffer[..bytes_read]);
        }

        Ok(self.encoding.to_string(&hasher.finalize()))
    }

    /// 流式计算 SHA512 哈希
    async fn compute_sha512(&self, file_path: &Path) -> anyhow::Result<String> {
        use sha2::{Digest, Sha512};

        let mut file = tokio::fs::File::open(file_path).await.map_err(|e| {
            tracing::error!("打开文件失败 {}: {}", file_path.display(), e);
            anyhow::anyhow!("打开文件失败 {}: {}", file_path.display(), e)
        })?;

        let mut hasher = Sha512::new();
        let mut buffer = vec![0; self.buffer_size];

        loop {
            let bytes_read = file.read(&mut buffer).await.map_err(|e| {
                tracing::error!("读取文件失败 {}: {}", file_path.display(), e);
                anyhow::anyhow!("读取文件失败 {}: {}", file_path.display(), e)
            })?;

            if bytes_read == 0 {
                break;
            }

            hasher.update(&buffer[..bytes_read]);
        }

        Ok(self.encoding.to_string(&hasher.finalize()))
    }

    /// 计算文件大小和哈希（组合操作）
    ///
    /// 一次读取同时获取文件大小和哈希，提高效率
    ///
    /// # 参数
    /// - `file_path`: 文件路径
    /// - `algorithm`: 哈希算法
    ///
    /// # 返回值
    /// 返回 (文件大小, 哈希) 元组
    pub async fn compute_size_and_hash(
        &self,
        file_path: &Path,
        algorithm: HashAlgorithm,
    ) -> anyhow::Result<(u64, String)> {
        info!(
            "计算文件大小和哈希: {} (算法: {})",
            file_path.display(),
            algorithm
        );

        let mut file = tokio::fs::File::open(file_path).await.map_err(|e| {
            tracing::error!("打开文件失败 {}: {}", file_path.display(), e);
            anyhow::anyhow!("打开文件失败 {}: {}", file_path.display(), e)
        })?;

        let mut total_size = 0u64;
        let mut buffer = vec![0; self.buffer_size];

        let hash = match algorithm {
            HashAlgorithm::MD5 => {
                use md5::Context as Md5;
                let mut hasher = Md5::new();

                loop {
                    let bytes_read = file.read(&mut buffer).await.map_err(|e| {
                        tracing::error!("读取文件失败 {}: {}", file_path.display(), e);
                        anyhow::anyhow!("读取文件失败 {}: {}", file_path.display(), e)
                    })?;

                    if bytes_read == 0 {
                        break;
                    }

                    total_size += bytes_read as u64;
                    hasher.consume(&buffer[..bytes_read]);
                }

                format!("{:x}", hasher.finalize())
            }
            HashAlgorithm::SHA256 => {
                use sha2::{Digest, Sha256};
                let mut hasher = Sha256::new();

                loop {
                    let bytes_read = file.read(&mut buffer).await.map_err(|e| {
                        tracing::error!("读取文件失败 {}: {}", file_path.display(), e);
                        anyhow::anyhow!("读取文件失败 {}: {}", file_path.display(), e)
                    })?;

                    if bytes_read == 0 {
                        break;
                    }

                    total_size += bytes_read as u64;
                    hasher.update(&buffer[..bytes_read]);
                }

                format!("{:x}", hasher.finalize())
            }
            HashAlgorithm::SHA512 => {
                use sha2::{Digest, Sha512};
                let mut hasher = Sha512::new();

                loop {
                    let bytes_read = file.read(&mut buffer).await.map_err(|e| {
                        tracing::error!("读取文件失败 {}: {}", file_path.display(), e);
                        anyhow::anyhow!("读取文件失败 {}: {}", file_path.display(), e)
                    })?;

                    if bytes_read == 0 {
                        break;
                    }

                    total_size += bytes_read as u64;
                    hasher.update(&buffer[..bytes_read]);
                }

                format!("{:x}", hasher.finalize())
            }
        };

        debug!(
            "文件 {} 大小: {} 字节, {} 哈希: {}",
            file_path.display(),
            total_size,
            algorithm,
            hash
        );

        Ok((total_size, hash))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_compute_hash_md5() {
        let validator = HashValidator::default();

        // 创建临时文件
        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = b"Hello, World!";
        temp_file.write_all(test_data).unwrap();

        // 计算 MD5
        let md5_result = validator
            .compute_hash(temp_file.path(), HashAlgorithm::MD5)
            .await
            .unwrap();

        // 验证结果 (Hello, World! 的 MD5)
        assert_eq!(md5_result, "65a8e27d8879283831b664bd8b7f0ad4");
    }

    #[tokio::test]
    async fn test_compute_hash_sha256() {
        let validator = HashValidator::default();

        // 创建临时文件
        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = b"Hello, World!";
        temp_file.write_all(test_data).unwrap();

        // 计算 SHA256
        let sha256_result = validator
            .compute_hash(temp_file.path(), HashAlgorithm::SHA256)
            .await
            .unwrap();

        // 验证结果 (Hello, World! 的 SHA256)
        assert_eq!(
            sha256_result,
            "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"
        );
    }

    #[tokio::test]
    async fn test_verify_hash_success() {
        let validator = HashValidator::default();

        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = b"Hello, World!";
        temp_file.write_all(test_data).unwrap();

        let expected_md5 = "65a8e27d8879283831b664bd8b7f0ad4";

        let result = validator
            .verify_hash(temp_file.path(), expected_md5, HashAlgorithm::MD5)
            .await
            .unwrap();

        assert!(result);
    }

    #[tokio::test]
    async fn test_verify_hash_failure() {
        let validator = HashValidator::default();

        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = b"Hello, World!";
        temp_file.write_all(test_data).unwrap();

        let wrong_checksum = "wrong_checksum";

        let result = validator
            .verify_hash(temp_file.path(), wrong_checksum, HashAlgorithm::MD5)
            .await
            .unwrap();

        assert!(!result);
    }

    #[test]
    fn test_compute_bytes_hash() {
        let validator = HashValidator::default();
        let test_data = b"Hello, World!";

        let md5_result = validator.compute_bytes_hash(test_data, HashAlgorithm::MD5);
        assert_eq!(md5_result, "65a8e27d8879283831b664bd8b7f0ad4");

        let sha256_result = validator.compute_bytes_hash(test_data, HashAlgorithm::SHA256);
        assert_eq!(
            sha256_result,
            "dffd6021bb2bd5b0af676290809ec3a53191dd81c7f70a4b28688a362182986f"
        );
    }

    #[tokio::test]
    async fn test_compute_size_and_hash() {
        let validator = HashValidator::default();

        let mut temp_file = NamedTempFile::new().unwrap();
        let test_data = b"Hello, World!";
        temp_file.write_all(test_data).unwrap();

        let (size, checksum) = validator
            .compute_size_and_hash(temp_file.path(), HashAlgorithm::MD5)
            .await
            .unwrap();

        assert_eq!(size, test_data.len() as u64);
        assert_eq!(checksum, "65a8e27d8879283831b664bd8b7f0ad4");
    }
}
