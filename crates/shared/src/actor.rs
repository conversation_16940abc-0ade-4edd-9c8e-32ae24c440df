//! 通用 Actor 模式实现
//!
//! 提供可复用的 Actor 基础设施，减少样板代码

use std::{pin::Pin, time::Duration};

use futures::FutureExt;
use tokio::sync::{mpsc, oneshot};

/// Actor trait
/// 定义 Actor 的基本行为
pub trait Actor<M>: Sized + Send + 'static
where
    M: Send + Identifiable + 'static,
{
    /// 获取 Actor 名称
    fn name(&self) -> &'static str;
    /// 在 Actor 启动时调用
    fn on_start(&mut self, handle: ActorHandle<M>);
    /// 处理消息
    fn handle(&mut self, msg: M) -> Pin<Box<dyn Future<Output = ()> + Send + '_>>;
    /// 在 Actor 退出时调用
    fn on_exit(&mut self) -> () {}
}

/// Actor 运行器
/// 负责运行 Actor 的事件循环
pub struct ActorRunner<A, C> {
    actor: A,
    receiver: mpsc::Receiver<C>,
}

pub trait Identifiable {
    fn name(&self) -> &'static str;
}

impl<A, C> ActorRunner<A, C>
where
    A: Actor<C>,
    C: Send + Identifiable + 'static,
{
    pub fn new(actor: A, receiver: mpsc::Receiver<C>) -> Self {
        Self { actor, receiver }
    }

    pub async fn run(mut self) {
        tracing::info!("{} actor started", self.actor.name());

        while let Some(cmd) = self.receiver.recv().await {
            let actor_name = self.actor.name();
            let cmd_name = cmd.name();
            let fut = Actor::handle(&mut self.actor, cmd);
            let t = Duration::from_millis(3_000);
            let sleep = tokio::time::sleep(t).boxed();
            match futures::future::select(fut, sleep).await {
                futures::future::Either::Left((_, _)) => {}
                futures::future::Either::Right((_, fut)) => {
                    tracing::warn!(
                        actor = actor_name,
                        command = cmd_name,
                        duration = t.as_millis(),
                        "Actor message handling took longer than expected. This might indicate a performance bottleneck."
                    );
                    fut.await;
                }
            };
        }

        tracing::info!("{} actor stopped", self.actor.name());
    }
}

/// Actor 句柄
/// 提供与 Actor 通信的接口
pub struct ActorHandle<M: Send + 'static> {
    sender: mpsc::Sender<M>,
}

impl<M: Send + 'static> Clone for ActorHandle<M> {
    fn clone(&self) -> Self {
        Self {
            sender: self.sender.clone(),
        }
    }
}

impl<M: Send + Identifiable + 'static> ActorHandle<M> {
    pub fn new(sender: mpsc::Sender<M>) -> Self {
        Self { sender }
    }

    pub fn sender(&self) -> &mpsc::Sender<M> {
        &self.sender
    }

    pub async fn send(&self, command: impl Into<M>) -> anyhow::Result<()> {
        let msg: M = command.into();
        let name = msg.name();
        self.sender.send(msg).await.map_err(|e| {
            tracing::error!("Failed to send command: {} with error: {}", name, e);
            anyhow::anyhow!("Failed to send command: {}", e)
        })
    }

    pub async fn call<R, F, C>(&self, f: F) -> anyhow::Result<R>
    where
        R: Send + 'static,
        F: FnOnce(oneshot::Sender<R>) -> C,
        C: Into<M> + Send + 'static,
    {
        let (tx, rx) = oneshot::channel();
        let command: M = f(tx).into();
        self.send(command).await?;
        rx.await.map_err(|e| {
            tracing::error!("Failed to receive response: {}", e);
            anyhow::anyhow!("Failed to receive response: {}", e)
        })
    }
}

pub enum ActorMessage<Cmd, Evt> {
    Cmd(Cmd),
    Evt(Evt),
}

impl<Cmd, Evt> Identifiable for ActorMessage<Cmd, Evt>
where
    Cmd: Identifiable,
    Evt: Identifiable + 'static,
{
    fn name(&self) -> &'static str {
        match self {
            ActorMessage::Cmd(cmd) => cmd.name(),
            ActorMessage::Evt(evt) => evt.name(),
        }
    }
}

/// 创建 Actor 系统
pub fn spawn_actor<A, C>(mut actor: A, buffer_size: usize) -> ActorHandle<C>
where
    A: Actor<C>,
    C: Send + Identifiable + 'static,
{
    let (sender, receiver) = mpsc::channel(buffer_size);
    actor.on_start(ActorHandle::new(sender.clone()));
    let runner = ActorRunner::new(actor, receiver);
    tokio::spawn(runner.run());
    ActorHandle::new(sender)
}

/// 辅助宏：简化命令匹配
#[macro_export]
macro_rules! match_actor_command {
    ($cmd:expr, $actor:expr, {
        $($pattern:pat => $handler:expr),* $(,)?
    }) => {
        match $cmd {
            $($pattern => Box::pin($handler),)*
        }
    };
}

pub type ActorRespondTo<T> = oneshot::Sender<anyhow::Result<T>>;
