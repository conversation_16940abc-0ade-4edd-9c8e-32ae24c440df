use serde::{Deserialize, Serialize};

/// 操作系统版本信息
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OsRelease {
    pub version: String,
    pub build_number: Option<String>,
    pub platform: String,
}

/// 获取当前操作系统的版本信息
///
/// 支持 Windows、macOS、Linux 三个平台
/// Windows: 获取 Version 和 BuildNumber
/// macOS: 获取 ProductVersion 和 BuildVersion  
/// Linux: 从 /etc/os-release 获取版本信息
pub async fn get_os_release() -> anyhow::Result<OsRelease> {
    #[cfg(target_os = "windows")]
    {
        get_windows_version().await
    }

    #[cfg(target_os = "macos")]
    {
        get_macos_version().await
    }

    #[cfg(target_os = "linux")]
    {
        get_linux_version().await
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
    {
        anyhow::bail!("Unsupported operating system")
    }
}

#[cfg(target_os = "windows")]
async fn get_windows_version() -> anyhow::Result<OsRelease> {
    // 使用 PowerShell 获取 Windows 版本信息

    use crate::command::create_command;
    let output = create_command("powershell")
        .args(&[
            "-Command",
            "Get-ItemProperty -Path 'HKLM:\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion' | Select-Object ProductName, ReleaseId, CurrentBuild, UBR | ConvertTo-Json"
        ])
        .output().await?;

    if !output.status.success() {
        anyhow::bail!("Failed to get Windows version info");
    }

    let json_str = String::from_utf8(output.stdout)?;
    let info: serde_json::Value = serde_json::from_str(&json_str)?;

    let version = info["ReleaseId"].as_str().unwrap_or("Unknown").to_string();

    let build = info["CurrentBuild"].as_str().unwrap_or("0");
    let ubr = info["UBR"].as_u64().unwrap_or(0);
    let build_number = format!("{}.{}", build, ubr);

    Ok(OsRelease {
        version,
        build_number: Some(build_number),
        platform: "windows".to_string(),
    })
}

#[cfg(target_os = "macos")]
async fn get_macos_version() -> anyhow::Result<OsRelease> {
    use crate::command::create_command;
    // 获取 macOS 版本
    let version_output = create_command("sw_vers")
        .arg("-productVersion")
        .output()
        .await?;

    let build_output = create_command("sw_vers")
        .arg("-buildVersion")
        .output()
        .await?;

    if !version_output.status.success() || !build_output.status.success() {
        anyhow::bail!("Failed to get macOS version info");
    }

    let version = String::from_utf8(version_output.stdout)?.trim().to_string();

    let build_number = String::from_utf8(build_output.stdout)?.trim().to_string();

    Ok(OsRelease {
        version,
        build_number: Some(build_number),
        platform: "macos".to_string(),
    })
}

#[cfg(target_os = "linux")]
async fn get_linux_version() -> anyhow::Result<OsRelease> {
    // 从 /etc/os-release 读取信息
    let contents = tokio::fs::read_to_string("/etc/os-release").await?;

    let mut version = String::new();
    let mut name = String::new();

    for line in contents.lines() {
        if line.starts_with("VERSION_ID=") {
            version = line
                .split('=')
                .nth(1)
                .unwrap_or("")
                .trim_matches('"')
                .to_string();
        } else if line.starts_with("NAME=") {
            name = line
                .split('=')
                .nth(1)
                .unwrap_or("")
                .trim_matches('"')
                .to_string();
        }
    }

    if version.is_empty() {
        version = "Unknown".to_string();
    }

    Ok(OsRelease {
        version,
        build_number: None,
        platform: if name.is_empty() {
            "linux".to_string()
        } else {
            name.to_lowercase()
        },
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_os_release() {
        let result = get_os_release().await;
        assert!(result.is_ok());

        let release = result.unwrap();
        println!("{:?}", release);
        assert!(!release.version.is_empty());
        assert!(!release.platform.is_empty());
    }
}
