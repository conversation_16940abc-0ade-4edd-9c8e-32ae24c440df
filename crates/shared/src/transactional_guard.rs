use std::sync::atomic::{AtomicBool, Ordering};

use tokio::sync::watch;

/// 事务性状态守卫，提供自动回滚机制
/// 
/// 当操作可能失败时，使用此守卫确保状态能够在失败时自动回滚到指定值。
/// 只有显式调用 `commit()` 或 `disarm()` 才会阻止回滚。
///
/// # 使用示例
///
/// ```rust
/// use tokio::sync::watch;
/// use crate::TransactionalStatusGuard;
///
/// let (sender, _) = watch::channel(Status::Idle);
/// 
/// // 开始一个可能失败的操作
/// {
///     let guard = TransactionalStatusGuard::new(&sender, Status::Error);
///     sender.send_replace(Status::Processing);
///     
///     // 执行一些可能失败的操作...
///     if operation_success {
///         guard.commit(); // 提交事务，阻止回滚
///     }
///     // 如果操作失败，guard 会在 drop 时自动回滚到 Status::Error
/// }
/// ```
#[derive(Debug)]
pub struct TransactionalStatusGuard<T> {
    sender: watch::Sender<T>,
    rollback_value: Option<T>,
    is_committed: AtomicBool,
}

impl<T> TransactionalStatusGuard<T> {
    /// 创建新的事务守卫
    /// 
    /// # 参数
    /// 
    /// * `sender` - 要保护的状态发送器
    /// * `rollback_value` - 回滚时要设置的值
    pub fn new(sender: &watch::Sender<T>, rollback_value: T) -> Self {
        Self {
            sender: sender.clone(),
            rollback_value: Some(rollback_value),
            is_committed: AtomicBool::new(false),
        }
    }

    /// 提交事务，阻止自动回滚
    /// 
    /// 调用此方法后，即使守卫被 drop，也不会执行回滚操作。
    pub fn commit(self) {
        self.is_committed.store(true, Ordering::Release);
        // 显式 drop 以触发 Drop trait，但此时 is_committed 为 true
        drop(self);
    }

    /// 取消回滚保护（commit 的别名）
    /// 
    /// 与 `commit` 功能相同，但语义更清晰地表达"解除保护"的意图。
    pub fn disarm(self) {
        self.commit();
    }

    /// 手动触发回滚并消费守卫
    /// 
    /// 立即执行回滚操作，无论之前是否已经 commit。
    pub fn rollback(mut self) {
        if let Some(rollback_value) = self.rollback_value.take() {
            let _ = self.sender.send(rollback_value);
        }
    }

    /// 检查是否已经提交
    pub fn is_committed(&self) -> bool {
        self.is_committed.load(Ordering::Acquire)
    }

    /// 更新回滚值
    /// 
    /// 如果已经提交，此操作无效。
    pub fn update_rollback_value(&mut self, new_value: T) {
        if !self.is_committed() {
            self.rollback_value = Some(new_value);
        }
    }
}

impl<T> Drop for TransactionalStatusGuard<T> {
    fn drop(&mut self) {
        // 使用 Acquire 确保能看到其他线程的 commit 操作
        if !self.is_committed.load(Ordering::Acquire) {
            if let Some(rollback_value) = self.rollback_value.take() {
                // 使用 send 而不是 send_replace，这样如果发送失败也不会 panic
                let _ = self.sender.send(rollback_value);
            }
        }
    }
}

// 为了方便使用，提供类型别名
pub type StatusGuard<T> = TransactionalStatusGuard<T>;
