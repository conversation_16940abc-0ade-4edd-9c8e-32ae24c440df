[workspace]
# 列出工作区中的所有 crate 成员
members = [
    "agent",
    "crates/protocol",
    "desktop/src-tauri",
    "crates/domain",
    "crates/shared",
    "helper-svc",
]
resolver = "2" # 推荐使用最新的依赖解析器

# 在这里定义所有 crate 共享的依赖项
# 这样可以保证版本统一，并且在子 crate 中引用时更简洁
[workspace.dependencies]
tokio = { version = "1.45.1", features = ["full"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
chrono = {version = "0.4.41", features = ["serde"]}
uuid = { version = "1.17.0", features = ["v4", "v7", "serde"] }
thiserror = "2.0.12"
anyhow = "1.0.98"
zstd = "0.13.3"
bytes = "1.10.1"
crc32fast = "1.4.2"
tracing = "0.1.41"
tracing-subscriber = { version = "0.3.18" }
pin-project-lite = "0.2.16"
dashmap = "6.1.0"
secrecy = { version = "0.10.3", features = ["serde"] }
sysinfo = "0.36.1"
libc = "0.2.172"

# 定义共享的构建配置（可选，但推荐）
[profile.release]
lto = true           # 链接时优化
codegen-units = 1    # 优化编译时间和性能
strip = true         # 剥离调试信息，减小二进制文件大小
panic = "abort"      # 发布版本在 panic 时直接退出
