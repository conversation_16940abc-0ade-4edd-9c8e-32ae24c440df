mod auth;
mod error;
mod handlers;
mod managers;
mod pipe_server;
mod pipe_server_v2;
mod service;

use std::ffi::OsString;
use tracing::{error, info};
#[cfg(target_os = "windows")]
use windows_service::{define_windows_service, service_dispatcher};

use crate::error::ServiceError;
#[cfg(target_os = "windows")]
use crate::service::HelperService;

#[cfg(target_os = "windows")]
const SERVICE_NAME: &str = "EchoWaveHelper";

#[cfg(target_os = "windows")]
define_windows_service!(ffi_service_main, service_main);

#[cfg(target_os = "windows")]
fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志系统
    init_logging()?;

    // 获取命令行参数
    let args: Vec<OsString> = std::env::args_os().collect();

    if args.len() > 1 {
        let command = args[1].to_string_lossy();
        match command.as_ref() {
            "install" => {
                info!("Installing Windows service...");
                service::install_service()?;
                println!("Service installed successfully");
                return Ok(());
            }
            "uninstall" => {
                info!("Uninstalling Windows service...");
                service::uninstall_service()?;
                println!("Service uninstalled successfully");
                return Ok(());
            }
            "run" => {
                info!("Running service in console mode...");
                run_console_mode()?;
                return Ok(());
            }
            _ => {
                eprintln!(
                    "Usage: {} [install|uninstall|run]",
                    args[0].to_string_lossy()
                );
                return Ok(());
            }
        }
    }

    // 默认启动服务
    info!("Starting Windows service dispatcher...");
    service_dispatcher::start(SERVICE_NAME, ffi_service_main)?;
    Ok(())
}

#[cfg(target_os = "windows")]
fn service_main(_arguments: Vec<OsString>) {
    if let Err(e) = run_service() {
        error!("Service error: {}", e);
    }
}

#[cfg(target_os = "windows")]
fn run_service() -> Result<(), ServiceError> {
    let rt = tokio::runtime::Runtime::new()
        .map_err(|e| ServiceError::Runtime(format!("Failed to create runtime: {}", e)))?;

    rt.block_on(async {
        let mut service = HelperService::new().await?;
        service.run().await
    })
}

#[cfg(target_os = "windows")]
fn run_console_mode() -> Result<(), ServiceError> {
    let rt = tokio::runtime::Runtime::new()
        .map_err(|e| ServiceError::Runtime(format!("Failed to create runtime: {}", e)))?;

    rt.block_on(async {
        let mut service = HelperService::new().await?;
        service.run_console().await
    })
}

#[cfg(target_os = "windows")]
fn init_logging() -> Result<(), Box<dyn std::error::Error>> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::fmt::layer()
                .with_target(false)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true)
                .with_level(true),
        )
        .with(tracing_subscriber::EnvFilter::from_default_env())
        .init();

    Ok(())
}

#[cfg(not(target_os = "windows"))]
fn main() {
    println!("This binary is only for Windows");
}
