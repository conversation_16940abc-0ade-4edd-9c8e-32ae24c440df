use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, error, info, instrument, warn};
use uuid::Uuid;

use crate::auth::AuthManager;
use crate::error::{ServiceError, ServiceResult};
use crate::managers::{<PERSON><PERSON><PERSON><PERSON><PERSON>, UpdateManager, VirtualizationChecker};
use protocol::events::{HelperSvcEvent, HelperSvcResponse};

/// 请求处理器 - 统一处理所有 HelperSvcEvent
#[derive(Clone)]
pub struct RequestHandler {
    _auth_manager: Arc<AuthManager>,
    dism_manager: DismManager,
    virtualization_checker: VirtualizationChecker,
    update_manager: UpdateManager,
    start_time: Instant,
}

impl RequestHandler {
    /// 创建新的请求处理器
    pub async fn new(auth_manager: Arc<AuthManager>) -> ServiceResult<Self> {
        let dism_manager = DismManager::new();
        let virtualization_checker = VirtualizationChecker::new();
        let update_manager = UpdateManager::new()?;

        Ok(Self {
            _auth_manager: auth_manager,
            dism_manager,
            virtualization_checker,
            update_manager,
            start_time: Instant::now(),
        })
    }

    /// 获取服务运行时间
    pub fn uptime(&self) -> Duration {
        self.start_time.elapsed()
    }

    /// 获取服务状态信息
    pub fn get_service_info(&self) -> serde_json::Value {
        serde_json::json!({
            "service_name": "EchoWave Helper Service",
            "version": env!("CARGO_PKG_VERSION"),
            "uptime_seconds": self.uptime().as_secs(),
            "uptime_human": format_duration(self.uptime()),
            "start_time": self.start_time.elapsed().as_secs(),
        })
    }

    /// 处理 HelperSvcEvent 事件
    #[instrument(skip(self), fields(event_type = ?std::mem::discriminant(&event)))]
    pub async fn handle_event(&self, event: HelperSvcEvent) -> HelperSvcResponse {
        debug!("Handling event: {:?}", event);

        match event {
            HelperSvcEvent::CheckWindowsFeature {
                trace_id,
                feature_name,
            } => {
                self.handle_check_windows_feature(trace_id, feature_name)
                    .await
            }
            HelperSvcEvent::GetAllWindowsFeatures { trace_id } => {
                self.handle_get_all_windows_features(trace_id).await
            }
            HelperSvcEvent::EnableWindowsFeature {
                trace_id,
                feature_name,
            } => {
                self.handle_enable_windows_feature(trace_id, feature_name)
                    .await
            }
            HelperSvcEvent::CheckVirtualization { trace_id } => {
                self.handle_check_virtualization(trace_id).await
            }
            HelperSvcEvent::InstallUpdate {
                trace_id,
                update_path,
            } => self.handle_install_update(trace_id, update_path).await,
            HelperSvcEvent::RollbackUpdate {
                trace_id,
                backup_path,
            } => self.handle_rollback_update(trace_id, backup_path).await,
            HelperSvcEvent::RestartSystem {
                trace_id,
                delay_seconds,
            } => self.handle_restart_system(trace_id, delay_seconds).await,
            HelperSvcEvent::LogForward(log_entry) => self.handle_log_forward(log_entry).await,
        }
    }

    async fn handle_get_all_windows_features(&self, trace_id: Uuid) -> HelperSvcResponse {
        info!("Getting all Windows features (trace_id: {})", trace_id);

        // Add timeout for long-running DISM operations
        let timeout_duration = Duration::from_secs(30);

        match tokio::time::timeout(
            timeout_duration,
            self.dism_manager.list_available_features(trace_id),
        )
        .await
        {
            Ok(Ok(features)) => {
                info!(
                    "Successfully retrieved {} Windows features (trace_id: {})",
                    features.len(),
                    trace_id
                );
                HelperSvcResponse::Success {
                    trace_id,
                    data: Some(serde_json::json!({
                        "features": features,
                        "count": features.len()
                    })),
                }
            }
            Ok(Err(e)) => {
                error!(
                    "Failed to get Windows features: {} (trace_id: {})",
                    e, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
            Err(_) => {
                error!(
                    "Timeout while getting Windows features (trace_id: {})",
                    trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: ServiceError::Timeout("Operation timed out".to_string()).error_code(),
                    message: "Operation timed out while retrieving Windows features".to_string(),
                }
            }
        }
    }
    /// 处理检查 Windows 功能状态
    async fn handle_check_windows_feature(
        &self,
        trace_id: Uuid,
        feature_name: String,
    ) -> HelperSvcResponse {
        info!(
            "Checking Windows feature: {} (trace_id: {})",
            feature_name, trace_id
        );

        // Validate feature name
        if let Err(validation_error) = self.validate_feature_name(&feature_name) {
            return HelperSvcResponse::Error {
                trace_id,
                code: validation_error.error_code(),
                message: validation_error.user_message(),
            };
        }

        // Add timeout for DISM operations
        let timeout_duration = Duration::from_secs(15);

        match tokio::time::timeout(
            timeout_duration,
            self.dism_manager
                .check_feature_status(trace_id, &feature_name),
        )
        .await
        {
            Ok(Ok((status, restart_required))) => {
                debug!(
                    "Feature {} status check completed: {:?} (trace_id: {})",
                    feature_name, status, trace_id
                );
                HelperSvcResponse::WindowsFeatureStatus {
                    trace_id,
                    feature_name,
                    status,
                    restart_required,
                }
            }
            Ok(Err(e)) => {
                error!(
                    "Failed to check Windows feature: {} (trace_id: {})",
                    e, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
            Err(_) => {
                error!(
                    "Timeout while checking Windows feature: {} (trace_id: {})",
                    feature_name, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: ServiceError::Timeout("Feature check timed out".to_string()).error_code(),
                    message: "Feature status check timed out".to_string(),
                }
            }
        }
    }

    /// 处理启用 Windows 功能
    async fn handle_enable_windows_feature(
        &self,
        trace_id: Uuid,
        feature_name: String,
    ) -> HelperSvcResponse {
        info!(
            "Enabling Windows feature: {} (trace_id: {})",
            feature_name, trace_id
        );

        // Validate feature name
        if let Err(validation_error) = self.validate_feature_name(&feature_name) {
            return HelperSvcResponse::Error {
                trace_id,
                code: validation_error.error_code(),
                message: validation_error.user_message(),
            };
        }

        // Add timeout for potentially long-running enable operations
        let timeout_duration = Duration::from_secs(120); // 2 minutes for feature enabling

        debug!(
            "Starting feature enable operation for: {} (trace_id: {})",
            feature_name, trace_id
        );

        match tokio::time::timeout(
            timeout_duration,
            self.dism_manager.enable_feature(trace_id, &feature_name),
        )
        .await
        {
            Ok(Ok((status, _detail, restart_required))) => {
                info!(
                    "Feature {} enabled successfully, restart required: {} (trace_id: {})",
                    feature_name, restart_required, trace_id
                );
                HelperSvcResponse::WindowsFeatureStatus {
                    trace_id,
                    feature_name,
                    status,
                    restart_required,
                }
            }
            Ok(Err(e)) => {
                error!(
                    "Failed to enable Windows feature: {} (trace_id: {})",
                    e, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
            Err(_) => {
                error!(
                    "Timeout while enabling Windows feature: {} (trace_id: {})",
                    feature_name, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: ServiceError::Timeout("Feature enable timed out".to_string())
                        .error_code(),
                    message: "Feature enable operation timed out".to_string(),
                }
            }
        }
    }

    /// 处理检查虚拟化支持
    async fn handle_check_virtualization(&self, trace_id: Uuid) -> HelperSvcResponse {
        info!("Checking virtualization support (trace_id: {})", trace_id);

        match self
            .virtualization_checker
            .check_virtualization_support(trace_id)
            .await
        {
            Ok((supported, enabled, details)) => HelperSvcResponse::VirtualizationInfo {
                trace_id,
                supported,
                enabled,
                details,
            },
            Err(e) => {
                error!(
                    "Failed to check virtualization: {} (trace_id: {})",
                    e, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
        }
    }

    /// 处理安装更新
    async fn handle_install_update(
        &self,
        trace_id: Uuid,
        update_path: String,
    ) -> HelperSvcResponse {
        info!(
            "Installing update from: {} (trace_id: {})",
            update_path, trace_id
        );

        // Enhanced input validation
        if let Err(validation_error) = self.validate_update_path(&update_path) {
            return HelperSvcResponse::Error {
                trace_id,
                code: validation_error.error_code(),
                message: validation_error.user_message(),
            };
        }

        // Add timeout for update installation
        let timeout_duration = Duration::from_secs(300); // 5 minutes for update installation

        debug!(
            "Starting update installation from: {} (trace_id: {})",
            update_path, trace_id
        );

        match tokio::time::timeout(
            timeout_duration,
            self.update_manager.install_update(trace_id, &update_path),
        )
        .await
        {
            Ok(Ok((success, message))) => {
                info!(
                    "Update installation completed: success={}, message={} (trace_id: {})",
                    success, message, trace_id
                );
                HelperSvcResponse::UpdateInstallResult {
                    trace_id,
                    success,
                    message,
                }
            }
            Ok(Err(e)) => {
                error!("Failed to install update: {} (trace_id: {})", e, trace_id);
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
            Err(_) => {
                error!(
                    "Timeout while installing update from: {} (trace_id: {})",
                    update_path, trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: ServiceError::Timeout("Update installation timed out".to_string())
                        .error_code(),
                    message: "Update installation operation timed out".to_string(),
                }
            }
        }
    }

    /// 处理回滚更新
    async fn handle_rollback_update(
        &self,
        trace_id: Uuid,
        backup_path: String,
    ) -> HelperSvcResponse {
        info!(
            "Rolling back update from: {} (trace_id: {})",
            backup_path, trace_id
        );

        // 验证输入
        if backup_path.is_empty() {
            return HelperSvcResponse::Error {
                trace_id,
                code: ServiceError::InvalidInput("Empty backup path".to_string()).error_code(),
                message: "Backup path cannot be empty".to_string(),
            };
        }

        let backup_path_buf = std::path::PathBuf::from(&backup_path);
        match self
            .update_manager
            .rollback_update(trace_id, &backup_path_buf)
            .await
        {
            Ok((success, message)) => HelperSvcResponse::UpdateInstallResult {
                trace_id,
                success,
                message,
            },
            Err(e) => {
                error!("Failed to rollback update: {} (trace_id: {})", e, trace_id);
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
        }
    }

    /// 处理重启系统
    async fn handle_restart_system(&self, trace_id: Uuid, delay_seconds: u32) -> HelperSvcResponse {
        info!(
            "Restarting system with delay: {} seconds (trace_id: {})",
            delay_seconds, trace_id
        );

        // Enhanced delay validation with reasonable bounds
        let safe_delay = self.validate_restart_delay(delay_seconds);
        if safe_delay != delay_seconds {
            warn!(
                "Delay time adjusted from {} to {} seconds for safety (trace_id: {})",
                delay_seconds, safe_delay, trace_id
            );
        }

        // Add timeout for restart operation
        let timeout_duration = Duration::from_secs(30);

        debug!(
            "Initiating system restart with {} second delay (trace_id: {})",
            safe_delay, trace_id
        );

        match tokio::time::timeout(
            timeout_duration,
            self.update_manager.restart_system(trace_id, safe_delay),
        )
        .await
        {
            Ok(Ok((success, message))) => {
                info!(
                    "System restart initiated: success={}, message={} (trace_id: {})",
                    success, message, trace_id
                );
                HelperSvcResponse::Success {
                    trace_id,
                    data: Some(serde_json::json!({
                        "success": success,
                        "message": message,
                        "delay_seconds": safe_delay,
                        "original_delay": delay_seconds
                    })),
                }
            }
            Ok(Err(e)) => {
                error!("Failed to restart system: {} (trace_id: {})", e, trace_id);
                HelperSvcResponse::Error {
                    trace_id,
                    code: e.error_code(),
                    message: e.user_message(),
                }
            }
            Err(_) => {
                error!(
                    "Timeout while initiating system restart (trace_id: {})",
                    trace_id
                );
                HelperSvcResponse::Error {
                    trace_id,
                    code: ServiceError::Timeout("Restart initiation timed out".to_string())
                        .error_code(),
                    message: "System restart initiation timed out".to_string(),
                }
            }
        }
    }

    /// 处理日志转发
    async fn handle_log_forward(
        &self,
        log_entry: protocol::events::StructuredLogEntry,
    ) -> HelperSvcResponse {
        debug!("Forwarding log entry: {:?}", log_entry);

        // 这里可以将日志转发到系统日志或其他地方
        // 为了简化，我们只是记录到本地日志
        let target = log_entry.target;
        match log_entry.level.as_str() {
            "ERROR" => error!("[FORWARDED] {}: {}", target, log_entry.message),
            "WARN" => warn!("[FORWARDED] {}: {}", target, log_entry.message),
            "INFO" => info!("[FORWARDED] {}: {}", target, log_entry.message),
            "DEBUG" => debug!("[FORWARDED] {}: {}", target, log_entry.message),
            _ => info!("[FORWARDED] {}: {}", target, log_entry.message),
        }

        HelperSvcResponse::Success {
            trace_id: log_entry.trace_id.unwrap_or_else(|| uuid::Uuid::new_v4()),
            data: None,
        }
    }

    /// 检查所有支持的功能 - Enhanced with timeout and better error handling
    #[allow(dead_code)] // Mark as allowed since it's not currently used but may be needed in future
    async fn check_all_supported_features(
        &self,
        trace_id: Uuid,
    ) -> ServiceResult<serde_json::Value> {
        debug!("Checking all supported features (trace_id: {})", trace_id);

        let features = vec![
            "WSL",
            "Hyper-V",
            "VirtualMachinePlatform",
            "Containers",
            "HypervisorPlatform",
        ];
        let mut results = serde_json::Map::new();
        let timeout_duration = Duration::from_secs(60); // Total timeout for all features

        let start_time = std::time::Instant::now();

        for feature in features {
            // Check if we're running out of time
            if start_time.elapsed() > timeout_duration {
                warn!(
                    "Timeout reached while checking features, stopping at {} (trace_id: {})",
                    feature, trace_id
                );
                results.insert(
                    feature.to_string(),
                    serde_json::json!({
                        "error": "Operation timed out"
                    }),
                );
                break;
            }

            debug!("Checking feature: {} (trace_id: {})", feature, trace_id);

            match tokio::time::timeout(
                Duration::from_secs(10), // Per-feature timeout
                self.dism_manager.check_feature_status(trace_id, feature),
            )
            .await
            {
                Ok(Ok((status, restart_required))) => {
                    debug!(
                        "Feature {} status: {:?} (trace_id: {})",
                        feature, status, trace_id
                    );
                    results.insert(
                        feature.to_string(),
                        serde_json::json!({
                            "status": status,
                            "restart_required": restart_required
                        }),
                    );
                }
                Ok(Err(e)) => {
                    warn!(
                        "Error checking feature {}: {} (trace_id: {})",
                        feature, e, trace_id
                    );
                    results.insert(
                        feature.to_string(),
                        serde_json::json!({
                            "error": e.to_string(),
                            "error_code": e.error_code()
                        }),
                    );
                }
                Err(_) => {
                    warn!(
                        "Timeout checking feature: {} (trace_id: {})",
                        feature, trace_id
                    );
                    results.insert(
                        feature.to_string(),
                        serde_json::json!({
                            "error": "Feature check timed out"
                        }),
                    );
                }
            }
        }

        info!(
            "Completed checking {} features in {:?} (trace_id: {})",
            results.len(),
            start_time.elapsed(),
            trace_id
        );

        Ok(serde_json::Value::Object(results))
    }

    /// Validate feature name for security and correctness
    fn validate_feature_name(&self, feature_name: &str) -> ServiceResult<()> {
        // Check for empty or whitespace-only names
        if feature_name.trim().is_empty() {
            return Err(ServiceError::InvalidInput(
                "Feature name cannot be empty".to_string(),
            ));
        }

        // Check length limits
        if feature_name.len() > 200 {
            return Err(ServiceError::InvalidInput(
                "Feature name too long".to_string(),
            ));
        }

        // Check for invalid characters that could be used for injection
        let invalid_chars = ['<', '>', '|', '&', ';', '`', '$', '(', ')', '{', '}'];
        if feature_name.chars().any(|c| invalid_chars.contains(&c)) {
            return Err(ServiceError::InvalidInput(
                "Feature name contains invalid characters".to_string(),
            ));
        }

        // Check for control characters
        if feature_name.chars().any(|c| c.is_control()) {
            return Err(ServiceError::InvalidInput(
                "Feature name contains control characters".to_string(),
            ));
        }

        Ok(())
    }

    /// Enhanced update path validation
    fn validate_update_path(&self, update_path: &str) -> ServiceResult<()> {
        // Check for empty path
        if update_path.trim().is_empty() {
            return Err(ServiceError::InvalidInput(
                "Update path cannot be empty".to_string(),
            ));
        }

        // Check length limits
        if update_path.len() > 1000 {
            return Err(ServiceError::InvalidInput(
                "Update path too long".to_string(),
            ));
        }

        // Security checks for path traversal
        if update_path.contains("..") || update_path.contains("\\\\") {
            return Err(ServiceError::InvalidInput(
                "Update path contains invalid characters".to_string(),
            ));
        }

        // Check for dangerous characters
        let dangerous_chars = ['<', '>', '|', '&', ';', '`', '$'];
        if update_path.chars().any(|c| dangerous_chars.contains(&c)) {
            return Err(ServiceError::InvalidInput(
                "Update path contains dangerous characters".to_string(),
            ));
        }

        // Ensure it's a reasonable file extension
        let path = std::path::Path::new(update_path);
        if let Some(extension) = path.extension() {
            let ext_str = extension.to_string_lossy().to_lowercase();
            let allowed_extensions = ["msu", "cab", "exe", "msi"];
            if !allowed_extensions.contains(&ext_str.as_str()) {
                return Err(ServiceError::InvalidInput(format!(
                    "Unsupported update file extension: {}",
                    ext_str
                )));
            }
        } else {
            return Err(ServiceError::InvalidInput(
                "Update path must have a file extension".to_string(),
            ));
        }

        Ok(())
    }

    /// Validate restart delay with reasonable bounds
    fn validate_restart_delay(&self, delay_seconds: u32) -> u32 {
        // Minimum delay: 5 seconds (to allow for cleanup)
        // Maximum delay: 10 minutes (600 seconds)
        delay_seconds.max(5).min(600)
    }

    /// 健康检查 - 检查所有组件是否正常工作
    pub async fn health_check(&self, trace_id: Uuid) -> HelperSvcResponse {
        info!("Performing health check (trace_id: {})", trace_id);

        let mut health_status = serde_json::Map::new();
        let mut overall_healthy = true;

        // 检查 DISM 管理器
        let dism_health = match tokio::time::timeout(
            Duration::from_secs(5),
            self.dism_manager.list_available_features(trace_id),
        )
        .await
        {
            Ok(Ok(_)) => "healthy",
            Ok(Err(_)) => {
                overall_healthy = false;
                "error"
            }
            Err(_) => {
                overall_healthy = false;
                "timeout"
            }
        };
        health_status.insert(
            "dism_manager".to_string(),
            serde_json::Value::String(dism_health.to_string()),
        );

        // 检查虚拟化检查器
        let virt_health = match tokio::time::timeout(
            Duration::from_secs(3),
            self.virtualization_checker
                .check_virtualization_support(trace_id),
        )
        .await
        {
            Ok(Ok(_)) => "healthy",
            Ok(Err(_)) => {
                overall_healthy = false;
                "error"
            }
            Err(_) => {
                overall_healthy = false;
                "timeout"
            }
        };
        health_status.insert(
            "virtualization_checker".to_string(),
            serde_json::Value::String(virt_health.to_string()),
        );

        // 添加服务信息
        health_status.insert("service_info".to_string(), self.get_service_info());
        health_status.insert(
            "overall_status".to_string(),
            serde_json::Value::String(
                if overall_healthy {
                    "healthy"
                } else {
                    "unhealthy"
                }
                .to_string(),
            ),
        );

        info!(
            "Health check completed: {} (trace_id: {})",
            if overall_healthy {
                "healthy"
            } else {
                "unhealthy"
            },
            trace_id
        );

        HelperSvcResponse::Success {
            trace_id,
            data: Some(serde_json::Value::Object(health_status)),
        }
    }
}

/// 格式化持续时间为人类可读格式
fn format_duration(duration: Duration) -> String {
    let total_seconds = duration.as_secs();
    let days = total_seconds / 86400;
    let hours = (total_seconds % 86400) / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;

    if days > 0 {
        format!("{}d {}h {}m {}s", days, hours, minutes, seconds)
    } else if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, seconds)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else {
        format!("{}s", seconds)
    }
}

#[cfg(test)]
mod tests {
    use protocol::events::LogSource;

    use super::*;
    use std::{collections::HashMap, time::Duration};

    #[tokio::test]
    async fn test_request_handler_creation() {
        let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
        let handler = RequestHandler::new(auth_manager).await;
        assert!(handler.is_ok());
    }

    #[tokio::test]
    async fn test_handle_log_forward() {
        let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
        let handler = RequestHandler::new(auth_manager).await.unwrap();

        let log_entry = protocol::events::StructuredLogEntry {
            source: LogSource::Service,
            level: protocol::events::LogLevel::Info,
            message: "Test message".to_string(),
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            trace_id: Some(Uuid::new_v4()),
            span_id: None,
            module: "test_module".to_string(),
            target: "test".to_string(),
            file: Some("test.rs".to_string()),
            line: Some(42),
            fields: HashMap::new(),
        };

        let response = handler.handle_log_forward(log_entry.clone()).await;
        match response {
            HelperSvcResponse::Success { trace_id, .. } => {
                assert_eq!(Some(trace_id), log_entry.trace_id);
            }
            _ => panic!("Expected Success response"),
        }
    }

    #[tokio::test]
    async fn test_input_validation() {
        let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
        let handler = RequestHandler::new(auth_manager).await.unwrap();

        let trace_id = Uuid::new_v4();

        // 测试空路径
        let response = handler.handle_install_update(trace_id, String::new()).await;
        match response {
            HelperSvcResponse::Error { message, .. } => {
                assert!(message.contains("empty"));
            }
            _ => panic!("Expected Error response for empty path"),
        }

        // 测试危险路径
        let response = handler
            .handle_install_update(trace_id, "../dangerous/path".to_string())
            .await;
        match response {
            HelperSvcResponse::Error { message, .. } => {
                assert!(message.contains("invalid"));
            }
            _ => panic!("Expected Error response for dangerous path"),
        }
    }

    #[tokio::test]
    async fn test_service_info() {
        let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
        let handler = RequestHandler::new(auth_manager).await.unwrap();

        let info = handler.get_service_info();
        assert!(info.get("service_name").is_some());
        assert!(info.get("version").is_some());
        assert!(info.get("uptime_seconds").is_some());
        assert!(info.get("uptime_human").is_some());
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(Duration::from_secs(30)), "30s");
        assert_eq!(format_duration(Duration::from_secs(90)), "1m 30s");
        assert_eq!(format_duration(Duration::from_secs(3661)), "1h 1m 1s");
        assert_eq!(format_duration(Duration::from_secs(90061)), "1d 1h 1m 1s");
    }

    #[tokio::test]
    async fn test_health_check() {
        let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
        let handler = RequestHandler::new(auth_manager).await.unwrap();

        let trace_id = Uuid::new_v4();
        let response = handler.health_check(trace_id).await;

        match response {
            HelperSvcResponse::Success {
                data: Some(data), ..
            } => {
                assert!(data.get("overall_status").is_some());
                assert!(data.get("dism_manager").is_some());
                assert!(data.get("virtualization_checker").is_some());
                assert!(data.get("service_info").is_some());
            }
            _ => panic!("Expected Success response with data"),
        }
    }

    #[tokio::test]
    async fn test_validate_restart_delay() {
        let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
        let handler = RequestHandler::new(auth_manager).await.unwrap();

        // 测试各种延迟值
        assert_eq!(handler.validate_restart_delay(0), 5); // 最小值
        assert_eq!(handler.validate_restart_delay(10), 10); // 正常值
        assert_eq!(handler.validate_restart_delay(1000), 600); // 最大值
    }
}
