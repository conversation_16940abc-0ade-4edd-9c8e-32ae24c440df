#![cfg(target_os = "windows")]
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::mpsc;
use tracing::{debug, error, info};
use windows_service::{
    service::{ServiceAccess, ServiceErrorControl, ServiceInfo, ServiceStartType},
    service::{
        ServiceControl, ServiceControlAccept, ServiceExitCode, ServiceState, ServiceStatus,
        ServiceType,
    },
    service_control_handler::{self, ServiceControlHandlerResult},
    service_manager::{ServiceManager, ServiceManagerAccess},
};

use crate::auth::AuthManager;
use crate::error::{ServiceError, ServiceResult};
use crate::pipe_server_v2::PipeServerV2;

const SERVICE_NAME: &str = "EchoWaveHelper";
const SERVICE_DISPLAY_NAME: &str = "EchoWave Helper Service";
const SERVICE_DESCRIPTION: &str =
    "EchoWave Windows Helper Service - Handles privileged system operations";

/// Windows 服务主程序
pub struct HelperService {
    auth_manager: Arc<AuthManager>,
    pipe_server: Option<PipeServerV2>,
    shutdown_tx: Option<mpsc::Sender<()>>,
    shutdown_rx: Option<mpsc::Receiver<()>>,
}

impl HelperService {
    /// 创建新的服务实例
    pub async fn new() -> ServiceResult<Self> {
        let auth_manager = Arc::new(AuthManager::default());
        let (shutdown_tx, shutdown_rx) = mpsc::channel(1);

        Ok(Self {
            auth_manager,
            pipe_server: None,
            shutdown_tx: Some(shutdown_tx),
            shutdown_rx: Some(shutdown_rx),
        })
    }

    /// 运行服务（Windows 服务模式）
    pub async fn run(&mut self) -> ServiceResult<()> {
        info!("Starting EchoWave Helper Service...");

        // 设置服务控制处理器
        let shutdown_tx = self.shutdown_tx.take().ok_or_else(|| {
            ServiceError::ServiceControl("Shutdown channel already taken".to_string())
        })?;

        let event_handler = move |control_event| -> ServiceControlHandlerResult {
            match control_event {
                ServiceControl::Stop | ServiceControl::Shutdown => {
                    info!("Received stop/shutdown signal");
                    if let Err(e) = shutdown_tx.try_send(()) {
                        error!("Failed to send shutdown signal: {}", e);
                    }
                    ServiceControlHandlerResult::NoError
                }
                ServiceControl::Interrogate => ServiceControlHandlerResult::NoError,
                _ => ServiceControlHandlerResult::NotImplemented,
            }
        };

        let status_handle = service_control_handler::register(SERVICE_NAME, event_handler)
            .map_err(|e| {
                ServiceError::ServiceControl(format!(
                    "Failed to register service control handler: {}",
                    e
                ))
            })?;

        // 设置服务状态为启动中
        status_handle
            .set_service_status(ServiceStatus {
                service_type: ServiceType::OWN_PROCESS,
                current_state: ServiceState::StartPending,
                controls_accepted: ServiceControlAccept::STOP | ServiceControlAccept::SHUTDOWN,
                exit_code: ServiceExitCode::Win32(0),
                checkpoint: 0,
                wait_hint: Duration::from_secs(3),
                process_id: None,
            })
            .map_err(|e| {
                ServiceError::ServiceControl(format!("Failed to set service status: {}", e))
            })?;

        // 启动服务组件
        self.start_components().await?;

        // 设置服务状态为运行中
        status_handle
            .set_service_status(ServiceStatus {
                service_type: ServiceType::OWN_PROCESS,
                current_state: ServiceState::Running,
                controls_accepted: ServiceControlAccept::STOP | ServiceControlAccept::SHUTDOWN,
                exit_code: ServiceExitCode::Win32(0),
                checkpoint: 0,
                wait_hint: Duration::default(),
                process_id: None,
            })
            .map_err(|e| {
                ServiceError::ServiceControl(format!("Failed to set service status: {}", e))
            })?;

        info!("EchoWave Helper Service started successfully");

        // 等待关闭信号
        let mut shutdown_rx = self.shutdown_rx.take().ok_or_else(|| {
            ServiceError::ServiceControl("Shutdown receiver already taken".to_string())
        })?;

        shutdown_rx.recv().await;

        // 停止服务组件
        self.stop_components().await?;

        // 设置服务状态为已停止
        status_handle
            .set_service_status(ServiceStatus {
                service_type: ServiceType::OWN_PROCESS,
                current_state: ServiceState::Stopped,
                controls_accepted: ServiceControlAccept::empty(),
                exit_code: ServiceExitCode::Win32(0),
                checkpoint: 0,
                wait_hint: Duration::default(),
                process_id: None,
            })
            .map_err(|e| {
                ServiceError::ServiceControl(format!("Failed to set service status: {}", e))
            })?;

        info!("EchoWave Helper Service stopped");
        Ok(())
    }

    /// 运行服务（控制台模式，用于调试）
    pub async fn run_console(&mut self) -> ServiceResult<()> {
        info!("Starting EchoWave Helper Service in console mode...");

        // 启动服务组件
        self.start_components().await?;

        info!("Service started in console mode. Press Ctrl+C to stop.");

        // 等待 Ctrl+C 信号
        tokio::signal::ctrl_c()
            .await
            .map_err(|e| ServiceError::Runtime(format!("Failed to wait for Ctrl+C: {}", e)))?;

        info!("Received Ctrl+C, shutting down...");

        // 停止服务组件
        self.stop_components().await?;

        info!("Service stopped");
        Ok(())
    }

    /// 启动服务组件
    async fn start_components(&mut self) -> ServiceResult<()> {
        debug!("Starting service components...");

        // 启动 Named Pipe 服务器
        let pipe_server = PipeServerV2::new(self.auth_manager.clone()).await?;
        self.pipe_server = Some(pipe_server);

        // 启动认证令牌清理任务
        let auth_manager = self.auth_manager.clone();
        tokio::spawn(crate::auth::token_cleanup_task(
            auth_manager,
            Duration::from_secs(300),
        ));

        debug!("Service components started successfully");
        Ok(())
    }

    /// 停止服务组件
    async fn stop_components(&mut self) -> ServiceResult<()> {
        debug!("Stopping service components...");

        // 停止 Named Pipe 服务器
        if let Some(mut pipe_server) = self.pipe_server.take() {
            pipe_server.shutdown().await?;
        }

        debug!("Service components stopped successfully");
        Ok(())
    }
}

/// 安装 Windows 服务
pub fn install_service() -> ServiceResult<()> {
    let manager =
        ServiceManager::local_computer(None::<&str>, ServiceManagerAccess::CREATE_SERVICE)
            .map_err(|e| {
                ServiceError::ServiceControl(format!("Failed to open service manager: {}", e))
            })?;

    let current_exe = std::env::current_exe().map_err(|e| {
        ServiceError::ServiceControl(format!("Failed to get current executable path: {}", e))
    })?;

    let service_info = ServiceInfo {
        name: SERVICE_NAME.into(),
        display_name: SERVICE_DISPLAY_NAME.into(),
        service_type: ServiceType::OWN_PROCESS,
        start_type: ServiceStartType::AutoStart,
        error_control: ServiceErrorControl::Normal,
        executable_path: current_exe,
        launch_arguments: vec![],
        dependencies: vec![],
        account_name: None, // 使用 LocalSystem 账户
        account_password: None,
    };

    let service = manager
        .create_service(&service_info, ServiceAccess::CHANGE_CONFIG)
        .map_err(|e| ServiceError::ServiceControl(format!("Failed to create service: {}", e)))?;

    service.set_description(SERVICE_DESCRIPTION).map_err(|e| {
        ServiceError::ServiceControl(format!("Failed to set service description: {}", e))
    })?;

    info!("Service installed successfully");
    Ok(())
}

/// 卸载 Windows 服务
pub fn uninstall_service() -> ServiceResult<()> {
    let manager = ServiceManager::local_computer(None::<&str>, ServiceManagerAccess::CONNECT)
        .map_err(|e| {
            ServiceError::ServiceControl(format!("Failed to open service manager: {}", e))
        })?;

    let service = manager
        .open_service(SERVICE_NAME, ServiceAccess::DELETE)
        .map_err(|e| ServiceError::ServiceControl(format!("Failed to open service: {}", e)))?;

    service
        .delete()
        .map_err(|e| ServiceError::ServiceControl(format!("Failed to delete service: {}", e)))?;

    info!("Service uninstalled successfully");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_service_creation() {
        let service = HelperService::new().await;
        assert!(service.is_ok());
    }

    #[test]
    fn test_service_constants() {
        assert_eq!(SERVICE_NAME, "EchoWaveHelper");
        assert_eq!(SERVICE_DISPLAY_NAME, "EchoWave Helper Service");
        assert!(!SERVICE_DESCRIPTION.is_empty());
    }
}
