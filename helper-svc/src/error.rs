use thiserror::Error;

/// Windows 服务模块的错误类型
#[derive(Erro<PERSON>, Debug)]
pub enum ServiceError {
    #[error("Runtime error: {0}")]
    Runtime(String),

    #[error("Service control error: {0}")]
    ServiceControl(String),

    #[error("Named pipe error: {0}")]
    NamedPipe(String),

    #[error("Pipe connection error: {0}")]
    PipeConnection(String),

    #[error("Pipe server error: {0}")]
    PipeServer(String),

    #[error("Pipe error: {0}")]
    PipeError(String),

    #[error("Authentication error: {0}")]
    Authentication(String),

    #[error("DISM operation error: {0}")]
    Dism(String),

    #[error("Virtualization check error: {0}")]
    Virtualization(String),

    #[error("Update operation error: {0}")]
    Update(String),

    #[error("System operation error: {0}")]
    System(String),

    #[error("Serialization error: {0}")]
    Serialization(String),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Windows API error: {0}")]
    WindowsApi(String),

    #[error("Protocol error: {0}")]
    Protocol(String),

    #[error("Configuration error: {0}")]
    Configuration(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Resource not found: {0}")]
    NotFound(String),

    #[error("Operation timeout: {0}")]
    Timeout(String),

    #[error("Invalid input: {0}")]
    InvalidInput(String),
}

impl ServiceError {
    /// 将错误转换为错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            ServiceError::Runtime(_) => 1001,
            ServiceError::ServiceControl(_) => 1002,
            ServiceError::NamedPipe(_) => 1003,
            ServiceError::PipeConnection(_) => 1018,
            ServiceError::PipeServer(_) => 1019,
            ServiceError::PipeError(_) => 1020,
            ServiceError::Authentication(_) => 1004,
            ServiceError::Dism(_) => 1005,
            ServiceError::Virtualization(_) => 1006,
            ServiceError::Update(_) => 1007,
            ServiceError::System(_) => 1008,
            ServiceError::Serialization(_) => 1009,
            ServiceError::Io(_) => 1010,
            ServiceError::WindowsApi(_) => 1011,
            ServiceError::Protocol(_) => 1012,
            ServiceError::Configuration(_) => 1013,
            ServiceError::PermissionDenied(_) => 1014,
            ServiceError::NotFound(_) => 1015,
            ServiceError::Timeout(_) => 1016,
            ServiceError::InvalidInput(_) => 1017,
        }
    }

    /// 检查错误是否可以重试
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            ServiceError::NamedPipe(_) | 
            ServiceError::PipeConnection(_) |
            ServiceError::PipeError(_) |
            ServiceError::Io(_) | 
            ServiceError::Timeout(_) |
            ServiceError::System(_)
        )
    }

    /// 获取用户友好的错误消息
    pub fn user_message(&self) -> String {
        match self {
            ServiceError::Authentication(_) => "Authentication failed. Please restart the application.".to_string(),
            ServiceError::PermissionDenied(_) => "Operation requires administrator privileges.".to_string(),
            ServiceError::Dism(_) => "Windows feature management operation failed.".to_string(),
            ServiceError::Virtualization(_) => "Virtualization check failed.".to_string(),
            ServiceError::Update(_) => "Update operation failed.".to_string(),
            ServiceError::PipeConnection(_) => "Communication pipe connection failed. Please try again.".to_string(),
            ServiceError::PipeServer(_) => "Communication pipe server error. Please restart the service.".to_string(),
            ServiceError::PipeError(_) => "Communication pipe error. Please check the service status.".to_string(),
            ServiceError::NamedPipe(_) => "Named pipe communication error. Please try again.".to_string(),
            _ => self.to_string(),
        }
    }
}

/// 结果类型别名
pub type ServiceResult<T> = Result<T, ServiceError>;

/// 将 Windows API 错误转换为 ServiceError
pub fn windows_error_to_service_error(error: windows::core::Error) -> ServiceError {
    ServiceError::WindowsApi(format!("Windows API error: {}", error))
}

/// 将 serde_json 错误转换为 ServiceError
impl From<serde_json::Error> for ServiceError {
    fn from(error: serde_json::Error) -> Self {
        ServiceError::Serialization(error.to_string())
    }
}

/// 将 Tokio 超时错误转换为 ServiceError
impl From<tokio::time::error::Elapsed> for ServiceError {
    fn from(error: tokio::time::error::Elapsed) -> Self {
        ServiceError::Timeout(format!("Operation timed out: {}", error))
    }
}

// 注释掉这个实现，因为FrameError可能不存在
// /// 将 protocol 错误转换为 ServiceError
// impl From<protocol::frame::FrameError> for ServiceError {
//     fn from(error: protocol::frame::FrameError) -> Self {
//         ServiceError::Protocol(error.to_string())
//     }
// }

/// PipeServerV2 专用错误处理工具
impl ServiceError {
    /// 创建管道连接错误
    pub fn pipe_connection_error(message: &str) -> Self {
        ServiceError::PipeConnection(message.to_string())
    }

    /// 创建管道服务器错误
    pub fn pipe_server_error(message: &str) -> Self {
        ServiceError::PipeServer(message.to_string())
    }

    /// 创建管道错误
    pub fn pipe_error(message: &str) -> Self {
        ServiceError::PipeError(message.to_string())
    }

    /// 创建协议错误
    pub fn protocol_error(message: &str) -> Self {
        ServiceError::Protocol(message.to_string())
    }

    /// 检查错误是否为管道相关错误
    pub fn is_pipe_related(&self) -> bool {
        matches!(
            self,
            ServiceError::NamedPipe(_) | 
            ServiceError::PipeConnection(_) |
            ServiceError::PipeServer(_) |
            ServiceError::PipeError(_)
        )
    }

    /// 获取错误的严重性级别
    pub fn severity(&self) -> ErrorSeverity {
        match self {
            ServiceError::Authentication(_) | ServiceError::PermissionDenied(_) => ErrorSeverity::Critical,
            ServiceError::PipeServer(_) | ServiceError::System(_) => ErrorSeverity::High,
            ServiceError::PipeConnection(_) | ServiceError::PipeError(_) | ServiceError::Timeout(_) => ErrorSeverity::Medium,
            ServiceError::NamedPipe(_) | ServiceError::Protocol(_) => ErrorSeverity::Low,
            _ => ErrorSeverity::Medium,
        }
    }
}

/// 错误严重性级别
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum ErrorSeverity {
    /// 关键错误，需要立即处理
    Critical,
    /// 高级错误，可能影响服务稳定性
    High,
    /// 中级错误，可能影响用户体验
    Medium,
    /// 低级错误，通常可以自动恢复
    Low,
}

impl ErrorSeverity {
    /// 转换为字符串
    pub fn as_str(&self) -> &'static str {
        match self {
            ErrorSeverity::Critical => "CRITICAL",
            ErrorSeverity::High => "HIGH",
            ErrorSeverity::Medium => "MEDIUM",
            ErrorSeverity::Low => "LOW",
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_codes() {
        assert_eq!(ServiceError::Runtime("test".to_string()).error_code(), 1001);
        assert_eq!(ServiceError::Authentication("test".to_string()).error_code(), 1004);
        assert_eq!(ServiceError::PermissionDenied("test".to_string()).error_code(), 1014);
    }

    #[test]
    fn test_retryable_errors() {
        assert!(ServiceError::NamedPipe("test".to_string()).is_retryable());
        assert!(ServiceError::Timeout("test".to_string()).is_retryable());
        assert!(!ServiceError::Authentication("test".to_string()).is_retryable());
        assert!(!ServiceError::PermissionDenied("test".to_string()).is_retryable());
    }

    #[test]
    fn test_user_messages() {
        let auth_error = ServiceError::Authentication("test".to_string());
        assert_eq!(auth_error.user_message(), "Authentication failed. Please restart the application.");

        let perm_error = ServiceError::PermissionDenied("test".to_string());
        assert_eq!(perm_error.user_message(), "Operation requires administrator privileges.");
    }
}
