use futures::FutureExt;
use std::sync::Arc;
use std::time::Duration;
use tokio::io::{AsyncRead, AsyncWrite, BufReader};
use tokio::sync::{mpsc, Mutex};
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use tracing::{debug, error, info, instrument, warn};
use windows::core::{HRESULT, PCWSTR};
use windows::Win32::Foundation::{
    <PERSON><PERSON><PERSON><PERSON>, GetLastError, HANDLE, INVALID_HANDLE_VALUE, WIN32_ERROR,
};
use windows::Win32::Security::{
    InitializeSecurityDescriptor, SetSecurityDescriptorDacl, PSECURITY_DESCRIPTOR,
    SECURITY_ATTRIBUTES, SECURITY_DESCRIPTOR,
};
use windows::Win32::Storage::FileSystem::{ReadFile, WriteFile, PIPE_ACCESS_DUPLEX};
use windows::Win32::System::Pipes::{
    ConnectNamedPipe, CreateNamedPipeW, Disconnect<PERSON><PERSON><PERSON><PERSON><PERSON>, PIPE_READMODE_BYTE, PIPE_TYPE_BYTE,
    PIPE_UNLIMITED_INSTANCES, PIPE_WAIT,
};
use windows::Win32::System::Threading::{CreateMutexW, ReleaseMutex, WaitForSingleObject};

use crate::auth::AuthManager;
use crate::error::{ServiceError, ServiceResult};
use crate::handlers::RequestHandler;
use protocol::events::{HelperSvcEvent, HelperSvcResponse};
use protocol::frame::{read_message_frame, send_message_frame, EventCode, MessageFrame};

const PIPE_NAME: &str = r"\\.\pipe\echowave_helper";
const BUFFER_SIZE: u32 = 4096;
const MAX_INSTANCES: u32 = 10;
const MUTEX_NAME: &str = "Global\\EchoWaveHelperSvcMutex";

/// Named Pipe 服务器
pub struct PipeServer {
    auth_manager: Arc<AuthManager>,
    request_handler: RequestHandler,
    shutdown_tx: Option<mpsc::Sender<()>>,
    shutdown_rx: Option<mpsc::Receiver<()>>,
    active_clients: Arc<Mutex<Vec<JoinHandle<()>>>>,
    instance_mutex: Option<HANDLE>,
}

impl PipeServer {
    /// 创建新的 Named Pipe 服务器
    pub async fn new(auth_manager: Arc<AuthManager>) -> ServiceResult<Self> {
        // 检查单实例控制
        let instance_mutex = Self::acquire_instance_mutex()?;

        let request_handler = RequestHandler::new(auth_manager.clone()).await?;
        let (shutdown_tx, shutdown_rx) = mpsc::channel(1);

        let mut server = Self {
            auth_manager,
            request_handler,
            shutdown_tx: Some(shutdown_tx),
            shutdown_rx: Some(shutdown_rx),
            active_clients: Arc::new(Mutex::new(Vec::new())),
            instance_mutex: Some(instance_mutex),
        };

        // 启动服务器
        server.start().await?;

        Ok(server)
    }

    /// 获取单实例互斥锁
    fn acquire_instance_mutex() -> ServiceResult<HANDLE> {
        let mutex_name_wide: Vec<u16> = MUTEX_NAME
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect();

        unsafe {
            let mutex_handle = CreateMutexW(None, true, PCWSTR(mutex_name_wide.as_ptr()));

            match mutex_handle {
                Ok(handle) => {
                    if handle.is_invalid() {
                        let error_code = GetLastError();
                        if let Err(err) = error_code {
                            let hres = err.code();
                            let message = hres.message();
                            let code = hres.0;
                            error!("Failed to create mutex: Windows {message:?}({code})");
                            return Err(ServiceError::NamedPipe(format!(
                                "Failed to create instance mutex: {message:?}({code})"
                            )));
                        } else {
                            error!("Failed to create mutex: unknown error");
                            return Err(ServiceError::NamedPipe(format!(
                                "Failed to create instance mutex: unknown error"
                            )));
                        }
                    }

                    // 检查是否已有实例在运行
                    let wait_result = WaitForSingleObject(handle, 0); // 不等待，立即返回
                    if wait_result.0 != 0 {
                        // 0 是 WAIT_OBJECT_0
                        let _ = CloseHandle(handle);
                        return Err(ServiceError::NamedPipe(
                            "Another instance of the helper service is already running".to_string(),
                        ));
                    }

                    info!("Successfully acquired instance mutex");
                    Ok(handle)
                }
                Err(err) => {
                    let message = err.message();
                    let code = err.code().0;
                    error!("Failed to create instance mutex: {message:?}({code})");
                    return Err(ServiceError::NamedPipe(format!(
                        "Failed to create instance mutex: {message:?}({code})"
                    )));
                }
            }
        }
    }

    /// 启动 Named Pipe 服务器
    async fn start(&mut self) -> ServiceResult<()> {
        info!("Starting Named Pipe server on {}", PIPE_NAME);

        let shutdown_rx = self.shutdown_rx.take().ok_or_else(|| {
            ServiceError::NamedPipe("Shutdown receiver already taken".to_string())
        })?;

        let auth_manager = self.auth_manager.clone();
        let request_handler = self.request_handler.clone();
        let active_clients = self.active_clients.clone();

        // 启动服务器任务
        tokio::spawn(async move {
            if let Err(e) =
                Self::server_loop(auth_manager, request_handler, shutdown_rx, active_clients).await
            {
                error!("Named Pipe server error: {}", e);
            }
        });

        debug!("Named Pipe server started successfully");
        Ok(())
    }

    /// 服务器主循环
    async fn server_loop(
        auth_manager: Arc<AuthManager>,
        request_handler: RequestHandler,
        mut shutdown_rx: mpsc::Receiver<()>,
        active_clients: Arc<Mutex<Vec<JoinHandle<()>>>>,
    ) -> ServiceResult<()> {
        loop {
            tokio::select! {
                // 检查关闭信号
                _ = shutdown_rx.recv() => {
                    info!("Received shutdown signal, stopping Named Pipe server");

                    // 取消所有活跃的客户端任务
                    let mut clients = active_clients.lock().await;
                    for handle in clients.drain(..) {
                        handle.abort();
                    }
                    break;
                }

                // 处理新连接
                result = Self::accept_connection() => {
                    match result {
                        Ok(pipe_handle) => {
                            let auth_manager = auth_manager.clone();
                            let request_handler = request_handler.clone();
                            let active_clients_clone = active_clients.clone();

                            // 为每个连接启动处理任务
                            let handle = tokio::spawn(async move {
                                if let Err(e) = Self::handle_client(pipe_handle, auth_manager, request_handler).await {
                                    warn!("Client handling error: {}", e);
                                }

                                // 从活跃客户端列表中移除自己
                                let mut clients = active_clients_clone.lock().await;
                                clients.retain(|h| !h.is_finished());
                            });

                            // 添加到活跃客户端列表
                            active_clients.lock().await.push(handle);
                        }
                        Err(e) => {
                            error!("Failed to accept connection: {}", e);
                            // 实现指数退避重试机制
                            Self::handle_connection_error(&e).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 处理连接错误，实现指数退避重试
    async fn handle_connection_error(error: &ServiceError) {
        // 根据错误类型决定退避策略
        let (base_delay, max_delay) = match error {
            ServiceError::NamedPipe(msg) if msg.contains("ACCESS_DENIED") => {
                // 权限错误，较长的延迟
                (Duration::from_secs(5), Duration::from_secs(60))
            }
            ServiceError::NamedPipe(msg) if msg.contains("PIPE_BUSY") => {
                // 管道忙，中等延迟
                (Duration::from_secs(1), Duration::from_secs(10))
            }
            _ => {
                // 其他错误，短延迟
                (Duration::from_millis(100), Duration::from_secs(5))
            }
        };

        // 使用线程局部变量跟踪重试次数
        thread_local! {
            static RETRY_COUNT: std::cell::RefCell<u32> = std::cell::RefCell::new(0);
        }

        let delay = RETRY_COUNT.with(|count| {
            let mut count = count.borrow_mut();
            *count += 1;

            // 指数退避：base_delay * 2^count，但不超过 max_delay
            let exponential_delay = base_delay.as_millis() * (2_u128.pow(*count - 1));
            let final_delay = std::cmp::min(exponential_delay, max_delay.as_millis());

            debug!(
                "Connection error retry #{}, waiting {}ms",
                count, final_delay
            );

            // 如果错误持续超过一定次数，重置计数器
            if *count > 10 {
                warn!("Connection errors persist, resetting retry counter");
                *count = 1;
            }

            Duration::from_millis(final_delay as u64)
        });

        tokio::time::sleep(delay).await;
    }

    /// 创建安全的管道安全描述符
    fn create_pipe_security_attributes() -> ServiceResult<SECURITY_ATTRIBUTES> {
        unsafe {
            // 创建安全描述符
            let mut security_descriptor = SECURITY_DESCRIPTOR::default();

            // 初始化安全描述符
            let result = InitializeSecurityDescriptor(
                PSECURITY_DESCRIPTOR(&mut security_descriptor as *mut _ as *mut _),
                1, // SECURITY_DESCRIPTOR_REVISION
            );

            if let Err(err) = result {
                let message = err.message();
                let code = err.code().0;
                error!("Failed to initialize security descriptor:  {message:?}({code})");
                return Err(ServiceError::NamedPipe(format!(
                    "Failed to initialize security descriptor:  {message:?}({code})"
                )));
            }

            // 设置 DACL 为 NULL，允许所有用户访问
            let result = SetSecurityDescriptorDacl(
                PSECURITY_DESCRIPTOR(&mut security_descriptor as *mut _ as *mut _),
                true,  // bDaclPresent
                None,  // pDacl (NULL = 允许所有访问)
                false, // bDaclDefaulted
            );

            if let Err(err) = result {
                let message = err.message();
                let code = err.code().0;
                error!("Failed to set security descriptor DACL: {message:?}({code})");
                return Err(ServiceError::NamedPipe(format!(
                    "Failed to set security descriptor DACL: {message:?}({code})"
                )));
            }

            let security_attributes = SECURITY_ATTRIBUTES {
                nLength: std::mem::size_of::<SECURITY_ATTRIBUTES>() as u32,
                lpSecurityDescriptor: &mut security_descriptor as *mut _ as *mut _,
                bInheritHandle: false.into(),
            };

            debug!("Created pipe security attributes");
            Ok(security_attributes)
        }
    }

    /// 接受新的 Named Pipe 连接
    async fn accept_connection() -> ServiceResult<HANDLE> {
        let pipe_name_wide: Vec<u16> = PIPE_NAME.encode_utf16().chain(std::iter::once(0)).collect();

        debug!("Attempting to create named pipe: {}", PIPE_NAME);

        // 创建安全描述符
        let mut security_attributes = Self::create_pipe_security_attributes()?;

        unsafe {
            let pipe_handle = CreateNamedPipeW(
                PCWSTR(pipe_name_wide.as_ptr()),
                PIPE_ACCESS_DUPLEX,
                PIPE_TYPE_BYTE | PIPE_READMODE_BYTE | PIPE_WAIT,
                PIPE_UNLIMITED_INSTANCES,
                BUFFER_SIZE,
                BUFFER_SIZE,
                0,
                Some(&mut security_attributes),
            );

            if pipe_handle == INVALID_HANDLE_VALUE {
                let error_code = GetLastError();
                if let Err(err) = error_code {
                    let hres = err.code();
                    let error_message = hres.message();
                    error!(
                        "Failed to create named pipe '{}': {} (Windows error code: {})",
                        PIPE_NAME, error_message, hres.0
                    );

                    return Err(ServiceError::NamedPipe(format!(
                        "Failed to create named pipe: {} (error code: {})",
                        error_message, hres.0
                    )));
                }
            }

            // 等待客户端连接
            match ConnectNamedPipe(pipe_handle, None) {
                Ok(_) => {
                    debug!("Successfully connected to named pipe client");
                }
                Err(err) => {
                    let message = err.message();
                    let code = err.code().0;
                    error!("Failed to connect named pipe: {message:?}({code})");
                    if let Err(err) = CloseHandle(pipe_handle) {
                        error!(
                            "Failed close pipe handle: {}({})",
                            err.message(),
                            err.code()
                        )
                    };
                    return Err(ServiceError::NamedPipe(format!(
                        "Failed to connect named pipe: {message:?}({code})"
                    )));
                }
            }

            debug!("New client connected to Named Pipe");
            Ok(pipe_handle)
        }
    }

    /// 处理客户端连接
    #[instrument(skip(pipe_handle, _auth_manager, request_handler))]
    async fn handle_client(
        pipe_handle: HANDLE,
        _auth_manager: Arc<AuthManager>,
        request_handler: RequestHandler,
    ) -> ServiceResult<()> {
        debug!("Handling new client connection");

        // 将 Windows HANDLE 包装为 Tokio 兼容的类型
        let mut pipe_stream = WindowsPipeStream::new(pipe_handle)?;

        // 设置连接超时
        let connection_timeout = Duration::from_secs(300); // 5 minutes per connection
        let operation_timeout = Duration::from_secs(30); // 30 seconds per operation

        let result = tokio::time::timeout(connection_timeout, async {
            // 创建一次 BufReader，在整个连接生命周期中重用
            let mut buf_reader = BufReader::new(&mut pipe_stream);

            loop {
                // 读取消息帧
                let frame = {
                    match tokio::time::timeout(
                        operation_timeout,
                        read_message_frame(&mut buf_reader),
                    )
                    .await
                    {
                        Ok(Ok(Some(frame))) => frame,
                        Ok(Ok(None)) => {
                            debug!("Received non-frame data, continuing...");
                            continue;
                        }
                        Ok(Err(e)) => {
                            // 区分不同类型的错误
                            match e.kind() {
                                std::io::ErrorKind::UnexpectedEof => {
                                    debug!("Client disconnected (EOF)");
                                    return Ok(());
                                }
                                std::io::ErrorKind::BrokenPipe => {
                                    debug!("Client disconnected (broken pipe)");
                                    return Ok(());
                                }
                                _ => {
                                    error!("Failed to read message frame: {}", e);
                                    return Err(ServiceError::NamedPipe(format!(
                                        "Read error: {}",
                                        e
                                    )));
                                }
                            }
                        }
                        Err(_) => {
                            warn!("Timeout reading message frame");
                            return Err(ServiceError::Timeout(
                                "Read operation timed out".to_string(),
                            ));
                        }
                    }
                };

                debug!(
                    "Received frame: event_code={:?}, operation_id={}, is_response={}",
                    frame.event_code, frame.operation_id, frame.is_response
                );

                // 处理不同类型的事件码
                match frame.event_code {
                    EventCode::Exit => {
                        info!("Client requested exit, closing connection");
                        break;
                    }
                    EventCode::Flush => {
                        debug!("Received flush request, continuing...");
                        continue;
                    }
                    EventCode::Data => {
                        let operation_id = frame.operation_id;
                        // 处理数据帧
                        let response = Self::process_data_frame(frame, &request_handler).await;

                        // 发送响应 - 需要直接使用 pipe_stream，因为 BufReader 可能会缓存数据
                        // 获取 pipe_stream 的可变引用
                        let pipe_stream_ref = buf_reader.get_mut();
                        if let Err(e) =
                            Self::send_response(pipe_stream_ref, response, operation_id).await
                        {
                            error!("Failed to send response: {}", e);
                            return Err(e);
                        }
                    }
                    EventCode::Binary => {
                        warn!("Received binary data, but not supported, ignoring");
                        continue;
                    }
                    EventCode::Reserved => {
                        warn!("Received reserved event code, ignoring");
                        continue;
                    }
                }
            }
            Ok(())
        })
        .await;

        // 处理超时和其他错误
        match result {
            Ok(Ok(())) => {
                debug!("Client connection handled successfully");
            }
            Ok(Err(e)) => {
                error!("Error handling client: {}", e);
                // 尝试发送错误响应 - 直接使用 pipe_stream
                let error_response = HelperSvcResponse::Error {
                    trace_id: uuid::Uuid::new_v4(),
                    code: e.error_code(),
                    message: e.user_message(),
                };
                let _ = Self::send_response(&mut pipe_stream, error_response, 0).await;
            }
            Err(_) => {
                warn!("Client connection timed out");
                let timeout_response = HelperSvcResponse::Error {
                    trace_id: uuid::Uuid::new_v4(),
                    code: ServiceError::Timeout("Connection timed out".to_string()).error_code(),
                    message: "Connection timed out".to_string(),
                };
                let _ = Self::send_response(&mut pipe_stream, timeout_response, 0).await;
            }
        }

        // 断开连接 - 句柄清理由 WindowsPipeStream::drop 负责
        unsafe {
            let _ = DisconnectNamedPipe(pipe_handle);
            // 移除重复的 CloseHandle 调用，避免双重关闭
            // CloseHandle 将在 WindowsPipeStream::drop 中处理
        }

        debug!("Client connection closed");
        Ok(())
    }

    /// 处理数据帧
    async fn process_data_frame(
        frame: MessageFrame,
        request_handler: &RequestHandler,
    ) -> HelperSvcResponse {
        // 反序列化事件
        match serde_json::from_slice::<HelperSvcEvent>(&frame.bytes) {
            Ok(event) => {
                debug!("Deserialized event: {:?}", std::mem::discriminant(&event));
                request_handler.handle_event(event).await
            }
            Err(e) => {
                error!("Failed to deserialize HelperSvcEvent: {}", e);
                HelperSvcResponse::Error {
                    trace_id: uuid::Uuid::new_v4(),
                    code: ServiceError::InvalidInput("Invalid event format".to_string())
                        .error_code(),
                    message: format!("Failed to parse event: {}", e),
                }
            }
        }
    }

    /// 发送响应
    async fn send_response(
        pipe_stream: &mut WindowsPipeStream,
        response: HelperSvcResponse,
        operation_id: u32,
    ) -> ServiceResult<()> {
        // 序列化响应
        let response_bytes = serde_json::to_vec(&response)
            .map_err(|e| ServiceError::NamedPipe(format!("Failed to serialize response: {}", e)))?;

        // 发送消息帧
        send_message_frame(
            pipe_stream,
            EventCode::Data,
            operation_id,
            true, // is_response = true
            &response_bytes,
        )
        .await
        .map_err(|e| ServiceError::NamedPipe(format!("Failed to send response frame: {}", e)))?;

        debug!("Response sent successfully, operation_id={}", operation_id);
        Ok(())
    }

    /// 关闭服务器
    pub async fn shutdown(&mut self) -> ServiceResult<()> {
        info!("Shutting down Named Pipe server");

        if let Some(shutdown_tx) = self.shutdown_tx.take() {
            if let Err(e) = shutdown_tx.send(()).await {
                warn!("Failed to send shutdown signal: {}", e);
            }
        }

        // 等待所有客户端任务完成
        let mut clients = self.active_clients.lock().await;
        for handle in clients.drain(..) {
            if !handle.is_finished() {
                handle.abort();
            }
        }

        debug!("Named Pipe server shutdown completed");
        Ok(())
    }
}

impl Drop for PipeServer {
    fn drop(&mut self) {
        // 释放单实例互斥锁
        if let Some(mutex_handle) = self.instance_mutex.take() {
            unsafe {
                let _ = ReleaseMutex(mutex_handle);
                let _ = CloseHandle(mutex_handle);
            }
            info!("Released instance mutex");
        }
    }
}

/// Windows Named Pipe 流包装器
struct WindowsPipeStream {
    handle: HANDLE,
    read_state: ReadState,
    write_state: WriteState,
}

/// 读取操作状态
enum ReadState {
    Idle,
    Reading(tokio::task::JoinHandle<std::io::Result<Vec<u8>>>),
}

/// 写入操作状态
enum WriteState {
    Idle,
    Writing(tokio::task::JoinHandle<std::io::Result<usize>>),
}

impl WindowsPipeStream {
    fn new(handle: HANDLE) -> ServiceResult<Self> {
        Ok(Self {
            handle,
            read_state: ReadState::Idle,
            write_state: WriteState::Idle,
        })
    }

    /// 阻塞读取数据
    fn blocking_read(&self, buf: &mut [u8]) -> std::io::Result<usize> {
        // 检查句柄有效性
        if self.handle == INVALID_HANDLE_VALUE {
            return Err(std::io::Error::new(
                std::io::ErrorKind::BrokenPipe,
                "Pipe handle is invalid",
            ));
        }

        let mut bytes_read = 0u32;

        unsafe {
            let result = ReadFile(self.handle, Some(buf), Some(&mut bytes_read), None);

            match result {
                Ok(_) => Ok(bytes_read as usize),
                Err(e) => Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("ReadFile failed: {}", e),
                )),
            }
        }
    }

    /// 阻塞写入数据
    fn blocking_write(&self, buf: &[u8]) -> std::io::Result<usize> {
        // 检查句柄有效性
        if self.handle == INVALID_HANDLE_VALUE {
            return Err(std::io::Error::new(
                std::io::ErrorKind::BrokenPipe,
                "Pipe handle is invalid",
            ));
        }

        let mut bytes_written = 0u32;

        unsafe {
            let result = WriteFile(self.handle, Some(buf), Some(&mut bytes_written), None);

            match result {
                Ok(_) => Ok(bytes_written as usize),
                Err(e) => Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    format!("WriteFile failed: {}", e),
                )),
            }
        }
    }
}

impl AsyncRead for WindowsPipeStream {
    fn poll_read(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
        buf: &mut tokio::io::ReadBuf<'_>,
    ) -> std::task::Poll<std::io::Result<()>> {
        let this = self.as_mut().get_mut();

        loop {
            match &mut this.read_state {
                ReadState::Idle => {
                    // 启动新的读取操作
                    let handle = this.handle;
                    let buffer_size = buf.remaining();

                    if buffer_size == 0 {
                        return std::task::Poll::Ready(Ok(()));
                    }

                    let read_task = tokio::task::spawn_blocking(move || {
                        // 检查句柄有效性
                        if handle == INVALID_HANDLE_VALUE {
                            return Err(std::io::Error::new(
                                std::io::ErrorKind::BrokenPipe,
                                "Pipe handle is invalid",
                            ));
                        }

                        let mut temp_buf = vec![0u8; buffer_size];

                        unsafe {
                            let mut bytes_read = 0u32;

                            let result =
                                ReadFile(handle, Some(&mut temp_buf), Some(&mut bytes_read), None);

                            match result {
                                Ok(_) => {
                                    temp_buf.truncate(bytes_read as usize);
                                    Ok(temp_buf)
                                }
                                Err(e) => Err(std::io::Error::new(
                                    std::io::ErrorKind::Other,
                                    format!("ReadFile failed: {}", e),
                                )),
                            }
                        }
                    });

                    this.read_state = ReadState::Reading(read_task);
                    continue;
                }
                ReadState::Reading(task) => {
                    match task.poll_unpin(cx) {
                        std::task::Poll::Ready(Ok(Ok(data))) => {
                            // 读取成功
                            this.read_state = ReadState::Idle;

                            if !data.is_empty() {
                                buf.put_slice(&data);
                            }

                            return std::task::Poll::Ready(Ok(()));
                        }
                        std::task::Poll::Ready(Ok(Err(e))) => {
                            // I/O 错误
                            this.read_state = ReadState::Idle;
                            return std::task::Poll::Ready(Err(e));
                        }
                        std::task::Poll::Ready(Err(e)) => {
                            // 任务 join 错误
                            this.read_state = ReadState::Idle;
                            return std::task::Poll::Ready(Err(std::io::Error::new(
                                std::io::ErrorKind::Other,
                                format!("Read task join error: {}", e),
                            )));
                        }
                        std::task::Poll::Pending => {
                            return std::task::Poll::Pending;
                        }
                    }
                }
            }
        }
    }
}

impl AsyncWrite for WindowsPipeStream {
    fn poll_write(
        mut self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
        buf: &[u8],
    ) -> std::task::Poll<Result<usize, std::io::Error>> {
        let this = self.as_mut().get_mut();

        loop {
            match &mut this.write_state {
                WriteState::Idle => {
                    // 启动新的写入操作
                    if buf.is_empty() {
                        return std::task::Poll::Ready(Ok(0));
                    }

                    let handle = this.handle;
                    let buf_vec = buf.to_vec();

                    let write_task = tokio::task::spawn_blocking(move || {
                        // 检查句柄有效性
                        if handle == INVALID_HANDLE_VALUE {
                            return Err(std::io::Error::new(
                                std::io::ErrorKind::BrokenPipe,
                                "Pipe handle is invalid",
                            ));
                        }

                        unsafe {
                            let mut bytes_written = 0u32;

                            let result =
                                WriteFile(handle, Some(&buf_vec), Some(&mut bytes_written), None);

                            match result {
                                Ok(_) => Ok(bytes_written as usize),
                                Err(e) => Err(std::io::Error::new(
                                    std::io::ErrorKind::Other,
                                    format!("WriteFile failed: {}", e),
                                )),
                            }
                        }
                    });

                    this.write_state = WriteState::Writing(write_task);
                    continue;
                }
                WriteState::Writing(task) => {
                    match task.poll_unpin(cx) {
                        std::task::Poll::Ready(Ok(Ok(bytes_written))) => {
                            // 写入成功
                            this.write_state = WriteState::Idle;
                            return std::task::Poll::Ready(Ok(bytes_written));
                        }
                        std::task::Poll::Ready(Ok(Err(e))) => {
                            // I/O 错误
                            this.write_state = WriteState::Idle;
                            return std::task::Poll::Ready(Err(e));
                        }
                        std::task::Poll::Ready(Err(e)) => {
                            // 任务 join 错误
                            this.write_state = WriteState::Idle;
                            return std::task::Poll::Ready(Err(std::io::Error::new(
                                std::io::ErrorKind::Other,
                                format!("Write task join error: {}", e),
                            )));
                        }
                        std::task::Poll::Pending => {
                            return std::task::Poll::Pending;
                        }
                    }
                }
            }
        }
    }

    fn poll_flush(
        self: std::pin::Pin<&mut Self>,
        _cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), std::io::Error>> {
        // Named Pipe 通常不需要显式刷新
        std::task::Poll::Ready(Ok(()))
    }

    fn poll_shutdown(
        self: std::pin::Pin<&mut Self>,
        _cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), std::io::Error>> {
        // 关闭操作在 Drop 中处理
        std::task::Poll::Ready(Ok(()))
    }
}

impl Drop for WindowsPipeStream {
    fn drop(&mut self) {
        // 取消正在进行的操作并等待它们完成
        match &mut self.read_state {
            ReadState::Reading(task) => {
                task.abort();
                // 等待任务完成，避免在句柄关闭后仍有任务运行
                // 注意：这是一个阻塞调用，但在 drop 中是必要的
                let _ = futures::executor::block_on(task);
            }
            ReadState::Idle => {}
        }

        match &mut self.write_state {
            WriteState::Writing(task) => {
                task.abort();
                // 等待任务完成
                let _ = futures::executor::block_on(task);
            }
            WriteState::Idle => {}
        }

        // 关闭 Windows 句柄
        unsafe {
            // 验证句柄有效性
            if self.handle != INVALID_HANDLE_VALUE {
                let result = CloseHandle(self.handle);
                if let Err(e) = result {
                    tracing::warn!("Failed to close pipe handle: {}", e);
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;

    #[test]
    fn test_pipe_constants() {
        assert_eq!(PIPE_NAME, r"\\.\pipe\echowave_helper");
        assert_eq!(BUFFER_SIZE, 4096);
        assert_eq!(MAX_INSTANCES, 10);
        assert_eq!(MUTEX_NAME, "Global\\EchoWaveHelperSvcMutex");
    }

    #[tokio::test]
    async fn test_pipe_server_creation() {
        let auth_manager = Arc::new(crate::auth::AuthManager::new(Duration::from_secs(3600)));
        let result = PipeServer::new(auth_manager).await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_windows_pipe_stream_creation() {
        // 测试使用无效句柄创建流
        let result = WindowsPipeStream::new(INVALID_HANDLE_VALUE);
        assert!(result.is_ok()); // 创建本身应该成功，使用时才会失败
    }

    #[tokio::test]
    async fn test_process_data_frame() {
        let auth_manager = Arc::new(crate::auth::AuthManager::new(Duration::from_secs(3600)));
        let request_handler = RequestHandler::new(auth_manager).await.unwrap();

        // 创建测试事件
        let test_event = HelperSvcEvent::CheckVirtualization {
            trace_id: uuid::Uuid::new_v4(),
        };

        // 序列化事件
        let event_bytes = serde_json::to_vec(&test_event).unwrap();

        // 创建消息帧
        let frame = MessageFrame {
            event_code: EventCode::Data,
            operation_id: 12345,
            is_response: false,
            bytes: event_bytes,
        };

        // 处理帧
        let response = PipeServer::process_data_frame(frame, &request_handler).await;

        // 验证响应
        match response {
            HelperSvcResponse::VirtualizationInfo { .. } => {
                // 预期的响应类型
            }
            HelperSvcResponse::Error { .. } => {
                // 也可能是错误响应，取决于系统状态
            }
            _ => panic!("Unexpected response type"),
        }
    }

    #[tokio::test]
    async fn test_process_invalid_data_frame() {
        let auth_manager = Arc::new(crate::auth::AuthManager::new(Duration::from_secs(3600)));
        let request_handler = RequestHandler::new(auth_manager).await.unwrap();

        // 创建无效的消息帧
        let frame = MessageFrame {
            event_code: EventCode::Data,
            operation_id: 12345,
            is_response: false,
            bytes: b"invalid json".to_vec(),
        };

        // 处理帧
        let response = PipeServer::process_data_frame(frame, &request_handler).await;

        // 应该返回错误响应
        match response {
            HelperSvcResponse::Error { message, .. } => {
                assert!(message.contains("Failed to parse event"));
            }
            _ => panic!("Expected Error response for invalid data"),
        }
    }

    #[test]
    fn test_mutex_creation() {
        // 测试互斥锁创建
        let result = PipeServer::acquire_instance_mutex();
        assert!(result.is_ok());

        // 清理
        if let Ok(handle) = result {
            unsafe {
                let _ = ReleaseMutex(handle);
                let _ = CloseHandle(handle);
            }
        }
    }

    #[test]
    fn test_security_attributes_creation() {
        // 测试安全描述符创建
        let result = PipeServer::create_pipe_security_attributes();
        assert!(result.is_ok());

        if let Ok(attrs) = result {
            assert_eq!(
                attrs.nLength,
                std::mem::size_of::<SECURITY_ATTRIBUTES>() as u32
            );
            assert!(!attrs.lpSecurityDescriptor.is_null());
            assert_eq!(attrs.bInheritHandle, false);
        }
    }

    #[tokio::test]
    async fn test_error_handling_delays() {
        // 测试不同错误类型的延迟策略
        let access_denied_error = ServiceError::NamedPipe(
            "ACCESS_DENIED - Service needs administrator privileges".to_string(),
        );
        let pipe_busy_error = ServiceError::NamedPipe(
            "PIPE_BUSY - Another instance is already using this pipe name".to_string(),
        );
        let other_error = ServiceError::NamedPipe("Some other error".to_string());

        // 这些测试实际上会引入延迟，所以我们只是验证函数不会panic
        let start_time = std::time::Instant::now();
        PipeServer::handle_connection_error(&access_denied_error).await;
        let duration = start_time.elapsed();

        // ACCESS_DENIED 应该至少有5秒延迟
        assert!(duration >= Duration::from_secs(5));

        let start_time = std::time::Instant::now();
        PipeServer::handle_connection_error(&pipe_busy_error).await;
        let duration = start_time.elapsed();

        // PIPE_BUSY 应该至少有1秒延迟
        assert!(duration >= Duration::from_secs(1));

        let start_time = std::time::Instant::now();
        PipeServer::handle_connection_error(&other_error).await;
        let duration = start_time.elapsed();

        // 其他错误应该至少有100毫秒延迟
        assert!(duration >= Duration::from_millis(100));
    }
}
