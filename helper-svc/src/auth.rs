use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use uuid::Uuid;
use windows::Win32::System::Threading::{GetCurrentProcessId, OpenProcess, PROCESS_QUERY_INFORMATION};
use windows::Win32::Foundation::CloseHandle;
use tracing::{debug, warn, error};

use crate::error::{ServiceError, ServiceResult};

/// 认证令牌信息
#[derive(Debug, Clone)]
pub struct AuthToken {
    pub token: String,
    pub process_id: u32,
    pub created_at: SystemTime,
    pub expires_at: SystemTime,
}

impl AuthToken {
    /// 创建新的认证令牌
    pub fn new(process_id: u32, ttl: Duration) -> Self {
        let now = SystemTime::now();
        let token = format!("{}-{}-{}", 
            process_id,
            now.duration_since(UNIX_EPOCH).unwrap().as_secs(),
            Uuid::new_v4()
        );

        Self {
            token,
            process_id,
            created_at: now,
            expires_at: now + ttl,
        }
    }

    /// 检查令牌是否过期
    pub fn is_expired(&self) -> bool {
        SystemTime::now() > self.expires_at
    }

    /// 验证进程是否仍然存在
    pub fn is_process_alive(&self) -> bool {
        unsafe {
            match OpenProcess(PROCESS_QUERY_INFORMATION, false, self.process_id) {
                Ok(handle) => {
                    let _ = CloseHandle(handle);
                    true
                }
                Err(_) => false,
            }
        }
    }

    /// 检查令牌是否有效
    pub fn is_valid(&self) -> bool {
        !self.is_expired() && self.is_process_alive()
    }
}

/// 认证管理器
pub struct AuthManager {
    tokens: Arc<Mutex<HashMap<String, AuthToken>>>,
    default_ttl: Duration,
}

impl AuthManager {
    /// 创建新的认证管理器
    pub fn new(default_ttl: Duration) -> Self {
        Self {
            tokens: Arc::new(Mutex::new(HashMap::new())),
            default_ttl,
        }
    }

    /// 生成新的认证令牌
    pub fn generate_token(&self, process_id: u32) -> ServiceResult<String> {
        let token = AuthToken::new(process_id, self.default_ttl);
        let token_str = token.token.clone();

        let mut tokens = self.tokens.lock()
            .map_err(|e| ServiceError::Authentication(format!("Failed to lock tokens: {}", e)))?;

        tokens.insert(token_str.clone(), token);
        debug!("Generated auth token for process {}: {}", process_id, token_str);

        Ok(token_str)
    }

    /// 验证认证令牌
    pub fn validate_token(&self, token: &str) -> ServiceResult<u32> {
        let mut tokens = self.tokens.lock()
            .map_err(|e| ServiceError::Authentication(format!("Failed to lock tokens: {}", e)))?;

        match tokens.get(token) {
            Some(auth_token) => {
                if auth_token.is_valid() {
                    debug!("Token validation successful for process {}", auth_token.process_id);
                    Ok(auth_token.process_id)
                } else {
                    warn!("Token validation failed: token expired or process dead");
                    tokens.remove(token);
                    Err(ServiceError::Authentication("Token expired or process not found".to_string()))
                }
            }
            None => {
                warn!("Token validation failed: token not found");
                Err(ServiceError::Authentication("Invalid token".to_string()))
            }
        }
    }

    /// 撤销认证令牌
    pub fn revoke_token(&self, token: &str) -> ServiceResult<()> {
        let mut tokens = self.tokens.lock()
            .map_err(|e| ServiceError::Authentication(format!("Failed to lock tokens: {}", e)))?;

        if tokens.remove(token).is_some() {
            debug!("Token revoked: {}", token);
            Ok(())
        } else {
            warn!("Attempted to revoke non-existent token: {}", token);
            Err(ServiceError::Authentication("Token not found".to_string()))
        }
    }

    /// 清理过期的令牌
    pub fn cleanup_expired_tokens(&self) -> ServiceResult<usize> {
        let mut tokens = self.tokens.lock()
            .map_err(|e| ServiceError::Authentication(format!("Failed to lock tokens: {}", e)))?;

        let initial_count = tokens.len();
        tokens.retain(|_, token| token.is_valid());
        let removed_count = initial_count - tokens.len();

        if removed_count > 0 {
            debug!("Cleaned up {} expired tokens", removed_count);
        }

        Ok(removed_count)
    }

    /// 获取当前活跃令牌数量
    pub fn active_token_count(&self) -> ServiceResult<usize> {
        let tokens = self.tokens.lock()
            .map_err(|e| ServiceError::Authentication(format!("Failed to lock tokens: {}", e)))?;

        Ok(tokens.len())
    }

    /// 验证调用者进程的合法性
    pub fn validate_caller_process(&self, expected_process_id: u32) -> ServiceResult<()> {
        let current_process_id = unsafe { GetCurrentProcessId() };

        if current_process_id == expected_process_id {
            return Ok(());
        }

        // 检查进程是否存在且可访问
        unsafe {
            match OpenProcess(PROCESS_QUERY_INFORMATION, false, expected_process_id) {
                Ok(handle) => {
                    let _ = CloseHandle(handle);
                    Ok(())
                }
                Err(_) => Err(ServiceError::Authentication(
                    format!("Process {} not found or inaccessible", expected_process_id)
                )),
            }
        }
    }
}

impl Default for AuthManager {
    fn default() -> Self {
        Self::new(Duration::from_secs(3600)) // 默认1小时过期
    }
}

/// 定期清理过期令牌的任务
pub async fn token_cleanup_task(auth_manager: Arc<AuthManager>, interval: Duration) {
    let mut cleanup_interval = tokio::time::interval(interval);
    
    loop {
        cleanup_interval.tick().await;
        
        match auth_manager.cleanup_expired_tokens() {
            Ok(count) => {
                if count > 0 {
                    debug!("Token cleanup completed, removed {} expired tokens", count);
                }
            }
            Err(e) => {
                error!("Token cleanup failed: {}", e);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;

    #[test]
    fn test_auth_token_creation() {
        let process_id = 1234;
        let ttl = Duration::from_secs(60);
        let token = AuthToken::new(process_id, ttl);

        assert_eq!(token.process_id, process_id);
        assert!(!token.is_expired());
        assert!(token.token.contains(&process_id.to_string()));
    }

    #[test]
    fn test_auth_token_expiration() {
        let process_id = 1234;
        let ttl = Duration::from_millis(10);
        let token = AuthToken::new(process_id, ttl);

        assert!(!token.is_expired());
        thread::sleep(Duration::from_millis(20));
        assert!(token.is_expired());
    }

    #[test]
    fn test_auth_manager_token_lifecycle() {
        let manager = AuthManager::new(Duration::from_secs(60));
        let process_id = unsafe { GetCurrentProcessId() };

        // 生成令牌
        let token = manager.generate_token(process_id).unwrap();
        assert_eq!(manager.active_token_count().unwrap(), 1);

        // 验证令牌
        let validated_pid = manager.validate_token(&token).unwrap();
        assert_eq!(validated_pid, process_id);

        // 撤销令牌
        manager.revoke_token(&token).unwrap();
        assert_eq!(manager.active_token_count().unwrap(), 0);

        // 验证已撤销的令牌应该失败
        assert!(manager.validate_token(&token).is_err());
    }

    #[test]
    fn test_auth_manager_cleanup() {
        let manager = AuthManager::new(Duration::from_millis(10));
        let process_id = unsafe { GetCurrentProcessId() };

        // 生成令牌
        let _token = manager.generate_token(process_id).unwrap();
        assert_eq!(manager.active_token_count().unwrap(), 1);

        // 等待令牌过期
        thread::sleep(Duration::from_millis(20));

        // 清理过期令牌
        let cleaned = manager.cleanup_expired_tokens().unwrap();
        assert_eq!(cleaned, 1);
        assert_eq!(manager.active_token_count().unwrap(), 0);
    }
}
