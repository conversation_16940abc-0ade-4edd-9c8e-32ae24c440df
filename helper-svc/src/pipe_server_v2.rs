//! # PipeServerV2 - Tokio Named Pipe Implementation
//!
//! This module provides a refactored implementation of the Windows Named Pipe server
//! using Tokio's official `tokio::net::windows::named_pipe` instead of manual Windows API calls.
//!
//! ## Key Improvements
//!
//! - **Stability**: Uses Tokio's thoroughly tested named pipe implementation
//! - **Simplicity**: Eliminates manual state management and complex handle lifecycle
//! - **Performance**: Removes `spawn_blocking` overhead with native async I/O
//! - **Maintainability**: Reduces code complexity from ~1000 lines to ~200 lines
//!
//! ## Architecture
//!
//! ```text
//! PipeServerV2
//! ├── server_loop()           # Main server loop with tokio::select!
//! ├── accept_connection()     # Create and connect named pipe
//! ├── handle_client()         # Process individual client connections
//! └── cleanup()              # Graceful shutdown and resource cleanup
//! ```
#![cfg(target_os = "windows")]
use std::collections::HashMap;
use std::sync::atomic::AtomicU32;
use std::sync::{Arc, OnceLock};
use std::time::Duration;
use tokio::io::{AsyncBufReadExt, AsyncWriteExt, BufReader};
use tokio::net::windows::named_pipe::{NamedPipeServer, ServerOptions};
use tokio::sync::{mpsc, Mutex};
use tokio::task::JoinHandle;
use tokio::time::timeout;
use tracing::{debug, error, info, warn};
use uuid;
use windows::core::PCWSTR;
use windows::Win32::Foundation::{CloseHandle, GetLastError, HANDLE, INVALID_HANDLE_VALUE};
use windows::Win32::Security::{
    InitializeSecurityDescriptor, SetSecurityDescriptorDacl, PSECURITY_DESCRIPTOR,
    SECURITY_ATTRIBUTES, SECURITY_DESCRIPTOR,
};
use windows::Win32::System::Threading::{CreateMutexW, ReleaseMutex, WaitForSingleObject};

use crate::auth::AuthManager;
use crate::error::{ServiceError, ServiceResult};
use crate::handlers::request_handler::RequestHandler;
use protocol::events::{HelperSvcEvent, HelperSvcResponse};
use protocol::frame::{read_message_frame, send_message_frame, EventCode, MessageFrame};

/// Named pipe name for the helper service
const PIPE_NAME: &str = r"\\.\pipe\echowave_helper";

/// Mutex name for single instance control
const MUTEX_NAME: &str = "Global\\EchoWaveHelperSvcMutex";

/// Default timeout for client operations
const CLIENT_TIMEOUT: Duration = Duration::from_secs(30);

/// Maximum number of concurrent client connections
const MAX_CONCURRENT_CLIENTS: usize = 10;

/// Thread-local cache for security descriptor to avoid recreating it on each connection
/// This significantly improves performance in high-frequency connection scenarios
thread_local! {
    static CACHED_SECURITY_DESCRIPTOR: OnceLock<SECURITY_DESCRIPTOR> = OnceLock::new();
}

/// PipeServerV2 - Simplified named pipe server using Tokio's official implementation
pub struct PipeServerV2 {
    /// Authentication manager for validating clients
    auth_manager: Arc<AuthManager>,

    /// Request handler for processing client commands
    request_handler: RequestHandler,

    /// Shutdown signal transmitter
    shutdown_tx: Option<mpsc::Sender<()>>,

    /// Active client connection handles
    active_clients: Arc<Mutex<HashMap<u32, JoinHandle<()>>>>,

    /// Single instance mutex handle
    instance_mutex: Option<HANDLE>,

    /// Operation id counter
    client_id_counter: Arc<AtomicU32>,
}

impl PipeServerV2 {
    /// Create a new PipeServerV2 instance
    pub async fn new(auth_manager: Arc<AuthManager>) -> ServiceResult<Self> {
        // Check single instance control
        let instance_mutex = Self::acquire_instance_mutex()?;

        let request_handler = RequestHandler::new(auth_manager.clone()).await?;

        let mut pipe_server = Self {
            auth_manager,
            request_handler,
            shutdown_tx: None,
            active_clients: Arc::new(Mutex::new(HashMap::new())),
            instance_mutex: Some(instance_mutex),
            client_id_counter: Arc::new(AtomicU32::new(0)),
        };

        pipe_server.start().await?;

        Ok(pipe_server)
    }

    /// Acquire single instance mutex
    fn acquire_instance_mutex() -> ServiceResult<HANDLE> {
        let mutex_name_wide: Vec<u16> = MUTEX_NAME
            .encode_utf16()
            .chain(std::iter::once(0))
            .collect();

        unsafe {
            let mutex_handle = CreateMutexW(None, true, PCWSTR(mutex_name_wide.as_ptr()));

            match mutex_handle {
                Ok(handle) => {
                    if handle.is_invalid() {
                        let error_code = GetLastError();
                        if let Err(err) = error_code {
                            let hres = err.code();
                            let message = hres.message();
                            let code = hres.0;
                            error!("Failed to create mutex: Windows {message:?}({code})");
                            return Err(ServiceError::NamedPipe(format!(
                                "Failed to create instance mutex: {message:?}({code})"
                            )));
                        } else {
                            error!("Failed to create mutex: unknown error");
                            return Err(ServiceError::NamedPipe(format!(
                                "Failed to create instance mutex: unknown error"
                            )));
                        }
                    }

                    // Check if another instance is already running
                    let wait_result = WaitForSingleObject(handle, 0); // Don't wait, return immediately
                    if wait_result.0 != 0 {
                        // 0 is WAIT_OBJECT_0
                        let _ = CloseHandle(handle);
                        return Err(ServiceError::NamedPipe(
                            "Another instance of the helper service is already running".to_string(),
                        ));
                    }

                    info!("Successfully acquired instance mutex");
                    Ok(handle)
                }
                Err(err) => {
                    let message = err.message();
                    let code = err.code().0;
                    error!("Failed to create instance mutex: {message:?}({code})");
                    return Err(ServiceError::NamedPipe(format!(
                        "Failed to create instance mutex: {message:?}({code})"
                    )));
                }
            }
        }
    }

    /// Start the named pipe server
    pub async fn start(&mut self) -> ServiceResult<()> {
        let (shutdown_tx, shutdown_rx) = mpsc::channel(1);
        self.shutdown_tx = Some(shutdown_tx);

        info!("Starting PipeServerV2 on {}", PIPE_NAME);

        let auth_manager = self.auth_manager.clone();
        let request_handler = self.request_handler.clone();
        let active_clients = self.active_clients.clone();
        let client_id_counter = self.client_id_counter.clone();
        // Run the main server loop
        tokio::spawn(async move {
            if let Err(e) = Self::server_loop(
                auth_manager.clone(),
                request_handler.clone(),
                active_clients.clone(),
                client_id_counter,
                shutdown_rx,
            )
            .await
            {
                error!("Named Pipe server error: {}", e);
            }
        });

        debug!("Named Pipe server started successfully");
        Ok(())
    }

    /// Stop the named pipe server
    pub async fn stop(&mut self) -> ServiceResult<()> {
        info!("Stopping PipeServerV2...");

        // Send shutdown signal
        if let Some(tx) = self.shutdown_tx.take() {
            let _ = tx.send(()).await;
        }

        // Wait for all active clients to finish
        self.cleanup_clients().await;

        info!("PipeServerV2 stopped successfully");
        Ok(())
    }

    /// Main server loop - handles incoming connections and shutdown signals
    async fn server_loop(
        auth_manager: Arc<AuthManager>,
        request_handler: RequestHandler,
        active_clients: Arc<Mutex<HashMap<u32, JoinHandle<()>>>>,
        client_id_counter: Arc<AtomicU32>,
        mut shutdown_rx: mpsc::Receiver<()>,
    ) -> ServiceResult<()> {
        loop {
            tokio::select! {
                // Handle shutdown signal
                _ = shutdown_rx.recv() => {
                    info!("Received shutdown signal");
                    break;
                }

                // Accept new client connections
                result = Self::accept_connection() => {
                    match result {
                        Ok(pipe) => {
                            // Check if we have too many concurrent clients
                            let client_count = active_clients.lock().await.len();
                            if client_count >= MAX_CONCURRENT_CLIENTS {
                                warn!("Maximum concurrent clients reached: {}", MAX_CONCURRENT_CLIENTS);
                                continue;
                            }
                            let auth_manager = auth_manager.clone();
                            let request_handler = request_handler.clone();

                            // Spawn a new task to handle the client
                            let active_clients_clone = active_clients.clone();
                            let client_id = client_id_counter.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                            let client_handle = tokio::spawn(async move {
                                if let Err(e) = Self::handle_client(pipe, auth_manager, request_handler).await {
                                    debug!("Client connection ended: {}", e);
                                }
                                active_clients_clone.lock().await.remove(&client_id);
                            });

                            // Store the client handle for cleanup
                            active_clients.lock().await.insert(client_id, client_handle);
                        }
                        Err(e) => {
                            error!("Failed to accept connection: {}", e);
                            // Implement exponential backoff retry mechanism
                            Self::handle_connection_error(&e).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Handle connection error with exponential backoff retry
    async fn handle_connection_error(error: &ServiceError) {
        // Determine backoff strategy based on error type
        let (base_delay, max_delay) = match error {
            ServiceError::NamedPipe(msg) if msg.contains("ACCESS_DENIED") => {
                // Permission error, longer delay
                (Duration::from_secs(5), Duration::from_secs(60))
            }
            ServiceError::NamedPipe(msg) if msg.contains("PIPE_BUSY") => {
                // Pipe busy, medium delay
                (Duration::from_secs(1), Duration::from_secs(10))
            }
            _ => {
                // Other errors, short delay
                (Duration::from_millis(100), Duration::from_secs(5))
            }
        };

        // Use thread-local variable to track retry count
        thread_local! {
            static RETRY_COUNT: std::cell::RefCell<u32> = std::cell::RefCell::new(0);
        }

        let delay = RETRY_COUNT.with(|count| {
            let mut count = count.borrow_mut();
            *count += 1;

            // Exponential backoff: base_delay * 2^count, but not exceeding max_delay
            let exponential_delay = base_delay.as_millis() * (2_u128.pow(*count - 1));
            let final_delay = std::cmp::min(exponential_delay, max_delay.as_millis());

            debug!(
                "Connection error retry #{}, waiting {}ms",
                count, final_delay
            );

            // Reset counter if errors persist for too long
            if *count > 10 {
                warn!("Connection errors persist, resetting retry counter");
                *count = 1;
            }

            Duration::from_millis(final_delay as u64)
        });

        tokio::time::sleep(delay).await;
    }

    /// Create secure pipe security attributes (legacy method - kept for compatibility)
    fn create_pipe_security_attributes() -> ServiceResult<SECURITY_ATTRIBUTES> {
        unsafe {
            // Create security descriptor
            let mut security_descriptor = SECURITY_DESCRIPTOR::default();

            // Initialize security descriptor
            let result = InitializeSecurityDescriptor(
                PSECURITY_DESCRIPTOR(&mut security_descriptor as *mut _ as *mut _),
                1, // SECURITY_DESCRIPTOR_REVISION
            );

            if let Err(err) = result {
                let message = err.message();
                let code = err.code().0;
                error!("Failed to initialize security descriptor: {message:?}({code})");
                return Err(ServiceError::NamedPipe(format!(
                    "Failed to initialize security descriptor: {message:?}({code})"
                )));
            }

            // Set DACL to NULL, allowing all users access
            let result = SetSecurityDescriptorDacl(
                PSECURITY_DESCRIPTOR(&mut security_descriptor as *mut _ as *mut _),
                true,  // bDaclPresent
                None,  // pDacl (NULL = allow all access)
                false, // bDaclDefaulted
            );

            if let Err(err) = result {
                let message = err.message();
                let code = err.code().0;
                error!("Failed to set security descriptor DACL: {message:?}({code})");
                return Err(ServiceError::NamedPipe(format!(
                    "Failed to set security descriptor DACL: {message:?}({code})"
                )));
            }

            let security_attributes = SECURITY_ATTRIBUTES {
                nLength: std::mem::size_of::<SECURITY_ATTRIBUTES>() as u32,
                lpSecurityDescriptor: &mut security_descriptor as *mut _ as *mut _,
                bInheritHandle: false.into(),
            };

            debug!("Created pipe security attributes");
            Ok(security_attributes)
        }
    }

    /// Get cached security attributes for improved performance
    /// This method uses thread-local storage to cache the security descriptor,
    /// avoiding the overhead of recreating it for each connection
    fn get_cached_security_attributes() -> ServiceResult<SECURITY_ATTRIBUTES> {
        // Helper function to create security descriptor
        fn create_security_descriptor() -> SECURITY_DESCRIPTOR {
            unsafe {
                let mut security_descriptor = SECURITY_DESCRIPTOR::default();

                // These operations should never fail with our static configuration
                let _ = InitializeSecurityDescriptor(
                    PSECURITY_DESCRIPTOR(&mut security_descriptor as *mut _ as *mut _),
                    1, // SECURITY_DESCRIPTOR_REVISION
                );

                let _ = SetSecurityDescriptorDacl(
                    PSECURITY_DESCRIPTOR(&mut security_descriptor as *mut _ as *mut _),
                    true,  // bDaclPresent
                    None,  // pDacl (NULL = allow all access)
                    false, // bDaclDefaulted
                );

                security_descriptor
            }
        }

        CACHED_SECURITY_DESCRIPTOR.with(|cached| {
            let security_descriptor = cached.get_or_init(create_security_descriptor);

            // Create SECURITY_ATTRIBUTES pointing to our cached descriptor
            let security_attributes = SECURITY_ATTRIBUTES {
                nLength: std::mem::size_of::<SECURITY_ATTRIBUTES>() as u32,
                lpSecurityDescriptor: security_descriptor as *const _ as *mut _,
                bInheritHandle: false.into(),
            };

            debug!("Using cached security attributes for pipe creation");
            Ok(security_attributes)
        })
    }

    /// Accept a new named pipe connection
    ///
    /// ## Performance Optimization
    /// This method now uses thread-local caching for security descriptors,
    /// significantly reducing overhead in high-frequency connection scenarios:
    /// - Security descriptor is created once per thread and reused
    /// - Eliminates repeated Windows API calls for identical configurations
    /// - Maintains thread safety through thread-local storage
    async fn accept_connection() -> ServiceResult<NamedPipeServer> {
        debug!("Creating named pipe with cached security attributes for all-user access");

        // Create pipe with cached security attributes in a blocking task
        let server = tokio::task::spawn_blocking(move || -> ServiceResult<NamedPipeServer> {
            // Use cached security attributes for better performance
            let security_attributes = Self::get_cached_security_attributes()?;

            unsafe {
                // Create the pipe with security attributes that allow all users
                // Remove first_pipe_instance(true) to allow multiple pipe instances for concurrent connections
                let server = ServerOptions::new()
                    //.first_pipe_instance(true)
                    .access_inbound(true)
                    .access_outbound(true)
                    .create_with_security_attributes_raw(
                        PIPE_NAME,
                        &security_attributes as *const _ as *mut std::ffi::c_void,
                    )
                    .map_err(|e| {
                        ServiceError::PipeError(format!("Failed to create pipe: {}", e))
                    })?;

                debug!("Named pipe created successfully with cached security attributes");
                Ok(server)
            }
        })
        .await
        .map_err(|e| ServiceError::PipeError(format!("Task join error: {}", e)))??;

        debug!("Waiting for client connection on {}", PIPE_NAME);

        // Wait for a client to connect
        server
            .connect()
            .await
            .map_err(|e| ServiceError::PipeError(format!("Failed to connect pipe: {}", e)))?;

        debug!("New client connected");
        Ok(server)
    }

    /// Handle an individual client connection
    async fn handle_client(
        mut pipe: NamedPipeServer,
        _auth_manager: Arc<AuthManager>,
        request_handler: RequestHandler,
    ) -> ServiceResult<()> {
        let mut buf_reader = BufReader::new(&mut pipe);

        // Main client communication loop
        loop {
            // Read message frame with timeout
            let frame = match timeout(CLIENT_TIMEOUT, read_message_frame(&mut buf_reader)).await {
                Ok(Ok(Some(frame))) => frame,
                Ok(Ok(None)) => {
                    debug!("Received EOF, closing connection");
                    break;
                }
                Ok(Err(e)) => {
                    debug!("Client read error: {}", e);
                    break;
                }
                Err(_) => {
                    debug!("Client read timeout");
                    break;
                }
            };

            let operation_id = frame.operation_id;

            // Process different event codes
            match frame.event_code {
                EventCode::Exit => {
                    info!("Client requested exit, closing connection");
                    break;
                }
                EventCode::Flush => {
                    debug!("Received flush request, continuing...");
                    continue;
                }
                EventCode::Data => {
                    // Process data frame
                    let response = Self::process_data_frame(frame, &request_handler).await;

                    // Send response with timeout
                    let pipe_stream_ref = buf_reader.get_mut();
                    if let Err(e) = timeout(
                        CLIENT_TIMEOUT,
                        Self::send_response(pipe_stream_ref, response, operation_id),
                    )
                    .await
                    {
                        error!("Failed to send response: {}", e);
                        break;
                    }
                }
                EventCode::Reserved => {
                    warn!("Received reserved event code, ignoring");
                    continue;
                }
            }
        }

        debug!("Client connection closed");
        Ok(())
    }

    /// Process a data frame
    async fn process_data_frame(
        frame: MessageFrame,
        request_handler: &RequestHandler,
    ) -> HelperSvcResponse {
        // Deserialize the event from the frame
        match serde_json::from_slice::<HelperSvcEvent>(&frame.bytes) {
            Ok(event) => {
                debug!("Deserialized event: {:?}", std::mem::discriminant(&event));
                request_handler.handle_event(event).await
            }
            Err(e) => {
                error!("Failed to deserialize HelperSvcEvent: {}", e);
                HelperSvcResponse::Error {
                    trace_id: uuid::Uuid::new_v4(),
                    code: ServiceError::InvalidInput("Invalid event format".to_string())
                        .error_code(),
                    message: format!("Failed to parse event: {}", e),
                }
            }
        }
    }

    /// Send a response to the client
    async fn send_response(
        pipe: &mut NamedPipeServer,
        response: HelperSvcResponse,
        operation_id: u32,
    ) -> ServiceResult<()> {
        // Serialize response
        let response_bytes = serde_json::to_vec(&response).map_err(|e| {
            ServiceError::Serialization(format!("Failed to serialize response: {}", e))
        })?;

        // Send message frame using protocol function
        send_message_frame(
            pipe,
            EventCode::Data,
            operation_id,
            true, // is_response = true
            &response_bytes,
        )
        .await
        .map_err(|e| ServiceError::Protocol(format!("Failed to send response frame: {}", e)))?;

        debug!("Response sent successfully, operation_id={}", operation_id);
        Ok(())
    }

    /// Clean up all active client connections
    async fn cleanup_clients(&self) {
        let mut clients = self.active_clients.lock().await;

        info!("Cleaning up {} active client connections", clients.len());

        // Cancel all client tasks
        for (_id, handle) in clients.iter() {
            handle.abort();
        }

        // Wait for all tasks to complete (with timeout)
        let cleanup_timeout = Duration::from_secs(5);
        for (_id, handle) in clients.drain() {
            let _ = timeout(cleanup_timeout, handle).await;
        }

        info!("All client connections cleaned up");
    }

    /// Shutdown the server gracefully
    pub async fn shutdown(&mut self) -> ServiceResult<()> {
        info!("Shutting down PipeServerV2");

        if let Some(shutdown_tx) = self.shutdown_tx.take() {
            if let Err(e) = shutdown_tx.send(()).await {
                warn!("Failed to send shutdown signal: {}", e);
            }
        }

        // Wait for all client tasks to complete
        let mut clients = self.active_clients.lock().await;
        for (_id, handle) in clients.drain() {
            if !handle.is_finished() {
                handle.abort();
            }
        }

        debug!("PipeServerV2 shutdown completed");
        Ok(())
    }
}

impl Drop for PipeServerV2 {
    fn drop(&mut self) {
        // Release single instance mutex
        if let Some(mutex_handle) = self.instance_mutex.take() {
            unsafe {
                let _ = ReleaseMutex(mutex_handle);
                let _ = CloseHandle(mutex_handle);
            }
            info!("Released instance mutex");
        }
    }
}
