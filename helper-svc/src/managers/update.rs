use std::path::{Path, PathBuf};
use std::process::Command;
use std::fs;
use std::time::{Duration, SystemTime};
use tokio::time::sleep;
use windows::Win32::System::Shutdown::{ExitWindowsEx, EWX_REBOOT, SHTDN_REASON_MAJOR_APPLICATION};


use tracing::{debug, info, warn, error};
use uuid::Uuid;

use crate::error::{ServiceError, ServiceResult};

/// 更新管理器
#[derive(Clone)]
pub struct UpdateManager {
    temp_dir: PathBuf,
    backup_dir: PathBuf,
}

impl UpdateManager {
    /// 创建新的更新管理器
    pub fn new() -> ServiceResult<Self> {
        let temp_dir = std::env::temp_dir().join("echowave_updates");
        let backup_dir = std::env::temp_dir().join("echowave_backups");

        // 确保目录存在
        fs::create_dir_all(&temp_dir)
            .map_err(|e| ServiceError::Update(format!("Failed to create temp directory: {}", e)))?;
        fs::create_dir_all(&backup_dir)
            .map_err(|e| ServiceError::Update(format!("Failed to create backup directory: {}", e)))?;

        Ok(Self {
            temp_dir,
            backup_dir,
        })
    }

    /// 安装更新
    pub async fn install_update(&self, trace_id: Uuid, update_path: &str) -> ServiceResult<(bool, String)> {
        info!("Installing update from: {} (trace_id: {})", update_path, trace_id);

        let update_file = Path::new(update_path);
        if !update_file.exists() {
            return Err(ServiceError::Update(format!("Update file not found: {}", update_path)));
        }

        // 验证更新文件
        self.validate_update_file(trace_id, update_file).await?;

        // 创建备份
        let backup_path = self.create_backup(trace_id).await?;
        info!("Backup created at: {} (trace_id: {})", backup_path.display(), trace_id);

        // 执行更新安装
        match self.execute_update_installation(trace_id, update_file).await {
            Ok(message) => {
                info!("Update installation successful: {} (trace_id: {})", message, trace_id);
                
                // 清理临时文件
                if let Err(e) = self.cleanup_temp_files(trace_id).await {
                    warn!("Failed to cleanup temp files: {} (trace_id: {})", e, trace_id);
                }

                Ok((true, message))
            }
            Err(e) => {
                error!("Update installation failed: {} (trace_id: {})", e, trace_id);
                
                // 尝试回滚
                if let Err(rollback_error) = self.rollback_update(trace_id, &backup_path).await {
                    error!("Rollback also failed: {} (trace_id: {})", rollback_error, trace_id);
                    return Err(ServiceError::Update(format!("Update failed and rollback failed: {} -> {}", e, rollback_error)));
                }

                Ok((false, format!("Update failed but rollback successful: {}", e)))
            }
        }
    }

    /// 回滚更新
    pub async fn rollback_update(&self, trace_id: Uuid, backup_path: &Path) -> ServiceResult<(bool, String)> {
        info!("Rolling back update from backup: {} (trace_id: {})", backup_path.display(), trace_id);

        if !backup_path.exists() {
            return Err(ServiceError::Update(format!("Backup file not found: {}", backup_path.display())));
        }

        // 停止主程序
        self.stop_main_application(trace_id).await?;

        // 恢复备份文件
        let current_exe = std::env::current_exe()
            .map_err(|e| ServiceError::Update(format!("Failed to get current executable path: {}", e)))?;

        let parent_dir = current_exe.parent()
            .ok_or_else(|| ServiceError::Update("Failed to get parent directory".to_string()))?;

        // 解压备份文件（假设是zip格式）
        match self.extract_backup(backup_path, parent_dir).await {
            Ok(_) => {
                info!("Rollback completed successfully (trace_id: {})", trace_id);
                
                // 重启主程序
                self.restart_main_application(trace_id).await?;
                
                Ok((true, "Rollback completed successfully".to_string()))
            }
            Err(e) => {
                error!("Rollback failed: {} (trace_id: {})", e, trace_id);
                Err(ServiceError::Update(format!("Rollback failed: {}", e)))
            }
        }
    }

    /// 重启系统
    pub async fn restart_system(&self, trace_id: Uuid, delay_seconds: u32) -> ServiceResult<(bool, String)> {
        info!("Restarting system in {} seconds (trace_id: {})", delay_seconds, trace_id);

        if delay_seconds > 0 {
            sleep(Duration::from_secs(delay_seconds as u64)).await;
        }

        unsafe {
            match ExitWindowsEx(EWX_REBOOT, SHTDN_REASON_MAJOR_APPLICATION) {
                Ok(_) => {
                    info!("System restart initiated (trace_id: {})", trace_id);
                    Ok((true, "System restart initiated".to_string()))
                }
                Err(error) => {
                    error!("Failed to restart system: {} (trace_id: {})", error, trace_id);
                    Err(ServiceError::System(format!("Failed to restart system: {}", error)))
                }
            }
        }
    }

    /// 验证更新文件
    pub async fn validate_update_file(&self, trace_id: Uuid, update_file: &Path) -> ServiceResult<()> {
        debug!("Validating update file: {} (trace_id: {})", update_file.display(), trace_id);

        // 检查文件大小
        let metadata = fs::metadata(update_file)
            .map_err(|e| ServiceError::Update(format!("Failed to read file metadata: {}", e)))?;

        if metadata.len() == 0 {
            return Err(ServiceError::Update("Update file is empty".to_string()));
        }

        if metadata.len() > 500 * 1024 * 1024 { // 500MB 限制
            return Err(ServiceError::Update("Update file is too large".to_string()));
        }

        // 检查文件扩展名
        match update_file.extension().and_then(|s| s.to_str()) {
            Some("exe") | Some("msi") | Some("zip") => {
                debug!("Update file format validated (trace_id: {})", trace_id);
                Ok(())
            }
            _ => Err(ServiceError::Update("Unsupported update file format".to_string())),
        }
    }

    /// 创建备份
    async fn create_backup(&self, trace_id: Uuid) -> ServiceResult<PathBuf> {
        debug!("Creating backup (trace_id: {})", trace_id);

        let current_exe = std::env::current_exe()
            .map_err(|e| ServiceError::Update(format!("Failed to get current executable path: {}", e)))?;

        let backup_name = format!("backup_{}.exe", 
            SystemTime::now()
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        );

        let backup_path = self.backup_dir.join(backup_name);

        fs::copy(&current_exe, &backup_path)
            .map_err(|e| ServiceError::Update(format!("Failed to create backup: {}", e)))?;

        debug!("Backup created successfully: {} (trace_id: {})", backup_path.display(), trace_id);
        Ok(backup_path)
    }

    /// 执行更新安装
    async fn execute_update_installation(&self, trace_id: Uuid, update_file: &Path) -> ServiceResult<String> {
        debug!("Executing update installation: {} (trace_id: {})", update_file.display(), trace_id);

        // 停止主程序
        self.stop_main_application(trace_id).await?;

        // 根据文件类型执行不同的安装方式
        match update_file.extension().and_then(|s| s.to_str()) {
            Some("exe") => self.install_exe_update(trace_id, update_file).await,
            Some("msi") => self.install_msi_update(trace_id, update_file).await,
            Some("zip") => self.install_zip_update(trace_id, update_file).await,
            _ => Err(ServiceError::Update("Unsupported update file format".to_string())),
        }
    }

    /// 安装 EXE 更新
    async fn install_exe_update(&self, trace_id: Uuid, update_file: &Path) -> ServiceResult<String> {
        debug!("Installing EXE update (trace_id: {})", trace_id);

        let output = Command::new(update_file)
            .args(&["/S", "/SILENT"]) // 静默安装参数
            .output()
            .map_err(|e| ServiceError::Update(format!("Failed to execute update installer: {}", e)))?;

        if output.status.success() {
            Ok("EXE update installed successfully".to_string())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(ServiceError::Update(format!("EXE update installation failed: {}", stderr)))
        }
    }

    /// 安装 MSI 更新
    async fn install_msi_update(&self, trace_id: Uuid, update_file: &Path) -> ServiceResult<String> {
        debug!("Installing MSI update (trace_id: {})", trace_id);

        let output = Command::new("msiexec")
            .args(&[
                "/i",
                update_file.to_str().unwrap(),
                "/quiet",
                "/norestart",
            ])
            .output()
            .map_err(|e| ServiceError::Update(format!("Failed to execute MSI installer: {}", e)))?;

        if output.status.success() {
            Ok("MSI update installed successfully".to_string())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(ServiceError::Update(format!("MSI update installation failed: {}", stderr)))
        }
    }

    /// 安装 ZIP 更新
    async fn install_zip_update(&self, trace_id: Uuid, update_file: &Path) -> ServiceResult<String> {
        debug!("Installing ZIP update (trace_id: {})", trace_id);

        let current_exe = std::env::current_exe()
            .map_err(|e| ServiceError::Update(format!("Failed to get current executable path: {}", e)))?;

        let parent_dir = current_exe.parent()
            .ok_or_else(|| ServiceError::Update("Failed to get parent directory".to_string()))?;

        // 解压文件到应用程序目录
        self.extract_zip(update_file, parent_dir).await?;

        Ok("ZIP update installed successfully".to_string())
    }

    /// 解压 ZIP 文件
    async fn extract_zip(&self, zip_file: &Path, target_dir: &Path) -> ServiceResult<()> {
        // 这里应该使用 ZIP 解压库，为了简化示例，使用 PowerShell
        let output = Command::new("powershell")
            .args(&[
                "-Command",
                &format!(
                    "Expand-Archive -Path '{}' -DestinationPath '{}' -Force",
                    zip_file.display(),
                    target_dir.display()
                ),
            ])
            .output()
            .map_err(|e| ServiceError::Update(format!("Failed to extract ZIP: {}", e)))?;

        if output.status.success() {
            Ok(())
        } else {
            let stderr = String::from_utf8_lossy(&output.stderr);
            Err(ServiceError::Update(format!("ZIP extraction failed: {}", stderr)))
        }
    }

    /// 解压备份文件
    async fn extract_backup(&self, backup_file: &Path, target_dir: &Path) -> ServiceResult<()> {
        // 简单的文件复制（假设备份是单个可执行文件）
        let target_file = target_dir.join("echowave.exe"); // 假设的主程序名
        
        fs::copy(backup_file, target_file)
            .map_err(|e| ServiceError::Update(format!("Failed to restore backup: {}", e)))?;

        Ok(())
    }

    /// 停止主应用程序
    async fn stop_main_application(&self, trace_id: Uuid) -> ServiceResult<()> {
        debug!("Stopping main application (trace_id: {})", trace_id);

        // 这里应该通过某种方式通知主程序优雅关闭
        // 为了简化，我们假设主程序会自动检测更新并关闭

        // 等待一段时间让主程序关闭
        sleep(Duration::from_secs(5)).await;

        Ok(())
    }

    /// 重启主应用程序
    async fn restart_main_application(&self, trace_id: Uuid) -> ServiceResult<()> {
        debug!("Restarting main application (trace_id: {})", trace_id);

        let current_exe = std::env::current_exe()
            .map_err(|e| ServiceError::Update(format!("Failed to get current executable path: {}", e)))?;

        let parent_dir = current_exe.parent()
            .ok_or_else(|| ServiceError::Update("Failed to get parent directory".to_string()))?;

        let main_exe = parent_dir.join("echowave.exe"); // 假设的主程序名

        if main_exe.exists() {
            Command::new(&main_exe)
                .spawn()
                .map_err(|e| ServiceError::Update(format!("Failed to restart main application: {}", e)))?;

            info!("Main application restarted (trace_id: {})", trace_id);
        }

        Ok(())
    }

    /// 清理临时文件
    async fn cleanup_temp_files(&self, trace_id: Uuid) -> ServiceResult<()> {
        debug!("Cleaning up temporary files (trace_id: {})", trace_id);

        if self.temp_dir.exists() {
            fs::remove_dir_all(&self.temp_dir)
                .map_err(|e| ServiceError::Update(format!("Failed to cleanup temp directory: {}", e)))?;
        }

        Ok(())
    }
}

impl Default for UpdateManager {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| {
            // 如果创建失败，使用默认路径
            Self {
                temp_dir: PathBuf::from("temp"),
                backup_dir: PathBuf::from("backup"),
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_update_manager_creation() {
        let manager = UpdateManager::new();
        assert!(manager.is_ok());
    }

    #[tokio::test]
    async fn test_validate_update_file() {
        let temp_dir = TempDir::new().unwrap();
        let test_file = temp_dir.path().join("test.exe");
        fs::write(&test_file, b"test content").unwrap();

        let manager = UpdateManager::new().unwrap();
        let trace_id = Uuid::new_v4();
        
        let result = manager.validate_update_file(trace_id, &test_file).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_validate_empty_file() {
        let temp_dir = TempDir::new().unwrap();
        let test_file = temp_dir.path().join("empty.exe");
        fs::write(&test_file, b"").unwrap();

        let manager = UpdateManager::new().unwrap();
        let trace_id = Uuid::new_v4();
        
        let result = manager.validate_update_file(trace_id, &test_file).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("empty"));
    }

    #[tokio::test]
    async fn test_validate_unsupported_format() {
        let temp_dir = TempDir::new().unwrap();
        let test_file = temp_dir.path().join("test.txt");
        fs::write(&test_file, b"test content").unwrap();

        let manager = UpdateManager::new().unwrap();
        let trace_id = Uuid::new_v4();
        
        let result = manager.validate_update_file(trace_id, &test_file).await;
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("Unsupported"));
    }
}
