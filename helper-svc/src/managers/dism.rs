use std::collections::HashMap;

use std::process::Command;
use tracing::{debug, error, info};
use uuid::Uuid;

use crate::error::{ServiceError, ServiceResult};
use protocol::events::FeatureStatus;

/// Windows 功能管理器
#[derive(Clone)]
pub struct DismManager {
    known_features: HashMap<String, &'static str>,
}

impl DismManager {
    /// 创建新的 DISM 管理器
    pub fn new() -> Self {
        let mut known_features = HashMap::new();

        // 添加常用的 Windows 功能
        known_features.insert("WSL".to_string(), "Microsoft-Windows-Subsystem-Linux");
        known_features.insert("WSL2".to_string(), "VirtualMachinePlatform");
        known_features.insert("Hyper-V".to_string(), "Microsoft-Hyper-V-All");
        known_features.insert("Containers".to_string(), "Containers");
        known_features.insert("HypervisorPlatform".to_string(), "HypervisorPlatform");
        known_features.insert(
            "VirtualMachinePlatform".to_string(),
            "VirtualMachinePlatform",
        );

        Self { known_features }
    }

    /// 检查 Windows 功能状态
    pub async fn check_feature_status(
        &self,
        trace_id: Uuid,
        feature_name: &str,
    ) -> ServiceResult<(FeatureStatus, bool)> {
        debug!(
            "Checking Windows feature status: {} (trace_id: {})",
            feature_name, trace_id
        );

        let dism_feature_name = self.resolve_feature_name(feature_name)?;

        // 使用 DISM 命令检查功能状态
        let output = Command::new("dism")
            .args(&[
                "/english",
                "/online",
                "/get-featureinfo",
                &format!("/featurename:{}", dism_feature_name),
            ])
            .output()
            .map_err(|e| ServiceError::Dism(format!("Failed to execute DISM command: {}", e)))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Dism(format!(
                "DISM command failed: {}",
                stderr
            )));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let (status, restart_required) = self.parse_feature_info(&stdout)?;

        info!(
            "Feature {} status: {:?}, restart required: {} (trace_id: {})",
            feature_name, status, restart_required, trace_id
        );

        Ok((status, restart_required))
    }

    /// 启用 Windows 功能
    pub async fn enable_feature(
        &self,
        trace_id: Uuid,
        feature_name: &str,
    ) -> ServiceResult<(FeatureStatus, String, bool)> {
        info!(
            "Enabling Windows feature: {} (trace_id: {})",
            feature_name, trace_id
        );

        let dism_feature_name = self.resolve_feature_name(feature_name)?;

        // 首先检查当前状态
        let (current_status, _) = self.check_feature_status(trace_id, feature_name).await?;

        if current_status == FeatureStatus::Enabled {
            return Ok((
                FeatureStatus::Enabled,
                "Feature is already enabled".to_string(),
                false,
            ));
        }

        // 使用 DISM 命令启用功能
        let output = Command::new("dism")
            .args(&[
                "/english",
                "/online",
                "/enable-feature",
                &format!("/featurename:{}", dism_feature_name),
                "/all",
                "/norestart",
            ])
            .output()
            .map_err(|e| {
                ServiceError::Dism(format!("Failed to execute DISM enable command: {}", e))
            })?;

        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        // 对于需要重启的项，dism 会返回 3010 状态码而不是 0
        let restart_required = output.status.code().unwrap_or(0) == 3010;
        if !output.status.success() && !restart_required {
            error!(
                "DISM enable command failed: {} (trace_id: {})",
                stderr, trace_id
            );
            return Err(ServiceError::Dism(format!(
                "Failed to enable feature: {}",
                stderr
            )));
        }

        // 解析结果
        let (final_status, detail) = self.parse_enable_result(&stdout)?;

        info!(
            "Feature {} enable result: {:?} - {} (trace_id: {})",
            feature_name, final_status, detail, trace_id
        );

        Ok((final_status, detail, restart_required))
    }

    /// 禁用 Windows 功能
    pub async fn disable_feature(
        &self,
        trace_id: Uuid,
        feature_name: &str,
    ) -> ServiceResult<(FeatureStatus, String, bool)> {
        info!(
            "Disabling Windows feature: {} (trace_id: {})",
            feature_name, trace_id
        );

        let dism_feature_name = self.resolve_feature_name(feature_name)?;

        // 首先检查当前状态
        let (current_status, _) = self.check_feature_status(trace_id, feature_name).await?;

        if current_status == FeatureStatus::Disabled {
            return Ok((
                FeatureStatus::Disabled,
                "Feature is already disabled".to_string(),
                false,
            ));
        }

        // 使用 DISM 命令禁用功能
        let output = Command::new("dism")
            .args(&[
                "/english",
                "/online",
                "/disable-feature",
                &format!("/featurename:{}", dism_feature_name),
                "/norestart",
            ])
            .output()
            .map_err(|e| {
                ServiceError::Dism(format!("Failed to execute DISM disable command: {}", e))
            })?;

        let stdout = String::from_utf8_lossy(&output.stdout);
        let stderr = String::from_utf8_lossy(&output.stderr);

        // 对于需要重启的项，dism 会返回 3010 状态码而不是 0
        let restart_required = output.status.code().unwrap_or(0) == 3010;
        if !output.status.success() && !restart_required {
            error!(
                "DISM disable command failed: {} (trace_id: {})",
                stderr, trace_id
            );
            return Err(ServiceError::Dism(format!(
                "Failed to disable feature: {}",
                stderr
            )));
        }

        // 解析结果
        let (final_status, detail) = self.parse_disable_result(&stdout)?;

        info!(
            "Feature {} disable result: {:?} - {} (trace_id: {})",
            feature_name, final_status, detail, trace_id
        );

        Ok((final_status, detail, restart_required))
    }

    /// 获取所有可用的 Windows 功能列表
    pub async fn list_available_features(&self, trace_id: Uuid) -> ServiceResult<Vec<String>> {
        debug!(
            "Listing available Windows features (trace_id: {})",
            trace_id
        );

        let output = Command::new("dism")
            .args(&["/english", "/online", "/get-features"])
            .output()
            .map_err(|e| {
                ServiceError::Dism(format!("Failed to execute DISM list command: {}", e))
            })?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(ServiceError::Dism(format!(
                "DISM list command failed: {}",
                stderr
            )));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        let features = self.parse_feature_list(&stdout)?;

        debug!(
            "Found {} available features (trace_id: {})",
            features.len(),
            trace_id
        );
        Ok(features)
    }

    /// 解析功能名称（将友好名称转换为 DISM 功能名称）
    fn resolve_feature_name(&self, feature_name: &str) -> ServiceResult<String> {
        if let Some(&dism_name) = self.known_features.get(feature_name) {
            Ok(dism_name.to_string())
        } else {
            // 如果不是已知的友好名称，假设它就是 DISM 功能名称
            Ok(feature_name.to_string())
        }
    }

    /// 解析功能信息输出
    fn parse_feature_info(&self, output: &str) -> ServiceResult<(FeatureStatus, bool)> {
        let mut status = FeatureStatus::Unknown;
        let mut restart_required = false;

        for line in output.lines() {
            let line = line.trim();

            if line.starts_with("State :") || line.starts_with("状态 :") {
                if line.contains("Enabled") || line.contains("已启用") {
                    status = FeatureStatus::Enabled;
                } else if line.contains("Disabled") || line.contains("已禁用") {
                    status = FeatureStatus::Disabled;
                }
            }

            if line.contains("restart") || line.contains("重新启动") || line.contains("重启")
            {
                restart_required = true;
            }
        }

        Ok((status, restart_required))
    }

    /// 解析启用功能的结果
    fn parse_enable_result(&self, output: &str) -> ServiceResult<(FeatureStatus, String)> {
        if output.contains("completed successfully") || output.contains("操作成功完成") {
            if output.contains("restart") || output.contains("重新启动") {
                Ok((
                    FeatureStatus::Enabled,
                    "Feature enabled successfully. Restart required.".to_string(),
                ))
            } else {
                Ok((
                    FeatureStatus::Enabled,
                    "Feature enabled successfully.".to_string(),
                ))
            }
        } else if output.contains("already enabled") || output.contains("已启用") {
            Ok((
                FeatureStatus::Enabled,
                "Feature is already enabled.".to_string(),
            ))
        } else {
            Ok((
                FeatureStatus::Unknown,
                "Enable operation completed with unknown status.".to_string(),
            ))
        }
    }

    /// 解析禁用功能的结果
    fn parse_disable_result(&self, output: &str) -> ServiceResult<(FeatureStatus, String)> {
        if output.contains("completed successfully") || output.contains("操作成功完成") {
            if output.contains("restart") || output.contains("重新启动") {
                Ok((
                    FeatureStatus::Disabled,
                    "Feature disabled successfully. Restart required.".to_string(),
                ))
            } else {
                Ok((
                    FeatureStatus::Disabled,
                    "Feature disabled successfully.".to_string(),
                ))
            }
        } else if output.contains("already disabled") || output.contains("已禁用") {
            Ok((
                FeatureStatus::Disabled,
                "Feature is already disabled.".to_string(),
            ))
        } else {
            Ok((
                FeatureStatus::Unknown,
                "Disable operation completed with unknown status.".to_string(),
            ))
        }
    }

    /// 解析功能列表
    fn parse_feature_list(&self, output: &str) -> ServiceResult<Vec<String>> {
        let mut features = Vec::new();

        for line in output.lines() {
            let line = line.trim();
            if line.starts_with("Feature Name :") || line.starts_with("功能名称 :") {
                if let Some(name) = line.split(':').nth(1) {
                    features.push(name.trim().to_string());
                }
            }
        }

        Ok(features)
    }
}

impl Default for DismManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dism_manager_creation() {
        let manager = DismManager::new();
        assert!(manager.known_features.contains_key("WSL"));
        assert!(manager.known_features.contains_key("Hyper-V"));
    }

    #[test]
    fn test_resolve_feature_name() {
        let manager = DismManager::new();

        // 测试已知功能名称
        assert_eq!(
            manager.resolve_feature_name("WSL").unwrap(),
            "Microsoft-Windows-Subsystem-Linux"
        );

        // 测试未知功能名称（应该原样返回）
        assert_eq!(
            manager.resolve_feature_name("CustomFeature").unwrap(),
            "CustomFeature"
        );
    }

    #[test]
    fn test_parse_feature_info() {
        let manager = DismManager::new();

        let enabled_output = "Feature Name : Microsoft-Windows-Subsystem-Linux\nState : Enabled\n";
        let (status, _) = manager.parse_feature_info(enabled_output).unwrap();
        assert_eq!(status, FeatureStatus::Enabled);

        let disabled_output =
            "Feature Name : Microsoft-Windows-Subsystem-Linux\nState : Disabled\n";
        let (status, _) = manager.parse_feature_info(disabled_output).unwrap();
        assert_eq!(status, FeatureStatus::Disabled);
    }

    #[test]
    fn test_parse_enable_result() {
        let manager = DismManager::new();

        let success_output = "The operation completed successfully.";
        let (status, detail) = manager.parse_enable_result(success_output).unwrap();
        assert_eq!(status, FeatureStatus::Enabled);
        assert!(detail.contains("successfully"));

        let restart_output = "The operation completed successfully. A restart is required.";
        let (status, detail) = manager.parse_enable_result(restart_output).unwrap();
        assert_eq!(status, FeatureStatus::Enabled);
        assert!(detail.contains("Restart required"));
    }
}
