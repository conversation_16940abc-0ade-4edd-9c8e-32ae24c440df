use std::process::Command;
use windows::Win32::System::SystemInformation::{
    GetSystemInfo, SYSTEM_INFO, PROCESSOR_ARCHITECTURE_AMD64, PROCESSOR_ARCHITECTURE_INTEL,
};



use tracing::{debug, info};
use uuid::Uuid;

use protocol::events::VirtualizationDetails;
use crate::error::{ServiceError, ServiceResult};

/// 虚拟化检查器
#[derive(Clone)]
pub struct VirtualizationChecker;

impl VirtualizationChecker {
    /// 创建新的虚拟化检查器
    pub fn new() -> Self {
        Self
    }

    /// 检查系统虚拟化支持
    pub async fn check_virtualization_support(&self, trace_id: Uuid) -> ServiceResult<(bool, bool, VirtualizationDetails)> {
        info!("Checking virtualization support (trace_id: {})", trace_id);

        let details = self.collect_virtualization_details(trace_id).await?;
        let supported = self.is_virtualization_supported(&details);
        let enabled = self.is_virtualization_enabled(&details);

        info!("Virtualization check result: supported={}, enabled={} (trace_id: {})", 
              supported, enabled, trace_id);

        Ok((supported, enabled, details))
    }

    /// 收集虚拟化详细信息
    async fn collect_virtualization_details(&self, trace_id: Uuid) -> ServiceResult<VirtualizationDetails> {
        debug!("Collecting virtualization details (trace_id: {})", trace_id);

        let hyper_v_available = self.check_hyper_v_availability().await?;
        let wsl_available = self.check_wsl_availability().await?;
        let virtualization_enabled_in_firmware = self.check_firmware_virtualization().await?;
        let data_execution_prevention_available = self.check_dep_support().await?;
        let vm_monitor_mode_extensions = self.check_vmx_support().await?;
        let second_level_address_translation = self.check_slat_support().await?;

        Ok(VirtualizationDetails {
            hyper_v_available,
            wsl_available,
            virtualization_enabled_in_firmware,
            data_execution_prevention_available,
            vm_monitor_mode_extensions,
            second_level_address_translation,
        })
    }

    /// 检查 Hyper-V 可用性
    async fn check_hyper_v_availability(&self) -> ServiceResult<bool> {
        debug!("Checking Hyper-V availability");

        // 使用 systeminfo 命令检查 Hyper-V 要求
        let output = Command::new("systeminfo")
            .output()
            .map_err(|e| ServiceError::Virtualization(format!("Failed to execute systeminfo: {}", e)))?;

        if !output.status.success() {
            return Ok(false);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        
        // 检查 Hyper-V 相关信息
        let hyper_v_requirements = [
            "VM Monitor Mode Extensions",
            "Virtualization Enabled In Firmware",
            "Second Level Address Translation",
            "Data Execution Prevention Available",
        ];

        let mut requirements_met = 0;
        for requirement in &hyper_v_requirements {
            if stdout.contains(requirement) && stdout.contains("Yes") {
                requirements_met += 1;
            }
        }

        Ok(requirements_met >= 3) // 至少满足3个要求
    }

    /// 检查 WSL 可用性
    async fn check_wsl_availability(&self) -> ServiceResult<bool> {
        debug!("Checking WSL availability");

        // 检查系统架构
        let is_64bit = self.is_64bit_system()?;
        if !is_64bit {
            return Ok(false);
        }

        // 检查 Windows 版本（WSL 需要 Windows 10 版本 1903 或更高）
        let windows_version = self.get_windows_version()?;
        Ok(windows_version >= 1903)
    }

    /// 检查固件中的虚拟化支持
    async fn check_firmware_virtualization(&self) -> ServiceResult<bool> {
        debug!("Checking firmware virtualization support");

        // 使用 wmic 命令检查 CPU 虚拟化支持
        let output = Command::new("wmic")
            .args(&["cpu", "get", "VirtualizationFirmwareEnabled", "/value"])
            .output()
            .map_err(|e| ServiceError::Virtualization(format!("Failed to execute wmic: {}", e)))?;

        if !output.status.success() {
            // 如果 wmic 失败，尝试其他方法
            return self.check_virtualization_via_systeminfo().await;
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        Ok(stdout.contains("VirtualizationFirmwareEnabled=TRUE"))
    }

    /// 通过 systeminfo 检查虚拟化支持
    async fn check_virtualization_via_systeminfo(&self) -> ServiceResult<bool> {
        let output = Command::new("systeminfo")
            .output()
            .map_err(|e| ServiceError::Virtualization(format!("Failed to execute systeminfo: {}", e)))?;

        if !output.status.success() {
            return Ok(false);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        Ok(stdout.contains("Virtualization Enabled In Firmware: Yes"))
    }

    /// 检查数据执行保护支持
    async fn check_dep_support(&self) -> ServiceResult<bool> {
        debug!("Checking DEP support");

        let output = Command::new("wmic")
            .args(&["OS", "get", "DataExecutionPrevention_SupportPolicy", "/value"])
            .output()
            .map_err(|e| ServiceError::Virtualization(format!("Failed to execute wmic: {}", e)))?;

        if !output.status.success() {
            return Ok(false);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        // DEP 支持策略：0=不支持，1=仅系统，2=选择加入，3=选择退出
        Ok(stdout.contains("DataExecutionPrevention_SupportPolicy=2") || 
           stdout.contains("DataExecutionPrevention_SupportPolicy=3"))
    }

    /// 检查 VMX 支持（Intel）或 SVM 支持（AMD）
    async fn check_vmx_support(&self) -> ServiceResult<bool> {
        debug!("Checking VMX/SVM support");

        // 使用 systeminfo 检查 VM Monitor Mode Extensions
        let output = Command::new("systeminfo")
            .output()
            .map_err(|e| ServiceError::Virtualization(format!("Failed to execute systeminfo: {}", e)))?;

        if !output.status.success() {
            return Ok(false);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        Ok(stdout.contains("VM Monitor Mode Extensions: Yes"))
    }

    /// 检查二级地址转换支持
    async fn check_slat_support(&self) -> ServiceResult<bool> {
        debug!("Checking SLAT support");

        let output = Command::new("systeminfo")
            .output()
            .map_err(|e| ServiceError::Virtualization(format!("Failed to execute systeminfo: {}", e)))?;

        if !output.status.success() {
            return Ok(false);
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        Ok(stdout.contains("Second Level Address Translation: Yes"))
    }

    /// 检查系统是否为64位
    fn is_64bit_system(&self) -> ServiceResult<bool> {
        unsafe {
            let mut system_info: SYSTEM_INFO = std::mem::zeroed();
            GetSystemInfo(&mut system_info);

            Ok(matches!(
                system_info.Anonymous.Anonymous.wProcessorArchitecture,
                PROCESSOR_ARCHITECTURE_AMD64 | PROCESSOR_ARCHITECTURE_INTEL
            ))
        }
    }

    /// 获取 Windows 版本号
    fn get_windows_version(&self) -> ServiceResult<u32> {
        // 简化实现，使用命令行方式获取版本
        let output = std::process::Command::new("cmd")
            .args(&["/c", "ver"])
            .output()
            .map_err(|e| ServiceError::System(format!("Failed to get Windows version: {}", e)))?;

        let version_str = String::from_utf8_lossy(&output.stdout);

        // 尝试从输出中提取版本号
        if version_str.contains("Windows 10") {
            Ok(1903) // 假设是支持WSL的版本
        } else if version_str.contains("Windows 11") {
            Ok(2000) // Windows 11
        } else {
            Ok(0) // 未知版本
        }
    }

    /// 判断虚拟化是否被支持
    fn is_virtualization_supported(&self, details: &VirtualizationDetails) -> bool {
        details.vm_monitor_mode_extensions && 
        details.second_level_address_translation &&
        details.data_execution_prevention_available
    }

    /// 判断虚拟化是否已启用
    fn is_virtualization_enabled(&self, details: &VirtualizationDetails) -> bool {
        details.virtualization_enabled_in_firmware &&
        (details.hyper_v_available || details.wsl_available)
    }
}

impl Default for VirtualizationChecker {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_virtualization_checker_creation() {
        let checker = VirtualizationChecker::new();
        // 基本创建测试
        assert_eq!(std::mem::size_of_val(&checker), 0); // ZST
    }

    #[test]
    fn test_is_64bit_system() {
        let checker = VirtualizationChecker::new();
        let result = checker.is_64bit_system();
        assert!(result.is_ok());
        // 在现代系统上应该是64位
        #[cfg(target_arch = "x86_64")]
        assert!(result.unwrap());
    }

    #[test]
    fn test_virtualization_support_logic() {
        let checker = VirtualizationChecker::new();
        
        let supported_details = VirtualizationDetails {
            hyper_v_available: true,
            wsl_available: true,
            virtualization_enabled_in_firmware: true,
            data_execution_prevention_available: true,
            vm_monitor_mode_extensions: true,
            second_level_address_translation: true,
        };
        
        assert!(checker.is_virtualization_supported(&supported_details));
        assert!(checker.is_virtualization_enabled(&supported_details));
        
        let unsupported_details = VirtualizationDetails {
            hyper_v_available: false,
            wsl_available: false,
            virtualization_enabled_in_firmware: false,
            data_execution_prevention_available: false,
            vm_monitor_mode_extensions: false,
            second_level_address_translation: false,
        };
        
        assert!(!checker.is_virtualization_supported(&unsupported_details));
        assert!(!checker.is_virtualization_enabled(&unsupported_details));
    }
}
