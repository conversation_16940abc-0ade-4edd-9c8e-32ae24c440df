[package]
name = "helper-svc"
version = "0.1.0"
edition = "2021"
description = "EchoWave Windows Service Module - Handles privileged system operations"

[[bin]]
name = "helper-svc"
path = "src/main.rs"

[[bin]]
name = "test-interactive"
path = "scripts/test_interactive.rs"

[lib]
name = "helper_svc"
path = "src/lib.rs"

[dependencies]
# 工作区依赖
protocol = { path = "../crates/protocol" }
tokio = { workspace = true, features = ["full"] }
serde = { workspace = true }
serde_json = { workspace = true }
uuid = { workspace = true }
tracing = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
futures = "0.3"

# Windows 特定依赖
windows-service = "0.6"
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_Services",
    "Win32_System_IO",
    "Win32_Storage_FileSystem",
    "Win32_Security",
    "Win32_System_Pipes",
    "Win32_System_Threading",
    "Win32_System_SystemInformation",
    "Win32_System_Registry",
    "Win32_System_Diagnostics_Debug",
    "Win32_System_WindowsProgramming",
    "Win32_System_Shutdown",
] }
chrono = { workspace = true }

# 将 tracing-subscriber 添加到主依赖中，用于日志初始化
tracing-subscriber = { workspace = true, features = ["env-filter"] }

[dev-dependencies]
tempfile = "3.8"
