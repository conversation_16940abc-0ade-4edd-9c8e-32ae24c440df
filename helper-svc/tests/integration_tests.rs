use std::fs;
use std::sync::Arc;
use std::time::Duration;
use tempfile::TempDir;
use uuid::Uuid;

use helper_svc::auth::AuthManager;
use helper_svc::error::ServiceError;
use helper_svc::handlers::RequestHandler;
use helper_svc::managers::{DismManager, UpdateManager, VirtualizationChecker};
use protocol::events::{FeatureStatus, HelperSvcEvent, HelperSvcResponse, SystemCheckType};

/// 集成测试：认证管理器
#[tokio::test]
async fn test_auth_manager_integration() {
    let auth_manager = AuthManager::new(Duration::from_secs(60));
    let process_id = unsafe { windows::Win32::System::Threading::GetCurrentProcessId() };

    // 生成令牌
    let token = auth_manager.generate_token(process_id).unwrap();
    assert!(!token.is_empty());

    // 验证令牌
    let validated_pid = auth_manager.validate_token(&token).unwrap();
    assert_eq!(validated_pid, process_id);

    // 撤销令牌
    auth_manager.revoke_token(&token).unwrap();

    // 验证已撤销的令牌应该失败
    assert!(auth_manager.validate_token(&token).is_err());
}

/// 集成测试：DISM 管理器
#[tokio::test]
async fn test_dism_manager_integration() {
    let dism_manager = DismManager::new();
    let trace_id = Uuid::new_v4();

    // 测试检查已知功能（这个测试可能在某些系统上失败，取决于系统配置）
    match dism_manager.check_feature_status(trace_id, "WSL").await {
        Ok((status, restart_required)) => {
            // 验证返回的状态是有效的
            assert!(matches!(
                status,
                FeatureStatus::Enabled | FeatureStatus::Disabled | FeatureStatus::Unknown
            ));
            println!(
                "WSL status: {:?}, restart required: {}",
                status, restart_required
            );
        }
        Err(e) => {
            // 在某些系统上可能没有 DISM 或权限不足
            println!("DISM check failed (expected on some systems): {}", e);
        }
    }

    // 测试列出可用功能
    match dism_manager.list_available_features(trace_id).await {
        Ok(features) => {
            println!("Available features count: {}", features.len());
            // 在正常的 Windows 系统上应该有一些功能
            if !features.is_empty() {
                assert!(features.len() > 0);
            }
        }
        Err(e) => {
            println!("List features failed (expected on some systems): {}", e);
        }
    }
}

/// 集成测试：虚拟化检查器
#[tokio::test]
async fn test_virtualization_checker_integration() {
    let checker = VirtualizationChecker::new();
    let trace_id = Uuid::new_v4();

    // 测试虚拟化支持检查
    match checker.check_virtualization_support(trace_id).await {
        Ok((supported, enabled, details)) => {
            println!(
                "Virtualization - Supported: {}, Enabled: {}",
                supported, enabled
            );
            println!("Details: {:?}", details);

            // 基本验证
            assert!(details.hyper_v_available || !details.hyper_v_available); // 总是true，但确保字段存在
            assert!(details.wsl_available || !details.wsl_available);
        }
        Err(e) => {
            println!("Virtualization check failed: {}", e);
            // 在某些系统上可能失败，这是可以接受的
        }
    }
}

/// 集成测试：更新管理器
#[tokio::test]
async fn test_update_manager_integration() {
    let update_manager = UpdateManager::new().unwrap();
    let trace_id = Uuid::new_v4();

    // 创建临时测试文件
    let temp_dir = TempDir::new().unwrap();
    let test_exe = temp_dir.path().join("test_update.exe");
    fs::write(&test_exe, b"fake exe content").unwrap();

    // 测试文件验证
    let result = update_manager
        .validate_update_file(trace_id, &test_exe)
        .await;
    assert!(result.is_ok());

    // 测试无效文件
    let invalid_file = temp_dir.path().join("invalid.txt");
    fs::write(&invalid_file, b"invalid content").unwrap();

    let result = update_manager
        .validate_update_file(trace_id, &invalid_file)
        .await;
    assert!(result.is_err());

    // 测试空文件
    let empty_file = temp_dir.path().join("empty.exe");
    fs::write(&empty_file, b"").unwrap();

    let result = update_manager
        .validate_update_file(trace_id, &empty_file)
        .await;
    assert!(result.is_err());
}

/// 集成测试：请求处理器
#[tokio::test]
async fn test_request_handler_integration() {
    let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
    let handler = RequestHandler::new(auth_manager).await.unwrap();

    let trace_id = Uuid::new_v4();

    // 测试日志转发
    let log_entry = protocol::events::StructuredLogEntry {
        source: "test".to_string(),
        level: protocol::events::LogLevel::Info,
        message: "Integration test log message".to_string(),
        timestamp: chrono::Utc::now().timestamp_millis() as u64,
        trace_id: Some(trace_id),
        module: "test_module".to_string(),
        target: Some("test".to_string()),
        file: Some("test.rs".to_string()),
        line: Some(42),
    };

    let event = HelperSvcEvent::LogForward(log_entry);
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Success {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
        }
        _ => panic!("Expected Success response for log forward"),
    }

    // 测试虚拟化检查
    let event = HelperSvcEvent::CheckVirtualization { trace_id };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::VirtualizationInfo {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
        }
        HelperSvcResponse::Error {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
            // 错误也是可以接受的，取决于系统配置
        }
        _ => panic!("Expected VirtualizationInfo or Error response"),
    }

    // 测试系统检查
    let event = HelperSvcEvent::SystemCheck {
        trace_id,
        check_type: SystemCheckType::All,
    };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Success {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
        }
        HelperSvcResponse::Error {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
        }
        _ => panic!("Expected Success or Error response for system check"),
    }
}

/// 集成测试：输入验证
#[tokio::test]
async fn test_input_validation_integration() {
    let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
    let handler = RequestHandler::new(auth_manager).await.unwrap();

    let trace_id = Uuid::new_v4();

    // 测试空更新路径
    let event = HelperSvcEvent::InstallUpdate {
        trace_id,
        update_path: String::new(),
    };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Error { code, message, .. } => {
            assert_eq!(
                code,
                ServiceError::InvalidInput("test".to_string()).error_code()
            );
            assert!(message.contains("empty"));
        }
        _ => panic!("Expected Error response for empty update path"),
    }

    // 测试危险路径
    let event = HelperSvcEvent::InstallUpdate {
        trace_id,
        update_path: "../dangerous/path".to_string(),
    };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Error { code, message, .. } => {
            assert_eq!(
                code,
                ServiceError::InvalidInput("test".to_string()).error_code()
            );
            assert!(message.contains("invalid"));
        }
        _ => panic!("Expected Error response for dangerous path"),
    }

    // 测试空备份路径
    let event = HelperSvcEvent::RollbackUpdate {
        trace_id,
        backup_path: String::new(),
    };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Error { code, message, .. } => {
            assert_eq!(
                code,
                ServiceError::InvalidInput("test".to_string()).error_code()
            );
            assert!(message.contains("empty"));
        }
        _ => panic!("Expected Error response for empty backup path"),
    }
}

/// 集成测试：错误处理
#[tokio::test]
async fn test_error_handling_integration() {
    let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
    let handler = RequestHandler::new(auth_manager).await.unwrap();

    let trace_id = Uuid::new_v4();

    // 测试不存在的更新文件
    let event = HelperSvcEvent::InstallUpdate {
        trace_id,
        update_path: "/nonexistent/path/update.exe".to_string(),
    };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Error {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
        }
        HelperSvcResponse::UpdateInstallResult { success: false, .. } => {
            // 也可以接受这种响应
        }
        _ => panic!("Expected Error or failed UpdateInstallResult response"),
    }

    // 测试不存在的备份文件
    let event = HelperSvcEvent::RollbackUpdate {
        trace_id,
        backup_path: "/nonexistent/backup.exe".to_string(),
    };
    let response = handler.handle_event(event).await;

    match response {
        HelperSvcResponse::Error {
            trace_id: resp_trace_id,
            ..
        } => {
            assert_eq!(resp_trace_id, trace_id);
        }
        HelperSvcResponse::UpdateInstallResult { success: false, .. } => {
            // 也可以接受这种响应
        }
        _ => panic!("Expected Error or failed UpdateInstallResult response"),
    }
}

/// 性能测试：并发请求处理
#[tokio::test]
async fn test_concurrent_request_handling() {
    let auth_manager = Arc::new(AuthManager::new(Duration::from_secs(3600)));
    let handler = RequestHandler::new(auth_manager).await.unwrap();

    let mut handles = Vec::new();

    // 启动多个并发请求
    for i in 0..10 {
        let handler_clone = handler.clone();
        let handle = tokio::spawn(async move {
            let trace_id = Uuid::new_v4();
            let log_entry = protocol::events::StructuredLogEntry {
                source: format!("test_{}", i),
                level: protocol::events::LogLevel::Info,
                message: format!("Concurrent test message {}", i),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                trace_id: Some(trace_id),
                module: format!("test_module_{}", i),
                target: Some(format!("test_{}", i)),
                file: Some("test.rs".to_string()),
                line: Some(42),
            };

            let event = HelperSvcEvent::LogForward(log_entry);
            handler_clone.handle_event(event).await
        });
        handles.push(handle);
    }

    // 等待所有请求完成
    for handle in handles {
        let response = handle.await.unwrap();
        match response {
            HelperSvcResponse::Success { .. } => {
                // 成功
            }
            _ => panic!("Expected Success response for concurrent log forward"),
        }
    }
}
