@echo off
echo ========================================
echo EchoWave Windows Service 交互式测试工具
echo ========================================
echo.

REM 检查是否在正确的目录
if not exist "Cargo.toml" (
    echo 错误: 请在 helper-svc 目录下运行此脚本
    pause
    exit /b 1
)

REM 检查是否有管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 检测到管理员权限
) else (
    echo ⚠️  警告: 未检测到管理员权限
    echo    某些功能（如启用Windows功能）可能无法正常工作
    echo    建议以管理员身份运行此脚本
)

echo.
echo 🔨 正在构建测试工具...
cargo build --bin test-interactive --release

if %errorLevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo ✅ 构建成功
echo.
echo 🚀 启动交互式测试工具...
echo.

REM 设置日志级别
set RUST_LOG=info

REM 运行测试工具 (工作区构建，文件在上级目录)
..\target\release\test-interactive.exe

echo.
echo 测试完成，按任意键退出...
pause >nul
