# EchoWave Windows Service 交互式测试工具启动脚本
param(
    [switch]$SkipBuild,
    [switch]$Debug,
    [string]$LogLevel = "info"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "EchoWave Windows Service 交互式测试工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查是否在正确的目录
if (-not (Test-Path "Cargo.toml")) {
    Write-Host "❌ 错误: 请在 helper-svc 目录下运行此脚本" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($isAdmin) {
    Write-Host "✅ 检测到管理员权限" -ForegroundColor Green
} else {
    Write-Host "⚠️  警告: 未检测到管理员权限" -ForegroundColor Yellow
    Write-Host "   某些功能（如启用Windows功能）可能无法正常工作" -ForegroundColor Yellow
    Write-Host "   建议以管理员身份运行PowerShell后执行此脚本" -ForegroundColor Yellow
    Write-Host ""
    
    $continue = Read-Host "是否继续? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        Write-Host "操作已取消" -ForegroundColor Yellow
        exit 0
    }
}

Write-Host ""

# 构建项目
if (-not $SkipBuild) {
    Write-Host "🔨 正在构建测试工具..." -ForegroundColor Blue
    
    $buildMode = if ($Debug) { "debug" } else { "release" }
    $buildArgs = @("build", "--bin", "test-interactive")
    
    if (-not $Debug) {
        $buildArgs += "--release"
    }
    
    $buildResult = & cargo @buildArgs
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 构建失败" -ForegroundColor Red
        Read-Host "按回车键退出"
        exit 1
    }
    
    Write-Host "✅ 构建成功" -ForegroundColor Green
} else {
    Write-Host "⏭️  跳过构建步骤" -ForegroundColor Yellow
}

Write-Host ""

# 设置环境变量
$env:RUST_LOG = $LogLevel

# 确定可执行文件路径 (工作区构建，文件在上级目录)
$exePath = if ($Debug) {
    "..\target\debug\test-interactive.exe"
} else {
    "..\target\release\test-interactive.exe"
}

# 检查可执行文件是否存在
if (-not (Test-Path $exePath)) {
    Write-Host "❌ 找不到可执行文件: $exePath" -ForegroundColor Red
    Write-Host "   请先运行构建或使用 -Debug 参数" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "🚀 启动交互式测试工具..." -ForegroundColor Blue
Write-Host "   可执行文件: $exePath" -ForegroundColor Gray
Write-Host "   日志级别: $LogLevel" -ForegroundColor Gray
Write-Host ""

# 运行测试工具
try {
    & $exePath
} catch {
    Write-Host "❌ 运行测试工具时出错: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试完成" -ForegroundColor Green
Read-Host "按回车键退出"
