use std::io::{self, Write};
use std::time::Duration;
use tokio;
use uuid::Uuid;

#[cfg(target_os = "windows")]
use helper_svc::{<PERSON><PERSON><PERSON>ana<PERSON>, DismManager, RequestHandler, UpdateManager, VirtualizationChecker};
#[cfg(target_os = "windows")]
use protocol::events::{FeatureStatus, HelperSvcEvent, HelperSvcResponse};

/// 交互式测试脚本
///
/// 这个脚本提供一个控制台界面来测试Windows service模块的各种功能
/// 不需要修改原有代码，直接调用现有的API进行真实测试
#[cfg(target_os = "windows")]
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志系统
    init_logging();

    println!("🚀 EchoWave Windows Service 交互式测试工具");
    println!("================================================");
    println!("这个工具可以直接测试Windows service模块的各种功能");
    println!("所有操作都是真实的系统调用，请谨慎使用需要管理员权限的功能");
    println!();

    // 初始化组件
    let auth_manager = std::sync::Arc::new(AuthManager::new(Duration::from_secs(3600))); // 1小时TTL
    let request_handler = RequestHandler::new(auth_manager.clone()).await?;

    // 主循环
    loop {
        show_menu();

        let choice = get_user_input("请选择操作 (输入数字): ")?;

        match choice.trim() {
            "1" => test_virtualization_check(&request_handler).await?,
            "2" => test_windows_feature_check(&request_handler).await?,
            "3" => test_windows_feature_enable(&request_handler).await?,
            "4" => test_dism_manager_direct().await?,
            "5" => test_virtualization_checker_direct().await?,
            "6" => test_update_manager_direct().await?,
            "7" => test_all_supported_features(&request_handler).await?,
            "8" => test_system_info_collection().await?,
            "0" => {
                println!("👋 退出测试工具");
                break;
            }
            _ => {
                println!("❌ 无效选择，请重新输入");
            }
        }

        println!("\n按回车键继续...");
        let _ = io::stdin().read_line(&mut String::new());
    }

    Ok(())
}
#[cfg(target_os = "windows")]
fn show_menu() {
    println!("\n📋 测试菜单:");
    println!("1. 🔍 检查虚拟化支持 (CheckVirtualization)");
    println!("2. 📦 检查Windows功能状态 (CheckWindowsFeature)");
    println!("3. ⚡ 启用Windows功能 (EnableWindowsFeature)");
    println!("4. 🛠️  直接测试DISM管理器");
    println!("5. 💻 直接测试虚拟化检查器");
    println!("6. 🔄 直接测试更新管理器");
    println!("7. 📊 检查所有支持的功能");
    println!("8. 🖥️  收集系统信息");
    println!("0. 🚪 退出");
    println!();
}
#[cfg(target_os = "windows")]
fn get_user_input(prompt: &str) -> io::Result<String> {
    print!("{}", prompt);
    io::stdout().flush()?;
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    Ok(input)
}
#[cfg(target_os = "windows")]
async fn test_virtualization_check(
    handler: &RequestHandler,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔍 测试虚拟化检查...");

    let trace_id = Uuid::new_v4();
    let event = HelperSvcEvent::CheckVirtualization { trace_id };

    println!("📤 发送事件: {:?}", event);

    let response = handler.handle_event(event).await;

    println!("📥 收到响应:");
    match &response {
        HelperSvcResponse::VirtualizationInfo {
            supported,
            enabled,
            details,
            ..
        } => {
            println!("  ✅ 虚拟化支持: {}", if *supported { "是" } else { "否" });
            println!("  ⚡ 虚拟化启用: {}", if *enabled { "是" } else { "否" });
            println!("  📋 详细信息:");
            println!("    - Hyper-V 可用: {}", details.hyper_v_available);
            println!("    - WSL 可用: {}", details.wsl_available);
            println!(
                "    - 固件虚拟化启用: {}",
                details.virtualization_enabled_in_firmware
            );
            println!(
                "    - DEP 可用: {}",
                details.data_execution_prevention_available
            );
            println!("    - VMX 扩展: {}", details.vm_monitor_mode_extensions);
            println!(
                "    - SLAT 支持: {}",
                details.second_level_address_translation
            );
        }
        HelperSvcResponse::Error { code, message, .. } => {
            println!("  ❌ 错误 (代码: {}): {}", code, message);
        }
        _ => {
            println!("  ⚠️  意外的响应类型: {:?}", response);
        }
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_windows_feature_check(
    handler: &RequestHandler,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📦 测试Windows功能状态检查...");

    let feature_name =
        get_user_input("请输入功能名称 (如: WSL, Hyper-V, VirtualMachinePlatform): ")?;
    let feature_name = feature_name.trim().to_string();

    let trace_id = Uuid::new_v4();
    let event = HelperSvcEvent::CheckWindowsFeature {
        trace_id,
        feature_name: feature_name.clone(),
    };

    println!("📤 发送事件: {:?}", event);

    let response = handler.handle_event(event).await;

    println!("📥 收到响应:");
    match &response {
        HelperSvcResponse::WindowsFeatureStatus {
            feature_name,
            status,
            restart_required,
            ..
        } => {
            let status_text = match status {
                FeatureStatus::Enabled => "✅ 已启用",
                FeatureStatus::Disabled => "❌ 已禁用",
                FeatureStatus::Unknown => "❓ 未知",
                FeatureStatus::NotAvailable => "🚫 不可用",
            };
            println!("  功能: {}", feature_name);
            println!("  状态: {}", status_text);
            println!(
                "  需要重启: {}",
                if *restart_required { "是" } else { "否" }
            );
        }
        HelperSvcResponse::Error { code, message, .. } => {
            println!("  ❌ 错误 (代码: {}): {}", code, message);
        }
        _ => {
            println!("  ⚠️  意外的响应类型: {:?}", response);
        }
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_windows_feature_enable(
    handler: &RequestHandler,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n⚡ 测试Windows功能启用...");
    println!("⚠️  警告: 这个操作需要管理员权限，可能会修改系统设置！");

    let confirm = get_user_input("确认要继续吗? (y/N): ")?;
    if confirm.trim().to_lowercase() != "y" {
        println!("操作已取消");
        return Ok(());
    }

    let feature_name = get_user_input("请输入要启用的功能名称 (如: WSL, Hyper-V): ")?;
    let feature_name = feature_name.trim().to_string();

    let trace_id = Uuid::new_v4();
    let event = HelperSvcEvent::EnableWindowsFeature {
        trace_id,
        feature_name: feature_name.clone(),
    };

    println!("📤 发送事件: {:?}", event);

    let response = handler.handle_event(event).await;

    println!("📥 收到响应:");
    match &response {
        HelperSvcResponse::WindowsFeatureStatus {
            feature_name,
            status,
            restart_required,
            ..
        } => {
            let status_text = match status {
                FeatureStatus::Enabled => "✅ 已启用",
                FeatureStatus::Disabled => "❌ 已禁用",
                FeatureStatus::Unknown => "❓ 未知",
                FeatureStatus::NotAvailable => "🚫 不可用",
            };
            println!("  功能: {}", feature_name);
            println!("  状态: {}", status_text);
            println!(
                "  需要重启: {}",
                if *restart_required { "是" } else { "否" }
            );

            if *restart_required {
                println!("  ⚠️  系统需要重启才能完成功能启用");
            }
        }
        HelperSvcResponse::Error { code, message, .. } => {
            println!("  ❌ 错误 (代码: {}): {}", code, message);
        }
        _ => {
            println!("  ⚠️  意外的响应类型: {:?}", response);
        }
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_dism_manager_direct() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🛠️  直接测试DISM管理器...");

    let dism_manager = DismManager::new();
    let trace_id = Uuid::new_v4();

    println!("1. 检查WSL功能状态");
    match dism_manager.check_feature_status(trace_id, "WSL").await {
        Ok((status, restart_required)) => {
            println!("  WSL状态: {:?}, 需要重启: {}", status, restart_required);
        }
        Err(e) => {
            println!("  检查失败: {}", e);
        }
    }

    println!("\n2. 列出可用功能 (前10个)");
    match dism_manager.list_available_features(trace_id).await {
        Ok(features) => {
            println!("  找到 {} 个功能:", features.len());
            for (i, feature) in features.iter().take(10).enumerate() {
                println!("    {}. {}", i + 1, feature);
            }
            if features.len() > 10 {
                println!("    ... 还有 {} 个功能", features.len() - 10);
            }
        }
        Err(e) => {
            println!("  列出功能失败: {}", e);
        }
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_virtualization_checker_direct() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n💻 直接测试虚拟化检查器...");

    let checker = VirtualizationChecker::new();
    let trace_id = Uuid::new_v4();

    println!("正在检查虚拟化支持...");
    match checker.check_virtualization_support(trace_id).await {
        Ok((supported, enabled, details)) => {
            println!("✅ 检查完成:");
            println!("  支持虚拟化: {}", if supported { "是" } else { "否" });
            println!("  虚拟化已启用: {}", if enabled { "是" } else { "否" });
            println!("  详细信息:");
            println!("    - Hyper-V 可用: {}", details.hyper_v_available);
            println!("    - WSL 可用: {}", details.wsl_available);
            println!(
                "    - 固件虚拟化: {}",
                details.virtualization_enabled_in_firmware
            );
            println!(
                "    - DEP 支持: {}",
                details.data_execution_prevention_available
            );
            println!("    - VMX 扩展: {}", details.vm_monitor_mode_extensions);
            println!(
                "    - SLAT 支持: {}",
                details.second_level_address_translation
            );
        }
        Err(e) => {
            println!("❌ 检查失败: {}", e);
        }
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_update_manager_direct() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🔄 直接测试更新管理器...");
    println!("⚠️  注意: 这只是测试更新管理器的创建和基本功能，不会执行实际更新");

    match UpdateManager::new() {
        Ok(update_manager) => {
            println!("✅ 更新管理器创建成功");

            // 测试一个不存在的更新文件（安全测试）
            let trace_id = Uuid::new_v4();
            let fake_update_path = "C:\\nonexistent\\update.exe";

            println!("测试不存在的更新文件: {}", fake_update_path);
            match update_manager
                .install_update(trace_id, fake_update_path)
                .await
            {
                Ok((success, message)) => {
                    println!("  结果: 成功={}, 消息={}", success, message);
                }
                Err(e) => {
                    println!("  预期的错误: {}", e);
                }
            }
        }
        Err(e) => {
            println!("❌ 更新管理器创建失败: {}", e);
        }
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_all_supported_features(
    handler: &RequestHandler,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📊 检查所有支持的功能...");

    let features = vec![
        "WSL",
        "Hyper-V",
        "VirtualMachinePlatform",
        "Containers",
        "HypervisorPlatform",
    ];

    for feature in features {
        let trace_id = Uuid::new_v4();
        let event = HelperSvcEvent::CheckWindowsFeature {
            trace_id,
            feature_name: feature.to_string(),
        };

        print!("检查 {}... ", feature);
        io::stdout().flush()?;

        let response = handler.handle_event(event).await;

        match response {
            HelperSvcResponse::WindowsFeatureStatus {
                status,
                restart_required,
                ..
            } => {
                let status_icon = match status {
                    FeatureStatus::Enabled => "✅",
                    FeatureStatus::Disabled => "❌",
                    FeatureStatus::Unknown => "❓",
                    FeatureStatus::NotAvailable => "🚫",
                };
                let restart_info = if restart_required { " (需重启)" } else { "" };
                println!("{} {:?}{}", status_icon, status, restart_info);
            }
            HelperSvcResponse::Error { message, .. } => {
                println!("❌ 错误: {}", message);
            }
            _ => {
                println!("⚠️  意外响应");
            }
        }

        // 添加小延迟避免过快请求
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }

    Ok(())
}
#[cfg(target_os = "windows")]
async fn test_system_info_collection() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🖥️  收集系统信息...");

    // 收集基本系统信息
    println!("1. 操作系统信息:");
    if let Ok(output) = std::process::Command::new("systeminfo")
        .args(&["/fo", "csv"])
        .output()
    {
        let info = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = info.lines().take(5).collect();
        for line in lines {
            if !line.trim().is_empty() {
                println!("  {}", line);
            }
        }
    } else {
        println!("  无法获取系统信息");
    }

    println!("\n2. PowerShell版本:");
    if let Ok(output) = std::process::Command::new("powershell")
        .args(&["-Command", "$PSVersionTable.PSVersion"])
        .output()
    {
        let version = String::from_utf8_lossy(&output.stdout);
        println!("  {}", version.trim());
    } else {
        println!("  无法获取PowerShell版本");
    }

    println!("\n3. .NET Framework版本:");
    if let Ok(output) = std::process::Command::new("reg")
        .args(&[
            "query",
            "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\NET Framework Setup\\NDP\\v4\\Full",
            "/v",
            "Release",
        ])
        .output()
    {
        let reg_info = String::from_utf8_lossy(&output.stdout);
        println!("  {}", reg_info.trim());
    } else {
        println!("  无法获取.NET Framework版本");
    }

    Ok(())
}
#[cfg(target_os = "windows")]
fn init_logging() {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::fmt::layer()
                .with_target(false)
                .with_thread_ids(false)
                .with_level(true),
        )
        .with(tracing_subscriber::EnvFilter::from_default_env())
        .init();
}

#[cfg(not(target_os = "windows"))]
fn main() {
    println!("This binary is only for Windows");
}
