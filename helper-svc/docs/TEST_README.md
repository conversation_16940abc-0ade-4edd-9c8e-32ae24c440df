# EchoWave Windows Service 交互式测试工具

这个测试工具提供了一个交互式控制台界面，用于测试Windows service模块的各种功能，无需修改原有代码。

## 🚀 快速开始

### 方法1: 使用批处理脚本 (推荐新手)
```cmd
# 在 helper-svc 目录下运行
run_test.bat
```

### 方法2: 使用PowerShell脚本 (推荐高级用户)
```powershell
# 在 helper-svc 目录下运行
.\run_test.ps1

# 或者使用参数
.\run_test.ps1 -Debug          # 使用debug构建
.\run_test.ps1 -SkipBuild      # 跳过构建步骤
.\run_test.ps1 -LogLevel debug # 设置日志级别
```

### 方法3: 手动构建和运行
```cmd
# 构建
cargo build --bin test-interactive --release

# 运行
set RUST_LOG=info
target\release\test-interactive.exe
```

## 🔧 功能说明

### 1. 🔍 检查虚拟化支持 (CheckVirtualization)
- 测试系统的虚拟化支持情况
- 检查Hyper-V、WSL、固件虚拟化等状态
- **无需管理员权限**

### 2. 📦 检查Windows功能状态 (CheckWindowsFeature)
- 检查指定Windows功能的启用状态
- 支持的功能: WSL, Hyper-V, VirtualMachinePlatform, Containers等
- **无需管理员权限**

### 3. ⚡ 启用Windows功能 (EnableWindowsFeature)
- 启用指定的Windows功能
- **需要管理员权限**
- ⚠️ **会修改系统设置，请谨慎使用**

### 4. 🛠️ 直接测试DISM管理器
- 直接调用DismManager的API
- 列出可用的Windows功能
- **部分功能需要管理员权限**

### 5. 💻 直接测试虚拟化检查器
- 直接调用VirtualizationChecker的API
- 获取详细的虚拟化支持信息
- **无需管理员权限**

### 6. 🔄 直接测试更新管理器
- 测试UpdateManager的创建和基本功能
- 安全测试，不会执行实际更新
- **无需管理员权限**

### 7. 📊 检查所有支持的功能
- 批量检查多个Windows功能的状态
- 提供快速的系统功能概览
- **无需管理员权限**

### 8. 🖥️ 收集系统信息
- 收集基本的系统信息
- 包括操作系统、PowerShell版本、.NET Framework等
- **无需管理员权限**

## ⚠️ 重要说明

### 权限要求
- **大部分功能无需管理员权限**，可以安全测试
- **启用Windows功能需要管理员权限**，会修改系统设置
- 建议先用普通权限测试查询功能，确认无误后再用管理员权限测试修改功能

### 安全性
- 所有测试都是**真实的系统调用**，不是模拟
- 查询类操作（检查状态、收集信息）是安全的
- 修改类操作（启用功能）会实际修改系统，请谨慎使用

### 系统要求
- Windows 10/11
- Rust 工具链
- 某些功能需要DISM工具（Windows自带）

## 🐛 故障排除

### 构建失败
```cmd
# 清理并重新构建
cargo clean
cargo build --bin test-interactive --release
```

### 权限问题
```cmd
# 以管理员身份运行PowerShell
# 然后执行脚本
.\run_test.ps1
```

### DISM命令失败
- 确保在Windows系统上运行
- 某些功能可能在虚拟机中不可用
- 检查Windows版本是否支持相关功能

## 📝 测试结果示例

### 虚拟化检查结果
```
🔍 测试虚拟化检查...
📤 发送事件: CheckVirtualization { trace_id: ... }
📥 收到响应:
  ✅ 虚拟化支持: 是
  ⚡ 虚拟化启用: 是
  📋 详细信息:
    - Hyper-V 可用: true
    - WSL 可用: true
    - 固件虚拟化启用: true
    - DEP 可用: true
    - VMX 扩展: true
    - SLAT 支持: true
```

### Windows功能状态检查
```
📦 测试Windows功能状态检查...
请输入功能名称 (如: WSL, Hyper-V, VirtualMachinePlatform): WSL
📤 发送事件: CheckWindowsFeature { trace_id: ..., feature_name: "WSL" }
📥 收到响应:
  功能: WSL
  状态: ✅ 已启用
  需要重启: 否
```

## 🔗 相关文件

- `test_interactive.rs` - 主测试脚本
- `run_test.bat` - Windows批处理启动脚本
- `run_test.ps1` - PowerShell启动脚本
- `Cargo.toml` - 包含测试工具的构建配置

## 📚 技术细节

这个测试工具直接使用了Windows service模块的以下组件：
- `RequestHandler` - 处理各种事件请求
- `DismManager` - Windows功能管理
- `VirtualizationChecker` - 虚拟化支持检查
- `UpdateManager` - 更新管理
- `AuthManager` - 认证管理

所有测试都通过`HelperSvcEvent`和`HelperSvcResponse`进行，完全模拟真实的服务调用场景。
