# Windows Service 模块管理员权限测试指南

## 🔐 管理员权限测试说明

某些Windows service模块功能需要管理员权限才能正常工作，特别是涉及DISM操作的功能。本指南说明如何以管理员权限运行测试工具。

## 🚀 以管理员权限运行测试

### 方法1: 使用PowerShell脚本 (推荐)

1. **以管理员身份打开PowerShell**:
   - 按 `Win + X`，选择 "Windows PowerShell (管理员)"
   - 或者搜索 "PowerShell"，右键选择 "以管理员身份运行"

2. **导航到项目目录**:
   ```powershell
   cd "C:\Users\<USER>\Desktop\echowave-windows-client\helper-svc"
   ```

3. **运行测试脚本**:
   ```powershell
   .\run_test.ps1
   ```

### 方法2: 使用批处理脚本

1. **以管理员身份打开命令提示符**:
   - 按 `Win + X`，选择 "命令提示符 (管理员)"
   - 或者搜索 "cmd"，右键选择 "以管理员身份运行"

2. **导航到项目目录**:
   ```cmd
   cd "C:\Users\<USER>\Desktop\echowave-windows-client\helper-svc"
   ```

3. **运行测试脚本**:
   ```cmd
   run_test.bat
   ```

### 方法3: 手动构建和运行

```cmd
# 构建 (如果需要)
cargo build --bin test-interactive --release

# 设置环境变量
set RUST_LOG=info

# 运行测试工具
..\target\release\test-interactive.exe
```

## 🧪 管理员权限测试项目

### 1. 📦 Windows功能状态检查

**测试步骤**:
1. 选择菜单项 `2` (检查Windows功能状态)
2. 输入功能名称，如: `WSL`, `Hyper-V`, `VirtualMachinePlatform`
3. 观察是否能正确返回功能状态

**预期结果**:
```
📦 测试Windows功能状态检查...
请输入功能名称: WSL
📤 发送事件: CheckWindowsFeature { ... }
📥 收到响应:
  功能: WSL
  状态: ✅ 已启用 (或 ❌ 已禁用)
  需要重启: 否
```

### 2. 🛠️ DISM管理器直接测试

**测试步骤**:
1. 选择菜单项 `4` (直接测试DISM管理器)
2. 观察WSL功能状态检查结果
3. 查看可用功能列表

**预期结果**:
```
🛠️  直接测试DISM管理器...
1. 检查WSL功能状态
  WSL状态: Enabled (或 Disabled), 需要重启: false

2. 列出可用功能 (前10个)
  找到 XX 个功能:
    1. Microsoft-Windows-Subsystem-Linux
    2. VirtualMachinePlatform
    3. Microsoft-Hyper-V-All
    ...
```

### 3. 📊 检查所有支持的功能

**测试步骤**:
1. 选择菜单项 `7` (检查所有支持的功能)
2. 观察批量检查结果

**预期结果**:
```
📊 检查所有支持的功能...
检查 WSL... ✅ Enabled
检查 Hyper-V... ❌ Disabled
检查 VirtualMachinePlatform... ✅ Enabled
检查 Containers... ❌ Disabled
检查 HypervisorPlatform... ❌ Disabled
```

### 4. ⚡ Windows功能启用 (谨慎测试)

**⚠️ 警告**: 这个功能会实际修改系统设置！

**测试步骤**:
1. 选择菜单项 `3` (启用Windows功能)
2. 确认警告提示
3. 输入要启用的功能名称 (建议先测试已启用的功能)
4. 观察操作结果

**建议测试功能**:
- 先测试已经启用的功能 (如WSL，如果已启用)
- 避免测试可能影响系统稳定性的功能

## 🔍 故障排除

### DISM命令仍然失败

**可能原因**:
1. **Windows版本不支持**: 某些功能在特定Windows版本中不可用
2. **系统策略限制**: 企业环境可能有组策略限制
3. **系统文件损坏**: DISM工具本身可能有问题

**解决方法**:
```cmd
# 检查DISM工具是否正常
dism /online /get-features

# 检查系统文件完整性
sfc /scannow

# 修复Windows映像
dism /online /cleanup-image /restorehealth
```

### 权限仍然不足

**检查方法**:
```powershell
# 检查当前用户权限
whoami /priv

# 检查是否真的以管理员身份运行
net session
```

### 功能检查返回"Unknown"状态

**可能原因**:
- 功能名称映射不正确
- Windows版本不支持该功能
- 系统配置异常

**调试方法**:
1. 手动运行DISM命令验证:
   ```cmd
   dism /online /get-featureinfo /featurename:Microsoft-Windows-Subsystem-Linux
   ```

2. 检查系统日志中的相关错误

## 📝 测试记录模板

```
测试时间: ____年__月__日
Windows版本: ________________
权限级别: 管理员 ✅

测试结果:
□ Windows功能状态检查: 成功/失败
□ DISM管理器直接测试: 成功/失败  
□ 批量功能检查: 成功/失败
□ 功能启用测试: 成功/失败/跳过

发现的问题:
1. ________________________
2. ________________________

建议改进:
1. ________________________
2. ________________________
```

## 🎯 测试目标

通过管理员权限测试，我们希望验证:

1. **DISM集成正确性**: 能够正确调用Windows DISM API
2. **功能状态检测准确性**: 能够准确识别Windows功能状态
3. **错误处理完整性**: 对各种DISM错误能够正确处理
4. **系统兼容性**: 在不同Windows版本上的兼容性
5. **安全性**: 管理员权限操作的安全性和可控性

完成管理员权限测试后，Windows service模块的功能验证将更加完整和可靠。
