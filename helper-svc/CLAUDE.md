# EchoWave Helper Service 模块

此文件为 Claude Code 提供在处理 EchoWave Helper Service 模块代码时的指导。

## 模块概述

Helper Service 是 EchoWave 客户端四模块架构中的 Windows 服务模块，作为系统级服务运行，专门处理需要管理员权限的操作。它通过命名管道与核心模块通信，提供系统配置和管理功能。

### 架构定位

Helper Service 在整体架构中的职责：

- **权限操作**: 处理需要管理员权限的系统操作
- **系统功能**: Windows 功能的开启/关闭（WSL、Hyper-V 等）
- **更新管理**: 协助完成系统级更新和安装
- **安全隔离**: 作为独立服务运行，通过认证机制保护

### 关键设计决策

1. **Windows 服务架构**: 基于 windows-service crate 实现标准服务
2. **命名管道通信**: 使用 Windows 命名管道进行进程间通信
3. **认证机制**: 基于共享密钥的连接认证
4. **异步架构**: 使用 Tokio 处理并发请求

## 项目结构

```
helper-svc/
├── src/
│   ├── main.rs                 # 服务入口，处理安装/卸载/运行
│   ├── lib.rs                  # 库入口，导出公共接口
│   ├── service.rs              # Windows 服务核心逻辑
│   ├── auth.rs                 # 认证管理器
│   ├── error.rs                # 错误定义
│   ├── pipe_server.rs          # 命名管道服务器（v1）
│   ├── pipe_server_v2.rs       # 命名管道服务器（v2）
│   ├── handlers/               # 请求处理器
│   │   ├── mod.rs
│   │   └── request_handler.rs  # 统一请求处理
│   └── managers/               # 功能管理器
│       ├── mod.rs
│       ├── dism.rs             # DISM 功能管理
│       ├── virtualization.rs   # 虚拟化检查
│       └── update.rs           # 更新管理
├── test_interactive.rs         # 交互式测试工具
├── run_test.bat               # Windows 测试脚本
├── run_test.ps1               # PowerShell 测试脚本
└── tests/                     # 集成测试
```

## 开发指南

### 环境要求

- **平台**: Windows 10/11
- **权限**: 部分功能需要管理员权限
- **Rust**: 1.70+
- **依赖**: windows crate, tokio, protocol crate

### 服务管理命令

```powershell
# 安装服务（需要管理员权限）
.\helper-svc.exe install

# 卸载服务（需要管理员权限）
.\helper-svc.exe uninstall

# 控制台模式运行（用于调试）
.\helper-svc.exe run

# 通过 Windows 服务管理
sc start EchoWaveHelper
sc stop EchoWaveHelper
sc query EchoWaveHelper
```

### 测试工具

项目提供了交互式测试工具，无需启动完整服务即可测试功能：

```powershell
# 使用脚本运行测试工具
.\run_test.ps1

# 或手动运行
cargo run --bin test-interactive
```

## 通信协议

### HelperSvcEvent 定义

```rust
pub enum HelperSvcEvent {
    // 系统检查
    CheckVirtualization { trace_id: Uuid },
    CheckWindowsFeature { trace_id: Uuid, feature: String },

    // 系统操作（需要管理员权限）
    EnableWindowsFeature { trace_id: Uuid, feature: String },
    DisableWindowsFeature { trace_id: Uuid, feature: String },

    // 更新管理
    InstallUpdate { trace_id: Uuid, update_path: String },

    // 服务管理
    GetServiceInfo { trace_id: Uuid },
}
```

### 命名管道协议

- **管道名称**: `\\.\pipe\echowave-helper`
- **消息格式**: MessageFrame 二进制协议
- **认证流程**: 连接后首先发送认证消息
- **超时控制**: 30 秒请求超时

### 错误处理

```rust
pub enum ServiceError {
    Io(std::io::Error),
    Windows(String),
    Auth(String),
    Protocol(String),
    Dism(String),
    Update(String),
    Runtime(String),
}
```

## 功能实现

### 1. 虚拟化检查

使用 Windows API 检查系统虚拟化支持：

- Hyper-V 状态
- WSL 支持
- 固件虚拟化（已废弃）
- VM 平台状态

### 2. Windows 功能管理

通过 DISM API 管理 Windows 可选功能：

- 功能状态查询
- 功能启用/禁用
- 支持的功能列表：
  - Microsoft-Windows-Subsystem-Linux
  - Microsoft-Hyper-V-All
  - VirtualMachinePlatform
  - Containers
  - HypervisorPlatform

### 3. 更新管理

协助系统级更新安装：

- MSI 包安装
- 更新验证
- 进度跟踪

### 4. 认证系统（未实现，需进一步调研）

基于共享密钥的安全认证：

- 密钥文件位置：`%ProgramData%\EchoWave\auth.key`
- 每次连接必须认证
- 认证失败自动断开

## 安全考虑

### 权限管理

1. **服务权限**: 以 LocalSystem 运行，拥有完整系统权限
2. **认证要求**: 所有连接必须通过认证
3. **操作审计**: 记录所有管理员操作

### 通信安全

1. **本地通信**: 仅允许本地进程连接
2. **认证机制**: 防止未授权访问
3. **超时保护**: 防止资源耗尽

### 错误处理

1. **优雅降级**: 功能不可用时返回明确错误
2. **资源清理**: 确保所有资源正确释放
3. **日志记录**: 详细记录错误信息

## 测试策略

### 单元测试

```bash
cargo test
```

测试覆盖：

- 认证逻辑
- 错误处理
- 消息解析

### 集成测试

使用交互式测试工具进行功能测试：

1. 虚拟化检查测试
2. 功能状态查询测试
3. 权限操作测试（需要管理员）

### 手动测试清单

1. **服务生命周期**

   - [ ] 服务安装/卸载
   - [ ] 服务启动/停止
   - [ ] 控制台模式运行

2. **功能测试**

   - [ ] 虚拟化检查
   - [ ] WSL 功能检查
   - [ ] 功能启用测试

3. **错误场景**
   - [ ] 无权限操作
   - [ ] 认证失败
   - [ ] 超时处理

## 日志和调试

### 日志配置

```powershell
# 设置日志级别
$env:RUST_LOG = "debug"

# 运行服务
.\helper-svc.exe run
```

### 日志位置

- 控制台模式：输出到标准输出
- 服务模式：Windows 事件日志

### 调试技巧

1. 使用控制台模式进行开发调试
2. 使用交互式测试工具测试单个功能
3. 检查 Windows 事件查看器中的服务日志

## 与其他模块的关系

- **Core 模块**: 服务的主要调用方，通过命名管道通信
- **Protocol 模块**: 提供消息协议定义
- **Desktop 模块**: 间接关系，UI 操作触发服务调用

## 常见问题

### 1. 服务安装失败

- 确保以管理员权限运行
- 检查是否有同名服务存在
- 查看 Windows 事件日志

### 2. 连接被拒绝

- 检查服务是否运行
- 验证认证密钥是否正确
- 确认命名管道名称

### 3. 功能操作失败

- 确认服务有足够权限
- 检查 Windows 版本兼容性
- 查看详细错误日志

### 4. 测试工具使用

- 某些操作需要管理员权限
- 使用提供的脚本自动处理权限提升
- 查看 TEST_README.md 获取详细说明

## 开发注意事项

1. **权限提升**: 涉及系统修改的操作必须检查权限
2. **错误传播**: 使用 Result 类型传播错误，避免 panic
3. **资源管理**: 确保所有 Windows 句柄正确关闭
4. **并发安全**: 管理器实现必须线程安全
5. **日志规范**: 包含 trace_id 用于请求追踪

## 未来改进方向

1. **配置管理**: 支持配置文件而非硬编码
2. **权限细化**: 实现更细粒度的权限控制
3. **性能监控**: 添加性能指标收集
4. **功能扩展**: 支持更多系统管理功能
5. **安全加强**: 实现更强的加密通信