{
    // 使用 IntelliSense 了解相关属性。 
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'agent'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=agent",
                    "--package=agent"
                ],
                "filter": {
                    "name": "agent",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'agent'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=agent",
                    "--package=agent"
                ],
                "filter": {
                    "name": "agent",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug integration test 'integration_test'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--test=integration_test",
                    "--package=agent"
                ],
                "filter": {
                    "name": "integration_test",
                    "kind": "test"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in library 'protocol'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib",
                    "--package=protocol"
                ],
                "filter": {
                    "name": "protocol",
                    "kind": "lib"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in library 'desktop'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib",
                    "--package=desktop"
                ],
                "filter": {
                    "name": "desktop",
                    "kind": "lib"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'desktop'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=desktop",
                    "--package=desktop"
                ],
                "filter": {
                    "name": "desktop",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'desktop'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=desktop",
                    "--package=desktop"
                ],
                "filter": {
                    "name": "desktop",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in library 'core'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib",
                    "--package=core"
                ],
                "filter": {
                    "name": "core",
                    "kind": "lib"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in library 'helper_svc'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--lib",
                    "--package=helper-svc"
                ],
                "filter": {
                    "name": "helper_svc",
                    "kind": "lib"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'helper-svc'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=helper-svc",
                    "--package=helper-svc"
                ],
                "filter": {
                    "name": "helper-svc",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'helper-svc'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=helper-svc",
                    "--package=helper-svc"
                ],
                "filter": {
                    "name": "helper-svc",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'test-interactive'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=test-interactive",
                    "--package=helper-svc"
                ],
                "filter": {
                    "name": "test-interactive",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug unit tests in executable 'test-interactive'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--bin=test-interactive",
                    "--package=helper-svc"
                ],
                "filter": {
                    "name": "test-interactive",
                    "kind": "bin"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        },
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug integration test 'integration_tests'",
            "cargo": {
                "args": [
                    "test",
                    "--no-run",
                    "--test=integration_tests",
                    "--package=helper-svc"
                ],
                "filter": {
                    "name": "integration_tests",
                    "kind": "test"
                }
            },
            "args": [],
            "cwd": "${workspaceFolder}"
        }
    ]
}