# EchoWave Windows 服务模块设计

## 模块定位

Windows 服务模块（helper-svc）是一个独立的 Windows 系统服务，专门负责处理所有需要管理员权限的操作。该模块与其他模块完全独立，仅通过 Windows Named Pipe 与客户端核心模块进行通信。

## 模块职责边界

### 本模块负责
1. **权限操作** - 所有需要管理员权限的 Windows 系统操作
2. **Windows 功能管理** - 通过 DISM API 管理 Windows 可选功能（WSL、Hyper-V 等）
3. **系统虚拟化检查** - 检查 BIOS 虚拟化支持状态
4. **更新安装** - 协助主程序完成需要管理员权限的更新安装
5. **系统级服务管理** - 安装、卸载、启动、停止自身服务

### 本模块不负责
1. **业务逻辑** - 不包含任何业务逻辑，仅执行权限操作
2. **UI 交互** - 不直接与用户界面交互
3. **任务管理** - 不处理任务引擎相关的任何逻辑
4. **网络通信** - 不进行任何网络请求或数据传输
5. **状态管理** - 不维护应用状态，仅响应请求

## 模块间通信

本模块仅与客户端核心模块（core）通过 Windows Named Pipe 进行通信：
- **通信协议**: 使用 `crates/protocol` 封装的消息帧
- **通信方式**: 请求-响应模式，被动响应核心模块的请求
- **安全机制**: 基于进程 ID 和时间戳的令牌认证

## 核心功能

### 1. 自动更新管理
- 接收主程序下载完成的更新包路径
- 重启主程序并执行更新安装
- 确保更新过程的权限和安全性

### 2. Windows 功能管理
- 检查 WSL、Hyper-V、虚拟机平台等功能状态
- 通过 DISM API 启用/禁用 Windows 可选功能
- 检查系统虚拟化支持

### 3. 系统检查增强
- 提供需要管理员权限的系统检查
- 协助主程序完成完整的系统环境验证

## 架构设计

```mermaid
graph TB
    subgraph "客户端核心模块 (core)"
        CORE[客户端核心<br/>业务逻辑]
        HELPER_CLIENT[Named Pipe 客户端<br/>使用 protocol 消息帧]
    end
    
    subgraph "Windows 服务模块 (helper-svc)"
        SERVICE_MAIN[服务主程序]
        PIPE_SERVER[Named Pipe 服务端<br/>使用 protocol 消息帧]
        REQUEST_HANDLER[请求处理器]
        
        subgraph "功能模块"
            DISM_MGR[DISM 管理器]
            UPDATE_MGR[更新管理器]
            SYSTEM_CHECK[系统检查器]
        end
    end
    
    subgraph "Windows 系统"
        DISM_API[DISM API]
        SERVICE_CONTROL[服务控制管理器]
        WINDOWS_FEATURES[Windows 功能]
    end
    
    CORE --> HELPER_CLIENT
    HELPER_CLIENT -.Named Pipe<br/>protocol 消息帧.-> PIPE_SERVER
    PIPE_SERVER --> REQUEST_HANDLER
    REQUEST_HANDLER --> DISM_MGR
    REQUEST_HANDLER --> UPDATE_MGR
    REQUEST_HANDLER --> SYSTEM_CHECK
    
    DISM_MGR --> DISM_API
    DISM_API --> WINDOWS_FEATURES
    SERVICE_MAIN --> SERVICE_CONTROL
```

## 通信协议

### Named Pipe 配置
- **管道名称**: `\\.\pipe\echowave_helper`
- **访问模式**: 双向通信
- **安全模式**: 仅允许本地连接
- **认证机制**: 基于进程ID和时间戳的令牌验证

### 消息格式

使用 `crates/protocol` 提供的消息帧封装，具体命令定义如下：

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HelperSvcEvent{
    // === Core -> HelperSvc ===
    // 要求服务助手更新自己
    InstallUpdate{
        // 日志追踪 ID
        trace_id: Uuid,
        // 程序的 PID（或许不用）
        pid: u16,
        // 软件包路径
        app_package_path: String,
    },
    // 获取指定名称的可选功能状态
    GetOptionalFeature{
        // 日志追踪 ID
        trace_id: Uuid,
        // 功能名称
        name: String
    },
    // 启用指定名称的可选功能
    EnableOptionalFeature{
        // 日志追踪 ID
        trace_id: Uuid,
        // 功能名称
        name: String
    },
    // 日志转发事件
    LogForward(LogEntry),

    // === HelperSvc -> Core ===
    // 服务助手发送该消息后会监听主程序的进程状态，一旦退出则立即进行更新
    InstallReady {
        trace_id: Uuid,
    },
    // 返回可选功能信息
    OptionalFeature {
        trace_id: Uuid,
        name: String,
        state: OptionalFeatureState,
        restart_required: bool
    },
    // 返回启用可选功能结果
    EnableFeatureResult {
        trace_id: Uuid,
        name: String,
        state: OptionalFeatureState,
        detail: String
    },
}
```

## 安全机制

### 1. 认证令牌
- 基于主程序进程ID和启动时间戳生成
- 每次通信都需要验证令牌
- 防止其他程序恶意调用服务

### 2. 权限控制
- 服务以 LocalSystem 权限运行
- Named Pipe 仅允许本地连接
- 验证调用方进程的合法性

### 3. 输入验证
- 严格验证所有输入参数
- 防止路径遍历和注入攻击
- 限制可操作的功能范围

## 服务生命周期

### 安装流程
1. 主程序安装时检测是否需要安装服务
2. 以管理员权限运行服务安装程序
3. 注册 Windows 服务到系统
4. 启动服务并验证运行状态

### 运行流程
1. 服务启动时初始化 Named Pipe 服务器
2. 监听客户端连接请求
3. 验证客户端身份和权限
4. 处理具体的功能请求
5. 返回处理结果

### 卸载流程
1. 主程序卸载时停止服务
2. 从系统中删除服务注册
3. 清理相关文件和配置

## 错误处理

### 错误分类
- **权限错误**: 操作需要更高权限
- **系统错误**: Windows API 调用失败
- **通信错误**: Named Pipe 通信异常
- **验证错误**: 认证或输入验证失败

### 错误恢复
- 自动重试机制
- 降级处理策略
- 详细的错误日志记录
- 向主程序报告错误状态

## 部署和维护

### 构建配置
```toml
[package]
name = "echowave-helper"
version = "1.0.0"
edition = "2021"

[[bin]]
name = "echowave-helper"
path = "src/main.rs"

[dependencies]
windows-service = "0.6"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
tracing = "0.1"
tracing-subscriber = "0.3"
anyhow = "1.0"
```

### 安装脚本
- NSIS 安装包集成
- 自动检测管理员权限
- 服务注册和启动验证
- 错误处理和回滚机制

### 监控和日志
- Windows 事件日志集成
- 性能计数器监控
- 定期健康检查
- 异常情况告警

## 测试策略

### 单元测试
- 各功能模块独立测试
- Mock Windows API 调用
- 错误场景覆盖测试

### 集成测试
- Named Pipe 通信测试
- 端到端功能验证
- 权限和安全测试

### 兼容性测试
- 多版本 Windows 支持
- 不同权限级别测试
- 系统资源占用测试

## 模块独立性保证

1. **无业务依赖** - 本模块不包含任何业务逻辑，仅执行系统级操作
2. **通信隔离** - 仅通过 Named Pipe 与核心模块通信，不直接访问其他模块
3. **独立部署** - 作为 Windows 服务独立安装和运行
4. **最小权限** - 仅在需要时以管理员权限运行特定操作

---

*本文档描述了 EchoWave Windows 服务模块的设计，该模块专注于处理需要管理员权限的系统操作，与其他模块保持清晰的职责边界。*
