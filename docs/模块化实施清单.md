# EchoWave 客户端重构 - 模块化实施清单

## 概述

本文档提供了基于四模块架构的 EchoWave 客户端重构实施清单。系统分为四个独立模块，通过明确的通信协议进行交互。

## 四模块架构

1. **渲染模块（desktop）** - Vue + Tauri，负责 UI 渲染和用户交互
2. **客户端核心模块（core）** - 所有业务逻辑，不包含 UI 和系统调用
3. **任务引擎代理模块（agent）** - 在 WSL/Linux 中运行，管理任务引擎内的服务
4. **Windows 服务模块（helper-svc）** - 处理需要管理员权限的 Windows 操作

## 模块间通信

- **渲染模块 ↔ 核心模块**: Tauri IPC (Commands/Events)
- **核心模块 ↔ 代理模块**: stdio + protocol 消息帧
- **核心模块 ↔ Windows 服务**: Named Pipe + protocol 消息帧

## 四模块架构图

```mermaid
graph TB
    subgraph "渲染模块 (desktop)"
        VUE[Vue 3 应用]
        TAURI[Tauri Runtime]
        UI_STATE[UI 状态管理]
    end

    subgraph "客户端核心模块 (crates/core)"
        CORE_STATE[核心状态]
        BIZ_LOGIC[业务逻辑]
        SERVICES[系统检查<br/>任务管理<br/>更新管理]
        ADAPTERS[Agent适配器<br/>Helper适配器<br/>HTTP适配器]
    end

    subgraph "任务引擎代理模块 (agent)"
        AGENT_MAIN[Agent主进程]
        PROCESS_MGR[进程管理器]
        DATA_COLLECTOR[数据收集器]
        TAILSCALE[Tailscale服务]
        DOCKER[Docker服务]
        NOMAD[Nomad服务]
    end

    subgraph "Windows服务模块 (helper-svc)"
        HELPER_SVC[Windows Service]
        DISM_MGR[DISM管理器]
        UPDATE_MGR[更新管理器]
        SYSTEM_CHECK[系统检查器]
    end

    subgraph "通信层 (crates/protocol)"
        PROTOCOL[消息帧协议]
    end

    VUE --> TAURI
    TAURI -.Tauri IPC.-> CORE_STATE

    CORE_STATE --> BIZ_LOGIC
    BIZ_LOGIC --> SERVICES
    SERVICES --> ADAPTERS

    ADAPTERS -.stdio<br/>protocol.-> AGENT_MAIN
    ADAPTERS -.Named Pipe<br/>protocol.-> HELPER_SVC

    AGENT_MAIN --> PROCESS_MGR
    PROCESS_MGR --> TAILSCALE
    PROCESS_MGR --> DOCKER
    PROCESS_MGR --> NOMAD
    AGENT_MAIN --> DATA_COLLECTOR

    HELPER_SVC --> DISM_MGR
    HELPER_SVC --> UPDATE_MGR
    HELPER_SVC --> SYSTEM_CHECK
```

## 实施清单

### 📋 第一步：基础设施（2 天）

#### 1.1 项目结构

- [ ] 创建 Cargo workspace
  ```toml
  [workspace]
  members = ["desktop/src-tauri", "agent", "helper-svc", "crates/*"]
  ```
- [ ] 创建模块结构
  - [ ] `desktop/` - 渲染模块（Vue + Tauri）
  - [ ] `crates/core/` - 客户端核心模块
  - [ ] `crates/protocol/` - 通信协议
  - [ ] `agent/` - 任务引擎代理模块
  - [ ] `helper-svc/` - Windows 服务模块

#### 1.2 通信协议

- [x] 创建 `crates/protocol/src/frame.rs`

#### 1.3 配置系统

- [ ] 创建 `crates/core/src/config.rs`

  ```rust
  #[derive(Debug, Clone, Deserialize)]
  pub struct Config {
      pub api_base_url: String,
      pub log_level: String,
      pub wsl_config: WslConfig,
  }

  impl Config {
      pub fn load() -> Result<Self> {
          // 直接加载，不需要"配置管理器"
          let path = dirs::config_dir()
              .unwrap()
              .join("echowave/config.toml");
          let content = std::fs::read_to_string(path)?;
          Ok(toml::from_str(&content)?)
      }
  }
  ```

#### 1.4 日志设置

- [ ] 在 `main.rs` 中初始化日志

  ```rust
  use tracing_subscriber::prelude::*;

  fn init_logging(config: &Config) {
      tracing_subscriber::registry()
          .with(tracing_subscriber::fmt::layer())
          .with(tracing_subscriber::EnvFilter::new(&config.log_level))
          .init();
  }
  ```

### 📋 第二步：核心模块实现（3 天）

#### 2.1 Agent 适配器

- [ ] 创建 `crates/core/src/adapters/agent.rs`

  ```rust
  use protocol::{MessageFrame, MessageType};
  use tokio::process::{Child, Command};
  use std::process::Stdio;

  pub struct AgentAdapter {
      process: Option<Child>,
  }

  impl AgentAdapter {
      pub fn new() -> Self {
          Self { process: None }
      }

      pub async fn start(&mut self) -> Result<()> {
          let child = Command::new("wsl")
              .args(["-e", "/opt/agent"])
              .stdin(Stdio::piped())
              .stdout(Stdio::piped())
              .spawn()?;

          self.process = Some(child);
          Ok(())
      }

      pub async fn send_message(&mut self, frame: MessageFrame) -> Result<MessageFrame> {
          // 通过 stdio 发送和接收消息帧
      }
  }
  ```

#### 2.2 Helper 适配器

- [ ] 创建 `crates/core/src/adapters/helper.rs`

  ```rust
  use protocol::{MessageFrame, MessageType};
  use tokio::net::windows::named_pipe::{ClientOptions, NamedPipeClient};

  pub struct HelperAdapter {
      pipe_name: String,
      auth_token: String,
  }

  impl HelperAdapter {
      pub fn new() -> Self {
          Self {
              pipe_name: r"\\.\pipe\echowave_helper".to_string(),
              auth_token: Self::generate_auth_token(),
          }
      }

      pub async fn send_request(&self, frame: MessageFrame) -> Result<MessageFrame> {
          let mut client = ClientOptions::new()
              .open(&self.pipe_name)
              .await?;

          // 发送请求帧并接收响应
      }
  }
  ```

#### 2.3 HTTP 适配器

- [ ] 创建 `crates/core/src/adapters/http.rs`

  ```rust
  pub struct HttpAdapter {
      client: reqwest::Client,
      base_url: String,
  }

  impl HttpAdapter {
      pub fn new(config: &Config) -> Self {
          Self {
              client: reqwest::Client::new(),
              base_url: config.api_base_url.clone(),
          }
      }

      pub async fn post<T: Serialize, R: DeserializeOwned>(
          &self,
          path: &str,
          body: &T,
      ) -> Result<R> {
          // 简单的 HTTP 封装
      }
  }
  ```

#### 2.4 文件适配器

- [ ] 创建 `crates/core/src/adapters/file.rs`

  ```rust
  pub struct FileAdapter;

  impl FileAdapter {
      pub fn new() -> Self {
          Self
      }

      pub async fn read_json<T: DeserializeOwned>(&self, path: &Path) -> Result<T> {
          let content = tokio::fs::read_to_string(path).await?;
          Ok(serde_json::from_str(&content)?)
      }

      pub async fn write_json<T: Serialize>(&self, path: &Path, data: &T) -> Result<()> {
          let content = serde_json::to_string_pretty(data)?;
          tokio::fs::write(path, content).await?;
          Ok(())
      }
  }
  ```

### 📋 第三步：渲染模块实现（2 天）

#### 3.1 Tauri 集成

- [ ] 创建 `desktop/src-tauri/src/main.rs`

  ```rust
  use tauri::Manager;

  fn main() {
      tauri::Builder::default()
          .setup(|app| {
              // 创建核心模块的通信通道
              let (tx, rx) = tokio::sync::mpsc::channel(100);

              // 启动核心模块
              let core_handle = std::thread::spawn(move || {
                  let rt = tokio::runtime::Runtime::new().unwrap();
                  rt.block_on(domain::run(rx));
              });

              app.manage(tx);
              Ok(())
          })
          .invoke_handler(tauri::generate_handler![
              // Tauri 命令
              check_system,
              start_accepting_tasks,
              stop_accepting_tasks,
          ])
          .run(tauri::generate_context!())
          .expect("error while running tauri application");
  }
  ```

#### 3.2 Tauri 命令

- [ ] 创建 `desktop/src-tauri/src/commands.rs`

  ```rust
  use tauri::State;
  use tokio::sync::mpsc::Sender;

  #[tauri::command]
  async fn check_system(
      tx: State<'_, Sender<CoreCommand>>
  ) -> Result<SystemCheckResult, String> {
      let (resp_tx, resp_rx) = tokio::sync::oneshot::channel();

      tx.send(CoreCommand::CheckSystem { resp: resp_tx })
          .await
          .map_err(|e| e.to_string())?;

      resp_rx.await.map_err(|e| e.to_string())?
  }
  ```

#### 3.3 Vue 前端

- [ ] 创建 `desktop/src/api/core.ts`

  ```typescript
  import { invoke } from "@tauri-apps/api/tauri";

  export const coreApi = {
    async checkSystem() {
      return invoke<SystemCheckResult>("check_system");
    },

    async startAcceptingTasks() {
      return invoke("start_accepting_tasks");
    },

    async stopAcceptingTasks() {
      return invoke("stop_accepting_tasks");
    },
  };
  ```

### 📋 第四步：核心业务服务（3 天）

#### 4.1 系统检查服务

- [ ] 创建 `crates/core/src/services/system_check.rs`

  ```rust
  use crate::adapters::{AgentAdapter, HelperAdapter};

  pub struct SystemCheckService {
      agent: Arc<Mutex<AgentAdapter>>,
      helper: Arc<HelperAdapter>,
  }

  impl SystemCheckService {
      pub async fn run_all_checks(&self) -> Result<SystemCheckResult> {
          // 并行执行检查
          let (platform, os, ps, virt, wsl, mirror) = tokio::join!(
              self.check_platform(),
              self.check_os_version(),
              self.check_powershell(),
              self.check_virtualization(),
              self.check_wsl(),
              self.check_mirror()
          );

          Ok(SystemCheckResult {
              platform: platform?,
              os_version: os?,
              powershell: ps?,
              virtualization: virt?,
              wsl: wsl?,
              mirror: mirror?,
          })
      }
  }
  ```

#### 4.2 任务管理服务

- [ ] 创建 `crates/core/src/services/task_management.rs`

  ```rust
  pub struct TaskManagementService {
      agent: Arc<Mutex<AgentAdapter>>,
      http: Arc<HttpAdapter>,
      is_accepting: AtomicBool,
  }

  impl TaskManagementService {
      pub async fn start_accepting_tasks(&self) -> Result<()> {
          // 通过 Agent 启动守护进程
          let mut agent = self.agent.lock().await;
          agent.send_message(MessageFrame::new(
              MessageType::Request,
              serde_json::to_vec(&AgentCommand::StartDaemons)?
          )).await?;

          // 注册节点
          self.register_node().await?;

          // 开始轮询
          self.is_accepting.store(true, Ordering::Relaxed);
          Ok(())
      }
  }
  ```

#### 4.3 更新服务

- [ ] 创建 `crates/core/src/services/update.rs`

  ```rust
  pub struct UpdateService {
      helper: Arc<HelperAdapter>,
      http: Arc<HttpAdapter>,
  }

  impl UpdateService {
      pub async fn install_update(&self, update_path: &str) -> Result<()> {
          // 通过 Windows 服务模块安装更新
          self.helper.send_request(MessageFrame::new(
              MessageType::Request,
              serde_json::to_vec(&HelperCommand::RestartForUpdate {
                  update_path: update_path.to_string()
              })?
          )).await?;
          Ok(())
      }
  }
  ```

### 📋 第五步：Agent 模块实现（2 天）

#### 5.1 Agent 主程序

- [ ] 创建 `agent/src/main.rs`

  ```rust
  use protocol::{MessageFrame, MessageType};
  use tokio::io::{AsyncBufReadExt, AsyncWriteExt};

  #[tokio::main]
  async fn main() -> Result<()> {
      // 设置为 Linux only
      #[cfg(not(target_os = "linux"))]
      compile_error!("Agent only runs on Linux");

      let stdin = tokio::io::stdin();
      let stdout = tokio::io::stdout();

      let mut reader = tokio::io::BufReader::new(stdin);
      let mut writer = stdout;

      loop {
          // 读取消息帧
          let frame = read_frame(&mut reader).await?;

          // 处理命令
          let response = handle_command(frame).await;

          // 发送响应
          write_frame(&mut writer, response).await?;
      }
  }
  ```

#### 5.2 进程管理器

- [ ] 创建 `agent/src/process_manager.rs`

  ```rust
  pub struct ProcessManager {
      processes: HashMap<String, Child>,
  }

  impl ProcessManager {
      pub async fn start_service(&mut self, name: &str) -> Result<()> {
          match name {
              "tailscale" => self.start_tailscale().await,
              "docker" => self.start_docker().await,
              "nomad" => self.start_nomad().await,
              _ => Err(anyhow!("Unknown service")),
          }
      }

      async fn start_tailscale(&mut self) -> Result<()> {
          let child = Command::new("tailscale")
              .arg("up")
              .spawn()?;
          self.processes.insert("tailscale".to_string(), child);
          Ok(())
      }
  }
  ```

### 📋 第六步：Windows 服务模块（2 天）

#### 6.1 服务主程序

- [ ] 创建 `helper-svc/src/main.rs`

  ```rust
  use windows_service::{define_windows_service, service_dispatcher};
  use protocol::{MessageFrame, MessageType};

  const SERVICE_NAME: &str = "EchoWaveHelper";
  const PIPE_NAME: &str = r"\\.\pipe\echowave_helper";

  define_windows_service!(ffi_service_main, service_main);

  fn main() -> Result<()> {
      service_dispatcher::start(SERVICE_NAME, ffi_service_main)?;
      Ok(())
  }
  ```

#### 6.2 DISM 管理器

- [ ] 创建 `helper-svc/src/dism.rs`

  ```rust
  use std::process::Command;

  pub struct DismManager;

  impl DismManager {
      pub async fn enable_windows_feature(&self, feature: &str) -> Result<()> {
          Command::new("dism")
              .args(["/online", "/enable-feature",
                     &format!("/featurename:{}", feature),
                     "/all", "/norestart"])
              .output()?;
          Ok(())
      }

      pub async fn check_virtualization(&self) -> Result<bool> {
          // 检查虚拟化支持
          Ok(true)
      }
  }
  ```

### 📋 第七步：测试（2 天）

#### 7.1 模块间通信测试

- [ ] 测试渲染模块与核心模块的 IPC 通信
- [ ] 测试核心模块与 Agent 的 stdio 通信
- [ ] 测试核心模块与 Windows 服务的 Named Pipe 通信

#### 7.2 集成测试

- [ ] 完整的系统检查流程测试
- [ ] 任务接单流程测试
- [ ] 更新安装流程测试

### 📋 第八步：部署（1 天）

#### 8.1 构建配置

- [ ] 配置 GitHub Actions
- [ ] Windows 代码签名
- [ ] NSIS 安装包（包含所有模块）

#### 8.2 安装流程

- [ ] 主程序安装
- [ ] Windows 服务注册
- [ ] Agent 部署到 WSL

## 时间估算

- 基础设施：2 天
- 核心模块：3 天
- 渲染模块：2 天
- 核心业务服务：3 天
- Agent 模块：2 天
- Windows 服务模块：2 天
- 测试：2 天
- 部署：1 天

**总计：17 天**

## 模块职责总结

### 渲染模块（desktop）

- UI 渲染和用户交互
- 通过 Tauri IPC 与核心模块通信
- 不包含业务逻辑

### 客户端核心模块（core）

- 所有业务逻辑
- 协调其他模块
- 通过适配器与 Agent 和 Windows 服务通信

### 任务引擎代理模块（agent）

- Linux 环境运行
- 管理任务引擎内的服务进程
- 通过 stdio 接收核心模块指令

### Windows 服务模块（helper-svc）

- 处理需要管理员权限的操作
- 通过 Named Pipe 接收核心模块请求
- 不包含业务逻辑

---

_本清单基于四模块架构设计，确保各模块职责清晰、通信明确、相互独立。_
