# EchoWave 客户端重构实施计划

基于四模块架构的重构计划，确保模块间职责清晰、通信明确。

## 1. 项目概述

### 1.1 重构目标

| 维度         | 当前状态 (Electron)     | 目标状态 (四模块架构) | 核心收益      |
| ------------ | ----------------------- | --------------------- | ------------- |
| **架构**     | 单体应用，前后端耦合    | 四模块架构，职责分离  | 高内聚低耦合  |
| **性能**     | 内存占用 ~200MB         | 内存占用 <50MB        | 4 倍性能提升  |
| **安全**     | JavaScript 执行系统命令 | 模块隔离，权限分离    | 安全性提升    |
| **开发效率** | 调试困难，类型不安全    | 模块独立开发测试      | 并行开发      |
| **部署**     | ~100MB 安装包           | ~10MB 安装包          | 10 倍体积优化 |

### 1.2 四模块架构

1. **渲染模块（desktop）** - Vue + Tauri，负责 UI 渲染
2. **客户端核心模块（core）** - 业务逻辑中心
3. **任务引擎代理模块（agent）** - WSL 镜像内服务管理
4. **Windows 服务模块（helper-svc）** - 处理需要管理员权限的操作

## 2. 架构设计

### 2.1 模块间通信架构

```mermaid
graph TB
    subgraph "渲染模块 (desktop)"
        VUE[Vue 3 应用]
        TAURI[Tauri Runtime]
    end

    subgraph "客户端核心模块 (crates/core)"
        CORE[业务逻辑]
        ADAPTERS[适配器层]
    end

    subgraph "任务引擎代理模块 (agent)"
        AGENT[Linux Only<br/>进程管理]
    end

    subgraph "Windows 服务模块 (helper-svc)"
        HELPER[Windows Service<br/>权限操作]
    end

    subgraph "通信协议 (crates/protocol)"
        PROTOCOL[统一消息帧]
    end

    VUE -.Tauri IPC.-> CORE
    CORE -.stdio + protocol.-> AGENT
    CORE -.Named Pipe + protocol.-> HELPER

    style PROTOCOL fill:#f9f,stroke:#333,stroke-width:2px
```

### 2.2 模块职责划分

#### 渲染模块职责

- UI 渲染和用户交互
- 状态驱动的界面更新
- 通过 Tauri IPC 发送命令和接收事件
- 不包含任何业务逻辑

#### 核心模块职责

- 所有业务逻辑实现
- 系统检查、任务管理、更新管理
- 协调其他模块的操作
- 维护应用状态

#### Agent 模块职责

- 在 WSL/Linux 环境中运行
- 管理 Tailscale、Docker、Nomad 进程
- 收集任务引擎数据
- 响应核心模块的指令

#### Windows 服务模块职责

- 处理需要管理员权限的操作
- Windows 功能管理（WSL、Hyper-V）
- 系统级更新安装
- 无状态服务，仅响应请求

## 3. 实施计划

### 第一阶段：基础构建（1 周）

#### Day 1-2: 项目结构和通信协议

- 创建 Cargo workspace 和模块结构
- 定义统一的消息帧协议
- 设置模块间通信基础
- 设计 trace_id 全链路追踪机制
- 配置统一的日志格式和输出

```toml
[workspace]
members = [
    "desktop/src-tauri",
    "crates/core",
    "crates/protocol",
    "agent",
    "helper-svc"
]
```

```rust
// crates/protocol/src/frame.rs
// 注意：MessageFrame 在 frame.rs 中定义，不需要 Serialize/Deserialize
#[derive(Debug)]
pub struct MessageFrame {
    pub event_code: EventCode,    // 低级别事件码（内部通信控制）
    pub operation_id: u32,         // 操作ID，用于关联请求响应
    pub is_response: bool,         // 是否是响应
    pub bytes: Vec<u8>,           // 序列化的业务数据（JSON）
}

#[repr(u8)]  // 重要：用于确保枚举值的二进制表示
#[derive(Debug, Copy, Clone, PartialEq, Eq)]
pub enum EventCode {
    Data = 0x01,      // 普通数据传输
    Reserved = 0x30,  // 保留
    Flush = 0x31,     // 要求目标立即推送缓冲区的数据
    Exit = 0x3f,      // 要求目标准备退出
}
```

#### Day 3-4: 核心模块适配器

- AgentAdapter - 与 Agent 模块通信
- HelperAdapter - 与 Windows 服务通信
- HttpAdapter - 服务端通信
- FileAdapter - 本地文件操作

```rust
// crates/core/src/adapters/agent.rs
use protocol::{frame::{send_message_frame, read_message_frame, EventCode}, events::agent::*};
use tokio::process::{Child, ChildStdin, ChildStdout};
use tokio::io::BufReader;

pub struct AgentAdapter {
    process: Option<Child>,
    stdin: Option<ChildStdin>,
    stdout: Option<BufReader<ChildStdout>>,
    operation_counter: u32,
}

impl AgentAdapter {
    pub async fn send_event(&mut self, event: AgentEvent) -> Result<EventResponse> {
        let operation_id = self.next_operation_id();

        // 序列化业务事件为 JSON
        let event_bytes = serde_json::to_vec(&event)?;

        // 使用 protocol 发送消息帧
        if let Some(stdin) = &mut self.stdin {
            send_message_frame(
                stdin,
                EventCode::Data,     // 普通数据传输
                operation_id,
                false,               // 这是请求，不是响应
                &event_bytes
            ).await?;
        }

        // 读取响应
        if let Some(stdout) = &mut self.stdout {
            if let Some(frame) = read_message_frame(stdout).await? {
                // 验证是响应且操作ID匹配
                if frame.is_response && frame.operation_id == operation_id {
                    let response: EventResponse = serde_json::from_slice(&frame.bytes)?;
                    return Ok(response);
                }
            }
        }

        Err(anyhow!("Failed to receive response"))
    }
}
```

#### Day 5: 模块间通信测试

- 测试 Tauri IPC 通信
- 测试 stdio 通信（核心 ↔ Agent）
- 测试 Named Pipe 通信（核心 ↔ Windows 服务）

### 第二阶段：核心服务实现（1 周）

#### Day 6-7: 核心业务服务

实现核心模块的三大服务：

```rust
// crates/core/src/services/system_check.rs
pub struct SystemCheckService {
    agent: Arc<Mutex<AgentAdapter>>,
    helper: Arc<HelperAdapter>,
}

impl SystemCheckService {
    pub async fn run_all_checks(&self) -> Result<SystemCheckResult> {
        // 本地检查
        let platform = self.check_platform();
        let os_version = self.check_os_version();

        // 通过 Windows 服务检查
        let virtualization = self.helper
            .send_request(HelperCommand::CheckVirtualization)
            .await?;

        // 通过 Agent 检查
        let mut agent = self.agent.lock().await;
        let event = AgentEvent::SystemCheck {
            trace_id: Uuid::new_v4(),
            check_type: SystemCheckType::All,
        };
        let response = agent.send_event(event).await?;

        Ok(SystemCheckResult {
            platform,
            os_version,
            virtualization,
            mirror_status,
            // ...
        })
    }
}
```

#### Day 8-9: Agent 模块实现

Linux 环境的服务管理：

```rust
// agent/src/main.rs
#[cfg(not(target_os = "linux"))]
compile_error!("Agent only runs on Linux");

use protocol::{frame::{send_message_frame, read_message_frame, EventCode}, events::agent::*};
use tokio::io::{BufReader, AsyncBufReadExt};

#[tokio::main]
async fn main() -> Result<()> {
    let mut process_manager = ProcessManager::new();
    let mut data_collector = DataCollector::new();

    let stdin = tokio::io::stdin();
    let mut stdout = tokio::io::stdout();
    let mut reader = BufReader::new(stdin);

    // stdio 通信循环
    loop {
        // 读取消息帧
        match read_message_frame(&mut reader).await? {
            Some(frame) => {
                // 处理内部控制事件
                match frame.event_code {
                    EventCode::Exit => {
                        // 优雅退出
                        process_manager.stop_all_services().await?;
                        break;
                    }
                    EventCode::Flush => {
                        // 立即发送缓冲的日志
                        stdout.flush().await?;
                        continue;
                    }
                    EventCode::Data => {
                        // 处理业务事件
                        let event: AgentEvent = serde_json::from_slice(&frame.bytes)?;
                        let response = handle_event(event, &mut process_manager, &mut data_collector).await;

                        // 发送响应
                        let response_bytes = serde_json::to_vec(&response)?;
                        send_message_frame(
                            &mut stdout,
                            EventCode::Data,
                            frame.operation_id,  // 使用相同的操作ID
                            true,                // 这是响应
                            &response_bytes
                        ).await?;
                    }
                    _ => continue,
                }
            }
            None => {
                // 收到控制台输出，记录日志
                continue;
            }
        }
    }

    Ok(())
}

async fn handle_event(
    event: AgentEvent,
    process_manager: &mut ProcessManager,
    data_collector: &mut DataCollector,
) -> EventResponse {
    match event {
        AgentEvent::DaemonControl { action, daemon, .. } => {
            match action {
                DaemonAction::Start => process_manager.start_service(&daemon).await,
                DaemonAction::Stop => process_manager.stop_service(&daemon).await,
                // ...
            }
        }
        AgentEvent::SystemCheck { check_type, .. } => {
            let results = data_collector.run_checks(check_type).await;
            EventResponse::SystemCheckResult { results }
        }
        // ...
    }
}
```

#### Day 10: Windows 服务模块

管理员权限操作服务：

```rust
// helper-svc/src/main.rs
async fn handle_request(request: HelperRequest) -> HelperResponse {
    match request.command {
        HelperCommand::CheckWindowsFeatures => {
            let features = DismManager::new()
                .get_windows_features()
                .await?;
            HelperResponse::success(features)
        }
        HelperCommand::EnableWindowsFeature { name } => {
            DismManager::new()
                .enable_feature(&name)
                .await?;
            HelperResponse::success(())
        }
        HelperCommand::RestartForUpdate { path } => {
            UpdateManager::new()
                .restart_and_update(&path)
                .await?;
            HelperResponse::success(())
        }
        // ...
    }
}
```

### 第三阶段：渲染模块（1 周）

#### Day 11-12: Tauri 集成层

渲染模块与核心模块的桥接：

```rust
// desktop/src-tauri/src/main.rs
fn main() {
    tauri::Builder::default()
        .setup(|app| {
            // 创建与核心模块的通信通道
            let (tx, rx) = tokio::sync::mpsc::channel(100);

            // 启动核心模块线程
            std::thread::spawn(move || {
                let rt = tokio::runtime::Runtime::new().unwrap();
                rt.block_on(domain::run(rx));
            });

            app.manage(CoreChannel(tx));
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            check_system,
            start_accepting_tasks,
            stop_accepting_tasks,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[tauri::command]
async fn check_system(
    channel: State<'_, CoreChannel>
) -> Result<SystemCheckResult, String> {
    let (resp_tx, resp_rx) = oneshot::channel();

    channel.0.send(CoreCommand::CheckSystem { resp: resp_tx })
        .await
        .map_err(|e| e.to_string())?;

    resp_rx.await.map_err(|e| e.to_string())
}
```

#### Day 13-14: Vue 应用

纯粹的 UI 渲染逻辑：

```typescript
// desktop/src/composables/useCore.ts
export function useCore() {
  const systemStatus = ref<SystemCheckResult>();
  const isChecking = ref(false);

  const checkSystem = async () => {
    isChecking.value = true;
    try {
      systemStatus.value = await invoke("check_system");
    } finally {
      isChecking.value = false;
    }
  };

  // 监听核心模块事件
  onMounted(() => {
    listen("core-event", (event) => {
      // 根据事件更新 UI
    });
  });

  return { systemStatus, isChecking, checkSystem };
}
```

#### Day 15: UI 组件和页面

- 系统检查页面（显示 6 项检查状态）
- 任务管理页面（显示任务状态）
- 设置页面（用户偏好设置）

### 第四阶段：集成测试和部署（5 天）

#### Day 16-17: 模块间集成测试

```rust
#[tokio::test]
async fn test_core_agent_communication() {
    // 测试核心模块与 Agent 的通信
    let mut agent = AgentAdapter::new();
    agent.start().await.unwrap();

    // 发送系统检查事件
    let event = AgentEvent::SystemCheck {
        trace_id: Uuid::new_v4(),
        check_type: SystemCheckType::All,
    };

    let response = agent.send_event(event).await.unwrap();

    match response {
        EventResponse::SystemCheckResult { results } => {
            assert!(!results.is_empty());
        }
        _ => panic!("Unexpected response type"),
    }
}

#[tokio::test]
async fn test_message_frame_protocol() {
    use protocol::frame::*;

    // 测试消息帧的发送和接收
    let test_data = serde_json::to_vec(&AgentEvent::Shutdown {
        trace_id: Uuid::new_v4(),
        graceful: true,
    }).unwrap();

    let mut buffer = Vec::new();

    // 发送消息帧
    send_message_frame(
        &mut buffer,
        EventCode::Data,
        12345,
        false,
        &test_data
    ).await.unwrap();

    // 验证数据已写入
    assert!(!buffer.is_empty());
    assert_eq!(&buffer[0..3], &[0x10, 0xAA, 0xFF]); // 验证识别码
}
```

#### Day 18: 端到端测试

- 完整的系统检查流程
- 任务接单和执行流程
- 更新安装流程

#### Day 19-20: 构建和部署

```yaml
# .github/workflows/release.yml
name: Release
on:
  push:
    tags:
      - "v*"

jobs:
  build:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v3

      - name: Build all modules
        run: |
          cargo build --release
          cd desktop && pnpm build && pnpm tauri build

      - name: Sign Windows Service
        run: |
          # 签名 helper-svc.exe

      - name: Create installer
        run: |
          # NSIS 打包，包含：
          # - 主程序
          # - Windows 服务
          # - Agent 二进制文件
```

### 第五阶段：优化和完善（2 天）

#### Day 21: 性能优化

- 启动时间优化（目标 < 2 秒）
- 内存占用优化（目标 < 50MB）
- 通信延迟优化

#### Day 22: 最终测试和发布

- 用户验收测试
- 文档完善
- 正式发布

## 4. 模块通信协议详解

### 4.1 Protocol 消息帧格式

基于 README.md 的协议设计，消息帧采用二进制格式，Header 固定 16 字节：

```
[识别码: 3u8][标志位: 1u8][操作码: 1u32][数据长度: 1u32][Checksum: 1u32]
```

**Header 详解：**

- 识别码：`0x10 0xAA 0xFF` (固定值，用于区分文本输出)
- 标志位：
  - bit 0: 是否压缩 (ZSTD)
  - bit 1: 是否响应 (0=请求, 1=响应)
  - bit 2-7: 事件码 (EventCode)
- 操作码：关联请求/响应的 ID (大端序)
- 数据长度：Payload 部分的字节数 (大端序)
- Checksum：Payload 的 CRC32 校验和 (大端序)

**实际使用的 API：**

```rust
// 发送消息帧
pub async fn send_message_frame<W>(
    writer: &mut W,
    event_code: EventCode,
    operation_id: u32,
    is_response: bool,
    bytes: &[u8],  // JSON 序列化的业务数据
) -> anyhow::Result<()>

// 读取消息帧
pub async fn read_message_frame<R>(
    reader: &mut R
) -> io::Result<Option<MessageFrame>>
```

### 4.2 模块间通信示例

#### 核心模块 → Agent 模块

```rust
use protocol::events::agent::*;

// 发送启动守护进程命令
let event = AgentEvent::DaemonControl {
    trace_id: Uuid::new_v4(),
    action: DaemonAction::Start,
    daemon: "tailscale".to_string(),
};

// AgentAdapter 内部会：
// 1. 序列化事件为 JSON
// 2. 调用 send_message_frame 发送
// 3. 等待并解析响应
let response = agent_adapter.send_event(event).await?;

match response {
    EventResponse::Success { .. } => println!("Daemon started"),
    EventResponse::Error { code, message } => eprintln!("Error {}: {}", code, message),
    _ => {}
}
```

#### 核心模块 → Windows 服务

```rust
use protocol::events::helper_svc::*;

// 请求启用 WSL 功能
let event = HelperEvent::EnableWindowsFeature {
    trace_id: Uuid::new_v4(),
    feature_name: "Microsoft-Windows-Subsystem-Linux".to_string(),
};

// HelperAdapter 使用 Named Pipe 而非 stdio
let response = helper_adapter.send_event(event).await?;
```

### 4.3 协议分层设计

Protocol crate 实现了两层协议：

1. **底层帧协议 (frame.rs)**

   - 二进制消息帧格式
   - 处理识别码、压缩、校验和
   - 区分文本输出和数据包
   - EventCode 用于内部控制（Exit、Flush 等）

2. **业务事件层 (events/)**
   - JSON 格式的业务数据
   - AgentEvent/EventResponse 用于 Agent 通信
   - HelperEvent/HelperResponse 用于 Windows 服务通信
   - 包含具体的业务逻辑（守护进程控制、系统检查等）

### 4.4 全链路追踪机制 (trace_id)

`trace_id` 是贯穿四个模块的追踪标识符，用于实现分布式追踪和问题排查：

#### 设计原则

1. **唯一性**: 每个用户操作生成唯一的 UUID v4 作为 trace_id
2. **传递性**: trace_id 在模块间通信时必须传递
3. **持久性**: 从操作开始到结束，使用同一个 trace_id
4. **可查性**: 所有日志都应包含 trace_id 字段

#### 实现方式

**1. 渲染模块生成 trace_id**

```typescript
// desktop/src/composables/useTracing.ts
import { v4 as uuidv4 } from "uuid";

export function useTracing() {
  const generateTraceId = () => uuidv4();

  // 为每个用户操作生成 trace_id
  const tracedInvoke = async (command: string, args?: any) => {
    const traceId = generateTraceId();
    console.log(`[${traceId}] Invoking ${command}`, args);

    try {
      const result = await invoke(command, { ...args, traceId });
      console.log(`[${traceId}] Success:`, result);
      return result;
    } catch (error) {
      console.error(`[${traceId}] Error:`, error);
      throw error;
    }
  };

  return { tracedInvoke, generateTraceId };
}
```

**2. 核心模块传递 trace_id**

```rust
// crates/core/src/context.rs
use tracing::{info, error, instrument};

pub struct RequestContext {
    pub trace_id: Uuid,
    pub start_time: Instant,
}

impl RequestContext {
    pub fn new(trace_id: Uuid) -> Self {
        info!(trace_id = %trace_id, "New request started");
        Self {
            trace_id,
            start_time: Instant::now(),
        }
    }
}

// 在适配器中传递 trace_id
impl AgentAdapter {
    #[instrument(skip(self), fields(trace_id = %ctx.trace_id))]
    pub async fn send_event_with_context(
        &mut self,
        mut event: AgentEvent,
        ctx: &RequestContext
    ) -> Result<EventResponse> {
        // 确保事件包含 trace_id
        match &mut event {
            AgentEvent::DaemonControl { trace_id, .. } |
            AgentEvent::SystemCheck { trace_id, .. } |
            AgentEvent::Shutdown { trace_id, .. } => {
                *trace_id = ctx.trace_id;
            }
            _ => {}
        }

        info!("Sending event to agent");
        self.send_event(event).await
    }
}
```

**3. Agent/Helper 模块日志记录**

```rust
// agent/src/main.rs
use tracing::{info, error, span, Level};

async fn handle_event(
    event: AgentEvent,
    process_manager: &mut ProcessManager,
    data_collector: &mut DataCollector,
) -> EventResponse {
    // 提取 trace_id
    let trace_id = match &event {
        AgentEvent::DaemonControl { trace_id, .. } |
        AgentEvent::SystemCheck { trace_id, .. } |
        AgentEvent::Shutdown { trace_id, .. } => trace_id,
        _ => &Uuid::nil(),
    };

    // 创建追踪 span
    let span = span!(Level::INFO, "handle_event", trace_id = %trace_id);
    let _enter = span.enter();

    info!("Processing event: {:?}", event);

    match event {
        AgentEvent::DaemonControl { action, daemon, .. } => {
            info!("Daemon control: {} - {:?}", daemon, action);
            match action {
                DaemonAction::Start => {
                    match process_manager.start_service(&daemon).await {
                        Ok(()) => {
                            info!("Successfully started {}", daemon);
                            EventResponse::Success { data: None }
                        }
                        Err(e) => {
                            error!("Failed to start {}: {}", daemon, e);
                            EventResponse::Error {
                                code: 500,
                                message: e.to_string(),
                            }
                        }
                    }
                }
                // ...
            }
        }
        // ...
    }
}
```

**4. 统一日志格式**

```rust
// 所有模块的日志初始化
use tracing_subscriber::{fmt, prelude::*, EnvFilter};

pub fn init_logging(module_name: &str) {
    tracing_subscriber::registry()
        .with(fmt::layer()
            .with_target(true)
            .with_thread_ids(true)
            .with_file(true)
            .with_line_number(true)
            .json()  // JSON 格式便于日志分析
            .with_current_span(true)  // 包含 span 信息
        )
        .with(EnvFilter::from_default_env())
        .init();

    info!(module = module_name, "Logger initialized");
}
```

#### 日志聚合和查询

**日志输出示例：**

```json
{
  "timestamp": "2024-01-20T10:30:45.123Z",
  "level": "INFO",
  "module": "core",
  "trace_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "System check started",
  "target": "echowave::core::services::system_check",
  "file": "src/services/system_check.rs",
  "line": 45
}
```

**查询特定请求的完整链路：**

```bash
# 查找特定 trace_id 的所有日志
grep "550e8400-e29b-41d4-a716-446655440000" /var/log/echowave/*.log

# 使用 jq 分析 JSON 日志
cat /var/log/echowave/core.log | jq 'select(.trace_id=="550e8400-e29b-41d4-a716-446655440000")'
```

#### 错误排查示例

当用户报告"系统检查失败"时：

1. 从前端日志获取 trace_id
2. 在所有模块日志中搜索该 trace_id
3. 按时间顺序查看完整调用链
4. 定位具体失败的模块和原因

```bash
# 快速定位错误
TRACE_ID="550e8400-e29b-41d4-a716-446655440000"
for log in core.log agent.log helper-svc.log; do
  echo "=== $log ==="
  grep $TRACE_ID /var/log/echowave/$log | jq '.level + " " + .message'
done
```

### 4.5 错误处理和重试

```rust
pub struct CommunicationError {
    pub module: String,
    pub error_type: ErrorType,
    pub message: String,
    pub recoverable: bool,
}

pub enum ErrorType {
    Timeout,
    InvalidMessage,
    ModuleNotAvailable,
    PermissionDenied,
}

// 自动重试机制
async fn send_with_retry<T>(
    adapter: &mut T,
    frame: MessageFrame,
    max_retries: u32,
) -> Result<MessageFrame> {
    for attempt in 0..max_retries {
        match adapter.send_frame(frame.clone()).await {
            Ok(response) => return Ok(response),
            Err(e) if e.recoverable && attempt < max_retries - 1 => {
                tokio::time::sleep(Duration::from_millis(100 * (attempt + 1))).await;
                continue;
            }
            Err(e) => return Err(e),
        }
    }
    unreachable!()
}
```

## 5. 验收标准

### 功能验收

- [ ] 四个模块能独立编译和运行
- [ ] 模块间通信正常工作
- [ ] 6 项系统检查全部通过
- [ ] 任务接单和执行流程完整
- [ ] 自动更新功能正常

### 性能验收

- [ ] 启动时间 < 2 秒
- [ ] 内存占用 < 50MB
- [ ] 安装包 < 30MB
- [ ] 模块间通信延迟 < 10ms

### 架构验收

- [ ] 模块职责清晰，无功能重叠
- [ ] 通信协议统一使用 protocol 消息帧
- [ ] 各模块可独立测试和部署
- [ ] 权限分离，安全性提升
- [ ] trace_id 全链路追踪机制完整实现
- [ ] 所有模块日志包含 trace_id 字段
- [ ] 可通过 trace_id 查询完整调用链

## 6. 时间线总结

| 阶段     | 时间 | 主要内容           |
| -------- | ---- | ------------------ |
| 第一阶段 | 1 周 | 基础构建和通信协议 |
| 第二阶段 | 1 周 | 核心服务实现       |
| 第三阶段 | 1 周 | 渲染模块开发       |
| 第四阶段 | 5 天 | 集成测试和部署     |
| 第五阶段 | 2 天 | 优化和发布         |

**总计：22 个工作日**

## 7. 风险和缓解措施

| 风险                    | 影响 | 缓解措施                                     |
| ----------------------- | ---- | -------------------------------------------- |
| 模块间通信复杂度        | 高   | 统一使用 protocol 消息帧，提供完善的错误处理 |
| Windows 服务权限问题    | 中   | 严格的权限验证，最小权限原则                 |
| Agent 在 WSL 中的兼容性 | 中   | 充分测试不同 WSL 版本，提供降级方案          |
| 性能目标难以达成        | 低   | 模块化架构便于针对性优化                     |

---

_本计划基于四模块架构设计，强调模块独立性、职责清晰和通信规范，确保系统的可维护性和扩展性。_
