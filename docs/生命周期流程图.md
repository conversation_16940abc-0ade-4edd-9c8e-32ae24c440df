# EchoWave 客户端生命周期流程图

基于四模块架构的生命周期管理，展示模块间的协作和通信机制。

## 模块生命周期概览

```mermaid
graph TD
    subgraph "渲染模块生命周期"
        R1[Vue应用初始化] --> R2[Tauri启动]
        R2 --> R3[建立IPC通道]
        R3 --> R4[UI渲染就绪]
        R4 --> R5[响应用户操作]
        R5 --> R6[应用关闭]
    end
    
    subgraph "核心模块生命周期"
        C1[配置加载] --> C2[适配器初始化]
        C2 --> C3[服务创建]
        C3 --> C4[消息循环]
        C4 --> C5[处理命令]
        C5 --> C6[资源清理]
    end
    
    subgraph "Agent模块生命周期"
        A1[进程启动] --> A2[stdio建立]
        A2 --> A3[等待命令]
        A3 --> A4[执行操作]
        A4 --> A5[返回结果]
        A5 --> A3
    end
    
    subgraph "Windows服务生命周期"
        W1[服务启动] --> W2[Pipe监听]
        W2 --> W3[接收请求]
        W3 --> W4[权限操作]
        W4 --> W5[返回响应]
        W5 --> W3
    end
```

## 模块间通信生命周期

```mermaid
sequenceDiagram
    participant Desktop as 渲染模块
    participant Core as 核心模块
    participant Agent as Agent模块
    participant Helper as Windows服务
    
    Note over Desktop,Helper: 应用启动阶段
    Desktop->>Desktop: 初始化Vue应用
    Desktop->>Core: 创建核心模块线程
    Core->>Core: 加载配置
    Core->>Helper: 连接Windows服务
    Helper-->>Core: 服务状态
    Core->>Agent: 启动Agent进程
    Agent-->>Core: Agent就绪
    Core-->>Desktop: 核心模块就绪
    
    Note over Desktop,Helper: 系统检查阶段 (带trace_id)
    Desktop->>Desktop: 生成trace_id
    Desktop->>Core: invoke('check_system', {traceId})
    Core->>Core: 记录trace_id到日志
    Core->>Core: 本地检查
    Core->>Agent: SystemCheck事件 (trace_id)
    Core->>Helper: CheckVirtualization (trace_id)
    Agent-->>Core: 镜像状态 (trace_id)
    Helper-->>Core: 虚拟化状态 (trace_id)
    Core-->>Desktop: 检查结果 (trace_id)
    
    Note over Desktop,Helper: 任务接单阶段 (带trace_id)
    Desktop->>Desktop: 生成trace_id
    Desktop->>Core: invoke('start_accepting_tasks', {traceId})
    Core->>Agent: DaemonControl事件 (trace_id)
    Agent->>Agent: 启动服务进程
    Agent-->>Core: 服务就绪 (trace_id)
    Core->>Core: 注册节点
    Core->>Core: 开始轮询
    Core-->>Desktop: emit('task-status-changed')
    
    Note over Desktop,Helper: 应用关闭阶段
    Desktop->>Core: 关闭信号
    Core->>Agent: Shutdown事件 (trace_id)
    Agent->>Agent: 停止服务进程
    Agent-->>Core: 服务已停止
    Core->>Core: 清理资源
    Core-->>Desktop: 关闭完成
```

## 核心模块生命周期管理

核心模块的生命周期包含以下阶段：

1. **初始化阶段**
   - 加载应用配置
   - 初始化 Agent 和 Helper 适配器
   - 创建业务服务（系统检查、任务管理等）
   - 设置日志记录器（包含 trace_id 支持）

2. **运行阶段**
   - 启动消息循环，监听来自渲染模块的命令
   - 为每个命令创建 RequestContext（包含 trace_id）
   - 协调 Agent 和 Helper 模块完成操作
   - 发送响应和事件回渲染模块

3. **清理阶段**
   - 停止所有运行的任务
   - 关闭 Agent 进程
   - 断开 Helper 服务连接
   - 保存应用状态

## Agent 模块生命周期

Agent 模块作为 Linux 进程在 WSL 中运行：

1. **启动阶段**
   - 验证 Linux 环境（编译时检查）
   - 初始化进程管理器和数据收集器
   - 建立 stdio 通信通道
   - 配置日志输出（JSON 格式，包含 trace_id）

2. **消息处理循环**
   - 读取来自核心模块的消息帧
   - 解析事件类型和 trace_id
   - 创建日志 span 进行追踪
   - 执行相应操作（启动/停止守护进程、系统检查等）
   - 返回带有相同 trace_id 的响应

3. **退出处理**
   - 收到 Exit 事件码时优雅退出
   - 停止所有管理的服务进程
   - 清理临时资源

## Windows 服务生命周期

Windows 服务模块以系统服务方式运行：

1. **服务注册**
   - 注册服务控制处理器
   - 设置服务类型和接受的控制命令
   - 初始化日志系统（支持 trace_id）

2. **Named Pipe 服务器**
   - 创建命名管道监听连接
   - 为每个客户端连接创建独立处理任务
   - 解析请求中的 trace_id 并记录到日志
   - 执行需要管理员权限的操作

3. **服务停止**
   - 响应 Windows 服务管理器的停止命令
   - 关闭所有活动连接
   - 更新服务状态为已停止

## 错误处理和恢复

### 模块间错误传播

错误在模块间传播时保持 trace_id，便于问题追踪：

1. **错误分类**
   - 可恢复错误：网络超时、服务暂时不可用
   - 不可恢复错误：权限不足、系统不支持
   - 协议错误：消息格式错误、版本不匹配

2. **错误响应格式**
   - 包含错误码、错误消息、是否可恢复
   - 保留原始 trace_id 用于追踪
   - 记录到各模块日志

3. **恢复策略**
   - 可恢复错误自动重试（指数退避）
   - 不可恢复错误向用户展示明确信息
   - 协议错误触发模块重启

## 资源清理机制

### 自动资源管理

使用 RAII 模式确保资源正确释放：

1. **Agent 进程管理**
   - 使用智能指针包装子进程
   - Drop 时发送 Exit 事件码
   - 等待优雅退出，超时后强制终止

2. **Windows 服务连接**
   - Named Pipe 连接自动管理
   - 断开时清理相关资源

3. **日志和追踪资源**
   - 确保所有 trace_id 相关的 span 正确关闭
   - 刷新日志缓冲区到文件

## 前端生命周期 (Vue 3)

前端生命周期管理要点：

1. **组件生命周期**
   - `onMounted`: 建立事件监听，生成初始 trace_id
   - `onBeforeUnmount`: 清理监听器和未完成的请求
   - 使用 composables 封装生命周期逻辑

2. **追踪管理**
   - 每个用户操作生成新的 trace_id
   - 在控制台和开发工具中记录 trace_id
   - 错误时展示 trace_id 便于用户反馈

3. **关闭保护**
   - 检测是否有运行中的任务
   - 提示用户确认退出
   - 等待任务完成或强制退出

## 优雅关闭流程

```mermaid
sequenceDiagram
    participant User
    participant Desktop as 渲染模块
    participant Core as 核心模块
    participant Agent as Agent模块
    participant Helper as Windows服务
    
    User->>Desktop: 关闭窗口
    Desktop->>Desktop: 生成shutdown_trace_id
    Desktop->>Desktop: 检查运行任务
    
    alt 有运行任务
        Desktop->>User: 确认对话框
        User->>Desktop: 确认退出
    end
    
    Desktop->>Core: 发送关闭命令 (trace_id)
    Core->>Core: 记录关闭请求到日志
    
    Core->>Agent: Shutdown事件 (trace_id)
    Agent->>Agent: 停止所有进程
    Agent-->>Core: 服务已停止 (trace_id)
    
    Core->>Core: 保存状态
    Core->>Core: 清理资源
    Core->>Core: 最终日志刷新
    
    Core-->>Desktop: 关闭完成 (trace_id)
    Desktop->>Desktop: 退出应用
```

## 生命周期最佳实践

### 1. 模块启动顺序
- Windows 服务应在系统启动时自动运行
- 核心模块依赖 Windows 服务，需检查其状态
- Agent 模块由核心模块按需启动
- 渲染模块最后启动，确保后端就绪

### 2. 错误恢复策略
- 可恢复错误：自动重试或提示用户
- 不可恢复错误：记录日志并优雅降级
- 模块通信中断：尝试重新建立连接
- 关键服务失败：阻止继续操作

### 3. 资源管理原则
- 使用 RAII 模式管理所有资源
- 进程和连接使用智能指针包装
- 异步任务使用取消令牌
- 避免资源泄漏和僵尸进程

### 4. 性能优化
- 模块间通信使用二进制协议
- 批量操作减少往返次数
- 并行执行独立的检查项
- 缓存频繁访问的状态

### 5. 日志和追踪
- 每个用户操作生成唯一 trace_id
- 所有模块统一使用 JSON 格式日志
- 日志包含模块名、时间戳、trace_id
- 支持按 trace_id 查询完整调用链
- 错误日志包含堆栈和上下文信息

---

*本文档展示了基于四模块架构的生命周期管理方案，强调模块独立性、清晰的通信协议和健壮的错误处理。*