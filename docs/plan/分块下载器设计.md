# 分块断点续传下载器设计文档

## 概述
基于用户需求，设计一个支持分块下载、断点续传、可配置校验算法的高性能下载器。

## 核心设计原理

### 分块策略
```
自适应块大小策略：
- 小文件（<100MB）  → 16MB 块
- 中文件（100-500MB）→ 32MB 块  
- 大文件（500MB-2GB）→ 64MB 块
- 超大文件（>2GB）   → 128MB 块

示例：1GB 文件 → 按 64MB 分块 → 16 个块
块文件命名：{filename}.part.{块号}
元数据文件：{filename}.meta.json
```

### 下载流程
1. **初始化阶段**
   - 获取文件总大小
   - 计算分块数量和每块大小
   - 检查本地已下载的块

2. **并发下载阶段**
   - 并发下载缺失的块
   - 每块独立重试机制
   - 实时更新下载进度

3. **合并验证阶段**
   - 合并所有块为完整文件
   - 执行整体文件校验
   - 清理临时块文件

## 数据结构设计

### 下载元数据
```rust
#[derive(Serialize, Deserialize)]
pub struct DownloadMetadata {
    pub url: String,
    pub total_size: u64,
    pub chunk_size: u64,
    pub total_chunks: usize,
    pub checksum_algorithm: ChecksumAlgorithm,
    pub expected_checksum: String,
    pub completed_chunks: HashSet<usize>,
    pub created_at: DateTime<Utc>,
    pub last_updated: DateTime<Utc>,
}

#[derive(Serialize, Deserialize)]
pub enum ChecksumAlgorithm {
    MD5,
    SHA256,
}
```

### 块信息
```rust
#[derive(Debug, Clone)]
pub struct ChunkInfo {
    pub index: usize,
    pub start_byte: u64,
    pub end_byte: u64,
    pub size: u64,
    pub downloaded: bool,
    pub checksum: Option<String>, // 块级校验
}
```

### 下载配置
```rust
#[derive(Debug, Clone)]
pub struct DownloadConfig {
    /// 默认块大小（64MB）
    pub default_chunk_size: u64,
    /// 最小块大小（16MB）
    pub min_chunk_size: u64,
    /// 最大块大小（128MB）
    pub max_chunk_size: u64,
    /// 启用自适应块大小
    pub adaptive_chunk_sizing: bool,
    /// 最大并发下载数（默认 4）
    pub max_concurrent_chunks: usize,
    /// 校验算法
    pub checksum_algorithm: ChecksumAlgorithm,
    /// 单块重试次数
    pub chunk_retry_count: u32,
    /// 块下载超时（秒）
    pub chunk_timeout: u64,
}

impl Default for DownloadConfig {
    fn default() -> Self {
        Self {
            default_chunk_size: 64 * 1024 * 1024, // 64MB
            min_chunk_size: 16 * 1024 * 1024,     // 16MB
            max_chunk_size: 128 * 1024 * 1024,    // 128MB
            adaptive_chunk_sizing: true,
            max_concurrent_chunks: 4,
            checksum_algorithm: ChecksumAlgorithm::SHA256,
            chunk_retry_count: 3,
            chunk_timeout: 60,
        }
    }
}
```

## 核心算法

### 断点续传逻辑
```rust
impl ChunkedDownloader {
    /// 检查本地已下载的块
    pub async fn scan_existing_chunks(&self, metadata: &DownloadMetadata) -> CoreResult<Vec<usize>> {
        let mut completed_chunks = Vec::new();
        
        for chunk_index in 0..metadata.total_chunks {
            let chunk_path = self.get_chunk_path(chunk_index);
            
            if chunk_path.exists() {
                // 验证块文件大小和校验和
                if self.verify_chunk(chunk_index, metadata).await? {
                    completed_chunks.push(chunk_index);
                } else {
                    // 删除损坏的块文件
                    tokio::fs::remove_file(&chunk_path).await?;
                }
            }
        }
        
        Ok(completed_chunks)
    }
    
    /// 计算缺失的块
    pub fn get_missing_chunks(&self, metadata: &DownloadMetadata) -> Vec<usize> {
        (0..metadata.total_chunks)
            .filter(|i| !metadata.completed_chunks.contains(i))
            .collect()
    }
}
```

### 并发下载实现
```rust
impl ChunkedDownloader {
    /// 并发下载缺失的块
    pub async fn download_missing_chunks(
        &self,
        missing_chunks: Vec<usize>,
        metadata: &mut DownloadMetadata,
        progress_callback: Arc<dyn Fn(DownloadProgress) + Send + Sync>,
        cancellation_token: CancellationToken,
    ) -> CoreResult<()> {
        let semaphore = Arc::new(Semaphore::new(self.config.max_concurrent_chunks));
        let mut handles = Vec::new();
        
        for chunk_index in missing_chunks {
            let semaphore = semaphore.clone();
            let metadata = metadata.clone();
            let progress_callback = progress_callback.clone();
            let cancellation_token = cancellation_token.clone();
            let downloader = self.clone();
            
            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                
                downloader.download_single_chunk(
                    chunk_index,
                    &metadata,
                    progress_callback,
                    cancellation_token,
                ).await
            });
            
            handles.push(handle);
        }
        
        // 等待所有块下载完成
        for handle in handles {
            handle.await??;
        }
        
        Ok(())
    }
}
```

### 校验系统
```rust
impl ChecksumValidator {
    /// 计算文件校验和
    pub async fn compute_checksum(
        &self,
        file_path: &Path,
        algorithm: ChecksumAlgorithm,
    ) -> CoreResult<String> {
        match algorithm {
            ChecksumAlgorithm::MD5 => self.compute_md5(file_path).await,
            ChecksumAlgorithm::SHA256 => self.compute_sha256(file_path).await,
        }
    }
    
    /// 流式计算校验和（适合大文件）
    async fn compute_sha256(&self, file_path: &Path) -> CoreResult<String> {
        use sha2::{Sha256, Digest};
        
        let mut file = tokio::fs::File::open(file_path).await?;
        let mut hasher = Sha256::new();
        let mut buffer = vec![0; 8192]; // 8KB 缓冲区
        
        loop {
            let bytes_read = file.read(&mut buffer).await?;
            if bytes_read == 0 {
                break;
            }
            hasher.update(&buffer[..bytes_read]);
        }
        
        Ok(format!("{:x}", hasher.finalize()))
    }
}
```

## 进度追踪

### 进度结构
```rust
#[derive(Debug, Clone)]
pub struct DownloadProgress {
    pub total_chunks: usize,
    pub completed_chunks: usize,
    pub total_bytes: u64,
    pub downloaded_bytes: u64,
    pub current_speed: f64, // bytes/second
    pub eta_seconds: Option<u64>,
    pub active_chunks: Vec<ChunkProgress>,
}

#[derive(Debug, Clone)]
pub struct ChunkProgress {
    pub index: usize,
    pub downloaded_bytes: u64,
    pub total_bytes: u64,
    pub speed: f64,
}
```

## 错误处理和重试

### 块级重试策略
```rust
impl ChunkedDownloader {
    async fn download_single_chunk_with_retry(
        &self,
        chunk_index: usize,
        metadata: &DownloadMetadata,
    ) -> CoreResult<()> {
        let mut attempt = 0;
        let max_retries = self.config.chunk_retry_count;
        
        while attempt < max_retries {
            match self.download_single_chunk(chunk_index, metadata).await {
                Ok(_) => return Ok(()),
                Err(e) if attempt < max_retries - 1 => {
                    warn!("块 {} 下载失败，第 {} 次重试: {}", chunk_index, attempt + 1, e);
                    
                    // 指数退避
                    let delay = Duration::from_millis(1000 * 2_u64.pow(attempt));
                    tokio::time::sleep(delay).await;
                    
                    attempt += 1;
                }
                Err(e) => return Err(e),
            }
        }
        
        Err(CoreError::operation_failed(&format!(
            "块 {} 下载失败，已重试 {} 次", chunk_index, max_retries
        )))
    }
}
```

## 性能优化策略

### 1. 内存优化
- 流式处理大文件，避免全量加载到内存
- 使用固定大小的缓冲区读写数据
- 及时释放已完成块的内存

### 2. 网络优化
- HTTP Range 请求下载指定字节范围
- 连接池复用 HTTP 连接
- 适当的超时设置

### 3. 磁盘I/O优化
- 使用异步文件操作
- 批量写入减少系统调用
- 预分配文件空间

## 使用示例

```rust
// 创建下载器配置
let config = DownloadConfig {
    default_chunk_size: 64 * 1024 * 1024, // 64MB
    adaptive_chunk_sizing: true,
    max_concurrent_chunks: 4,
    checksum_algorithm: ChecksumAlgorithm::SHA256,
    chunk_retry_count: 3,
    chunk_timeout: 60,
    ..Default::default()
};

// 创建下载器
let downloader = ChunkedDownloader::new(config);

// 创建进度回调
let progress_callback = Arc::new(|progress: DownloadProgress| {
    println!("进度: {:.1}% ({}/{}块)", 
        progress.completed_chunks as f64 / progress.total_chunks as f64 * 100.0,
        progress.completed_chunks,
        progress.total_chunks
    );
});

// 开始下载
let result = downloader.download(
    "https://example.com/large-file.tar",
    "/tmp/large-file.tar",
    "sha256_checksum_here",
    progress_callback,
    cancellation_token,
).await;
```

## 与现有系统集成

### 在 SystemCheckService 中的使用
```rust
impl SystemCheckService {
    async fn download_wsl_mirror(&self) -> CoreResult<()> {
        let config = self.get_download_config();
        let downloader = ChunkedDownloader::new(config);
        
        // 更新下载状态
        let progress_callback = {
            let state_sender = self.state.clone();
            Arc::new(move |progress: DownloadProgress| {
                state_sender.send_modify(|state| {
                    state.wsl_mirror_status.download_progress = 
                        (progress.completed_chunks as f64 / progress.total_chunks as f64 * 100.0) as u8;
                });
            })
        };
        
        downloader.download(
            &self.config.mirror_url,
            &self.get_mirror_cache_path(),
            &self.config.mirror_checksum,
            progress_callback,
            self.signal.clone(),
        ).await
    }
}
```

这个设计文档为实现高性能的分块断点续传下载器提供了完整的技术方案。