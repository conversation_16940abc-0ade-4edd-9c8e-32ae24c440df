# 步骤1快速开始指南

## 立即开始（5分钟设置）

### 1. 环境验证
```bash
# 确保在项目根目录
cd /Users/<USER>/work/echowave-windows-client

# 验证项目编译
cargo check --workspace

# 如果有错误，先解决编译问题
cargo clean && cargo check --workspace
```

### 2. 查看现有基础
```bash
# 查看现有事件定义
ls -la crates/protocol/src/events/
cat crates/protocol/src/events/agent.rs
cat crates/protocol/src/events/helper_svc.rs

# 查看消息帧协议（已完成）
cat crates/protocol/src/frame.rs | head -50
```

### 3. 运行现有测试
```bash
# 测试协议模块
cargo test -p protocol

# 查看测试覆盖情况
cargo test -p protocol -- --nocapture
```

## Day 1 立即开始任务

### 任务1：分析现有事件定义（30分钟）

**第一步：查看 Agent 事件**
```bash
# 打开 agent 事件文件
code crates/protocol/src/events/agent.rs
```

**需要检查的内容：**
- [ ] 现有事件类型有哪些？
- [ ] 是否包含 trace_id 字段？
- [ ] 是否支持系统检查、守护进程控制、数据收集？
- [ ] 响应事件是否完整？

**第二步：查看 Helper 事件**
```bash
# 打开 helper 事件文件  
code crates/protocol/src/events/helper_svc.rs
```

**需要检查的内容：**
- [ ] Windows 功能管理事件是否存在？
- [ ] 虚拟化检查事件是否定义？
- [ ] 更新安装事件是否完整？
- [ ] 响应格式是否统一？

### 任务2：完善 Agent 事件定义（2小时）

**创建完整的 Agent 事件定义：**

```rust
// 在 crates/protocol/src/events/agent.rs 中添加或修改

use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AgentEvent {
    /// 系统检查事件
    SystemCheck {
        trace_id: Uuid,
        check_type: SystemCheckType,
    },
    /// 守护进程控制事件
    DaemonControl {
        trace_id: Uuid,
        action: DaemonAction,
        daemon: String,
    },
    /// 数据收集事件
    DataCollection {
        trace_id: Uuid,
        collection_type: DataCollectionType,
    },
    /// 优雅关闭事件
    Shutdown {
        trace_id: Uuid,
        graceful: bool,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SystemCheckType {
    All,
    MirrorStatus,
    ServiceStatus,
    NetworkConnectivity,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DaemonAction {
    Start,
    Stop,
    Restart,
    Status,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataCollectionType {
    SystemInfo,
    ServiceLogs,
    PerformanceMetrics,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EventResponse {
    Success {
        trace_id: Uuid,
        data: Option<serde_json::Value>,
    },
    Error {
        trace_id: Uuid,
        code: u32,
        message: String,
    },
    SystemCheckResult {
        trace_id: Uuid,
        results: Vec<CheckResult>,
    },
    DaemonStatus {
        trace_id: Uuid,
        daemon: String,
        status: DaemonStatus,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CheckResult {
    pub check_name: String,
    pub status: CheckStatus,
    pub message: Option<String>,
    pub details: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CheckStatus {
    Pass,
    Fail,
    Warning,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DaemonStatus {
    Running,
    Stopped,
    Failed,
    Unknown,
}
```

**验证步骤：**
```bash
# 编译检查
cargo check -p protocol

# 运行测试
cargo test -p protocol

# 如果需要，添加测试
cargo test -p protocol -- --nocapture
```

### 任务3：完善 Helper 事件定义（2小时）

**创建完整的 Helper 事件定义：**

```rust
// 在 crates/protocol/src/events/helper_svc.rs 中添加或修改

use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HelperEvent {
    /// 检查 Windows 功能状态
    CheckWindowsFeature {
        trace_id: Uuid,
        feature_name: String,
    },
    /// 启用 Windows 功能
    EnableWindowsFeature {
        trace_id: Uuid,
        feature_name: String,
    },
    /// 检查虚拟化支持
    CheckVirtualization {
        trace_id: Uuid,
    },
    /// 安装更新
    InstallUpdate {
        trace_id: Uuid,
        update_path: String,
    },
    /// 重启系统
    RestartSystem {
        trace_id: Uuid,
        delay_seconds: u32,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HelperResponse {
    Success {
        trace_id: Uuid,
        data: Option<serde_json::Value>,
    },
    Error {
        trace_id: Uuid,
        code: u32,
        message: String,
    },
    WindowsFeatureStatus {
        trace_id: Uuid,
        feature_name: String,
        status: FeatureStatus,
    },
    VirtualizationInfo {
        trace_id: Uuid,
        supported: bool,
        enabled: bool,
        details: VirtualizationDetails,
    },
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FeatureStatus {
    Enabled,
    Disabled,
    Unknown,
    NotAvailable,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VirtualizationDetails {
    pub hyper_v_available: bool,
    pub wsl_available: bool,
    pub virtualization_enabled_in_firmware: bool,
    pub data_execution_prevention_available: bool,
}
```

## Day 1 结束检查

### 完成验证
```bash
# 1. 编译检查
cargo check --workspace

# 2. 运行所有测试
cargo test --workspace

# 3. 代码格式化
cargo fmt --all

# 4. 代码质量检查
cargo clippy --workspace -- -D warnings
```

### 预期结果
- [ ] 所有事件定义包含 trace_id 字段
- [ ] Agent 事件支持系统检查、守护进程控制、数据收集
- [ ] Helper 事件支持 Windows 功能管理、虚拟化检查、更新安装
- [ ] 所有事件可以正确序列化为 JSON
- [ ] 编译无错误，测试全部通过

### 如果遇到问题

**编译错误：**
```bash
# 查看详细错误信息
cargo check --workspace --verbose

# 清理并重新编译
cargo clean && cargo check --workspace
```

**测试失败：**
```bash
# 查看测试详情
cargo test --workspace -- --nocapture

# 运行特定测试
cargo test -p protocol test_name
```

**依赖问题：**
```bash
# 更新依赖
cargo update

# 检查依赖树
cargo tree
```

## 下一步预览

Day 2 将开始：
1. 建立 trace_id 传递机制
2. 实现统一日志系统
3. 创建追踪 span 机制

确保 Day 1 的所有任务完成后再继续，这是后续工作的重要基础。

---

*这个快速开始指南帮助您立即开始步骤1的工作，重点是完善事件定义系统。*
