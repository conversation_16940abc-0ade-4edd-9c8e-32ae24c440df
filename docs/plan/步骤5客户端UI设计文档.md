# 步骤5：EchoWave 客户端UI设计文档

## 设计概述

### 设计理念
EchoWave客户端采用现代化、简洁的设计语言，以深色主题为主，体现科技感和专业性。界面设计遵循"信息优先、操作简化"的原则，确保用户能够快速了解系统状态并进行高效操作。

### 设计原则
1. **信息层次清晰**：重要信息突出显示，次要信息适当弱化
2. **状态反馈及时**：所有操作和状态变化都有明确的视觉反馈
3. **操作流程简化**：减少用户操作步骤，提供智能化功能
4. **视觉一致性**：统一的色彩、字体、间距和交互规范
5. **响应式设计**：适配不同屏幕尺寸和分辨率

### 技术规范
- **框架**：Vue 3 + Tauri
- **UI库**：基于现有的组件库进行定制
- **主题**：深色主题为主，支持动画效果控制
- **字体**：系统默认字体 + 等宽字体（数据显示）
- **图标**：统一的图标库，支持多种状态

## 整体布局设计

### 应用窗口规格
- **默认尺寸**：1200px × 800px
- **最小尺寸**：800px × 600px
- **窗口特性**：可调整大小、可最小化到托盘、无边框设计

### 主要布局区域
```
┌─────────────────────────────────────────────────────┐
│                    Header 区域                      │
│  Logo + 网络状态 + 钱包信息 + 用户头像              │
├─────────────────────────────────────────────────────┤
│                                                     │
│                  Main Content 区域                  │
│                                                     │
│  ┌─────────────────┐  ┌─────────────────────────┐   │
│  │   系统检查区域   │  │      任务控制区域        │   │
│  │                │  │                        │   │
│  │  ✓ 平台检查     │  │   [开始接单] 按钮       │   │
│  │  ✓ 系统检查     │  │                        │   │
│  │  ✓ PowerShell  │  │   当前状态显示          │   │
│  │  ✓ 虚拟化       │  │                        │   │
│  │  ✓ WSL         │  │   自动接单开关          │   │
│  │  ✓ 镜像        │  │                        │   │
│  └─────────────────┘  └─────────────────────────┘   │
│                                                     │
│  ┌─────────────────────────────────────────────────┐ │
│  │              设置和状态信息区域                  │ │
│  │  开机自启 | 动画效果 | 设备信息 | 版本信息      │ │
│  └─────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────┘
```

## 详细页面设计

### 1. Header 区域设计

#### 1.1 布局结构
```
┌─────────────────────────────────────────────────────┐
│ [Logo] [网络状态指示器] [空白区域] [钱包] [用户头像] │
└─────────────────────────────────────────────────────┘
```

#### 1.2 组件详细设计

**Logo区域**
- EchoWave 品牌标识
- 尺寸：120px × 32px
- 颜色：品牌蓝色 (#2196F3)

**网络状态指示器**
- Tailscale连接状态：绿色圆点 + 延迟数值
- 互联网连接状态：信号强度图标
- 状态文本：如"已连接 (12ms)" 或"连接中..."
- 颜色：绿色(正常) / 黄色(警告) / 红色(错误)

**钱包信息**
- 余额显示：大号等宽字体
- 格式：¥ 123.45
- 提现按钮：小型按钮，hover时高亮
- 刷新指示器：余额更新时显示

**用户头像**
- 圆形头像，32px × 32px
- 默认头像：用户名首字母
- 在线状态指示：右下角小绿点

### 2. 系统检查区域设计

#### 2.1 整体布局
```
┌─────────────────────────────────────┐
│          系统环境检查 (6/6)          │
├─────────────────────────────────────┤
│ ✓ 平台检查        Windows 11       │
│ ✓ 操作系统检查     版本兼容          │
│ ✓ PowerShell     v7.3.0 已安装     │
│ ✓ 虚拟化支持      已启用            │
│ ✓ WSL 环境       v2.0.0 运行中      │
│ ✓ 任务引擎镜像    v1.2.3 已安装     │
├─────────────────────────────────────┤
│              [重新检查]              │
└─────────────────────────────────────┘
```

#### 2.2 状态指示设计
- **成功状态**：绿色 ✓ 图标 + 绿色文字
- **进行中状态**：蓝色 ⟳ 旋转图标 + 蓝色文字
- **错误状态**：红色 ✗ 图标 + 红色文字 + 错误描述
- **警告状态**：黄色 ⚠ 图标 + 黄色文字 + 警告信息

#### 2.3 交互设计
- 点击检查项：显示详细信息弹窗
- 重新检查按钮：触发全部检查，显示进度
- 自动检查：每小时自动执行一次

### 3. 任务控制区域设计

#### 3.1 主要状态布局

**待机状态**
```
┌─────────────────────────────────────┐
│            任务控制中心              │
├─────────────────────────────────────┤
│                                    │
│         [开始接单]                  │
│       (大型主要按钮)                │
│                                    │
│    ○ 自动接单  [开关]               │
│                                    │
│    当前状态：等待接单                │
│    系统就绪：✓ 所有检查通过          │
│                                    │
└─────────────────────────────────────┘
```

**接单中状态**
```
┌─────────────────────────────────────┐
│            任务控制中心              │
├─────────────────────────────────────┤
│                                    │
│         [暂停接单]                  │
│       (大型警告按钮)                │
│                                    │
│    ● 自动接单  [开关]               │
│                                    │
│    当前状态：正在接单中              │
│    运行时间：02:34:15               │
│                                    │
└─────────────────────────────────────┘
```

#### 3.2 按钮设计规范
- **开始接单按钮**：绿色渐变，圆角，阴影效果
- **暂停接单按钮**：橙色渐变，圆角，阴影效果
- **禁用状态**：灰色，降低透明度，显示禁用原因
- **加载状态**：按钮内显示旋转图标

#### 3.3 状态信息设计
- **当前状态**：大号字体，状态颜色
- **运行时间**：等宽字体，实时更新
- **自动接单开关**：现代化开关组件，状态同步

### 4. 设置和状态信息区域

#### 4.1 布局设计
```
┌─────────────────────────────────────────────────────┐
│  [开机自启] [动画效果]    设备号：EW-WIN-001234      │
│                         版本：v2.0.0 (最新)        │
└─────────────────────────────────────────────────────┘
```

#### 4.2 组件设计
- **开关组件**：现代化滑动开关，即时保存
- **设备信息**：等宽字体，可复制
- **版本信息**：显示当前版本，更新状态

## 登录页面设计

### 页面布局
```
┌─────────────────────────────────────────────────────┐
│                                                     │
│                  [EchoWave Logo]                    │
│                                                     │
│              ┌─────────────────────┐                │
│              │                     │                │
│              │   手机号：[_______]  │                │
│              │                     │                │
│              │   密码：  [_______]  │                │
│              │                     │                │
│              │   ☐ 记住登录        │                │
│              │                     │                │
│              │      [登录]         │                │
│              │                     │                │
│              └─────────────────────┘                │
│                                                     │
│                  忘记密码？ | 注册账号                │
│                                                     │
└─────────────────────────────────────────────────────┘
```

### 设计特点
- **居中布局**：登录表单居中显示
- **简洁设计**：最小化干扰元素
- **状态反馈**：登录过程显示加载状态
- **错误处理**：友好的错误信息显示

## 托盘图标设计

### 图标状态
1. **正常状态**：蓝色 EchoWave 图标
2. **接单中状态**：绿色图标 + 小动画
3. **错误状态**：红色图标
4. **离线状态**：灰色图标

### 托盘菜单
```
┌─────────────────┐
│ 显示窗口        │
│ 隐藏窗口        │
├─────────────────┤
│ 检查更新        │
├─────────────────┤
│ 退出            │
└─────────────────┘
```

## 色彩规范

### 主色调
- **主要蓝色**：#2196F3 (品牌色)
- **成功绿色**：#4CAF50 (成功状态)
- **警告橙色**：#FF9800 (警告状态)
- **错误红色**：#F44336 (错误状态)
- **中性灰色**：#9E9E9E (次要信息)

### 背景色
- **主背景**：#1E1E1E (深色主题)
- **卡片背景**：#2D2D2D (组件背景)
- **输入框背景**：#3D3D3D (表单元素)
- **悬停背景**：#404040 (交互反馈)

### 文字色
- **主要文字**：#FFFFFF (重要信息)
- **次要文字**：#B0B0B0 (说明文字)
- **链接文字**：#64B5F6 (可点击元素)
- **禁用文字**：#666666 (禁用状态)

## 动画和交互

### 动画规范
- **过渡时间**：0.3s (标准过渡)
- **缓动函数**：ease-in-out (自然感觉)
- **加载动画**：旋转、脉冲效果
- **状态变化**：平滑的颜色和大小变化

### 交互反馈
- **按钮悬停**：轻微放大 + 阴影增强
- **按钮点击**：轻微缩小 + 涟漪效果
- **开关切换**：平滑滑动动画
- **状态更新**：淡入淡出效果

### 焦点状态控制
- **窗口失焦**：暂停非必要动画
- **窗口获焦**：恢复动画 + 刷新关键数据
- **性能优化**：根据焦点状态调整刷新频率

## 响应式设计

### 断点设置
- **大屏幕**：> 1200px (默认布局)
- **中等屏幕**：800px - 1200px (紧凑布局)
- **小屏幕**：< 800px (垂直堆叠)

### 适配策略
- **组件缩放**：保持比例，调整间距
- **布局重排**：小屏幕时垂直排列
- **字体调整**：根据屏幕大小调整字号
- **触摸优化**：增大点击区域

---

*本UI设计文档基于现代化设计理念，结合EchoWave的品牌特色和用户需求，为步骤5的UI实现提供详细的视觉和交互指导。*
