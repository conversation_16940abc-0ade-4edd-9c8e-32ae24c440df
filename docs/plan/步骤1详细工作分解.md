# 步骤1详细工作分解：基础架构完善

## 超深度思考（Ultrathink）分析

### 现状评估
基于项目现有基础，发现：
- ✅ **Cargo workspace** 已完整配置
- ✅ **消息帧协议** 已实现且功能完整（90%完成）
- ✅ **四模块目录结构** 已建立
- ✅ **基础依赖** 已在 workspace 级别统一管理
- ⚠️ **事件定义** 框架存在但需要完善业务事件
- ⚠️ **日志追踪系统** 需要建立 trace_id 机制
- ⚠️ **核心模块架构** 需要建立适配器层和服务层

### 风险重新评估
- **通信协议风险**：从高风险降为低风险（协议已实现）
- **项目结构风险**：已消除（结构已建立）
- **新增风险**：现有 agent 代码可能需要重构以符合新架构

### 步骤1重新定义目标
1. 完善业务事件定义系统
2. 建立统一的日志和 trace_id 追踪机制
3. 实现核心模块的适配器层框架
4. 验证和完善现有通信协议
5. 建立基础的错误处理框架

## Day 1: 事件定义系统完善

### 任务1.1：分析现有事件定义（2小时）
**目标**：理解现有事件结构，识别需要补充的业务事件

**具体工作**：
- [ ] 审查 `crates/protocol/src/events/agent.rs` 现有定义
- [ ] 审查 `crates/protocol/src/events/helper_svc.rs` 现有定义
- [ ] 对比 docs 中的业务需求，识别缺失的事件
- [ ] 分析事件的序列化和反序列化需求

**输出物**：
- 现有事件定义分析报告
- 缺失事件清单

### 任务1.2：完善 Agent 事件定义（3小时）
**目标**：实现完整的 Agent 模块业务事件

**具体工作**：
- [ ] 实现 `AgentEvent` 枚举，包含：
  ```rust
  pub enum AgentEvent {
      // 系统检查事件
      SystemCheck {
          trace_id: Uuid,
          check_type: SystemCheckType,
      },
      // 守护进程控制事件
      DaemonControl {
          trace_id: Uuid,
          action: DaemonAction,
          daemon: String,
      },
      // 数据收集事件
      DataCollection {
          trace_id: Uuid,
          collection_type: DataCollectionType,
      },
      // 优雅关闭事件
      Shutdown {
          trace_id: Uuid,
          graceful: bool,
      },
  }
  ```

- [ ] 实现 `EventResponse` 枚举，包含：
  ```rust
  pub enum EventResponse {
      Success {
          trace_id: Uuid,
          data: Option<serde_json::Value>,
      },
      Error {
          trace_id: Uuid,
          code: u32,
          message: String,
      },
      SystemCheckResult {
          trace_id: Uuid,
          results: Vec<CheckResult>,
      },
      DaemonStatus {
          trace_id: Uuid,
          daemon: String,
          status: DaemonStatus,
      },
  }
  ```

- [ ] 添加相关的辅助类型定义
- [ ] 编写事件序列化测试

**输出物**：
- 完整的 Agent 事件定义
- 事件序列化测试用例

### 任务1.3：完善 Helper 服务事件定义（3小时）
**目标**：实现完整的 Windows 服务模块业务事件

**具体工作**：
- [ ] 实现 `HelperEvent` 枚举，包含：
  ```rust
  pub enum HelperEvent {
      // 检查 Windows 功能
      CheckWindowsFeature {
          trace_id: Uuid,
          feature_name: String,
      },
      // 启用 Windows 功能
      EnableWindowsFeature {
          trace_id: Uuid,
          feature_name: String,
      },
      // 检查虚拟化支持
      CheckVirtualization {
          trace_id: Uuid,
      },
      // 安装更新
      InstallUpdate {
          trace_id: Uuid,
          update_path: String,
      },
  }
  ```

- [ ] 实现 `HelperResponse` 枚举
- [ ] 添加 Windows 功能状态和虚拟化检查结果类型
- [ ] 编写事件序列化测试

**输出物**：
- 完整的 Helper 服务事件定义
- 事件序列化测试用例

## Day 2: 日志和追踪系统建立

### 任务2.1：设计 trace_id 传递机制（2小时）
**目标**：设计统一的 trace_id 生成和传递机制

**具体工作**：
- [ ] 在 `crates/core` 中创建 `src/tracing.rs`
- [ ] 实现 `RequestContext` 结构：
  ```rust
  pub struct RequestContext {
      pub trace_id: Uuid,
      pub start_time: Instant,
      pub module: String,
  }
  
  impl RequestContext {
      pub fn new(trace_id: Uuid, module: &str) -> Self;
      pub fn elapsed(&self) -> Duration;
  }
  ```

- [ ] 实现 trace_id 生成工具函数
- [ ] 设计事件中 trace_id 的标准化传递方式

**输出物**：
- RequestContext 实现
- trace_id 生成和传递规范

### 任务2.2：建立统一日志系统（3小时）
**目标**：为所有模块建立统一的日志配置

**具体工作**：
- [ ] 在 `crates/core` 中创建 `src/logging.rs`
- [ ] 实现统一的日志初始化函数：
  ```rust
  pub fn init_logging(module_name: &str, log_level: &str) -> Result<()> {
      tracing_subscriber::registry()
          .with(fmt::layer()
              .with_target(true)
              .with_thread_ids(true)
              .with_file(true)
              .with_line_number(true)
              .json()
              .with_current_span(true)
          )
          .with(EnvFilter::new(log_level))
          .init();
      
      info!(module = module_name, "Logger initialized");
      Ok(())
  }
  ```

- [ ] 实现带 trace_id 的日志宏封装
- [ ] 为每个模块创建日志配置
- [ ] 测试日志输出格式和 trace_id 包含

**输出物**：
- 统一日志系统实现
- 各模块日志配置
- 日志格式验证测试

### 任务2.3：实现追踪 span 机制（3小时）
**目标**：实现跨模块的追踪 span 机制

**具体工作**：
- [ ] 实现 `TracingSpan` 工具：
  ```rust
  pub struct TracingSpan {
      span: tracing::Span,
      context: RequestContext,
  }
  
  impl TracingSpan {
      pub fn new(trace_id: Uuid, operation: &str, module: &str) -> Self;
      pub fn enter(&self) -> tracing::span::Entered<'_>;
      pub fn record_success(&self);
      pub fn record_error(&self, error: &dyn std::error::Error);
  }
  ```

- [ ] 实现自动 span 管理的过程宏（可选）
- [ ] 为适配器层集成 span 追踪
- [ ] 测试 span 的创建、进入和记录

**输出物**：
- TracingSpan 实现
- 适配器层 span 集成
- 追踪机制测试用例

## Day 3: 核心模块适配器层框架

### 任务3.1：设计适配器层架构（2小时）
**目标**：设计统一的适配器层架构和接口

**具体工作**：
- [ ] 在 `crates/core` 中创建 `src/adapters/mod.rs`
- [ ] 设计适配器 trait：
  ```rust
  #[async_trait]
  pub trait ModuleAdapter {
      type Event;
      type Response;
      type Error;
      
      async fn send_event(&mut self, event: Self::Event, ctx: &RequestContext) 
          -> Result<Self::Response, Self::Error>;
      async fn is_connected(&self) -> bool;
      async fn reconnect(&mut self) -> Result<(), Self::Error>;
  }
  ```

- [ ] 设计错误处理和重试机制
- [ ] 定义适配器配置结构

**输出物**：
- 适配器层架构设计
- 统一适配器接口定义

### 任务3.2：实现 Agent 适配器框架（3小时）
**目标**：实现与 Agent 模块通信的适配器

**具体工作**：
- [ ] 创建 `src/adapters/agent.rs`
- [ ] 实现 `AgentAdapter` 结构：
  ```rust
  pub struct AgentAdapter {
      process: Option<Child>,
      stdin: Option<ChildStdin>,
      stdout: Option<BufReader<ChildStdout>>,
      operation_counter: AtomicU32,
      config: AgentConfig,
  }
  
  impl AgentAdapter {
      pub fn new(config: AgentConfig) -> Self;
      pub async fn start(&mut self) -> Result<()>;
      pub async fn stop(&mut self) -> Result<()>;
      async fn send_event_internal(&mut self, event: AgentEvent) -> Result<EventResponse>;
  }
  ```

- [ ] 实现 stdio 通信逻辑
- [ ] 集成消息帧协议
- [ ] 实现操作ID管理和请求响应匹配
- [ ] 添加连接状态检查和重连机制

**输出物**：
- AgentAdapter 完整实现
- stdio 通信测试用例

### 任务3.3：实现 Helper 适配器框架（3小时）
**目标**：实现与 Windows 服务模块通信的适配器

**具体工作**：
- [ ] 创建 `src/adapters/helper.rs`
- [ ] 实现 `HelperAdapter` 结构：
  ```rust
  pub struct HelperAdapter {
      pipe_name: String,
      operation_counter: AtomicU32,
      config: HelperConfig,
  }
  
  impl HelperAdapter {
      pub fn new(config: HelperConfig) -> Self;
      async fn connect(&self) -> Result<NamedPipeClient>;
      async fn send_event_internal(&self, event: HelperEvent) -> Result<HelperResponse>;
  }
  ```

- [ ] 实现 Named Pipe 通信逻辑
- [ ] 集成消息帧协议
- [ ] 实现认证和安全机制
- [ ] 添加连接管理和错误处理

**输出物**：
- HelperAdapter 完整实现
- Named Pipe 通信测试用例

## Day 4: HTTP 和文件适配器实现

### 任务4.1：实现 HTTP 适配器（4小时）
**目标**：实现与服务端通信的 HTTP 适配器

**具体工作**：
- [ ] 创建 `src/adapters/http.rs`
- [ ] 实现 `HttpAdapter` 结构：
  ```rust
  pub struct HttpAdapter {
      client: reqwest::Client,
      base_url: String,
      auth_token: Option<String>,
      config: HttpConfig,
  }
  
  impl HttpAdapter {
      pub fn new(config: HttpConfig) -> Self;
      pub async fn get<T: DeserializeOwned>(&self, path: &str, ctx: &RequestContext) -> Result<T>;
      pub async fn post<T: Serialize, R: DeserializeOwned>(&self, path: &str, body: &T, ctx: &RequestContext) -> Result<R>;
      pub async fn put<T: Serialize, R: DeserializeOwned>(&self, path: &str, body: &T, ctx: &RequestContext) -> Result<R>;
  }
  ```

- [ ] 实现认证机制（Bearer Token）
- [ ] 集成 trace_id 到 HTTP 头部
- [ ] 实现重试和超时机制
- [ ] 添加请求和响应日志记录

**输出物**：
- HttpAdapter 完整实现
- HTTP 通信测试用例

### 任务4.2：实现文件适配器（4小时）
**目标**：实现本地文件操作的适配器

**具体工作**：
- [ ] 创建 `src/adapters/file.rs`
- [ ] 实现 `FileAdapter` 结构：
  ```rust
  pub struct FileAdapter {
      base_path: PathBuf,
      config: FileConfig,
  }
  
  impl FileAdapter {
      pub fn new(config: FileConfig) -> Self;
      pub async fn read_json<T: DeserializeOwned>(&self, path: &Path, ctx: &RequestContext) -> Result<T>;
      pub async fn write_json<T: Serialize>(&self, path: &Path, data: &T, ctx: &RequestContext) -> Result<()>;
      pub async fn read_text(&self, path: &Path, ctx: &RequestContext) -> Result<String>;
      pub async fn write_text(&self, path: &Path, content: &str, ctx: &RequestContext) -> Result<()>;
      pub async fn exists(&self, path: &Path) -> bool;
      pub async fn create_dir_all(&self, path: &Path, ctx: &RequestContext) -> Result<()>;
  }
  ```

- [ ] 实现安全的路径验证
- [ ] 添加文件操作日志记录
- [ ] 实现原子写入机制
- [ ] 添加备份和恢复功能

**输出物**：
- FileAdapter 完整实现
- 文件操作测试用例

## Day 5: 协议验证和错误处理框架

### 任务5.1：协议完整性验证（3小时）
**目标**：验证现有消息帧协议满足所有业务需求

**具体工作**：
- [ ] 测试所有新定义事件的序列化和反序列化
- [ ] 验证大数据包的压缩和解压缩
- [ ] 测试并发场景下的协议稳定性
- [ ] 验证错误场景下的协议恢复能力
- [ ] 性能基准测试（延迟、吞吐量）

**输出物**：
- 协议完整性测试报告
- 性能基准测试结果
- 协议优化建议（如有）

### 任务5.2：建立错误处理框架（3小时）
**目标**：建立统一的错误处理和传播机制

**具体工作**：
- [ ] 在 `crates/core` 中创建 `src/error.rs`
- [ ] 实现统一的错误类型：
  ```rust
  #[derive(Debug, thiserror::Error)]
  pub enum CoreError {
      #[error("Agent communication failed: {0}")]
      AgentError(#[from] AgentError),
      
      #[error("Helper service error: {0}")]
      HelperError(#[from] HelperError),
      
      #[error("HTTP request failed: {0}")]
      HttpError(#[from] HttpError),
      
      #[error("File operation failed: {0}")]
      FileError(#[from] FileError),
      
      #[error("Configuration error: {0}")]
      ConfigError(String),
  }
  ```

- [ ] 实现错误上下文保持（包含 trace_id）
- [ ] 实现错误恢复策略
- [ ] 添加错误分类和可恢复性标记

**输出物**：
- 统一错误处理框架
- 错误恢复策略实现

### 任务5.3：集成测试和文档（2小时）
**目标**：进行适配器层集成测试并完善文档

**具体工作**：
- [ ] 编写适配器层集成测试
- [ ] 测试 trace_id 在适配器间的传递
- [ ] 测试错误处理和恢复机制
- [ ] 编写适配器使用文档和示例
- [ ] 更新项目 README 和架构文档

**输出物**：
- 适配器层集成测试套件
- 完整的使用文档和示例

## 验收标准

### 功能验收
- [ ] 所有业务事件定义完整且可序列化
- [ ] trace_id 机制在所有模块间正常工作
- [ ] 四个适配器（Agent、Helper、HTTP、File）功能完整
- [ ] 消息帧协议支持所有业务场景
- [ ] 错误处理框架覆盖所有错误类型

### 性能验收
- [ ] 消息帧协议延迟 < 5ms
- [ ] 适配器连接建立时间 < 1秒
- [ ] 日志系统性能开销 < 5%
- [ ] 内存占用增长 < 10MB

### 质量验收
- [ ] 代码覆盖率 > 80%
- [ ] 所有公共 API 有文档
- [ ] 集成测试通过率 100%
- [ ] 静态分析无警告

## 风险缓解措施

### 技术风险
- **现有 Agent 代码冲突**：先分析现有代码，制定重构计划
- **Windows 权限问题**：在虚拟机中测试 Helper 适配器
- **性能不达标**：预留性能优化时间，建立基准测试

### 进度风险
- **任务复杂度超预期**：每日进度检查，及时调整任务范围
- **依赖阻塞**：并行开发，减少任务间依赖

## 实施检查清单

### Day 1 检查清单
- [ ] **上午**：完成现有事件定义分析
  - [ ] 运行 `cargo check` 确保项目编译通过
  - [ ] 查看 `crates/protocol/src/events/` 下所有文件
  - [ ] 记录现有事件类型和缺失的业务事件

- [ ] **下午**：完成 Agent 事件定义
  - [ ] 在 `crates/protocol/src/events/agent.rs` 中添加完整事件定义
  - [ ] 运行 `cargo test -p protocol` 确保测试通过
  - [ ] 验证事件的 JSON 序列化输出格式

### Day 2 检查清单
- [ ] **上午**：完成 Helper 事件定义和 trace_id 机制
  - [ ] 完善 `crates/protocol/src/events/helper_svc.rs`
  - [ ] 在 `crates/core/src/` 中创建 `tracing.rs` 和 `logging.rs`
  - [ ] 运行基础编译检查

- [ ] **下午**：完成日志系统集成
  - [ ] 为每个模块添加日志初始化
  - [ ] 测试 JSON 格式日志输出
  - [ ] 验证 trace_id 在日志中正确显示

### Day 3 检查清单
- [ ] **上午**：完成适配器架构设计
  - [ ] 创建 `crates/core/src/adapters/` 目录结构
  - [ ] 实现统一的适配器 trait
  - [ ] 运行 `cargo check -p core` 验证编译

- [ ] **下午**：完成 Agent 适配器实现
  - [ ] 实现 `AgentAdapter` 结构和方法
  - [ ] 集成消息帧协议
  - [ ] 编写基础单元测试

### Day 4 检查清单
- [ ] **上午**：完成 Helper 适配器
  - [ ] 实现 `HelperAdapter` 结构
  - [ ] 测试 Named Pipe 连接（需要 Windows 环境）
  - [ ] 验证消息帧协议集成

- [ ] **下午**：完成 HTTP 和文件适配器
  - [ ] 实现 `HttpAdapter` 和 `FileAdapter`
  - [ ] 编写适配器测试用例
  - [ ] 运行完整的适配器测试套件

### Day 5 检查清单
- [ ] **上午**：协议验证和性能测试
  - [ ] 运行所有协议相关测试
  - [ ] 执行性能基准测试
  - [ ] 验证大数据包处理能力

- [ ] **下午**：错误处理和文档完善
  - [ ] 实现统一错误处理框架
  - [ ] 编写使用文档和示例
  - [ ] 运行完整的集成测试

### 每日结束检查
- [ ] 运行 `cargo test --workspace` 确保所有测试通过
- [ ] 运行 `cargo clippy --workspace` 确保代码质量
- [ ] 提交代码并记录当日完成的功能
- [ ] 更新进度和遇到的问题

## 开发环境准备

### 必需工具
```bash
# 安装 Rust 工具链
rustup update stable
rustup component add clippy rustfmt

# 安装开发工具
cargo install cargo-watch cargo-nextest

# 验证环境
cargo --version
rustc --version
```

### 推荐 VS Code 扩展
- rust-analyzer
- CodeLLDB
- Better TOML
- Error Lens

### 开发命令
```bash
# 实时编译检查
cargo watch -x check

# 运行测试
cargo nextest run

# 代码格式化
cargo fmt --all

# 代码质量检查
cargo clippy --all-targets --all-features
```

---

*本工作分解基于项目现有基础，重点完善事件定义、日志追踪和适配器层，为后续核心业务实现奠定坚实基础。*
