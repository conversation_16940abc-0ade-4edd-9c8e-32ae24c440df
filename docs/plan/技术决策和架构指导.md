# EchoWave 重构技术决策和架构指导

## 核心技术决策

### 1. 架构模式选择

#### 四模块分离架构
**决策理由**：
- **职责分离**：每个模块有明确的职责边界
- **技术栈优化**：每个模块使用最适合的技术
- **安全隔离**：权限分离降低安全风险
- **独立部署**：模块可独立更新和维护

**替代方案对比**：
| 方案 | 优势 | 劣势 | 选择理由 |
|------|------|------|----------|
| 单体重写 | 简单直接 | 耦合度高，难维护 | ❌ 不符合长期目标 |
| 微服务架构 | 高度解耦 | 过度复杂，运维成本高 | ❌ 桌面应用不适合 |
| 四模块架构 | 平衡复杂度和解耦 | 通信复杂度适中 | ✅ 最适合当前需求 |

### 2. 技术栈选择

#### 核心技术栈决策
**Rust + Tauri**：
- **性能优势**：内存安全 + 零成本抽象
- **体积优势**：编译后体积小，无运行时依赖
- **安全优势**：类型安全 + 内存安全
- **生态优势**：丰富的 crate 生态系统

**Vue 3 + TypeScript**：
- **开发效率**：组合式 API + 响应式系统
- **类型安全**：TypeScript 提供编译时检查
- **生态成熟**：丰富的组件库和工具链
- **团队熟悉度**：现有技术栈延续

#### 通信协议决策
**二进制消息帧 + JSON Payload**：
- **性能**：二进制头部减少解析开销
- **可读性**：JSON payload 便于调试
- **扩展性**：支持压缩和版本控制
- **兼容性**：跨语言和平台支持

### 3. 模块通信设计

#### 通信方式选择
```mermaid
graph TB
    subgraph "通信方式对比"
        A[Tauri IPC] --> A1[类型安全<br/>自动序列化<br/>性能优秀]
        B[stdio + protocol] --> B1[跨平台<br/>进程隔离<br/>调试友好]
        C[Named Pipe + protocol] --> C1[Windows原生<br/>高性能<br/>安全可控]
    end
```

**选择依据**：
- **渲染 ↔ 核心**：Tauri IPC（类型安全，性能最佳）
- **核心 ↔ Agent**：stdio（跨平台，进程管理简单）
- **核心 ↔ Helper**：Named Pipe（Windows 原生，权限控制）

## 架构设计原则

### 1. 模块独立性原则

#### 职责单一原则
每个模块只负责一个明确的职责域：
- **渲染模块**：仅负责 UI 渲染和用户交互
- **核心模块**：仅负责业务逻辑和模块协调
- **Agent 模块**：仅负责 Linux 环境的进程管理
- **Helper 模块**：仅负责 Windows 权限操作

#### 依赖倒置原则
高层模块不依赖低层模块，都依赖抽象：
```rust
// 核心模块定义抽象
trait AgentInterface {
    async fn start_daemon(&self, name: &str) -> Result<()>;
    async fn stop_daemon(&self, name: &str) -> Result<()>;
}

// Agent 适配器实现抽象
impl AgentInterface for AgentAdapter {
    // 具体实现
}
```

### 2. 通信设计原则

#### 消息驱动架构
模块间通过消息进行通信，避免直接调用：
- **异步非阻塞**：所有通信都是异步的
- **请求-响应模式**：明确的请求和响应配对
- **事件发布订阅**：状态变化通过事件通知

#### 错误传播原则
错误在模块间传播时保持上下文：
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct ModuleError {
    pub module: String,
    pub error_code: u32,
    pub message: String,
    pub trace_id: Uuid,
    pub recoverable: bool,
    pub context: Option<serde_json::Value>,
}
```

### 3. 安全设计原则

#### 最小权限原则
每个模块只获得完成其职责所需的最小权限：
- **渲染模块**：无系统权限，仅 UI 操作
- **核心模块**：用户级权限，无管理员操作
- **Agent 模块**：Linux 用户权限，无 Windows 操作
- **Helper 模块**：Windows 管理员权限，仅特定操作

#### 输入验证原则
所有模块边界都进行严格的输入验证：
```rust
pub fn validate_daemon_name(name: &str) -> Result<()> {
    if name.is_empty() || name.len() > 64 {
        return Err(anyhow!("Invalid daemon name length"));
    }
    if !name.chars().all(|c| c.is_alphanumeric() || c == '-' || c == '_') {
        return Err(anyhow!("Invalid daemon name characters"));
    }
    Ok(())
}
```

## 关键技术实现指导

### 1. 消息帧协议实现

#### 协议设计要点
```rust
// 消息帧结构
pub struct MessageFrame {
    pub event_code: EventCode,    // 内部控制事件
    pub operation_id: u32,        // 请求响应匹配
    pub is_response: bool,         // 请求/响应标识
    pub bytes: Vec<u8>,          // JSON 序列化的业务数据
}

// 二进制格式
// [识别码:3u8][标志位:1u8][操作码:1u32][长度:1u32][校验:1u32][数据:Nu8]
```

#### 实现关键点
- **识别码**：`0x10 0xAA 0xFF` 区分文本输出
- **压缩支持**：大于 1KB 的数据自动 ZSTD 压缩
- **校验机制**：CRC32 校验确保数据完整性
- **版本兼容**：预留版本字段支持协议升级

### 2. 全链路追踪实现

#### trace_id 设计
```typescript
// 前端生成 trace_id
export function useTracing() {
  const generateTraceId = () => uuidv4()
  
  const tracedInvoke = async (command: string, args?: any) => {
    const traceId = generateTraceId()
    console.log(`[${traceId}] Invoking ${command}`)
    
    try {
      const result = await invoke(command, { ...args, traceId })
      return result
    } catch (error) {
      console.error(`[${traceId}] Error:`, error)
      throw error
    }
  }
  
  return { tracedInvoke }
}
```

#### 日志聚合策略
```rust
// 统一日志格式
use tracing::{info, error, instrument};

#[instrument(skip(self), fields(trace_id = %ctx.trace_id))]
pub async fn handle_request(&self, ctx: &RequestContext) -> Result<Response> {
    info!("Processing request");
    // 业务逻辑
    Ok(response)
}
```

### 3. 错误处理策略

#### 分层错误处理
```rust
// 应用层错误
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("System check failed: {0}")]
    SystemCheckFailed(String),
    
    #[error("Agent communication failed: {0}")]
    AgentError(#[from] AgentError),
    
    #[error("Helper service error: {0}")]
    HelperError(#[from] HelperError),
}

// 模块间错误转换
impl From<AgentError> for ModuleError {
    fn from(err: AgentError) -> Self {
        ModuleError {
            module: "agent".to_string(),
            error_code: err.code(),
            message: err.to_string(),
            trace_id: err.trace_id(),
            recoverable: err.is_recoverable(),
            context: err.context(),
        }
    }
}
```

### 4. 性能优化指导

#### 启动时间优化
- **并行初始化**：模块并行启动，减少串行等待
- **延迟加载**：非关键功能延迟到需要时加载
- **缓存机制**：缓存系统检查结果和配置信息
- **预编译**：使用 Rust 的零成本抽象和编译时优化

#### 内存占用优化
- **智能指针**：使用 `Arc<Mutex<T>>` 共享状态
- **流式处理**：大数据使用流式处理避免全量加载
- **资源池**：复用连接和缓冲区
- **及时释放**：使用 RAII 模式自动管理资源

#### 通信性能优化
- **批量操作**：合并多个小请求为批量请求
- **连接复用**：保持长连接避免重复建立
- **压缩传输**：大数据自动压缩传输
- **异步处理**：使用 tokio 异步运行时

## 开发规范和最佳实践

### 1. 代码组织规范

#### 模块结构标准
```
crates/core/
├── src/
│   ├── lib.rs              # 模块入口
│   ├── config.rs           # 配置管理
│   ├── error.rs            # 错误定义
│   ├── adapters/           # 适配器层
│   │   ├── mod.rs
│   │   ├── agent.rs
│   │   ├── helper.rs
│   │   └── http.rs
│   └── services/           # 业务服务层
│       ├── mod.rs
│       ├── system_check.rs
│       ├── task_management.rs
│       └── update.rs
```

#### 命名约定
- **模块名**：snake_case
- **结构体**：PascalCase
- **函数名**：snake_case
- **常量**：SCREAMING_SNAKE_CASE
- **trait**：PascalCase，通常以 -able 或 -er 结尾

### 2. 测试策略

#### 测试分层
```rust
// 单元测试
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_system_check_success() {
        // 测试逻辑
    }
}

// 集成测试
#[tokio::test]
async fn test_agent_communication() {
    // 模块间通信测试
}

// 端到端测试
#[tokio::test]
async fn test_complete_workflow() {
    // 完整业务流程测试
}
```

#### Mock 和测试替身
```rust
#[async_trait]
pub trait AgentInterface {
    async fn start_daemon(&self, name: &str) -> Result<()>;
}

// 生产实现
pub struct AgentAdapter { /* ... */ }

// 测试替身
pub struct MockAgent {
    pub start_daemon_calls: Arc<Mutex<Vec<String>>>,
}

#[async_trait]
impl AgentInterface for MockAgent {
    async fn start_daemon(&self, name: &str) -> Result<()> {
        self.start_daemon_calls.lock().await.push(name.to_string());
        Ok(())
    }
}
```

### 3. 文档和注释规范

#### API 文档标准
```rust
/// 系统检查服务
/// 
/// 负责执行所有系统环境检查，包括平台检查、虚拟化支持、WSL 状态等。
/// 
/// # Examples
/// 
/// ```rust
/// let service = SystemCheckService::new(agent, helper);
/// let result = service.run_all_checks().await?;
/// ```
pub struct SystemCheckService {
    agent: Arc<Mutex<AgentAdapter>>,
    helper: Arc<HelperAdapter>,
}

impl SystemCheckService {
    /// 执行所有系统检查
    /// 
    /// # Arguments
    /// 
    /// * `trace_id` - 用于追踪的唯一标识符
    /// 
    /// # Returns
    /// 
    /// 返回包含所有检查结果的 `SystemCheckResult`
    /// 
    /// # Errors
    /// 
    /// 当任何检查失败时返回错误
    pub async fn run_all_checks(&self, trace_id: Uuid) -> Result<SystemCheckResult> {
        // 实现
    }
}
```

---

*本文档提供了重构过程中的关键技术决策依据和实现指导，确保架构设计的一致性和代码质量。*
