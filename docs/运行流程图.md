# EchoWave 客户端运行流程图

## 四模块架构概览

基于清晰职责划分的四模块架构，通过统一的通信协议进行交互。

```mermaid
graph TB
    subgraph "渲染模块 (desktop)"
        UI[UI组件层<br/>Vue3 + TypeScript]
        VM[视图模型层<br/>组合式API]
        TAURI[Tauri Runtime<br/>WebView + IPC]
    end
    
    subgraph "客户端核心模块 (crates/core)"
        CORE_STATE[核心状态管理]
        BIZ_LOGIC[业务逻辑层<br/>系统检查/任务管理/更新管理]
        ADAPTERS[适配器层<br/>AgentAdapter<br/>HelperAdapter<br/>HttpAdapter]
    end
    
    subgraph "任务引擎代理模块 (agent)"
        AGENT_MAIN[Agent主进程<br/>Linux Only]
        PROC_MGR[进程管理器<br/>Tailscale/Docker/Nomad]
        DATA_COL[数据收集器<br/>状态监控]
    end
    
    subgraph "Windows服务模块 (helper-svc)"
        HELPER_SVC[Windows Service]
        DISM_MGR[DISM管理器<br/>Windows功能]
        UPDATE_MGR[更新管理器<br/>权限操作]
    end
    
    subgraph "通信协议层 (crates/protocol)"
        PROTOCOL[统一消息帧<br/>MessageFrame]
    end
    
    UI --> VM
    VM --> TAURI
    TAURI -.Rust Import & Direct.-> CORE_STATE
    
    CORE_STATE --> BIZ_LOGIC
    BIZ_LOGIC --> ADAPTERS
    
    ADAPTERS -.stdio<br/>protocol.-> AGENT_MAIN
    ADAPTERS -.Named Pipe<br/>protocol.-> HELPER_SVC
    
    AGENT_MAIN --> PROC_MGR
    AGENT_MAIN --> DATA_COL
    
    HELPER_SVC --> DISM_MGR
    HELPER_SVC --> UPDATE_MGR
    
    style PROTOCOL fill:#f9f,stroke:#333,stroke-width:2px
```

## 系统状态流转图

```mermaid
stateDiagram-v2
    [*] --> 应用启动
    
    state "应用生命周期" as lifecycle {
        应用启动 --> 初始化: 生成启动trace_id
        初始化 --> 登录检查: AppState构建完成
        初始化 --> 错误状态: 初始化失败<br/>[记录trace_id]
        
        登录检查 --> 登录页面: Token失效/首次
        登录检查 --> 系统检查: Token有效
        登录页面 --> 系统检查: 登录成功<br/>[新trace_id]

        系统检查 --> WSL准备: 6/6检查通过
        系统检查 --> 环境修复: 检查未通过<br/>[保留trace_id]
        环境修复 --> 系统检查: 自动修复
        
        WSL准备 --> 镜像管理: WSL就绪
        WSL准备 --> 错误状态: WSL启动失败<br/>[错误含trace_id]
        
        镜像管理 --> 待接单: 镜像运行中
        镜像管理 --> 错误状态: 镜像启动失败<br/>[错误含trace_id]
        
        待接单 --> 守护进程启动: 开始接单<br/>[新trace_id]
        守护进程启动 --> 监控运行: 进程就绪
        守护进程启动 --> 错误状态: 启动失败<br/>[错误含trace_id]
        
        监控运行 --> 任务执行: 接收到任务<br/>[任务trace_id]
        任务执行 --> 监控运行: 任务完成
        监控运行 --> 待接单: 停止接单<br/>[新trace_id]
        
        监控运行 --> 关闭中: 退出应用
        待接单 --> 关闭中: 退出应用
        错误状态 --> 待接单: 错误修复
        错误状态 --> 关闭中: 退出应用<br/>[关闭trace_id]
    }
    
    关闭中 --> [*]
```

## 模块职责详解

### 1. 渲染模块职责
- **UI 渲染**: 使用 Vue 3 渲染用户界面
- **状态展示**: 根据核心模块状态更新 UI
- **用户交互**: 响应用户操作，发送命令到核心模块
- **不包含**: 任何业务逻辑、系统调用、直接的网络请求

### 2. 客户端核心模块职责
- **业务逻辑**: 所有业务规则和流程控制
- **状态管理**: 维护应用的全局状态
- **模块协调**: 通过适配器协调其他模块
- **不包含**: UI 逻辑、直接的系统调用、进程管理

### 3. 任务引擎代理模块职责
- **进程管理**: 管理 Tailscale、Docker、Nomad 进程
- **数据收集**: 收集任务引擎运行数据
- **环境限制**: 仅在 Linux/WSL 环境运行
- **不包含**: 业务逻辑、Windows 操作、UI 相关

### 4. Windows 服务模块职责
- **权限操作**: 处理需要管理员权限的操作
- **系统功能**: Windows 功能开启/关闭
- **更新安装**: 协助完成系统级更新
- **不包含**: 业务逻辑、状态维护、任务管理

## 模块间通信机制

### 1. 渲染模块 ↔ 核心模块 (Tauri IPC)

**通信特点：**
- 使用 Tauri 的 invoke/listen 机制
- 自动序列化/反序列化
- 类型安全（TypeScript + Rust）
- 每个 invoke 调用生成新的 trace_id

**通信流程：**
1. 用户操作触发，生成 trace_id
2. 通过 invoke 发送命令（包含 trace_id）
3. 核心模块处理并记录日志
4. 返回结果或通过事件推送状态更新

### 2. 核心模块 ↔ Agent 模块 (stdio + protocol)

**通信特点：**
- 使用标准输入/输出进行通信
- 二进制消息帧格式（支持压缩）
- Agent 作为子进程由核心模块管理
- 事件包含 trace_id 用于追踪

**协议设计：**
- Header: 16字节固定格式
- 包含识别码、标志位、操作ID、数据长度、校验和
- Payload: JSON 格式的业务数据
- 支持 Exit、Flush 等内部控制事件

### 3. 核心模块 ↔ Windows 服务 (Named Pipe + protocol)

**通信特点：**
- 使用 Windows 命名管道
- 同样采用 protocol 消息帧格式
- 服务以系统权限运行
- 每个请求包含 trace_id

**安全机制：**
- 本地通信，无网络暴露
- 管道名称包含随机令牌
- 请求需要认证
- 操作日志包含 trace_id 便于审计
## 应用启动流程

### 模块启动顺序

```mermaid
sequenceDiagram
    participant User
    participant Desktop as 渲染模块
    participant Core as 核心模块
    participant Agent as Agent模块
    participant Helper as Windows服务
    
    User->>Desktop: 启动应用
    Desktop->>Desktop: 初始化 Vue + Tauri
    Desktop->>Desktop: 配置日志系统 (JSON格式)
    Desktop->>Core: 创建核心模块线程
    Core->>Core: 加载配置
    Core->>Core: 初始化适配器
    Core->>Core: 设置追踪日志 (with trace_id)
    
    Core->>Helper: 检查 Windows 服务状态
    Helper-->>Core: 服务就绪
    
    Core->>Agent: 启动 Agent 进程
    Agent->>Agent: 初始化日志 (JSON格式)
    Agent-->>Core: Agent 就绪
    
    Core-->>Desktop: 核心模块就绪
    Desktop-->>User: 显示登录界面
```

### Tauri 主进程实现

主进程负责：
1. **初始化日志系统**
   - 配置 JSON 格式输出
   - 设置日志级别和输出目标
   - 确保所有日志包含模块信息

2. **创建核心模块**
   - 在独立线程中运行
   - 建立消息通道通信
   - 管理核心模块生命周期

3. **注册 IPC 命令**
   - check_system (系统检查)
   - start_accepting_tasks (开始接单)
   - stop_accepting_tasks (停止接单)
   - 每个命令处理包含 trace_id
## 业务流程实现

### 1. 系统检查流程（跨模块协作）

```mermaid
flowchart TD
    subgraph 渲染模块
        UI[用户点击检查<br/>生成trace_id]
        DISPLAY[显示检查结果<br/>包含trace_id]
    end
    
    subgraph 核心模块
        CHECK[SystemCheckService<br/>记录trace_id]
        LOCAL[本地检查<br/>平台/OS版本/PWSH/检查镜像状态]
        REMOTE[远程检查<br/>传递trace_id]
    end
    
    subgraph Windows服务
        VIRT[检查虚拟化<br/>日志含trace_id]
        WSL[检查WSL环境-->检查Windows功能<br/>日志含trace_id]
    end
    
    UI --> CHECK
    CHECK --> LOCAL
    CHECK --> REMOTE
    
    REMOTE --> WSL
    REMOTE --> VIRT
    
    LOCAL --> DISPLAY
    WSL --> DISPLAY
    VIRT --> DISPLAY
```

### 2. 任务接单流程（模块协同）

```mermaid
flowchart TD
    subgraph 渲染模块
        BTN[开始接单按钮]
        STATUS[状态显示]
    end
    
    subgraph 核心模块
        TASK_SVC[TaskManagementService]
        HTTP[HTTP适配器]
        POLL[轮询节点状态]
    end
    
    subgraph Agent模块
        PROC_MGR[进程管理器]
        TAIL[Tailscale]
        DOCK[Docker]
        NOMAD[Nomad]
    end
    
    subgraph 服务端
        API[节点管理API]
    end
    
    BTN --> TASK_SVC
    TASK_SVC --> PROC_MGR
    
    PROC_MGR -.Tailscale 随 Agent 一并启动<br>这里仅确保正常.-> TAIL
    PROC_MGR --> DOCK
    PROC_MGR --> NOMAD
    
    TASK_SVC --> HTTP
    HTTP -.启用节点.-> API
    
    TASK_SVC --> POLL
    POLL --> STATUS
    API --> POLL
```

## 错误处理机制

### 模块间错误传递

**错误结构设计：**
- 包含模块名称、错误码、错误消息
- 必须包含 trace_id 用于追踪
- 标记是否可恢复
- 支持序列化为 JSON

**错误类型：**
1. **可恢复错误**
   - 网络超时
   - 服务暂时不可用
   - 资源竞争

2. **不可恢复错误**
   - 权限不足
   - 系统不支持
   - 配置错误

**错误处理流程：**
1. 捕获原始错误
2. 转换为模块错误格式
3. 保留原始 trace_id
4. 记录到模块日志
5. 通过消息帧返回给调用方

### 错误追踪示例

当错误发生时，可通过 trace_id 查看完整错误链：

```bash
# 查找特定错误的完整追踪
grep "ERROR.*550e8400-e29b-41d4-a716-446655440000" /var/log/echowave/*.log | jq

# 输出示例：
# core.log: "Failed to start daemons"
# agent.log: "Docker daemon failed to start: permission denied"
```

## 性能和安全考虑

### 1. 模块隔离
- 各模块运行在独立进程中
- 通信通过明确定义的协议
- 权限分离，最小权限原则

### 2. 通信安全
- Agent 仅通过 stdio 通信，无网络暴露
- Windows 服务使用本地 Named Pipe
- 所有通信都有认证机制

### 3. 性能优化
- 消息帧使用二进制序列化，支持 ZSTD 压缩
- 批量操作支持
- 异步非阻塞通信
- 日志使用结构化格式，便于高效查询

### 4. 日志和监控
- 统一的 JSON 格式日志
- 每条日志包含 trace_id、模块名、时间戳
- 支持日志聚合和分析
- 关键操作记录详细上下文

## 部署架构

```mermaid
graph LR
    subgraph "安装包"
        INSTALLER[NSIS安装程序]
        MAIN[主程序<br/>desktop.exe]
        AGENT_BIN[Agent二进制<br/>agent]
        HELPER_EXE[Windows服务<br/>helper-svc.exe]
    end
    
    subgraph "安装后"
        DESKTOP_DIR["C:\Program Files\EchoWave\"]
        WSL_DIR["/opt/agent"]
        SERVICE[Windows服务注册]
    end
    
    INSTALLER --> DESKTOP_DIR
    MAIN --> DESKTOP_DIR
    AGENT_BIN --> WSL_DIR
    HELPER_EXE --> SERVICE
```

---

*本文档展示了基于四模块架构的 EchoWave 客户端设计，强调模块独立性、清晰的通信协议和职责分离。*