# EchoWave 更新系统使用指南

EchoWave 客户端实现了类似 electron-updater 的更新功能，支持从 CDN 清单文件进行更新检查、下载和安装。

## 核心特性

### 🚀 完整的更新流程
- **检查更新**: 从 CDN 获取最新清单文件，智能比较版本
- **下载更新**: 支持断点续传的分块下载，实时进度回调
- **安装更新**: Windows 平台下的静默安装

### 🎯 金丝雀部署支持
- 基于设备信息的条件匹配（机器 ID、CPU 架构、内存等）
- 正则表达式规则引擎
- 渐进式灰度发布

### 📦 平台兼容性
- 智能平台检测（Windows x64/x86/ARM64、macOS、Linux）
- 系统要求验证
- 关键更新标识

## 使用方法

### 1. 配置更新服务

```rust
use domain::services::update_service::{UpdateConfig, UpdateService};
use domain::services::update_service::strategy::UpdateStrategyType;

// 配置 Manifest 更新策略
let config = UpdateConfig {
    strategy: UpdateStrategyType::Manifest,
    cdn_base_url: Some("https://cdn.echowave.ai/releases".to_string()),
    allow_prerelease: false,
    auto_update_enabled: false,
    check_interval: 3600,   // 1小时检查一次
    download_timeout: 1800, // 30分钟下载超时
    install_retries: 3,
};

// 创建更新服务
let update_service = Arc::new(UpdateService::new(
    config,
    device_service,
    settings_service,
    http_adapter,
    file_adapter,
    helper_adapter,
    agent_adapter,
)?);

// 启动服务
update_service.start().await?;
```

### 2. 使用 electron-updater 风格的 API

```rust
use domain::services::update_service::components::ElectronLikeUpdater;
use uuid::Uuid;

// 创建类似 electron-updater 的更新器
let updater = ElectronLikeUpdater::new(update_service.clone());
let trace_id = Uuid::new_v4();

// 检查更新
println!("🔍 检查更新中...");
let check_result = updater
    .with_trace_id(trace_id)
    .check_for_updates()
    .await?;

if check_result.update_available {
    let update_info = check_result.update_info.unwrap();
    
    println!("✅ 发现可用更新!");
    println!("   当前版本: {}", check_result.current_version);
    println!("   新版本: {}", update_info.version);
    println!("   发布时间: {}", update_info.release_date);
    println!("   文件大小: {} MB", update_info.download_info.size / 1024 / 1024);
    
    if update_info.critical {
        println!("⚠️  这是一个关键安全更新，强烈建议立即安装");
    }

    // 下载更新（带进度回调）
    println!("\n⬇️  开始下载更新...");
    let installer_path = updater.download_update(Some(|progress| {
        println!("   下载进度: {:.1}% ({} KB/s, 剩余 {}s)", 
            progress.percentage, 
            progress.speed_bytes_per_sec / 1024,
            progress.eta_seconds.unwrap_or(0)
        );
    })).await?;

    println!("✅ 下载完成: {}", installer_path.display());

    // 安装更新
    println!("\n🔧 开始安装更新...");
    updater.quit_and_install(&installer_path).await?;
    
    println!("✅ 更新安装完成，应用程序将重启");
} else {
    println!("✅ 当前已是最新版本: {}", check_result.current_version);
}
```

### 3. 直接使用更新服务 API

```rust
use uuid::Uuid;
use tokio_util::sync::CancellationToken;

let trace_id = Uuid::new_v4();

// 检查更新
let check_result = update_service.check_for_updates(&trace_id).await?;

if check_result.update_available {
    let update_info = check_result.update_info.unwrap();
    
    // 下载更新
    let cancellation_token = CancellationToken::new();
    let progress_callback = Some(Box::new(|progress| {
        tracing::info!(
            percentage = progress.percentage,
            speed = progress.speed_bytes_per_sec,
            "下载进度更新"
        );
    }) as Box<dyn Fn(_) + Send + Sync>);
    
    let installer_path = update_service.download_update(
        &update_info,
        progress_callback,
        cancellation_token,
        &trace_id,
    ).await?;
    
    // 安装更新
    update_service.install_update(&installer_path, &trace_id).await?;
}
```

## CDN 清单文件格式

### 基础清单结构

```json
{
  "version": "2.1.0",
  "release_date": "2024-01-15T10:30:00Z",
  "path": "/releases/v2.1.0",
  "files": [
    {
      "url": "https://cdn.echowave.ai/releases/v2.1.0/echowave-setup-2.1.0.exe",
      "sha256": "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
      "size": 52428800,
      "platform": "win32-x64"
    },
    {
      "url": "https://cdn.echowave.ai/releases/v2.1.0/echowave-setup-2.1.0-arm64.exe",
      "sha256": "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567",
      "size": 48234496,
      "platform": "win32-arm64"
    }
  ]
}
```

### 金丝雀部署配置

```json
{
  "version": "2.1.0",
  "release_date": "2024-01-15T10:30:00Z",
  "path": "/releases/v2.1.0",
  "files": [...],
  "canary": {
    "version": "2.2.0-beta.1",
    "release_date": "2024-01-20T15:45:00Z",
    "path": "/releases/canary/v2.2.0-beta.1",
    "conditions": [
      {
        "type": "Regexp",
        "field": "MACHINE_ID",
        "value": "^test-.*"
      },
      {
        "type": "Regexp", 
        "field": "CPU_ARCH",
        "value": "x86_64"
      }
    ],
    "files": [
      {
        "url": "https://cdn.echowave.ai/releases/canary/v2.2.0-beta.1/echowave-setup-2.2.0-beta.1.exe",
        "sha256": "c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678",
        "size": 53477376,
        "platform": "win32-x64"
      }
    ]
  }
}
```

### 支持的条件匹配字段

- `MACHINE_ID`: 机器唯一标识
- `DEVICE_NAME`: 设备名称
- `OS_NAME`: 操作系统名称
- `OS_VERSION`: 操作系统版本
- `KERNEL_VERSION`: 内核版本
- `CPU_BRAND`: CPU 品牌
- `CPU_ARCH`: CPU 架构
- `CPU_CORE_COUNT`: CPU 核心数
- `MEMORY_TIER`: 内存级别
- `TOTAL_MEMORY_GB`: 总内存大小
- `GPU_NAME`: 显卡名称
- `GPU_BRAND`: 显卡品牌
- `HOSTNAME`: 主机名

## 平台标识符

| 平台 | 架构 | 标识符 |
|------|------|--------|
| Windows | x64 | `win32-x64` |
| Windows | x86 | `win32-ia32` |  
| Windows | ARM64 | `win32-arm64` |
| macOS | x64 | `darwin-x64` |
| macOS | ARM64 | `darwin-arm64` |
| Linux | x64 | `linux-x64` |
| Linux | x86 | `linux-ia32` |
| Linux | ARM64 | `linux-arm64` |

## 错误处理

更新系统使用 `anyhow` 进行统一的错误处理，所有中文错误消息便于用户理解：

```rust
match updater.check_for_updates().await {
    Ok(result) => {
        // 处理更新结果
    },
    Err(e) => {
        tracing::error!(error = %e, "更新检查失败");
        println!("❌ 更新检查失败: {}", e);
    }
}
```

## 日志追踪

系统使用 `trace_id` 进行全链路日志追踪，便于问题定位：

```rust
use uuid::Uuid;

let trace_id = Uuid::new_v4();
tracing::info!(trace_id = %trace_id, "开始更新流程");

// 所有相关操作都会包含这个 trace_id
let result = updater.with_trace_id(trace_id)
    .check_for_updates()
    .await?;
```

## 最佳实践

### 1. 版本比较
- 支持语义化版本比较（major.minor.patch）
- 预发布版本控制（-alpha、-beta、-rc）
- 关键更新自动识别

### 2. 网络优化
- 断点续传支持
- 分块并发下载
- 智能重试机制
- 下载进度实时反馈

### 3. 安全性
- SHA256 文件完整性验证
- HTTPS 强制传输加密
- 安装程序数字签名验证

### 4. 用户体验
- 静默后台下载
- 非阻塞式更新检查
- 用户可控的更新策略
- 详细的更新说明展示

这个更新系统提供了与 electron-updater 相似的 API 体验，同时增加了更强大的金丝雀部署和平台适配能力。