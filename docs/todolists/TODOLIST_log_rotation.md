# EchoWave 日志轮转重构任务清单

## 项目概述

重构 `crates/domain/src/logging/file.rs` 中的日志轮转机制，从同步轮转改为后台线程异步轮转，以解决以下问题：
- 递归调用风险（轮转过程中的日志输出可能触发新的轮转检查）
- 性能瓶颈（每次写入都需要轮转检查，轮转时阻塞写入线程）
- 并发复杂性（多线程轮转竞争和锁竞争）

## 架构设计目标

实现基于后台线程的轮转管理器，包含：
- `BackgroundRotationManager` - 后台轮转管理器
- `RotationRequest` - 轮转请求消息队列
- 三种轮转触发机制：阈值触发、定时检查、事件驱动
- 完全消除递归风险，提升写入性能

## 任务进度

### ✅ 已完成任务

1. **深入理解当前轮转实现的问题和架构** ✅
   - 分析了现有 `file.rs` 的轮转逻辑
   - 识别了递归调用、性能瓶颈和并发复杂性问题
   - 理解了 `SourceFileState` 结构和 `DashMap` 使用模式

2. **研究Rust异步轮转和线程通信最佳实践** ✅
   - 学习了 Tokio `mpsc::channel` 用法
   - 研究了 `tokio::spawn` 和 `tokio::select!` 异步模式
   - 理解了 `Arc` 和原子操作在异步环境中的使用

3. **设计后台轮转线程架构** ✅
   - 设计了 `RotationRequest` 枚举和 `RotationReason` 类型
   - 设计了 `BackgroundRotationManager` 结构和生命周期管理
   - 制定了轮转触发机制：定时检查(30秒) + 阈值触发 + 事件驱动
   - 设计了错误处理和重试机制

### 🔄 正在进行的任务

4. **实现RotationManager和轮转请求队列** 🔄
   - ✅ 已添加必要的依赖（tokio::sync::mpsc, JoinHandle）
   - ❌ 正在添加数据结构定义（被中断）
   - ❌ 待实现具体的轮转循环逻辑
   - ❌ 待实现轮转请求处理

### ❌ 待完成任务

5. **重构FileLayer去除同步轮转逻辑**
   - 修改 `FileLayer::new()` 集成 `BackgroundRotationManager`
   - 重构 `write_log_entry()` 移除同步轮转检查
   - 添加轮转请求发送逻辑（非阻塞）
   - 保持现有API兼容性

6. **实现轮转触发机制(定时+事件驱动)**
   - 定时检查：每30秒检查所有source的轮转条件
   - 阈值触发：文件大小超过10MB时立即发送轮转请求
   - 事件驱动：日期变更时立即触发轮转

7. **测试并发安全性和性能**
   - 编写多线程写入压力测试
   - 验证轮转过程中的并发安全性
   - 测试轮转失败的错误恢复机制
   - 性能对比测试（重构前后）

## 关键理解和技术细节

### 现有实现的深度分析

#### 递归调用路径详解
```rust
write_log_entry()
-> rotate_log_if_needed()  // 检查轮转条件
-> tracing::info!("开始执行日志轮转...")  // 轮转过程中的日志输出
-> FileLayer::on_event()  // tracing事件触发
-> write_log_entry()  // 递归调用！
```

**防护机制现状**:
- `is_rotating: AtomicBool` - 防止递归，但不完全可靠
- `RotationGuard` RAII模式 - 确保标记清理
- 问题：轮转过程中的 `tracing::info!` 等调用仍可能触发递归

#### 性能瓶颈分析
1. **每次写入的开销**:
   ```rust
   // 每次写入都执行这些检查
   let should_check_rotation = {
       bytes_to_write >= LARGE_WRITE_THRESHOLD ||  // 1MB阈值检查
       current_write_count.saturating_sub(last_check) >= ROTATION_CHECK_INTERVAL ||  // 50次间隔
       current_write_count == 1  // 首次写入
   };
   ```

2. **轮转时的阻塞**:
   - 文件重命名操作 (`std::fs::rename`)
   - 新文件创建和写入器更新
   - 后台压缩任务启动
   - 所有这些都在写入线程中同步执行

3. **锁竞争**:
   - `DashMap` 外层并发安全
   - `Mutex<Option<std::fs::File>>` 内层写入器锁
   - `Mutex<String>` 当前日期锁
   - 多线程写入时锁竞争激烈

#### 并发模式复杂性
- **状态管理**: 每个 `LogSource` 有独立的 `SourceFileState`
- **原子操作**: `file_size`, `is_rotating`, `write_count` 等使用原子类型
- **同步点**: 轮转操作需要协调多个原子状态的更新

### 重构架构的深度设计

#### 通信模式选择理由
**为什么选择 `mpsc::unbounded_channel`**:
- `unbounded`: 确保写入线程永不阻塞（重要！）
- `mpsc`: 多个写入线程向一个轮转线程发送请求
- 替代方案及其问题：
  - `bounded_channel`: 可能阻塞写入线程
  - `broadcast`: 不需要多消费者
  - `oneshot`: 只能单次通信

#### 轮转触发策略细节
1. **阈值触发** (最重要):
   ```rust
   // 在写入完成后检查，非阻塞发送
   if new_size >= MAX_LOG_FILE_SIZE {
       let _ = rotation_manager.request_rotation(
           RotationRequest::Immediate { 
               source, 
               reason: RotationReason::SizeExceeded(new_size) 
           }
       );
   }
   ```

2. **定时检查** (兜底机制):
   ```rust
   let mut periodic_timer = tokio::time::interval(Duration::from_secs(30));
   
   tokio::select! {
       _ = periodic_timer.tick() => {
           // 检查所有source的轮转条件
           check_all_sources_for_rotation().await;
       }
   }
   ```

3. **事件驱动** (日期变更):
   - 通过比较 `stored_date` 和 `current_date`
   - 日期变更时立即发送 `RotationRequest::Immediate`

#### 状态同步机制
**关键问题**: 写入线程和轮转线程如何安全地共享状态？

**解决方案**:
```rust
// 共享状态结构
Arc<DashMap<LogSource, SourceFileState>>

// 轮转标记的原子操作
if state.is_rotating.compare_exchange(false, true, Ordering::SeqCst, Ordering::Relaxed).is_err() {
    return Ok(()); // 避免重复轮转
}
```

**状态更新顺序**:
1. 设置 `is_rotating = true`
2. 关闭旧的文件写入器 (`writer = None`)
3. 执行文件系统操作（重命名、创建）
4. 创建新的文件写入器
5. 重置 `file_size = 0`
6. 更新性能统计
7. 清除 `is_rotating = false` (通过RAII)

#### 错误恢复策略
**轮转失败的处理**:
```rust
match execute_rotation_internal().await {
    Ok(()) => {
        // 成功：更新性能统计
    }
    Err(e) => {
        tracing::error!("轮转失败: {}", e);
        // 策略1: 重试一次
        schedule_retry_rotation(source, reason).await?;
        // 策略2: 降级到旧文件继续写入
        // 策略3: 紧急模式，直接写入stderr
    }
}
```

**关键原则**: 轮转失败不能影响日志写入的正常功能

### 性能优化细节

#### 写入路径优化
**重构前**:
```rust
write_log_entry() {
    1. 检查轮转条件 (可能很重)
    2. 执行轮转 (阻塞操作)
    3. 写入日志
    4. 更新统计
}
```

**重构后**:
```rust
write_log_entry() {
    1. 写入日志 (核心操作)
    2. 更新统计
    3. 轻量级轮转检查 (非阻塞)
    4. 可选发送轮转请求
}
```

#### 内存使用优化
- **请求队列**: 使用 `unbounded_channel` 可能累积请求，需要监控
- **状态共享**: `Arc<DashMap>` 避免状态复制
- **文件句柄**: 及时关闭旧的文件写入器

#### 并发性能提升
- **锁持有时间减少**: 轮转操作移到独立线程
- **写入吞吐量提升**: 移除同步轮转检查
- **更好的缓存局部性**: 每个写入线程专注于写入

### 兼容性和迁移考虑

#### API兼容性
```rust
// 保持不变的公共接口
impl FileLayer {
    pub fn new(log_dir: PathBuf, debug_cache: Arc<LruCache>) -> Self
    pub fn get_performance_stats(&self, source: LogSource) -> Option<PerformanceStats>
    pub fn get_all_performance_stats(&self) -> Vec<PerformanceStats>
    // ...
}

impl LogWriter for FileLayer {
    fn write_log(&self, entry: StructuredLogEntry) -> Result<()>
}
```

#### 内部重构点
- `FileLayer` 增加 `rotation_manager: BackgroundRotationManager` 字段
- `write_log_entry()` 逻辑简化
- 移除同步的 `rotate_log_if_needed()` 方法
- 保留所有现有的辅助方法 (`compress_log_file_by_path`, `cleanup_old_logs_in_dir`)

#### 文件格式兼容性
- 文件命名规则完全不变: `{source}-{date}.jsonl`, `{source}-{date}-{timestamp}.jsonl`
- 压缩格式不变: `.zst` 后缀
- 日志内容格式不变: JSON Lines

### 测试策略细节

#### 并发安全测试
```rust
#[tokio::test]
async fn test_concurrent_rotation() {
    // 模拟场景：100个线程同时写入，文件大小刚好超限
    let handles: Vec<_> = (0..100).map(|i| {
        tokio::spawn(async move {
            // 每个线程写入大量数据
            for _ in 0..1000 {
                file_layer.write_log(create_large_entry()).await.unwrap();
            }
        })
    }).collect();
    
    // 验证：
    // 1. 所有写入都成功
    // 2. 只发生了预期数量的轮转
    // 3. 没有数据丢失
    // 4. 文件状态一致
}
```

#### 性能基准测试
```rust
#[bench]
fn bench_write_performance_before_refactor(b: &mut Bencher) {
    // 测试重构前的写入性能
}

#[bench] 
fn bench_write_performance_after_refactor(b: &mut Bencher) {
    // 测试重构后的写入性能
    // 预期提升：减少20-50%的写入延迟
}
```

#### 错误注入测试
- 磁盘空间不足时的行为
- 文件权限错误的处理
- 轮转线程意外崩溃的恢复
- 文件系统IO延迟的影响

## 详细实现计划

### 数据结构设计

```rust
// 轮转请求类型
#[derive(Debug, Clone)]
pub enum RotationRequest {
    Immediate { source: LogSource, reason: RotationReason },
    PeriodicCheck,
    Shutdown,
}

#[derive(Debug, Clone)]
pub enum RotationReason {
    SizeExceeded(u64),
    DateChanged(String),
    Manual,
}

// 后台轮转管理器
pub struct BackgroundRotationManager {
    request_sender: mpsc::UnboundedSender<RotationRequest>,
    shutdown_signal: Arc<AtomicBool>,
    join_handle: Option<JoinHandle<()>>,
}
```

### 核心轮转循环

```rust
async fn rotation_loop(
    mut request_receiver: mpsc::UnboundedReceiver<RotationRequest>,
    source_states: Arc<DashMap<LogSource, SourceFileState>>,
    log_dir: PathBuf,
) {
    let mut periodic_timer = tokio::time::interval(Duration::from_secs(30));
    
    loop {
        tokio::select! {
            request = request_receiver.recv() => {
                // 处理轮转请求
            }
            _ = periodic_timer.tick() => {
                // 定时检查所有source
            }
        }
    }
}
```

### 写入线程集成

```rust
fn write_log_entry(&self, entry: &StructuredLogEntry) -> Result<()> {
    // 1. 写入日志（无轮转检查）
    // 2. 更新文件大小计数器
    // 3. 检查是否需要发送轮转请求（非阻塞）
    if should_request_rotation(source, new_size) {
        let _ = self.rotation_manager.request_rotation(
            RotationRequest::Immediate { source, reason }
        );
    }
}
```

## 关键技术决策

1. **通信机制**: 使用 `tokio::sync::mpsc::unbounded_channel` 确保写入线程不被阻塞
2. **轮转策略保持不变**: 支持日期轮转和大小轮转，保持现有文件命名规则
3. **错误处理**: 轮转失败不影响日志写入，有重试和fallback机制
4. **性能监控**: 保持现有的 `PerformanceStats` 接口和统计功能
5. **向后兼容**: 保持公共API不变，内部实现升级

## 预期收益

- **完全消除递归风险**: 写入线程不再执行轮转逻辑
- **显著提升写入性能**: 移除每次写入的轮转检查开销 (预期20-50%提升)
- **更好的并发性能**: 统一的轮转管理，减少锁竞争
- **更强的错误恢复能力**: 轮转失败不影响日志写入
- **支持更复杂的轮转策略**: 可以实现优先级、批量轮转等高级功能
- **更好的可观测性**: 轮转操作集中管理，便于监控和调试

## 风险评估

- **复杂性增加**: 引入异步轮转增加了系统复杂性，需要仔细测试
- **文件大小可能暂时超限**: 轮转是异步的，文件大小可能短暂超过10MB（可接受的权衡）
- **轮转延迟**: 轮转不再是立即的，可能有几秒到30秒的延迟（通过阈值触发减轻）

## 下一步行动

1. 恢复 `BackgroundRotationManager` 数据结构的添加
2. 实现轮转循环和请求处理逻辑
3. 修改 `FileLayer` 集成后台轮转管理器
4. 编写全面的测试用例
5. 性能对比和优化调优

---

*最后更新: 2025-01-25*
*状态: 进行中 - 任务4实施阶段*
*当前文件: crates/domain/src/logging/file.rs:76 (LARGE_WRITE_THRESHOLD 常量后)*