# EchoWave WSL 镜像检查实现计划

## 目标

完成 `@crates/core/src/services/system_check.rs` 中 `check_wsl_mirror` 方法的实现，提供完整的 WSL 镜像检查、下载、导入和健康检查功能。

## 当前状态

- ✅ 基础架构已搭建（SystemCheckService、状态管理、错误处理）
- ✅ `check_wsl_mirror` 方法已实现核心逻辑
- ✅ WSL 管理工具已完成
- ✅ 下载器组件已完成
- ✅ 磁盘空间检查工具已完成（复用现有disk_capacity模块）

## 实施阶段

### ✅ 阶段 1：基础工具模块开发 

**目标：创建支撑 WSL 镜像检查的基础工具** ✅ **已完成**

#### 1.1 创建磁盘空间检查工具 ✅

- [x] 复用现有 `crates/core/src/utils/disk_capacity.rs` 模块
- [x] 使用 `get_disk_capacity(path: &Path) -> Result<DiskCapacity>` 函数
- [x] 支持 Windows 磁盘空间查询
- [x] 已有完整的单元测试覆盖

#### 1.2 创建 WSL 管理工具 ⭐ ✅

- [x] 创建 `crates/core/src/utils/wsl_manager.rs`
- [x] 实现 `list_wsl_distributions()` - 使用 `wsl -l -v` 列出所有分发
- [x] **重点**：处理 UTF-16LE 编码的 WSL 命令输出
- [x] 实现正则表达式解析：`distroName + "(\\s)+Running"` 等状态
- [x] 实现 `is_distribution_imported(name: &str)` - 检查特定分发是否已导入
- [x] 实现 `import_distribution(name, tar_path, install_path)` - 导入 WSL 分发
- [x] 实现 `set_wsl2_version(name: &str)` - 设置分发为 WSL2 模式
- [x] 实现 `unregister_distribution(name: &str)` - 删除损坏的分发
- [x] 处理特殊错误：`WSL_E_VM_MODE_INVALID_STATE` 等可忽略错误
- [x] 解析 WSL 命令返回的错误
- [x] 添加错误处理和重试机制

#### 1.3 创建下载器组件([分块下载器设计](./分块下载器设计.md)) ⭐ ✅

- [x] 创建 `crates/core/src/utils/downloader.rs`
- [x] 实现基于 reqwest 的流式下载功能
- [x] **核心功能**：分块断点续传下载器
  - [x] 按块划分大文件（如 1GB 按 64MB 一块，共 16 块）
  - [x] 自适应块大小：小文件 32MB，中文件 64MB，大文件 128MB
  - [x] 判断当前已下载的块，继续下载缺失的块
  - [x] 支持并发下载多个块提升速度
  - [x] 实现块级重试机制（单块失败不影响整体）
- [x] **校验系统**：创建 `crates/core/src/utils/checksum.rs`
  - [x] 支持 MD5 和 SHA256 校验（可配置选择）
  - [x] 实现块级校验和整体文件校验
  - [x] 支持校验失败时的块重下载
- [x] 实现进度回调机制（使用 watch::Sender 更新状态）
- [x] 使用 CancellationToken 支持下载取消
- [x] 添加重试和超时处理

#### 1.4 创建镜像配置管理 ✅

- [x] 创建 `crates/core/src/utils/mirror_config.rs`
- [x] 定义镜像 CDN 地址常量
- [x] 定义默认镜像名称和版本（参考 legacy 中的 distroName）
- [x] **下载配置**：定义分块下载参数
  - [x] 默认块大小（64MB）和自适应块大小策略
  - [x] 最大并发下载数（默认 4 个并发块）
  - [x] 校验算法选择（MD5/SHA256）
- [x] 定义最小磁盘空间要求（50GB）
- [x] 提供配置验证功能

### ✅ 阶段 2：核心逻辑实现

**目标：实现 check_wsl_mirror 方法的核心检查逻辑** ✅ **已完成**

#### 2.1 集成设置服务 ✅

- [x] 在 `SystemCheckService` 中添加 settings_service 依赖
- [x] 实现获取 `distro_install_path` 配置
- [x] 处理配置缺失的情况

#### 2.2 实现磁盘空间检查 ✅

- [x] 集成磁盘空间检查工具
- [x] 检查安装路径所在磁盘是否有 >= 50GB 空间
- [x] 磁盘空间不足时设置 `ManualAction::ChangeInstallPath`

#### 2.3 实现 WSL 镜像状态检查 ⭐ ✅

- [x] 使用 `wsl -l -v` 命令检查 EchoWave 分发状态
- [x] 解析输出识别分发状态：Running、Stopped、Installing
- [x] 检查镜像 TAR 文件是否存在于本地缓存
- [x] 支持三种状态：{installed, running, installing} (参考 legacy)
- [x] 更新 `wsl_mirror_status` 状态字段

#### 2.4 实现 Agent 健康检查 ✅

- [x] 启动 WSL 镜像（如果已导入）
- [x] 通过 `AgentAdapter` 连接 WSL 中的 agent 服务
- [x] 发送 health 指令验证镜像完整性
- [x] 处理连接超时和失败情况

### ✅ 阶段 3：自动修复功能

**目标：实现自动下载、导入和修复功能** ✅ **已完成**

#### 3.1 实现镜像下载逻辑 ⭐ ✅

- [x] **分块断点续传下载**：
  - [x] 检查本地缓存的块文件（`.part.{块号}` 格式）
  - [x] 计算已下载的块和缺失的块
  - [x] 并发下载缺失的块（可配置并发数）
  - [x] 支持块级重试（单块失败不影响其他块）
- [x] **校验和验证**：
  - [x] 支持 MD5/SHA256 校验（从配置读取算法类型）
  - [x] 实现块级校验和整体文件校验
  - [x] 校验失败的块自动重新下载
- [x] 从 CDN 下载镜像文件到临时目录 `dirname/tmp/`
- [x] 实时更新 `download_progress` 状态（基于已完成块数/总块数）
- [x] 支持下载取消和错误重试
- [x] 下载完成后合并所有块为完整文件

#### 3.2 实现镜像导入逻辑 ⭐ ✅

- [x] **重要**：导入前检查并删除已存在的数据卷目录（参考 legacy）
- [x] 使用 `wsl --import <distroName> <installPath> <tarPath>` 导入镜像
- [x] 执行 `wsl --set-version <distroName> 2` 设置为 WSL2
- [x] 处理特殊错误 `WSL_E_VM_MODE_INVALID_STATE`（可忽略）
- [x] 实时更新 `install_progress` 状态（第二阶段进度）
- [x] 处理导入失败情况和回滚机制
- [x] 清理临时 TAR 文件

#### 3.3 实现镜像修复逻辑 ✅

- [x] 检测镜像损坏情况
- [x] 删除损坏的分发
- [x] 重新下载和导入镜像
- [x] 提供用户反馈

#### 阶段 3 实现总结 ✅

第 3 阶段的所有自动修复功能已经完整实现，包含以下关键特性：

**分块断点续传下载器** (`crates/core/src/utils/downloader.rs`)：
- ✅ 智能分块策略：自适应块大小（32MB-128MB）
- ✅ 并发下载：默认 4 个并发块，提升下载速度
- ✅ 断点续传：支持块级断点续传，单块失败不影响整体
- ✅ 校验系统：支持 MD5/SHA256 可配置校验算法
- ✅ 进度回调：实时更新下载进度和速度统计

**WSL 镜像管理** (`crates/core/src/utils/wsl_manager.rs`)：
- ✅ 完整导入流程：检查现有分发、删除旧版本、导入新镜像
- ✅ WSL2 设置：自动设置为 WSL2 模式
- ✅ 特殊错误处理：处理 `WSL_E_VM_MODE_INVALID_STATE` 等可忽略错误
- ✅ UTF-16LE 编码：正确处理 WSL 命令的编码输出

**自动修复流程** (`crates/core/src/services/system_check.rs`)：
- ✅ 智能检测：磁盘空间、镜像状态、Agent 健康检查
- ✅ 自动修复：损坏检测、重新下载、重新导入
- ✅ 进度反馈：两阶段进度（下载 + 安装）实时更新
- ✅ 错误恢复：完善的错误处理和用户友好提示

### 🧪 阶段 4：错误处理和优化

**目标：完善错误处理、用户体验和性能优化**

#### 4.1 完善错误处理 ⭐

- [ ] 网络连接失败处理（重试机制）
- [ ] WSL 命令执行失败处理（区分 error 和 stderr）
- [ ] **重要**：UTF-16LE 编码错误处理
- [ ] 特殊 WSL 错误处理（`WSL_E_VM_MODE_INVALID_STATE` 等）
- [ ] 磁盘空间不足处理
- [ ] Agent 连接超时处理
- [ ] 提供详细的错误信息和用户友好的提示

#### 4.2 状态管理优化

- [ ] 确保所有操作正确更新检查状态
- [ ] 实现取消正在进行的下载
- [ ] 处理并发检查请求
- [ ] 优化内存使用

#### 4.3 用户体验优化

- [ ] 提供清晰的进度提示
- [ ] 合理设置各种操作的超时时间
- [ ] 优化长时间操作的用户反馈
- [ ] 添加操作日志记录

### 🔍 阶段 5：测试和验证

**目标：确保功能的正确性和稳定性**

#### 5.1 单元测试

- [ ] 为所有工具模块编写单元测试
- [ ] 测试各种错误路径
- [ ] 模拟网络失败情况
- [ ] 测试文件操作边界情况

#### 5.2 集成测试

- [ ] 完整的镜像检查流程测试
- [ ] 下载和导入流程测试
- [ ] Agent 连接测试
- [ ] 错误恢复测试

#### 5.3 性能测试

- [ ] 大文件下载性能测试
- [ ] 并发操作测试
- [ ] 内存使用测试
- [ ] 长时间运行稳定性测试

## 技术难点和风险

### 高风险项

1. **WSL 版本兼容性**：不同 Windows 版本的 WSL 命令可能有差异
2. **UTF-16LE 编码处理**：WSL 命令输出需要正确的编码转换（legacy 经验）
3. **Agent 连接稳定性**：网络问题可能导致健康检查失败
4. **分块下载复杂性**：并发块下载、块级重试、块文件管理的复杂实现

### 中风险项

1. **特殊 WSL 错误处理**：`WSL_E_VM_MODE_INVALID_STATE` 等错误的正确处理（legacy 经验）
2. **磁盘空间检查准确性**：跨分区和网络驱动器的处理
3. **并发操作处理**：多个检查同时运行时的状态管理
4. **块文件管理复杂性**：部分块下载失败时的清理和恢复机制
5. **校验算法性能**：大文件 SHA256 校验的性能优化

### Legacy 项目验证的解决方案

- ✅ **UTF-16LE 编码**：使用特定编码参数执行 WSL 命令
- 🔄 **断点续传升级**：从简单 MD5 校验升级为分块下载 + 可选校验算法
- ✅ **WSL 错误处理**：已知可忽略的特定错误类型
- ✅ **两阶段进度**：下载和安装的独立进度跟踪

### 新增技术特性

- 🆕 **智能分块下载**：1GB 按 64MB 分块（16 块），支持并发下载
- 🆕 **自适应块大小**：根据文件大小自动调整块大小（32MB-128MB）
- 🆕 **可配置校验**：支持 MD5/SHA256 选择
- 🆕 **块级重试**：单块失败不影响整体下载
- 🆕 **性能优化**：减少 HTTP 请求数，提升带宽利用率

### 缓解策略

- 为每个风险点设计详细的测试用例
- 实现完善的日志记录便于问题排查
- 提供用户友好的错误提示和解决建议
- 采用增量开发，每个阶段都保证基本功能可用

## 预期时间线

- 阶段 1：2-3 天（基础工具开发）
- 阶段 2：3-4 天（核心逻辑实现）
- 阶段 3：4-5 天（自动修复功能）
- 阶段 4：2-3 天（错误处理优化）
- 阶段 5：2-3 天（测试和验证）

**总预计时间：13-18 工作日**

## 成功标准

- [ ] `check_wsl_mirror` 方法完全实现，移除 `todo!()`
- [ ] 支持完整的磁盘空间检查、镜像下载、导入和健康检查流程
- [ ] **验证关键功能**：
  - [ ] 分块断点续传下载（1GB 按 64MB 分块，共 16 块）
  - [ ] 自适应块大小策略验证
  - [ ] 可配置校验算法（MD5/SHA256）
  - [ ] UTF-16LE 编码处理、WSL2 设置
- [ ] 所有自动修复功能正常工作（参考 legacy 的两阶段进度）
- [ ] 错误处理完善，用户体验良好（包含特殊 WSL 错误处理）
- [ ] **性能验证**：
  - [ ] 并发下载比单线程下载提升 >= 50% 速度
  - [ ] 大文件校验性能满足用户体验要求
- [ ] 单元测试覆盖率 >= 80%
- [ ] 集成测试通过所有核心流程（包含网络中断恢复测试）
- [ ] 代码审查通过，符合项目规范

## Legacy 项目关键参考文件

- `legacy/BackL0/Operations/CheckMirror.mjs` - WSL 状态检查核心逻辑
- `legacy/BackL0/Operations/InstallMirror.mjs` - 镜像下载和导入流程
- `legacy/Utils/DownloadFile.mjs` - MD5 断点续传实现
- `legacy/Utils/CheckMD5.mjs` - 文件校验工具
