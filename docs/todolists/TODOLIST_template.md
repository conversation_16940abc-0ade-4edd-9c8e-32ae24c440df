# [功能/项目名称] - 任务清单

## 关键强制性规则 - 请先阅读

## 关键：没有模拟数据/没有回退策略

## 重要规则 - 请先阅读

1. **在进行任何工作之前务必阅读 CLAUDE.md**
2. 每次继续操作之前，**务必在代码库中搜索文件**
3. **始终遵循工作流程**：分析 → 重用 → 验证 → 集成
4. **始终遵循**：研究 → 计划 → 实施（切勿直接跳到编码）
5. **优雅永不过时**：以外科手术般的精准度编辑代码

## 1. 项目概述 (Project Overview)

- **背景**: (简要描述为什么要做这个项目，要解决什么问题。)
- **目标**: (列出此项目要达成的核心业务或技术目标。)
- **范围**: (明确定义项目的边界，哪些做，哪些不做。)

---

## 2. 架构设计与技术选型 (Architecture & Tech Stack)

- **核心思路**: (描述关键的思路决策，用文字表示。)
- **技术选型**: (列出主要的技术、库或框架，并简述选择原因。)
- **关键数据结构/接口**: (定义核心的数据结构或 API 接口。)

---

## 3. 详细任务分解 (Task Breakdown)

### 阶段一: [阶段名称，如：基础功能实现]

- [ ] **任务 1.1**: [任务名称]

  - **任务描述**: (详细描述任务内容。)
  - **技术细节**: (实现该任务需要的技术要点。)
  - **验收标准**: (如何判断该任务已完成。)

- [ ] **任务 1.2**: [任务名称]
  - ...

### 阶段二: [阶段名称，如：错误处理与优化]

- [ ] **任务 2.1**: [任务名称]

  - ...

- [ ] **任务 2.2**: [任务名称]
  - ...

---

## 4. 进度跟踪 (Progress Tracker)

### ✅ 已完成 (Completed)

- **[任务/功能名称]**: (完成日期: YYYY-MM-DD)
  - 实现细节: (简要说明完成了什么。)

### 🔄 进行中 (In Progress)

- **[任务/功能名称]**:
  - 当前状态: (描述目前的进展。)
  - 阻塞项: (是否存在阻塞问题。)

### 📋 待办事项 (To-Do / Backlog)

- **[任务/功能名称]**: (优先级: 高/中/低)
- **[任务/功能名称]**: (优先级: 高/中/低)

---

## 5. 风险评估与缓解策略 (Risk Assessment & Mitigation)

| 风险点 (Risk)             | 可能性 (Likelihood) | 影响程度 (Impact) | 缓解措施 (Mitigation Plan)                   |
| :------------------------ | :------------------ | :---------------- | :------------------------------------------- |
| [例如：第三方 API 不稳定] | 中 (Medium)         | 高 (High)         | [例如：实现带指数退避的重试机制，增加缓存层] |
| [风险点 2]                | 低 (Low)            | 中 (Medium)       | [缓解措施 2]                                 |

---

## 6. 质量保证与验收标准 (Quality Assurance & Success Criteria)

- **功能性标准**:

  - [ ] [核心功能 A] 必须能正常工作。
  - [ ] [核心功能 B] 必须通过所有测试用例。

- **非功能性标准**:

  - **性能**: [例如：API 响应时间 < 200ms]。
  - **可靠性**: [例如：服务可用性 > 99.9%]。
  - **测试覆盖率**: [例如：单元测试覆盖率 < 5%(只要最小测试用例)]。

- **文档**:
  - [ ] 更新用户 TODO LIST 文档
  - [ ] 完善代码注释和 API 文档。

### 建议实施顺序

建议按照阶段顺序实施，每完成一个小任务后暂停，确认需求理解正确后再继续下一个任务。分析代码建议使用 Serena，学习依赖包最新用法建议使用 Content7

## 🧠: 最后的记忆

在此处填写最后的总结

**当前状态**: 一句话概括当前状态

---

_最后更新: YYYY-MM-DD_
_负责人: [你的名字]_
